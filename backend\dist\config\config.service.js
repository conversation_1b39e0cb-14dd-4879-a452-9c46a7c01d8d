"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigService = exports.ConfigSchema = void 0;
const zod_1 = require("zod");
const config_1 = require("@nestjs/config");
const common_1 = require("@nestjs/common");
function boolean(_default = false) {
    return zod_1.z
        .string()
        .optional()
        .transform((str) => str === undefined ? _default : !!str && str !== "0");
}
exports.ConfigSchema = zod_1.z.object({
    instance: zod_1.z.object({
        name: zod_1.z.string().nonempty(),
        emailDomain: zod_1.z.string().nonempty(),
    }),
    auth: zod_1.z.object({
        disableRegisterInviteCheck: boolean(),
        disableRegisterOtpCheck: boolean(),
        disableLoginOtpCheck: boolean(),
        otpExpirationTimeMs: zod_1.z.coerce.number().int().positive(),
    }),
    minio: zod_1.z.object({
        endpoint: zod_1.z.string().nonempty(),
        port: zod_1.z.coerce.number().int().positive(),
        accessKey: zod_1.z.string().nonempty(),
        secretKey: zod_1.z.string().nonempty(),
        useSSL: boolean(true),
    }),
    email: zod_1.z.object({
        host: zod_1.z.string().nonempty(),
        port: zod_1.z.coerce.number().int().positive(),
        user: zod_1.z.string().nonempty(),
        pass: zod_1.z.string().nonempty(),
        disableAllEmails: boolean(),
        disableOtpEmails: boolean(),
        disableInviteEmails: boolean(),
        ignoreErrors: boolean(),
        rejectUnauthorized: zod_1.z.boolean().optional(),
        otpEmailSender: zod_1.z.string().nonempty(),
        inviteEmailSender: zod_1.z.string().nonempty(),
    }),
});
let ConfigService = class ConfigService {
    constructor(configService) {
        this.configService = configService;
    }
    onModuleInit() {
        const rawConfig = {
            instance: {
                name: this.configService.get("INSTANCE_NAME"),
                emailDomain: this.configService.get("INSTANCE_EMAIL_DOMAIN"),
            },
            auth: {
                disableRegisterInviteCheck: this.configService.get("DISABLE_REGISTER_INVITE_CHECK"),
                disableRegisterOtpCheck: this.configService.get("DISABLE_REGISTER_OTP_CHECK"),
                disableLoginOtpCheck: this.configService.get("DISABLE_LOGIN_OTP_CHECK"),
                otpExpirationTimeMs: this.configService.get("OTP_EXPIRATION_TIME_MS"),
            },
            email: {
                host: this.configService.get("EMAIL_HOST"),
                port: this.configService.get("EMAIL_PORT"),
                user: this.configService.get("EMAIL_USER"),
                pass: this.configService.get("EMAIL_PASSWORD"),
                disableAllEmails: this.configService.get("DISABLE_ALL_EMAILS"),
                disableOtpEmails: this.configService.get("DISABLE_OTP_EMAILS"),
                disableInviteEmails: this.configService.get("DISABLE_INVITE_EMAILS"),
                ignoreErrors: this.configService.get("EMAIL_IGNORE_ERRORS"),
                rejectUnauthorized: this.configService.get("EMAIL_REJECT_UNAUTHORIZED"),
                otpEmailSender: this.configService.get("OTP_EMAIL_SENDER"),
                inviteEmailSender: this.configService.get("INVITE_EMAIL_SENDER"),
            },
            minio: {
                endpoint: this.configService.get("MINIO_ENDPOINT"),
                port: this.configService.get("MINIO_PORT"),
                accessKey: this.configService.get("MINIO_ACCESS_KEY"),
                secretKey: this.configService.get("MINIO_SECRET_KEY"),
                useSSL: this.configService.get("MINIO_USE_SSL"),
            },
        };
        const parsedConfig = exports.ConfigSchema.safeParse(rawConfig);
        if (!parsedConfig.success) {
            throw new Error(`Invalid configuration: ${JSON.stringify(parsedConfig.error.issues, null, 2)}`);
        }
        this.config = parsedConfig.data;
    }
};
exports.ConfigService = ConfigService;
exports.ConfigService = ConfigService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], ConfigService);
//# sourceMappingURL=config.service.js.map