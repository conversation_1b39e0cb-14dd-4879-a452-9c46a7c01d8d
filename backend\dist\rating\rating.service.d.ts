import type { Rating } from "@commune/api";
import type { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { Prisma, UserRatingEntityType } from "@prisma/client";
export declare class RatingService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    getKarmaPoints(input: Rating.GetKarmaPointsInput): Promise<({
        sourceUser: {
            name: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import("@prisma/client").$Enums.Locale;
                value: string;
            }[];
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
            imageId: string | null;
            deletedAt: Date | null;
        };
        comment: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        sourceUserId: string;
        targetUserId: string;
        quantity: number;
        createdAt: Date;
        updatedAt: Date;
    })[]>;
    spendKarmaPoint(data: Rating.SpendKarmaPointInput, user: CurrentUser): Promise<{
        id: string;
    }>;
    getUserFeedbacks(input: Rating.GetUserFeedbacksInput): Promise<({
        sourceUser: {
            name: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import("@prisma/client").$Enums.Locale;
                value: string;
            }[];
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
            imageId: string | null;
            deletedAt: Date | null;
        };
        text: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        sourceUserId: string;
        targetUserId: string;
        createdAt: Date;
        updatedAt: Date;
        value: number;
        isAnonymous: boolean;
    })[]>;
    createUserFeedback(data: Rating.CreateUserFeedbackInput, user: CurrentUser): Promise<{
        id: string;
    }>;
    getUserSummary(input: Rating.GetUserSummaryInput): Promise<{
        rating: number;
        karma: number;
        rate: number | null;
    }>;
    upsertRelativeUserRating(data: {
        sourceUserId: string;
        targetUserId: string;
        entityType: UserRatingEntityType;
        entityId: string;
        value: number;
    }, prisma?: Prisma.TransactionClient): Promise<void>;
    deleteRelativeUserRating(data: {
        sourceUserId: string;
        targetUserId: string;
        entityType: UserRatingEntityType;
        entityId: string;
    }, prisma?: Prisma.TransactionClient): Promise<void>;
}
