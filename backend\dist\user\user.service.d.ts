import { User } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { MinioService, FileInfo } from "src/minio/minio.service";
import { UserNameGeneratorService } from "./user-name-generator/user-name-generator.service";
export type CreateUser = {
    referrerId: string | null;
    email: string;
};
export declare class UserService {
    private readonly prisma;
    private readonly minioService;
    private readonly userNameGeneratorService;
    constructor(prisma: PrismaService, minioService: MinioService, userNameGeneratorService: UserNameGeneratorService);
    getUsers(input: User.GetUsersInput, currentUser: CurrentUser): Promise<{
        description: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        image: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        } | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        name: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
    }[]>;
    getUserByEmail(email: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        email: string;
        referrerId: string | null;
        role: import("@prisma/client").$Enums.UserRole;
        imageId: string | null;
    } | null>;
    getUser(id: string, currentUser: CurrentUser): Promise<{
        description: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        image: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        } | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        name: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
    } | undefined>;
    createUser(data: CreateUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        email: string;
        referrerId: string | null;
        role: import("@prisma/client").$Enums.UserRole;
        imageId: string | null;
    }>;
    updateUser(input: User.UpdateUserInput, currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        email: string;
        referrerId: string | null;
        role: import("@prisma/client").$Enums.UserRole;
        imageId: string | null;
    }>;
    updateUserImage(userId: string, file: FileInfo, currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        url: string;
    }>;
}
