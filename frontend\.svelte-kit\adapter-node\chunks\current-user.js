import { z } from "zod";
var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var sitemap_exports = {};
__export(sitemap_exports, {
  GetSitemapGenerationDataOutputSchema: () => GetSitemapGenerationDataOutputSchema
});
var common_exports = {};
__export(common_exports, {
  FormDataToObject: () => FormDataToObject,
  ImageSchema: () => ImageSchema,
  ImagesSchema: () => ImagesSchema,
  JsonStringToObject: () => JsonStringToObject,
  LocalizationLocaleSchema: () => LocalizationLocaleSchema,
  LocalizationLocalesSchema: () => LocalizationLocalesSchema,
  LocalizationSchema: () => LocalizationSchema,
  LocalizationsSchema: () => LocalizationsSchema,
  ObjectWithIdSchema: () => ObjectWithIdSchema,
  PaginationSchema: () => PaginationSchema,
  WebsiteLocaleSchema: () => WebsiteLocaleSchema,
  createdAt: () => createdAt,
  deletedAt: () => deletedAt,
  email: () => email,
  id: () => id,
  idOrNull: () => idOrNull,
  imageUrl: () => imageUrl,
  maybeImageUrl: () => maybeImageUrl,
  pagination: () => pagination,
  parseInput: () => parseInput,
  parseUnknown: () => parseUnknown,
  query: () => query,
  searchIds: () => searchIds,
  searchQuery: () => searchQuery,
  stringToDate: () => stringToDate,
  updatedAt: () => updatedAt,
  url: () => url
});
var consts_exports = {};
__export(consts_exports, {
  ALLOWED_IMAGE_FILE_TYPES: () => ALLOWED_IMAGE_FILE_TYPES,
  MAX_IMAGE_FILE_SIZE: () => MAX_IMAGE_FILE_SIZE,
  PAGE_SIZE: () => PAGE_SIZE
});
var PAGE_SIZE = 20;
var ALLOWED_IMAGE_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];
var MAX_IMAGE_FILE_SIZE = 5 * 1024 * 1024;
var id = z.string().nanoid();
var idOrNull = id.nullable().default(null);
var url = z.string().url();
var email = z.string().email();
var query = z.string().nonempty();
var imageUrl = z.string().nonempty();
var maybeImageUrl = imageUrl.nullable();
var createdAt = z.date();
var updatedAt = z.date();
var deletedAt = z.date().nullable();
var searchIds = z.array(id).min(1);
var searchQuery = z.string().nonempty();
var stringToDate = z.union([z.number(), z.string(), z.date()]).pipe(z.coerce.date());
function JsonStringToObject(schema) {
  return z.string().transform((value) => JSON.parse(value)).pipe(z.object(schema));
}
function FormDataToObject(schema) {
  return z.object({
    data: JsonStringToObject(schema)
  });
}
var ObjectWithIdSchema = z.object({ id });
var WebsiteLocaleSchema = z.enum(["en", "ru"]);
var LocalizationLocaleSchema = z.enum(["en", "ru"]);
var LocalizationLocalesSchema = z.array(LocalizationLocaleSchema).min(1);
var LocalizationSchema = z.object({
  locale: LocalizationLocaleSchema,
  value: z.string().nonempty()
});
var LocalizationsSchema = z.array(LocalizationSchema);
var ImageSchema = z.object({
  id,
  url: z.string(),
  createdAt: stringToDate,
  updatedAt: stringToDate
});
var ImagesSchema = z.array(ImageSchema);
var pagination = {
  offset: z.coerce.number().int().default(0),
  limit: z.coerce.number().int().positive().max(100).default(PAGE_SIZE),
  page: z.coerce.number().int().positive().default(1),
  size: z.coerce.number().int().positive().max(100).default(PAGE_SIZE)
};
var PaginationSchema = z.object({
  page: pagination.page,
  size: pagination.size
}).default({
  page: 1,
  size: PAGE_SIZE
});
function parseInput(schema, value) {
  return schema.parse(value);
}
function parseUnknown(schema, value) {
  return schema.parse(value);
}
var GetSitemapGenerationDataOutputSchema = z.object({
  communeIds: z.array(id),
  reactorPostIds: z.array(id),
  reactorHubIds: z.array(id),
  reactorCommunityIds: z.array(id)
});
var auth_exports = {};
__export(auth_exports, {
  SendOtpInputSchema: () => SendOtpInputSchema,
  SendOtpOutputSchema: () => SendOtpOutputSchema,
  SigninInputSchema: () => SigninInputSchema,
  SignupInputSchema: () => SignupInputSchema,
  SuccessfulOutputSchema: () => SuccessfulOutputSchema,
  otp: () => otp
});
var user_exports = {};
__export(user_exports, {
  CreateUserTitleInputSchema: () => CreateUserTitleInputSchema,
  DeleteUserInviteInputSchema: () => DeleteUserInviteInputSchema,
  GetMeOutputSchema: () => GetMeOutputSchema,
  GetUserInvitesInputSchema: () => GetUserInvitesInputSchema,
  GetUserInvitesOutputSchema: () => GetUserInvitesOutputSchema,
  GetUserNoteInputSchema: () => GetUserNoteInputSchema,
  GetUserNoteOutputSchema: () => GetUserNoteOutputSchema,
  GetUserOutputSchema: () => GetUserOutputSchema,
  GetUserTitlesInputSchema: () => GetUserTitlesInputSchema,
  GetUserTitlesOutputSchema: () => GetUserTitlesOutputSchema,
  GetUsersInputSchema: () => GetUsersInputSchema,
  GetUsersOutputSchema: () => GetUsersOutputSchema,
  SimpleUserSchema: () => SimpleUserSchema,
  UpdateUserInputSchema: () => UpdateUserInputSchema,
  UpdateUserNoteInputSchema: () => UpdateUserNoteInputSchema,
  UpdateUserTitleInputSchema: () => UpdateUserTitleInputSchema,
  UpsertUserInviteInputSchema: () => UpsertUserInviteInputSchema,
  UserRoleSchema: () => UserRoleSchema,
  userDescription: () => userDescription,
  userImage: () => userImage,
  userName: () => userName,
  userNoteText: () => userNoteText,
  userTitleColor: () => userTitleColor,
  userTitleIsActive: () => userTitleIsActive,
  userTitleName: () => userTitleName
});
var userName = LocalizationsSchema.min(1);
var userDescription = LocalizationsSchema;
var userImage = imageUrl.nullable();
var userTitleName = LocalizationsSchema.min(1);
var userTitleIsActive = z.boolean();
var userTitleColor = z.string().nonempty().nullable();
var userNoteText = z.string().nonempty();
var UserRoleSchema = z.enum([
  "admin",
  "moderator",
  "user"
]);
var SimpleUserSchema = z.object({
  id,
  name: userName,
  image: userImage
});
var GetMeOutputSchema = z.object({
  id,
  email,
  role: UserRoleSchema,
  name: userName,
  description: userDescription,
  image: imageUrl.nullable(),
  createdAt,
  updatedAt
});
var GetUsersInputSchema = z.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery
}).partial();
var GetUserOutputSchema = z.object({
  id,
  role: UserRoleSchema,
  name: userName,
  description: userDescription,
  image: userImage,
  createdAt,
  updatedAt,
  deletedAt: deletedAt.optional()
});
var GetUsersOutputSchema = z.array(GetUserOutputSchema);
var UpdateUserInputSchema = z.object({
  id,
  name: userName.optional(),
  description: userDescription.optional()
});
var CreateUserTitleInputSchema = z.object({
  userId: id,
  name: userTitleName,
  isActive: userTitleIsActive,
  color: userTitleColor
});
var UpdateUserTitleInputSchema = z.object({
  id,
  name: userTitleName.optional(),
  isActive: userTitleIsActive.optional(),
  color: userTitleColor.optional()
});
var GetUserTitlesInputSchema = z.object({
  userId: id,
  ids: searchIds.optional(),
  isActive: userTitleIsActive.optional()
});
var GetUserTitlesOutputSchema = z.array(
  z.object({
    id,
    userId: id,
    name: userTitleName,
    isActive: userTitleIsActive,
    color: userTitleColor,
    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional()
  })
);
var GetUserNoteInputSchema = z.object({
  userId: id
});
var GetUserNoteOutputSchema = z.object({
  text: userNoteText.nullable()
});
var UpdateUserNoteInputSchema = z.object({
  userId: id,
  text: userNoteText.nullable()
});
var GetUserInvitesInputSchema = z.object({
  pagination: PaginationSchema
});
var GetUserInvitesOutputSchema = z.array(z.object({
  id,
  email,
  name: z.string().nonempty().nullable(),
  locale: LocalizationLocaleSchema,
  isUsed: z.boolean()
}));
var UpsertUserInviteInputSchema = z.object({
  email,
  name: z.string().nonempty().nullable(),
  locale: LocalizationLocaleSchema
});
var DeleteUserInviteInputSchema = z.object({
  id
});
var otp = z.string().nonempty().length(6);
var SendOtpInputSchema = z.object({
  email
});
var SendOtpOutputSchema = z.object({
  isSent: z.boolean()
});
var SignupInputSchema = z.object({
  referrerId: id.nullable(),
  email,
  otp
});
var SigninInputSchema = z.object({
  email,
  otp
});
var SuccessfulOutputSchema = z.object({
  id,
  email,
  role: UserRoleSchema
});
var commune_exports = {};
__export(commune_exports, {
  CommuneInvitationStatusSchema: () => CommuneInvitationStatusSchema,
  CommuneJoinRequestStatusSchema: () => CommuneJoinRequestStatusSchema,
  CommuneMemberTypeSchema: () => CommuneMemberTypeSchema,
  CreateCommuneInputSchema: () => CreateCommuneInputSchema,
  CreateCommuneInvitationInputSchema: () => CreateCommuneInvitationInputSchema,
  CreateCommuneJoinRequestInputSchema: () => CreateCommuneJoinRequestInputSchema,
  CreateCommuneMemberInputSchema: () => CreateCommuneMemberInputSchema,
  GetCommuneInvitationsInputSchema: () => GetCommuneInvitationsInputSchema,
  GetCommuneInvitationsOutputSchema: () => GetCommuneInvitationsOutputSchema,
  GetCommuneJoinRequestsInputSchema: () => GetCommuneJoinRequestsInputSchema,
  GetCommuneJoinRequestsOutputSchema: () => GetCommuneJoinRequestsOutputSchema,
  GetCommuneMemberOutputSchema: () => GetCommuneMemberOutputSchema,
  GetCommuneMembersInputSchema: () => GetCommuneMembersInputSchema,
  GetCommuneMembersOutputSchema: () => GetCommuneMembersOutputSchema,
  GetCommuneOutputSchema: () => GetCommuneOutputSchema,
  GetCommunesInputSchema: () => GetCommunesInputSchema,
  GetCommunesOutputSchema: () => GetCommunesOutputSchema,
  TransferHeadStatusInputSchema: () => TransferHeadStatusInputSchema,
  UpdateCommuneInputSchema: () => UpdateCommuneInputSchema,
  communeDescription: () => communeDescription,
  communeMemberActorType: () => communeMemberActorType,
  communeMemberName: () => communeMemberName,
  communeName: () => communeName
});
var CommuneMemberTypeSchema = z.enum(["user"]);
var communeName = LocalizationsSchema.min(1);
var communeDescription = LocalizationsSchema;
var communeMemberActorType = CommuneMemberTypeSchema;
var communeMemberName = z.union([userName, communeName]);
var TransferHeadStatusInputSchema = z.object({
  communeId: id,
  newHeadUserId: id
});
var GetCommunesInputSchema = z.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery,
  userId: id
}).partial();
var GetCommuneOutputSchema = z.object({
  id,
  name: communeName,
  description: communeDescription,
  headMember: z.object({
    actorType: communeMemberActorType,
    actorId: id,
    name: communeMemberName,
    image: maybeImageUrl
  }),
  memberCount: z.number().int().positive(),
  image: maybeImageUrl,
  createdAt,
  updatedAt,
  deletedAt: deletedAt.optional()
});
var GetCommunesOutputSchema = z.array(GetCommuneOutputSchema);
var CreateCommuneInputSchema = z.object({
  headUserId: id.optional(),
  name: communeName,
  description: communeDescription
});
var UpdateCommuneInputSchema = z.object({
  id,
  name: communeName.optional(),
  description: communeDescription.optional()
});
var GetCommuneMembersInputSchema = z.object({
  pagination: PaginationSchema,
  communeId: id
});
var GetCommuneMemberOutputSchema = z.object({
  id,
  actorType: communeMemberActorType,
  actorId: id,
  name: communeMemberName,
  image: maybeImageUrl,
  createdAt,
  deletedAt: deletedAt.nullable()
});
var GetCommuneMembersOutputSchema = z.array(GetCommuneMemberOutputSchema);
var CreateCommuneMemberInputSchema = z.object({
  communeId: id,
  userId: id
});
var CommuneInvitationStatusSchema = z.enum(["pending", "accepted", "rejected", "expired"]);
var GetCommuneInvitationsInputSchema = z.object({
  pagination: PaginationSchema,
  communeId: id.optional()
});
var GetCommuneInvitationsOutputSchema = z.array(
  z.object({
    id,
    communeId: id,
    userId: id,
    status: CommuneInvitationStatusSchema,
    createdAt: z.date(),
    updatedAt: z.date()
  })
);
var CreateCommuneInvitationInputSchema = z.object({
  communeId: id,
  userId: id
});
var CommuneJoinRequestStatusSchema = z.enum(["pending", "accepted", "rejected"]);
var GetCommuneJoinRequestsInputSchema = z.object({
  pagination: PaginationSchema,
  communeId: id.optional()
});
var GetCommuneJoinRequestsOutputSchema = z.array(
  z.object({
    id,
    communeId: id,
    userId: id,
    status: CommuneJoinRequestStatusSchema,
    createdAt: z.date(),
    updatedAt: z.date()
  })
);
var CreateCommuneJoinRequestInputSchema = z.object({
  communeId: id,
  userId: id
});
var reactor_exports = {};
__export(reactor_exports, {
  AnonimifyCommentInputSchema: () => AnonimifyCommentInputSchema,
  CommentEntityTypeSchema: () => CommentEntityTypeSchema,
  CreateCommentInputSchema: () => CreateCommentInputSchema,
  CreateCommunityInputSchema: () => CreateCommunityInputSchema,
  CreateHubInputSchema: () => CreateHubInputSchema,
  CreateLensInputSchema: () => CreateLensInputSchema,
  CreatePostInputSchema: () => CreatePostInputSchema,
  DeleteCommentInputSchema: () => DeleteCommentInputSchema,
  DeletePostInputSchema: () => DeletePostInputSchema,
  GetCommentsInputSchema: () => GetCommentsInputSchema,
  GetCommentsOutputSchema: () => GetCommentsOutputSchema,
  GetCommunitiesInputSchema: () => GetCommunitiesInputSchema,
  GetCommunitiesOutputSchema: () => GetCommunitiesOutputSchema,
  GetHubsInputSchema: () => GetHubsInputSchema,
  GetHubsOutputSchema: () => GetHubsOutputSchema,
  GetLensesOutputSchema: () => GetLensesOutputSchema,
  GetPostImagesInputSchema: () => GetPostImagesInputSchema,
  GetPostImagesOutputSchema: () => GetPostImagesOutputSchema,
  GetPostOutputSchema: () => GetPostOutputSchema,
  GetPostsInputSchema: () => GetPostsInputSchema,
  GetPostsOutputSchema: () => GetPostsOutputSchema,
  PostImageSchema: () => PostImageSchema,
  PostUsefulnessSchema: () => PostUsefulnessSchema,
  RatingSchema: () => RatingSchema,
  RatingTypeSchema: () => RatingTypeSchema,
  UpdateCommentInputSchema: () => UpdateCommentInputSchema,
  UpdateCommentRatingInputSchema: () => UpdateCommentRatingInputSchema,
  UpdateCommentRatingOutputSchema: () => UpdateCommentRatingOutputSchema,
  UpdateCommunityInputSchema: () => UpdateCommunityInputSchema,
  UpdateHubInputSchema: () => UpdateHubInputSchema,
  UpdateLensInputSchema: () => UpdateLensInputSchema,
  UpdatePostInputSchema: () => UpdatePostInputSchema,
  UpdatePostRatingInputSchema: () => UpdatePostRatingInputSchema,
  UpdatePostRatingOutputSchema: () => UpdatePostRatingOutputSchema,
  UpdatePostUsefulnessInputSchema: () => UpdatePostUsefulnessInputSchema,
  UpdatePostUsefulnessOutputSchema: () => UpdatePostUsefulnessOutputSchema,
  commentBody: () => commentBody,
  communityDescription: () => communityDescription,
  communityImage: () => communityImage,
  communityName: () => communityName,
  hubDescription: () => hubDescription,
  hubImage: () => hubImage,
  hubName: () => hubName,
  lensCode: () => lensCode,
  lensName: () => lensName,
  postBody: () => postBody,
  postTitle: () => postTitle,
  postUsefulness: () => postUsefulness
});
var tag_exports = {};
__export(tag_exports, {
  CreateTagInputSchema: () => CreateTagInputSchema,
  GetTagsInputSchema: () => GetTagsInputSchema,
  GetTagsOutputSchema: () => GetTagsOutputSchema,
  UpdateTagInputSchema: () => UpdateTagInputSchema,
  tagName: () => tagName
});
var tagName = LocalizationsSchema.min(1);
var GetTagsInputSchema = z.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery
}).partial();
var GetTagsOutputSchema = z.array(
  z.object({
    id,
    name: tagName,
    deletedAt: deletedAt.optional()
  })
);
var CreateTagInputSchema = z.object({
  name: tagName
});
var UpdateTagInputSchema = z.object({
  id,
  name: tagName
});
var RatingTypeSchema = z.enum(["like", "dislike"]);
var RatingSchema = z.object({
  likes: z.number().int().nonnegative(),
  dislikes: z.number().int().nonnegative(),
  status: RatingTypeSchema.nullable()
});
var hubName = LocalizationsSchema.min(1);
var hubDescription = LocalizationsSchema.min(1);
var hubImage = maybeImageUrl;
var communityName = LocalizationsSchema.min(1);
var communityDescription = LocalizationsSchema.min(1);
var communityImage = maybeImageUrl;
var postUsefulness = z.number().int().min(0).max(10);
var PostUsefulnessSchema = z.object({
  value: postUsefulness.nullable(),
  count: z.number().int().nonnegative(),
  totalValue: z.number().min(0).max(10).nullable()
});
var postTitle = LocalizationsSchema.min(1);
var postBody = LocalizationsSchema.min(1);
var PostImageSchema = z.object({
  id,
  url: imageUrl
});
var GetPostOutputSchema = z.object({
  id,
  hub: z.object({
    id,
    name: hubName,
    image: hubImage
  }).nullable(),
  community: z.object({
    id,
    name: communityName,
    image: communityImage
  }).nullable(),
  author: SimpleUserSchema,
  rating: RatingSchema,
  usefulness: PostUsefulnessSchema,
  title: postTitle,
  body: postBody,
  tags: z.array(
    z.object({
      id,
      name: tagName
    })
  ),
  createdAt,
  updatedAt,
  deletedAt: deletedAt.optional()
});
var GetPostsInputSchema = z.object({
  pagination: PaginationSchema,
  id: id.optional(),
  lensId: id.nullable()
});
var GetPostsOutputSchema = z.array(GetPostOutputSchema);
var GetPostImagesInputSchema = z.object({
  id
});
var GetPostImagesOutputSchema = z.array(PostImageSchema);
var CreatePostInputSchema = z.object({
  hubId: id.nullable(),
  communityId: id.nullable(),
  title: postTitle,
  body: postBody,
  tagIds: z.array(id),
  imageIds: z.array(id)
});
var UpdatePostInputSchema = z.object({
  id,
  title: postTitle.optional(),
  body: postBody.optional(),
  tagIds: z.array(id).optional(),
  imageIds: z.array(id).optional()
});
var DeletePostInputSchema = z.object({
  id,
  reason: z.string().nonempty().nullable()
});
var UpdatePostRatingInputSchema = z.object({
  id,
  type: RatingTypeSchema
});
var UpdatePostRatingOutputSchema = RatingSchema;
var UpdatePostUsefulnessInputSchema = z.object({
  id,
  value: postUsefulness.nullable()
});
var UpdatePostUsefulnessOutputSchema = PostUsefulnessSchema;
var CommentEntityTypeSchema = z.enum(["post", "comment"]);
var commentBody = LocalizationsSchema.min(1);
var GetCommentsInputSchema = z.union([
  z.object({
    id,
    entityType: z.never().optional(),
    entityId: z.never().optional()
  }),
  z.object({
    id: z.never().optional(),
    entityType: CommentEntityTypeSchema,
    entityId: id
  })
]);
var GetCommentsOutputSchema = z.array(
  z.object({
    id,
    path: z.string().nonempty(),
    author: SimpleUserSchema.nullable(),
    isAnonymous: z.boolean(),
    anonimityReason: z.string().nonempty().nullable(),
    rating: RatingSchema,
    body: commentBody.nullable(),
    childrenCount: z.number().int().nonnegative(),
    deleteReason: z.string().nonempty().nullable(),
    createdAt,
    updatedAt,
    deletedAt
  })
);
var CreateCommentInputSchema = z.object({
  entityType: CommentEntityTypeSchema,
  entityId: id,
  body: commentBody
});
var UpdateCommentInputSchema = z.object({
  id,
  body: commentBody.optional()
});
var DeleteCommentInputSchema = z.object({
  id,
  reason: z.string().nonempty().nullable()
});
var UpdateCommentRatingInputSchema = z.object({
  id,
  type: RatingTypeSchema
});
var UpdateCommentRatingOutputSchema = RatingSchema;
var AnonimifyCommentInputSchema = z.object({
  id,
  reason: z.string().nonempty().nullable()
});
var lensName = z.string().nonempty();
var lensCode = z.string().nonempty();
var GetLensesOutputSchema = z.array(
  z.object({
    id,
    name: lensName,
    code: lensCode
  })
);
var CreateLensInputSchema = z.object({
  name: lensName,
  code: lensCode
});
var UpdateLensInputSchema = z.object({
  id,
  name: lensName.optional(),
  code: lensCode.optional()
});
var GetHubsInputSchema = z.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery
}).partial();
var GetHubsOutputSchema = z.array(
  z.object({
    id,
    headUser: SimpleUserSchema,
    image: hubImage,
    name: hubName,
    description: hubDescription,
    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional()
  })
);
var CreateHubInputSchema = z.object({
  headUserId: id.nullable(),
  name: hubName,
  description: hubDescription
});
var UpdateHubInputSchema = z.object({
  id,
  name: hubName.optional(),
  description: hubDescription.optional()
});
var GetCommunitiesInputSchema = z.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery,
  hubId: id
}).partial();
var GetCommunitiesOutputSchema = z.array(
  z.object({
    id,
    hub: z.object({
      id,
      name: hubName,
      image: hubImage
    }).nullable(),
    headUser: SimpleUserSchema,
    image: communityImage,
    name: communityName,
    description: communityDescription,
    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional()
  })
);
var CreateCommunityInputSchema = z.object({
  hubId: id.nullable(),
  headUserId: id.nullable(),
  name: communityName,
  description: communityDescription
});
var UpdateCommunityInputSchema = z.object({
  id,
  name: communityName.optional(),
  description: communityDescription.optional()
});
var rating_exports = {};
__export(rating_exports, {
  CreateUserFeedbackInputSchema: () => CreateUserFeedbackInputSchema,
  GetKarmaPointsInputSchema: () => GetKarmaPointsInputSchema,
  GetKarmaPointsOutputSchema: () => GetKarmaPointsOutputSchema,
  GetUserFeedbacksInputSchema: () => GetUserFeedbacksInputSchema,
  GetUserFeedbacksOutputSchema: () => GetUserFeedbacksOutputSchema,
  GetUserSummaryInputSchema: () => GetUserSummaryInputSchema,
  GetUserSummaryOutputSchema: () => GetUserSummaryOutputSchema,
  SpendKarmaPointInputSchema: () => SpendKarmaPointInputSchema,
  karmaPointComment: () => karmaPointComment,
  karmaPointQuantity: () => karmaPointQuantity,
  userFeedbackText: () => userFeedbackText,
  userFeedbackValue: () => userFeedbackValue
});
var karmaPointQuantity = z.number().int();
var karmaPointComment = LocalizationsSchema.min(1);
var GetKarmaPointsInputSchema = z.object({
  pagination: PaginationSchema,
  userId: id
});
var GetKarmaPointsOutputSchema = z.array(
  z.object({
    id,
    author: SimpleUserSchema,
    quantity: karmaPointQuantity,
    comment: karmaPointComment
  })
);
var SpendKarmaPointInputSchema = z.object({
  sourceUserId: id,
  targetUserId: id,
  quantity: karmaPointQuantity,
  comment: karmaPointComment
});
var userFeedbackValue = z.number().int().min(0).max(10);
var userFeedbackText = LocalizationsSchema.min(1);
var GetUserFeedbacksInputSchema = z.object({
  pagination: PaginationSchema,
  userId: id
});
var GetUserFeedbacksOutputSchema = z.array(
  z.object({
    id,
    author: SimpleUserSchema.nullable(),
    isAnonymous: z.boolean(),
    value: userFeedbackValue,
    text: userFeedbackText
  })
);
var CreateUserFeedbackInputSchema = z.object({
  sourceUserId: id,
  targetUserId: id,
  value: userFeedbackValue,
  isAnonymous: z.boolean(),
  text: userFeedbackText
});
var GetUserSummaryInputSchema = z.object({
  userId: id
});
var GetUserSummaryOutputSchema = z.object({
  rating: z.number().int(),
  karma: z.number().int(),
  rate: z.number().min(0).max(10).nullable()
});
z.object({
  id: common_exports.id,
  email: common_exports.email,
  role: user_exports.UserRoleSchema
});
export {
  consts_exports as a,
  rating_exports as b,
  common_exports as c,
  commune_exports as d,
  auth_exports as e,
  reactor_exports as r,
  tag_exports as t,
  user_exports as u
};
