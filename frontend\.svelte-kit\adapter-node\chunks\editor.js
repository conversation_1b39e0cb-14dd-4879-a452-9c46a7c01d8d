import { W as current_component, u as push, G as attr_class, T as clsx, y as attr, V as bind_props, w as pop, O as copy_payload, P as assign_payload } from "./index.js";
import { j as fallback } from "./utils.js";
function onDestroy(fn) {
  var context = (
    /** @type {Component} */
    current_component
  );
  (context.d ??= []).push(fn);
}
const uuid = (prefix) => {
  return prefix + "_" + Math.floor(Math.random() * 1e9) + String(Date.now());
};
function Editor$1($$payload, $$props) {
  push();
  let id = fallback($$props["id"], () => uuid("tinymce-svelte"), true);
  let inline = fallback($$props["inline"], void 0);
  let disabled = fallback($$props["disabled"], false);
  let readonly = fallback($$props["readonly"], false);
  let apiKey = fallback($$props["apiKey"], "no-api-key");
  let licenseKey = fallback($$props["licenseKey"], void 0);
  let channel = fallback($$props["channel"], "7");
  let scriptSrc = fallback($$props["scriptSrc"], void 0);
  let conf = fallback($$props["conf"], () => ({}), true);
  let modelEvents = fallback($$props["modelEvents"], "change input undo redo");
  let value = fallback($$props["value"], "");
  let text = fallback($$props["text"], "");
  let cssClass = fallback($$props["cssClass"], "tinymce-wrapper");
  onDestroy(() => {
  });
  $$payload.out.push(`<div${attr_class(clsx(
    //
    // bind model events
    cssClass
  ))}>`);
  if (inline) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div${attr("id", id)}></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<textarea${attr("id", id)} style="visibility:hidden"></textarea>`);
  }
  $$payload.out.push(`<!--]--></div>`);
  bind_props($$props, {
    id,
    inline,
    disabled,
    readonly,
    apiKey,
    licenseKey,
    channel,
    scriptSrc,
    conf,
    modelEvents,
    value,
    text,
    cssClass
  });
  pop();
}
function Editor($$payload, $$props) {
  push();
  let { content = void 0, onEditorInit } = $$props;
  const editorConfig = {
    // plugins: ["lists", "link", "image", "code", "table"],
    // toolbar:
    //   "undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist | link image | code",
    // menubar: false,
    // height: 300,
    toolbar: "undo redo | bold italic strikethrough underline | alignleft aligncenter alignright",
    menubar: false,
    height: 300,
    promotion: false,
    // branding: false,
    // Preserve absolute URLs and prevent TinyMCE from modifying them
    relative_urls: false,
    init_instance_callback: (editor) => {
      onEditorInit?.(editor);
    }
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Editor$1($$payload2, {
      licenseKey: "gpl",
      scriptSrc: "/tinymce/tinymce.min.js",
      conf: editorConfig,
      get value() {
        return content;
      },
      set value($$value) {
        content = $$value;
        $$settled = false;
      }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { content });
  pop();
}
export {
  Editor as E
};
