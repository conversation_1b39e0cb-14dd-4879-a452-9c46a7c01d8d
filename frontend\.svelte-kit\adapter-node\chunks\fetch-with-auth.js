import { g as goto } from "./client.js";
import "./current-user.js";
async function fetchWithAuth(input, init) {
  const requestInit = {
    ...init,
    credentials: "include"
  };
  const response = await fetch(input, requestInit);
  if (response.status === 401) {
    return redirectToLoginPage();
  }
  return response;
}
function redirectToLoginPage() {
  goto();
  return new Promise(() => {
  });
}
export {
  fetchWithAuth as f
};
