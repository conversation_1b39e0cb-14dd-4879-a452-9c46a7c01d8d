import { y as attr, z as escape_html, G as attr_class, w as pop, u as push } from "./index.js";
import "./current-user.js";
import "@sveltejs/kit/internal";
import "./exports.js";
import "./state.svelte.js";
import "@formatjs/intl-localematcher";
import "@sveltejs/kit";
function Locale_switcher($$payload, $$props) {
  push();
  const { currentLocale } = $$props;
  let isOpen = false;
  function getLocaleDisplayText(locale) {
    switch (locale) {
      case "en":
        return "English";
      case "ru":
        return "Русский";
      case null:
        return "Auto";
      default:
        return "Auto";
    }
  }
  $$payload.out.push(`<div class="dropdown mx-2"><button class="btn btn-outline-secondary btn-sm dropdown-toggle d-flex align-items-center justify-content-center" style="width: 110px; min-width: 110px;" type="button" id="locale-dropdown"${attr("aria-expanded", isOpen)}><i class="bi bi-globe me-1"></i> ${escape_html(getLocaleDisplayText(currentLocale))}</button> <ul${attr_class(`dropdown-menu ${""}`)} aria-labelledby="locale-dropdown"><li><button${attr_class(`dropdown-item ${currentLocale === "en" ? "active" : ""}`)}><i class="bi bi-translate me-2"></i> English</button></li> <li><button${attr_class(`dropdown-item ${currentLocale === "ru" ? "active" : ""}`)}><i class="bi bi-translate me-2"></i> Русский</button></li> <li><button${attr_class(`dropdown-item ${currentLocale === null ? "active" : ""}`)}><i class="bi bi-globe me-2"></i> Auto</button></li></ul></div>`);
  pop();
}
export {
  Locale_switcher as L
};
