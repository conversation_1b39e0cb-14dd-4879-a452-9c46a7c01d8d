import { u as push, y as attr, z as escape_html, G as attr_class, K as ensure_array_like, V as bind_props, w as pop, J as stringify } from "./index.js";
function Localized_textarea($$payload, $$props) {
  push();
  const i18n = {
    en: {
      languages: { en: "English", ru: "Russian" },
      providedTranslations: "Provided translations:"
    },
    ru: {
      languages: {
        en: "Английский",
        ru: "Русский"
      },
      providedTranslations: "Указанные переводы:"
    }
  };
  let { value = void 0, $$slots, $$events, ...props } = $$props;
  const {
    id,
    label,
    placeholder,
    rows = 3,
    required = false,
    locale,
    languageSelectPosition = "top",
    children
  } = props;
  const t = i18n[locale];
  let selectedLanguage = locale;
  function getCurrentValue() {
    const localization = value.find((val) => val.locale === selectedLanguage);
    return localization?.value || "";
  }
  function getLanguageDisplay() {
    return selectedLanguage.toUpperCase();
  }
  $$payload.out.push(`<div class="mb-3">`);
  if (languageSelectPosition === "top") {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="d-flex justify-content-between align-items-center mb-2"><label${attr("for", id)} class="form-label mb-0">${escape_html(label)} `);
    if (required) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<span class="text-danger">*</span>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></label> <div class="dropdown"><button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"${attr("id", `dropdown-${id}`)} data-bs-toggle="dropdown" aria-expanded="false" style="width: 60px; display: flex; justify-content: space-between; align-items: center;">${escape_html(getLanguageDisplay())}</button> <ul class="dropdown-menu dropdown-menu-end"${attr("aria-labelledby", `dropdown-${id}`)}><li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === "en" ? "active" : "")}`)} type="button">${escape_html(t.languages.en)}</button></li> <li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === "ru" ? "active" : "")}`)} type="button">${escape_html(t.languages.ru)}</button></li></ul></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<label${attr("for", id)} class="form-label mb-2">${escape_html(label)} `);
    if (required) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<span class="text-danger">*</span>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></label>`);
  }
  $$payload.out.push(`<!--]--> <textarea class="form-control"${attr("id", id)}${attr("rows", rows)}${attr("placeholder", placeholder)}${attr("required", required, true)}>`);
  const $$body = escape_html(getCurrentValue());
  if ($$body) {
    $$payload.out.push(`${$$body}`);
  }
  $$payload.out.push(`</textarea> `);
  if (languageSelectPosition === "bottom") {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="d-flex justify-content-between align-items-center mt-2"><div class="d-flex align-items-center">`);
    if (children) {
      $$payload.out.push("<!--[-->");
      children($$payload);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> <div class="dropdown"><button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"${attr("id", `dropdown-${id}`)} data-bs-toggle="dropdown" aria-expanded="false" style="width: 60px; display: flex; justify-content: space-between; align-items: center;">${escape_html(getLanguageDisplay())}</button> <ul class="dropdown-menu dropdown-menu-end"${attr("aria-labelledby", `dropdown-${id}`)}><li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === "en" ? "active" : "")}`)} type="button">${escape_html(t.languages.en)}</button></li> <li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === "ru" ? "active" : "")}`)} type="button">${escape_html(t.languages.ru)}</button></li></ul></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (value.length > 0) {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(value.filter(Boolean));
    $$payload.out.push(`<div class="mt-2 small text-muted"><div>${escape_html(t.providedTranslations)}</div> <ul class="list-unstyled mb-0 mt-1"><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let val = each_array[$$index];
      $$payload.out.push(`<li class="badge bg-light text-dark me-1">${escape_html(t.languages[val.locale])}: ${escape_html(val.value.length > 50 ? val.value.slice(0, 47) + "..." : val.value)}</li>`);
    }
    $$payload.out.push(`<!--]--></ul></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  bind_props($$props, { value });
  pop();
}
export {
  Localized_textarea as L
};
