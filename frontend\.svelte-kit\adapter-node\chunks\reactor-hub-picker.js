import { u as push, z as escape_html, V as bind_props, w as pop } from "./index.js";
import { g as getClient } from "./acrpc.js";
/* empty css                                                       */
function Reactor_hub_picker($$payload, $$props) {
  push();
  const i18n = {
    en: {
      hub: "Hub",
      selectHub: "Select hub",
      searchHubs: "Search hubs...",
      noHubsFound: "No hubs found",
      loading: "Loading...",
      clearSelection: "Clear selection"
    },
    ru: {
      hub: "Хаб",
      selectHub: "Выбрать хаб",
      searchHubs: "Поиск хабов...",
      noHubsFound: "Хабы не найдены",
      loading: "Загрузка...",
      clearSelection: "Очистить выбор"
    }
  };
  const { fetcher: api } = getClient();
  let { selectedHubId = void 0, locale, label, placeholder } = $$props;
  const t = i18n[locale];
  let showHubInput = false;
  $$payload.out.push(`<div class="mb-3">`);
  if (label && showHubInput) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<label for="hub-search-input" class="form-label">${escape_html(label)}</label>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="hub-picker svelte-11tmxf3"><div class="selected-hub d-flex align-items-center gap-2 mb-2 svelte-11tmxf3">`);
  {
    $$payload.out.push("<!--[!-->");
    {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<button type="button" class="btn btn-outline-secondary"><i class="bi bi-collection-fill"></i> ${escape_html(t.selectHub)}</button>`);
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]--></div> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></div>`);
  bind_props($$props, { selectedHubId });
  pop();
}
export {
  Reactor_hub_picker as R
};
