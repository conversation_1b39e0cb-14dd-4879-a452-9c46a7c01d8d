import { u as push, z as escape_html, V as bind_props, w as pop } from "./index.js";
import { g as getClient } from "./acrpc.js";
/* empty css                                                       */
function User_picker($$payload, $$props) {
  push();
  const i18n = {
    en: {
      user: "User",
      selectUser: "Select user",
      searchUsers: "Search users...",
      noUsersFound: "No users found",
      loading: "Loading...",
      clearSelection: "Clear selection"
    },
    ru: {
      user: "Пользователь",
      selectUser: "Выбрать пользователя",
      searchUsers: "Поиск пользователей...",
      noUsersFound: "Пользователи не найдены",
      loading: "Загрузка...",
      clearSelection: "Очистить выбор"
    }
  };
  const { fetcher: api } = getClient();
  let { selectedUserId = void 0, locale, label, placeholder } = $$props;
  const t = i18n[locale];
  let showUserInput = false;
  $$payload.out.push(`<div class="mb-3">`);
  if (label && showUserInput) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<label for="user-search-input" class="form-label">${escape_html(label)}</label>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="user-picker svelte-fbacwr"><div class="selected-user d-flex align-items-center gap-2 mb-2 svelte-fbacwr">`);
  {
    $$payload.out.push("<!--[!-->");
    {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<button type="button" class="btn btn-outline-secondary"><i class="bi bi-person-plus"></i> ${escape_html(t.selectUser)}</button>`);
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]--></div> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></div>`);
  bind_props($$props, { selectedUserId });
  pop();
}
export {
  User_picker as U
};
