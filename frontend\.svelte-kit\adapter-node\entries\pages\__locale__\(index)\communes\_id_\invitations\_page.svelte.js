import { x as head, z as escape_html, y as attr, K as ensure_array_like, G as attr_class, w as pop, u as push } from "../../../../../../../chunks/index.js";
import "../../../../../../../chunks/current-user.js";
import { g as getClient } from "../../../../../../../chunks/acrpc.js";
import "@sveltejs/kit/internal";
import "../../../../../../../chunks/exports.js";
import "../../../../../../../chunks/state.svelte.js";
import "@formatjs/intl-localematcher";
import "@sveltejs/kit";
import { f as formatDate } from "../../../../../../../chunks/format-date.js";
function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Commune Invitations — Commune" },
      communeInvitations: "Commune Invitations",
      loading: "Loading...",
      noInvitations: "No invitations found",
      errorFetchingInvitations: "Failed to fetch invitations",
      errorOccurred: "An error occurred while fetching invitations",
      loadingMore: "Loading more invitations...",
      cancel: "Cancel Invitation",
      pending: "Pending",
      accepted: "Accepted",
      rejected: "Rejected",
      expired: "Expired",
      sentOn: "Sent on",
      cancelingInvitation: "Canceling...",
      errorCancelingInvitation: "Failed to cancel invitation",
      invitationCanceled: "Invitation canceled",
      backToCommune: "Back to Commune",
      invitedUser: "Invited User",
      status: "Status",
      actions: "Actions",
      confirmCancel: "Are you sure you want to cancel this invitation?"
    },
    ru: {
      _page: {
        title: "Приглашения коммуны — Коммуна"
      },
      communeInvitations: "Приглашения коммуны",
      loading: "Загрузка...",
      noInvitations: "Приглашения не найдены",
      errorFetchingInvitations: "Не удалось загрузить приглашения",
      errorOccurred: "Произошла ошибка при загрузке приглашений",
      loadingMore: "Загружаем больше приглашений...",
      cancel: "Отменить приглашение",
      pending: "Ожидает",
      accepted: "Принято",
      rejected: "Отклонено",
      expired: "Истекло",
      sentOn: "Отправлено",
      cancelingInvitation: "Отменяем...",
      errorCancelingInvitation: "Не удалось отменить приглашение",
      invitationCanceled: "Приглашение отменено",
      backToCommune: "Назад к коммуне",
      invitedUser: "Приглашенный пользователь",
      status: "Статус",
      actions: "Действия",
      confirmCancel: "Вы уверены, что хотите отменить это приглашение?"
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const {
    locale,
    routeLocale,
    toLocaleHref,
    getAppropriateLocalization
  } = data;
  const t = i18n[locale];
  let invitations = data.invitations;
  let isHasMoreInvitations = data.isHasMoreInvitations;
  let loadingStates = {};
  function getStatusBadgeClass(status) {
    switch (status) {
      case "pending":
        return "bg-warning text-dark";
      case "accepted":
        return "bg-success";
      case "rejected":
        return "bg-danger";
      case "expired":
        return "bg-secondary";
      default:
        return "bg-secondary";
    }
  }
  function getStatusText(status) {
    switch (status) {
      case "pending":
        return t.pending;
      case "accepted":
        return t.accepted;
      case "rejected":
        return t.rejected;
      case "expired":
        return t.expired;
      default:
        return status;
    }
  }
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;
  });
  $$payload.out.push(`<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4"><div><h1>${escape_html(t.communeInvitations)}</h1> <p class="text-muted mb-0">${escape_html(getAppropriateLocalization(data.commune.name))}</p></div> <a${attr("href", toLocaleHref(`/communes/${data.commune.id}`))} class="btn btn-outline-secondary">${escape_html(t.backToCommune)}</a></div> `);
  if (invitations.length === 0) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-5"><p class="text-muted">${escape_html(t.noInvitations)}</p></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    const each_array = ensure_array_like(invitations);
    const each_array_1 = ensure_array_like(invitations);
    $$payload.out.push(`<div class="d-none d-md-block"><div class="table-responsive"><table class="table table-hover"><thead><tr><th>${escape_html(t.invitedUser)}</th><th>${escape_html(t.status)}</th><th>${escape_html(t.sentOn)}</th><th>${escape_html(t.actions)}</th></tr></thead><tbody><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let invitation = each_array[$$index];
      $$payload.out.push(`<tr><td><div class="d-flex align-items-center">`);
      if (invitation.user.image) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<img${attr("src", `/images/${invitation.user.image}`)} alt="User avatar" class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;"/>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;"><i class="bi bi-person text-white"></i></div>`);
      }
      $$payload.out.push(`<!--]--> <div><div class="fw-medium">${escape_html(getAppropriateLocalization(invitation.user.name))}</div></div></div></td><td><span${attr_class(`badge ${getStatusBadgeClass(invitation.status)}`)}>${escape_html(getStatusText(invitation.status))}</span></td><td class="text-muted">${escape_html(formatDate(invitation.createdAt, locale))}</td><td>`);
      if (invitation.status === "pending") {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<button class="btn btn-sm btn-outline-danger"${attr("disabled", loadingStates[invitation.id] === "canceling", true)}>`);
        if (loadingStates[invitation.id] === "canceling") {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<span class="spinner-border spinner-border-sm me-1" role="status"></span> ${escape_html(t.cancelingInvitation)}`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`${escape_html(t.cancel)}`);
        }
        $$payload.out.push(`<!--]--></button>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<span class="text-muted">—</span>`);
      }
      $$payload.out.push(`<!--]--></td></tr>`);
    }
    $$payload.out.push(`<!--]--></tbody></table></div></div> <div class="d-md-none"><div class="row g-3"><!--[-->`);
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let invitation = each_array_1[$$index_1];
      $$payload.out.push(`<div class="col-12"><div class="card"><div class="card-body"><div class="d-flex align-items-start justify-content-between mb-3"><div class="d-flex align-items-center">`);
      if (invitation.user.image) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<img${attr("src", `/images/${invitation.user.image}`)} alt="User avatar" class="rounded-circle me-3" style="width: 48px; height: 48px; object-fit: cover;"/>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;"><i class="bi bi-person text-white"></i></div>`);
      }
      $$payload.out.push(`<!--]--> <div><div class="fw-medium">${escape_html(getAppropriateLocalization(invitation.user.name))}</div></div></div> <span${attr_class(`badge ${getStatusBadgeClass(invitation.status)}`)}>${escape_html(getStatusText(invitation.status))}</span></div> <div class="d-flex justify-content-between align-items-center"><small class="text-muted">${escape_html(t.sentOn)}
                    ${escape_html(formatDate(invitation.createdAt, locale))}</small> `);
      if (invitation.status === "pending") {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<button class="btn btn-sm btn-outline-danger"${attr("disabled", loadingStates[invitation.id] === "canceling", true)}>`);
        if (loadingStates[invitation.id] === "canceling") {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<span class="spinner-border spinner-border-sm me-1" role="status"></span> ${escape_html(t.cancelingInvitation)}`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`${escape_html(t.cancel)}`);
        }
        $$payload.out.push(`<!--]--></button>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--></div></div></div></div>`);
    }
    $$payload.out.push(`<!--]--></div></div>`);
  }
  $$payload.out.push(`<!--]--> `);
  if (isHasMoreInvitations) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-3">`);
    {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}
export {
  _page as default
};
