import { x as head, z as escape_html, y as attr, K as ensure_array_like, G as attr_class, w as pop, u as push } from "../../../../../../../chunks/index.js";
import "../../../../../../../chunks/current-user.js";
import { g as getClient } from "../../../../../../../chunks/acrpc.js";
import "@sveltejs/kit/internal";
import "../../../../../../../chunks/exports.js";
import "../../../../../../../chunks/state.svelte.js";
import "@formatjs/intl-localematcher";
import "@sveltejs/kit";
import { f as formatDate } from "../../../../../../../chunks/format-date.js";
function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Join Requests — Commune" },
      communeJoinRequests: "Join Requests",
      loading: "Loading...",
      noJoinRequests: "No join requests found",
      errorFetchingJoinRequests: "Failed to fetch join requests",
      errorOccurred: "An error occurred while fetching join requests",
      loadingMore: "Loading more join requests...",
      accept: "Accept",
      reject: "Reject",
      pending: "Pending",
      accepted: "Accepted",
      rejected: "Rejected",
      requestedOn: "Requested on",
      acceptingRequest: "Accepting...",
      rejectingRequest: "Rejecting...",
      errorAcceptingRequest: "Failed to accept join request",
      errorRejectingRequest: "Failed to reject join request",
      requestAccepted: "Join request accepted",
      requestRejected: "Join request rejected",
      backToCommune: "Back to Commune",
      requestingUser: "Requesting User",
      status: "Status",
      actions: "Actions",
      confirmAccept: "Are you sure you want to accept this join request?",
      confirmReject: "Are you sure you want to reject this join request?"
    },
    ru: {
      _page: {
        title: "Заявки на вступление — Коммуна"
      },
      communeJoinRequests: "Заявки на вступление",
      loading: "Загрузка...",
      noJoinRequests: "Заявки не найдены",
      errorFetchingJoinRequests: "Не удалось загрузить заявки",
      errorOccurred: "Произошла ошибка при загрузке заявок",
      loadingMore: "Загружаем больше заявок...",
      accept: "Принять",
      reject: "Отклонить",
      pending: "Ожидает",
      accepted: "Принято",
      rejected: "Отклонено",
      requestedOn: "Подана",
      acceptingRequest: "Принимаем...",
      rejectingRequest: "Отклоняем...",
      errorAcceptingRequest: "Не удалось принять заявку",
      errorRejectingRequest: "Не удалось отклонить заявку",
      requestAccepted: "Заявка принята",
      requestRejected: "Заявка отклонена",
      backToCommune: "Назад к коммуне",
      requestingUser: "Пользователь",
      status: "Статус",
      actions: "Действия",
      confirmAccept: "Вы уверены, что хотите принять эту заявку?",
      confirmReject: "Вы уверены, что хотите отклонить эту заявку?"
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const {
    locale,
    routeLocale,
    toLocaleHref,
    getAppropriateLocalization
  } = data;
  const t = i18n[locale];
  let joinRequests = data.joinRequests;
  let isHasMoreJoinRequests = data.isHasMoreJoinRequests;
  let loadingStates = {};
  function getStatusBadgeClass(status) {
    switch (status) {
      case "pending":
        return "bg-warning text-dark";
      case "accepted":
        return "bg-success";
      case "rejected":
        return "bg-danger";
      default:
        return "bg-secondary";
    }
  }
  function getStatusText(status) {
    switch (status) {
      case "pending":
        return t.pending;
      case "accepted":
        return t.accepted;
      case "rejected":
        return t.rejected;
      default:
        return status;
    }
  }
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;
  });
  $$payload.out.push(`<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4"><div><h1>${escape_html(t.communeJoinRequests)}</h1> <p class="text-muted mb-0">${escape_html(getAppropriateLocalization(data.commune.name))}</p></div> <a${attr("href", toLocaleHref(`/communes/${data.commune.id}`))} class="btn btn-outline-secondary">${escape_html(t.backToCommune)}</a></div> `);
  if (joinRequests.length === 0) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-5"><p class="text-muted">${escape_html(t.noJoinRequests)}</p></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    const each_array = ensure_array_like(joinRequests);
    const each_array_1 = ensure_array_like(joinRequests);
    $$payload.out.push(`<div class="d-none d-md-block"><div class="table-responsive"><table class="table table-hover"><thead><tr><th>${escape_html(t.requestingUser)}</th><th>${escape_html(t.status)}</th><th>${escape_html(t.requestedOn)}</th><th>${escape_html(t.actions)}</th></tr></thead><tbody><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let joinRequest = each_array[$$index];
      $$payload.out.push(`<tr><td><div class="d-flex align-items-center">`);
      if (joinRequest.user.image) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<img${attr("src", `/images/${joinRequest.user.image}`)} alt="User avatar" class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;"/>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;"><i class="bi bi-person text-white"></i></div>`);
      }
      $$payload.out.push(`<!--]--> <div><div class="fw-medium">${escape_html(getAppropriateLocalization(joinRequest.user.name))}</div></div></div></td><td><span${attr_class(`badge ${getStatusBadgeClass(joinRequest.status)}`)}>${escape_html(getStatusText(joinRequest.status))}</span></td><td class="text-muted">${escape_html(formatDate(joinRequest.createdAt, locale))}</td><td>`);
      if (joinRequest.status === "pending") {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="d-flex gap-1"><button class="btn btn-sm btn-success"${attr("disabled", loadingStates[joinRequest.id] === "accepting", true)}>`);
        if (loadingStates[joinRequest.id] === "accepting") {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<span class="spinner-border spinner-border-sm me-1" role="status"></span> ${escape_html(t.acceptingRequest)}`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`${escape_html(t.accept)}`);
        }
        $$payload.out.push(`<!--]--></button> <button class="btn btn-sm btn-outline-danger"${attr("disabled", loadingStates[joinRequest.id] === "rejecting", true)}>`);
        if (loadingStates[joinRequest.id] === "rejecting") {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<span class="spinner-border spinner-border-sm me-1" role="status"></span> ${escape_html(t.rejectingRequest)}`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`${escape_html(t.reject)}`);
        }
        $$payload.out.push(`<!--]--></button></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<span class="text-muted">—</span>`);
      }
      $$payload.out.push(`<!--]--></td></tr>`);
    }
    $$payload.out.push(`<!--]--></tbody></table></div></div> <div class="d-md-none"><div class="row g-3"><!--[-->`);
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let joinRequest = each_array_1[$$index_1];
      $$payload.out.push(`<div class="col-12"><div class="card"><div class="card-body"><div class="d-flex align-items-start justify-content-between mb-3"><div class="d-flex align-items-center">`);
      if (joinRequest.user.image) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<img${attr("src", `/images/${joinRequest.user.image}`)} alt="User avatar" class="rounded-circle me-3" style="width: 48px; height: 48px; object-fit: cover;"/>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;"><i class="bi bi-person text-white"></i></div>`);
      }
      $$payload.out.push(`<!--]--> <div><div class="fw-medium">${escape_html(getAppropriateLocalization(joinRequest.user.name))}</div></div></div> <span${attr_class(`badge ${getStatusBadgeClass(joinRequest.status)}`)}>${escape_html(getStatusText(joinRequest.status))}</span></div> <div class="d-flex justify-content-between align-items-center"><small class="text-muted">${escape_html(t.requestedOn)}
                    ${escape_html(formatDate(joinRequest.createdAt, locale))}</small> `);
      if (joinRequest.status === "pending") {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="d-flex gap-1"><button class="btn btn-sm btn-success"${attr("disabled", loadingStates[joinRequest.id] === "accepting", true)}>`);
        if (loadingStates[joinRequest.id] === "accepting") {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<span class="spinner-border spinner-border-sm me-1" role="status"></span> ${escape_html(t.acceptingRequest)}`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`${escape_html(t.accept)}`);
        }
        $$payload.out.push(`<!--]--></button> <button class="btn btn-sm btn-outline-danger"${attr("disabled", loadingStates[joinRequest.id] === "rejecting", true)}>`);
        if (loadingStates[joinRequest.id] === "rejecting") {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<span class="spinner-border spinner-border-sm me-1" role="status"></span> ${escape_html(t.rejectingRequest)}`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`${escape_html(t.reject)}`);
        }
        $$payload.out.push(`<!--]--></button></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--></div></div></div></div>`);
    }
    $$payload.out.push(`<!--]--></div></div>`);
  }
  $$payload.out.push(`<!--]--> `);
  if (isHasMoreJoinRequests) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-3">`);
    {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}
export {
  _page as default
};
