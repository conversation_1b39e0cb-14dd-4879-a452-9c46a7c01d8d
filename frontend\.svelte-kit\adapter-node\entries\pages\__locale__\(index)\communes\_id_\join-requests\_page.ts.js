import { error } from "@sveltejs/kit";
import { a as consts_exports } from "../../../../../../../chunks/current-user.js";
import { g as getClient } from "../../../../../../../chunks/acrpc.js";
const load = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();
  const [
    user,
    [commune]
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.commune.list.get({ ids: [params.id] }, { fetch, ctx: { url } })
  ]);
  if (!commune) {
    throw error(404, "Commune not found");
  }
  const isAdmin = user?.role === "admin";
  const isHeadMember = user && commune.headMember.actorType === "user" && commune.headMember.actorId === user.id;
  if (!isAdmin && !isHeadMember) {
    throw new Error("Access denied: You must be an admin or commune head to view join requests");
  }
  const joinRequests = await api.commune.joinRequest.list.get(
    { communeId: params.id },
    { fetch, ctx: { url } }
  );
  const users = joinRequests.length ? await api.user.list.get(
    { ids: joinRequests.map(({ userId }) => userId) },
    { fetch, ctx: { url } }
  ) : [];
  const userMap = new Map(users.map((user2) => [user2.id, user2]));
  const joinRequestsWithUserDetails = joinRequests.map((joinRequest) => ({
    ...joinRequest,
    user: userMap.get(joinRequest.userId)
  }));
  return {
    commune,
    joinRequests: joinRequestsWithUserDetails,
    isHasMoreJoinRequests: joinRequests.length === consts_exports.PAGE_SIZE,
    userPermissions: {
      isAdmin,
      isHeadMember,
      canManageJoinRequests: isAdmin || isHeadMember
    }
  };
};
export {
  load
};
