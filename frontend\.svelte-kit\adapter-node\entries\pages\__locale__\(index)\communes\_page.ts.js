import { a as consts_exports } from "../../../../../chunks/current-user.js";
import { g as getClient } from "../../../../../chunks/acrpc.js";
const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  const [
    me,
    communes
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.commune.list.get({}, { fetch, ctx: { url } })
  ]);
  let pendingInvitationsCount = 0;
  if (me) {
    const invitations = await api.commune.invitation.list.get({}, { fetch, ctx: { url } });
    pendingInvitationsCount = invitations.filter(({ status }) => status === "pending").length;
  }
  return {
    communes,
    isHasMoreCommunes: communes.length === consts_exports.PAGE_SIZE,
    pendingInvitationsCount,
    isLoggedIn: !!me
  };
};
export {
  load
};
