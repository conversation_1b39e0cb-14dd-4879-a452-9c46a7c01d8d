import { a as consts_exports } from "../../../../../../chunks/current-user.js";
import { g as getClient } from "../../../../../../chunks/acrpc.js";
const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  const invitations = await api.commune.invitation.list.get({}, { fetch, ctx: { url } });
  const communes = invitations.length ? await api.commune.list.get(
    { ids: invitations.map(({ communeId }) => communeId) },
    { fetch, ctx: { url } }
  ) : [];
  const communeMap = new Map(communes.map((commune) => [commune.id, commune]));
  const invitationsWithDetails = invitations.map((invitation) => ({
    ...invitation,
    commune: communeMap.get(invitation.communeId)
  }));
  return {
    invitations: invitationsWithDetails,
    isHasMoreInvitations: invitations.length === consts_exports.PAGE_SIZE
  };
};
export {
  load
};
