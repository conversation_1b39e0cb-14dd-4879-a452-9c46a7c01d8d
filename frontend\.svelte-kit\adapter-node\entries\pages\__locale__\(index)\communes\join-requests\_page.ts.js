import { a as consts_exports } from "../../../../../../chunks/current-user.js";
import { g as getClient } from "../../../../../../chunks/acrpc.js";
const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  const joinRequests = await api.commune.joinRequest.list.get({}, { fetch, ctx: { url } });
  const communes = joinRequests.length ? await api.commune.list.get(
    { ids: joinRequests.map(({ communeId }) => communeId) },
    { fetch, ctx: { url } }
  ) : [];
  const communeMap = new Map(communes.map((commune) => [commune.id, commune]));
  const joinRequestsWithDetails = joinRequests.map((joinRequest) => ({
    ...joinRequest,
    commune: communeMap.get(joinRequest.communeId)
  }));
  return {
    joinRequests: joinRequestsWithDetails,
    isHasMoreJoinRequests: joinRequests.length === consts_exports.PAGE_SIZE
  };
};
export {
  load
};
