import { error } from "@sveltejs/kit";
import { g as getClient } from "../../../../../../chunks/acrpc.js";
const load = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();
  const [
    [user],
    note,
    summary
  ] = await Promise.all([
    api.user.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
    api.user.note.get({ userId: params.id }, { fetch, ctx: { url } }),
    api.rating.summary.get({ userId: params.id }, { fetch, ctx: { url } })
  ]);
  if (!user) {
    throw error(404, "User not found");
  }
  return {
    user,
    userNote: note.text,
    ratingSummary: summary
  };
};
export {
  load
};
