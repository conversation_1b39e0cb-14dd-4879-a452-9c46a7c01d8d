import { error } from "@sveltejs/kit";
import { a as consts_exports } from "../../../../../../../chunks/current-user.js";
import { g as getClient } from "../../../../../../../chunks/acrpc.js";
const load = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();
  const [
    me,
    [user],
    feedbacks
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.user.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
    api.rating.feedback.list.get({ userId: params.id }, { fetch, ctx: { url } })
  ]);
  if (!user) {
    throw error(404, "User not found");
  }
  return {
    me,
    user,
    feedbacks,
    isHasMoreFeedbacks: feedbacks.length === consts_exports.PAGE_SIZE
  };
};
export {
  load
};
