import { a as consts_exports } from "../../../../../chunks/current-user.js";
import { g as getClient } from "../../../../../chunks/acrpc.js";
const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  const users = await api.user.list.get({}, { fetch, ctx: { url } });
  return {
    users,
    isHasMoreUsers: users.length === consts_exports.PAGE_SIZE
  };
};
export {
  load
};
