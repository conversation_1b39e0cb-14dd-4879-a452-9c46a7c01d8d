import Negotiator from "negotiator";
import { match } from "@formatjs/intl-localematcher";
import { c as common_exports } from "../../../chunks/current-user.js";
import { g as getClient } from "../../../chunks/acrpc.js";
const load = async (event) => {
  const { fetcher: api } = getClient();
  const me = await api.user.me.get({ fetch: event.fetch, skipInterceptor: true }).catch(() => null);
  const userLocales = getUserLocales(event);
  const preferredLocale = getPreferredLocale(event);
  return {
    me,
    preferredLocale,
    userLocales
  };
};
function getUserLocales(event) {
  const acceptLanguage = event.request.headers.get("accept-language");
  if (acceptLanguage) {
    const negotiatorRequest = {
      headers: {
        "accept-language": acceptLanguage
      }
    };
    const preferredLanguages = new Negotiator(negotiatorRequest).languages();
    return preferredLanguages;
  }
  return [];
}
function getPreferredLocale(event) {
  const userLocales = getUserLocales(event);
  return match(
    userLocales,
    common_exports.WebsiteLocaleSchema._def.values,
    "en"
  );
}
export {
  load
};
