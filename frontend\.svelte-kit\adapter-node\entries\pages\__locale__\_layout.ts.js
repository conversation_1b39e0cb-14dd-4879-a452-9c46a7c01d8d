import { c as common_exports } from "../../../chunks/current-user.js";
import "clsx";
import "@sveltejs/kit/internal";
import "../../../chunks/exports.js";
import "../../../chunks/state.svelte.js";
import { match } from "@formatjs/intl-localematcher";
import "@sveltejs/kit";
const DEFAULT_LOCALE = "en";
function getAppropriateLocalizationFactory(routeLocale, userLocales) {
  const requestedLocales = routeLocale ? [routeLocale, ...userLocales] : userLocales;
  function getAppropriateLocalization(localizations) {
    const availableLocales = localizations.map((localization) => localization.locale);
    try {
      const matchedLocale = match(requestedLocales, availableLocales, DEFAULT_LOCALE);
      const matchedLocalization = localizations.find(
        (localization) => localization.locale === matchedLocale
      );
      return matchedLocalization?.value ?? localizations[0]?.value ?? null;
    } catch (error) {
      console.error("Error during locale negotiation:", error);
      return null;
    }
  }
  return getAppropriateLocalization;
}
const load = (event) => {
  const me = event.data.me;
  const user = me ? {
    id: me.id,
    email: me.email,
    role: me.role
  } : null;
  const routeLocale = getRouteLocale(event);
  const hrefLocale = routeLocale ? `/${routeLocale}` : "";
  const locale = routeLocale ?? event.data.preferredLocale ?? "en";
  return {
    routeLocale,
    preferredLocale: event.data.preferredLocale,
    locale,
    user,
    toLocaleHref(href) {
      return `${hrefLocale}${href}`;
    },
    getAppropriateLocalization: getAppropriateLocalizationFactory(
      routeLocale,
      event.data.userLocales
    )
  };
};
function getRouteLocale(event) {
  const parsedLocale = common_exports.WebsiteLocaleSchema.safeParse(event.params.locale);
  if (parsedLocale.success) {
    return parsedLocale.data;
  }
  return null;
}
export {
  load
};
