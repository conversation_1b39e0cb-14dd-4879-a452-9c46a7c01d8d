import { error } from "@sveltejs/kit";
import { g as getClient } from "../../../../../chunks/acrpc.js";
const load = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();
  const [
    [post],
    comments
  ] = await Promise.all([
    api.reactor.post.list.get({ id: params.id, lensId: null }, { fetch, ctx: { url } }),
    api.reactor.comment.list.get({ entityType: "post", entityId: params.id }, { fetch, ctx: { url } })
  ]);
  if (!post) {
    throw error(404, "Post not found");
  }
  return {
    post,
    comments
  };
};
export {
  load
};
