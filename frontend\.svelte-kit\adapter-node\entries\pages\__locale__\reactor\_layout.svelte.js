import { y as attr, F as attr_style, G as attr_class, z as escape_html, J as stringify } from "../../../../chunks/index.js";
/* empty css                                                                        */
import { L as Locale_switcher } from "../../../../chunks/locale-switcher.js";
import "../../../../chunks/current-user.js";
import "@sveltejs/kit";
import "../../../../chunks/schema.js";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import "@formatjs/intl-localematcher";
function _layout($$payload, $$props) {
  const i18n = {
    ru: {
      navGap: "mx-1",
      commune: "Коммуна",
      theLaw: "Право",
      rules: "Правила",
      newEnglish: "Новый английский",
      newCalendar: "Новый календарь",
      news: "Новости",
      users: "Пользователи",
      communes: "Коммуны",
      hubs: "Хабы",
      communities: "Сообщества",
      profile: "Профиль",
      feed: "Лента",
      hole: "Яма"
    },
    en: {
      navGap: "mx-2",
      commune: "Commune",
      theLaw: "The Law",
      rules: "Rules",
      newEnglish: "New English",
      newCalendar: "New Calendar",
      news: "News",
      users: "Users",
      communes: "Communes",
      hubs: "Hubs",
      communities: "Communities",
      profile: "Profile",
      feed: "Feed",
      hole: "Pit"
    }
  };
  const { children, data } = $$props;
  const { locale, routeLocale, toLocaleHref } = data;
  const t = i18n[locale];
  $$payload.out.push(`<div class="page-wrapper svelte-5oi5pp"><nav class="navbar navbar-expand-lg sticky-top reactor-navbar svelte-5oi5pp"><div class="container"><a${attr("href", toLocaleHref("/"))} class="navbar-brand py-0 ps-5 svelte-5oi5pp"><img src="/images/full-v3-transparent-white.svg" alt="Site Logo"${attr("height", 60)}${attr("width", 60)}${attr_style("", { width: "auto" })}/></a> <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation"><span class="navbar-toggler-icon"></span></button> <div class="collapse navbar-collapse svelte-5oi5pp" id="navbarNav"><ul class="navbar-nav mx-auto svelte-5oi5pp"><li${attr_class(`nav-item dropdown ${stringify(t.navGap)} text-nowrap`, "svelte-5oi5pp")}><a${attr("href", toLocaleHref("/"))} class="nav-link svelte-5oi5pp">${escape_html(t.commune)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, "svelte-5oi5pp")}><a${attr("href", toLocaleHref("/reactor"))} class="nav-link svelte-5oi5pp">${escape_html(t.feed)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, "svelte-5oi5pp")}><a${attr("href", toLocaleHref("/reactor/hubs"))} class="nav-link svelte-5oi5pp">${escape_html(t.hubs)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, "svelte-5oi5pp")}><a${attr("href", toLocaleHref("/reactor/communities"))} class="nav-link svelte-5oi5pp">${escape_html(t.communities)}</a></li></ul> <ul class="navbar-nav svelte-5oi5pp"><li class="nav-item svelte-5oi5pp">`);
  Locale_switcher($$payload, { currentLocale: routeLocale });
  $$payload.out.push(`<!----></li></ul></div></div></nav> <main class="container-fluid flex-grow-1 mb-5 reactor-container svelte-5oi5pp">`);
  children($$payload);
  $$payload.out.push(`<!----></main></div>`);
}
export {
  _layout as default
};
