import { a as consts_exports } from "../../../../chunks/current-user.js";
import { g as getClient } from "../../../../chunks/acrpc.js";
const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  await api.user.me.get({ fetch, skipInterceptor: true }).catch(() => null);
  const [posts, lenses] = await Promise.all([
    api.reactor.post.list.get({ lensId: null }, { fetch, ctx: { url } }),
    api.reactor.lens.list.get({ fetch, ctx: { url } })
  ]);
  return {
    posts,
    lenses,
    isHasMorePosts: posts.length === consts_exports.PAGE_SIZE
  };
};
export {
  load
};
