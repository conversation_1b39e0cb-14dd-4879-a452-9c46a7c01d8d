import { error } from "@sveltejs/kit";
import { g as getClient } from "../../../../../../chunks/acrpc.js";
const load = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();
  const [
    me,
    [community]
  ] = await Promise.all([
    api.user.me.get({ fetch, skipInterceptor: true }).catch(() => null),
    api.reactor.community.list.get({ ids: [params.id] }, { fetch, ctx: { url } })
  ]);
  if (!community) {
    throw error(404, "Community not found");
  }
  const canEdit = me && (me.role === "admin" || me.id === community.headUser.id);
  return {
    me,
    community,
    canEdit
  };
};
export {
  load
};
