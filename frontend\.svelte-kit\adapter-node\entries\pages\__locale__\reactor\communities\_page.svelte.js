import { O as copy_payload, P as assign_payload, w as pop, u as push, x as head, z as escape_html, y as attr, K as ensure_array_like } from "../../../../../chunks/index.js";
import "../../../../../chunks/current-user.js";
import { g as goto } from "../../../../../chunks/client.js";
import "@formatjs/intl-localematcher";
import "@sveltejs/kit";
import { f as formatDate } from "../../../../../chunks/format-date.js";
import { g as getClient } from "../../../../../chunks/acrpc.js";
import { M as Modal } from "../../../../../chunks/modal.js";
import { L as Localized_input } from "../../../../../chunks/localized-input.js";
import { L as Localized_textarea } from "../../../../../chunks/localized-textarea.js";
/* empty css                                                                           */
import { U as User_picker } from "../../../../../chunks/user-picker.js";
import { R as Reactor_hub_picker } from "../../../../../chunks/reactor-hub-picker.js";
function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Communities — Reactor of Commune" },
      communities: "Communities",
      createCommunity: "Create Community",
      noCommunities: "No communities found",
      head: "Head",
      errorFetchingCommunities: "Failed to fetch communities",
      errorOccurred: "An error occurred while fetching communities",
      loadingMore: "Loading more communities...",
      createdOn: "Created on",
      createCommunityTitle: "Create New Community",
      communityName: "Community Name",
      communityDescription: "Community Description",
      headUser: "Head User",
      headUserPlaceholder: "Leave empty to use current user",
      hub: "Hub",
      hubPlaceholder: "Leave empty for no hub",
      communityNamePlaceholder: "Enter community name",
      communityDescriptionPlaceholder: "Enter community description",
      create: "Create",
      cancel: "Cancel",
      creating: "Creating...",
      communityCreatedSuccess: "Community created successfully!",
      errorCreatingCommunity: "Failed to create community",
      required: "This field is required",
      searchPlaceholder: "Search communities...",
      noHub: "No hub"
    },
    ru: {
      _page: {
        title: "Сообщества — Реактор Коммуны"
      },
      communities: "Сообщества",
      createCommunity: "Создать сообщество",
      noCommunities: "Сообщества не найдены",
      head: "Глава",
      errorFetchingCommunities: "Не удалось загрузить сообщества",
      errorOccurred: "Произошла ошибка при загрузке сообществ",
      loadingMore: "Загружаем больше сообществ...",
      createdOn: "Создано",
      createCommunityTitle: "Создать новое сообщество",
      communityName: "Название сообщества",
      communityDescription: "Описание сообщества",
      headUser: "Главный пользователь",
      headUserPlaceholder: "Оставьте пустым для использования текущего пользователя",
      hub: "Хаб",
      hubPlaceholder: "Оставьте пустым для отсутствия хаба",
      communityNamePlaceholder: "Введите название сообщества",
      communityDescriptionPlaceholder: "Введите описание сообщества",
      create: "Создать",
      cancel: "Отмена",
      creating: "Создаем...",
      communityCreatedSuccess: "Сообщество успешно создано!",
      errorCreatingCommunity: "Не удалось создать сообщество",
      required: "Это поле обязательно",
      searchPlaceholder: "Поиск сообществ...",
      noHub: "Нет хаба"
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const { locale, toLocaleHref, getAppropriateLocalization } = data;
  const t = i18n[locale];
  let communities = data.communities;
  let showCreateModal = false;
  let searchInputValue = data.searchQuery || "";
  let isHasMoreCommunities = data.isHasMoreCommunities;
  let isCreating = false;
  let createError = null;
  let createSuccess = null;
  let communityName = [];
  let communityDescription = [];
  let headUserId = null;
  let hubId = null;
  function closeCreateModal() {
    showCreateModal = false;
  }
  function validateCreateForm() {
    if (!communityName.some((item) => item.value.trim().length > 0)) {
      createError = t.required;
      return false;
    }
    if (!communityDescription.some((item) => item.value.trim().length > 0)) {
      createError = t.required;
      return false;
    }
    return true;
  }
  async function handleCreateCommunity() {
    if (!validateCreateForm()) return;
    isCreating = true;
    createError = null;
    createSuccess = null;
    try {
      const { id } = await api.reactor.community.post({
        hubId: hubId || null,
        headUserId,
        name: communityName,
        description: communityDescription
      });
      createSuccess = t.communityCreatedSuccess;
      setTimeout(
        () => {
          goto(toLocaleHref(`/reactor/communities/${id}`));
        },
        1500
      );
    } catch (err) {
      createError = err instanceof Error ? err.message : t.errorCreatingCommunity;
      console.error(err);
    } finally {
      isCreating = false;
    }
  }
  function truncateDescription(text, maxLength = 200) {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + "...";
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>${escape_html(t._page.title)}</title>`;
    });
    $$payload2.out.push(`<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4 gap-3"><h1 class="mb-0">${escape_html(t.communities)}</h1> <div class="d-flex align-items-center gap-3"><div class="search-container svelte-jyzss"><input type="text" class="form-control svelte-jyzss"${attr("placeholder", t.searchPlaceholder)}${attr("value", searchInputValue)} style="min-width: 250px;"/></div> `);
    if (data.user?.role === "admin") {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<button class="btn btn-primary">${escape_html(t.createCommunity)}</button>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div></div> `);
    if (communities.length === 0) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="text-center py-5"><p class="text-muted">${escape_html(t.noCommunities)}</p></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      const each_array = ensure_array_like(communities);
      $$payload2.out.push(`<div class="row g-4"><!--[-->`);
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let community = each_array[$$index];
        $$payload2.out.push(`<div class="col-12"><div class="card shadow-sm h-100 svelte-jyzss"><div class="row g-0 h-100"><div class="col-md-3 col-lg-2"><div class="community-image-container svelte-jyzss">`);
        if (community.image) {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`<img${attr("src", `/images/${community.image}`)}${attr("alt", getAppropriateLocalization(community.name) || "Community")} class="community-image svelte-jyzss"/>`);
        } else {
          $$payload2.out.push("<!--[!-->");
          $$payload2.out.push(`<div class="community-image-placeholder svelte-jyzss"><i class="bi bi-people fs-1 text-muted"></i></div>`);
        }
        $$payload2.out.push(`<!--]--></div></div> <div class="col-md-9 col-lg-10"><div class="card-body d-flex flex-column h-100 p-4"><div class="d-flex justify-content-between align-items-start mb-3"><h4 class="card-title mb-0 flex-grow-1"><a${attr("href", toLocaleHref(`/reactor/communities/${community.id}`))} style="text-decoration: none;">${escape_html(getAppropriateLocalization(community.name) || "No name?")}</a></h4> <small class="text-muted ms-3">${escape_html(t.createdOn)}
                      ${escape_html(formatDate(community.createdAt, locale))}</small></div> <p class="card-text text-muted mb-3 flex-grow-1">${escape_html(truncateDescription(getAppropriateLocalization(community.description) || ""))}</p> `);
        if (community.hub) {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`<div class="mb-3"><div class="d-flex align-items-center"><div class="me-3">`);
          if (community.hub.image) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<img${attr("src", `/images/${community.hub.image}`)}${attr("alt", getAppropriateLocalization(community.hub.name))} class="rounded" style="width: 32px; height: 32px; object-fit: cover;"/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
            $$payload2.out.push(`<div class="rounded bg-secondary d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;"><i class="bi bi-collection text-white"></i></div>`);
          }
          $$payload2.out.push(`<!--]--></div> <div><a${attr("href", toLocaleHref(`/reactor/hubs/${community.hub.id}`))} class="fw-medium" style="text-decoration: none;">${escape_html(getAppropriateLocalization(community.hub.name))}</a></div></div></div>`);
        } else {
          $$payload2.out.push("<!--[!-->");
        }
        $$payload2.out.push(`<!--]--> <div class="d-flex align-items-center"><div class="me-3">`);
        if (community.headUser.image) {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`<img${attr("src", `/images/${community.headUser.image}`)}${attr("alt", getAppropriateLocalization(community.headUser.name))} class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;"/>`);
        } else {
          $$payload2.out.push("<!--[!-->");
          $$payload2.out.push(`<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;"><i class="bi bi-person-fill text-white"></i></div>`);
        }
        $$payload2.out.push(`<!--]--></div> <div><a${attr("href", toLocaleHref(`/users/${community.headUser.id}`))} class="fw-medium" style="text-decoration: none;">${escape_html(getAppropriateLocalization(community.headUser.name))}</a></div></div></div></div></div></div></div>`);
      }
      $$payload2.out.push(`<!--]--></div>`);
    }
    $$payload2.out.push(`<!--]--> `);
    if (isHasMoreCommunities) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="text-center py-3">`);
      {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div> `);
    if (data.user?.role === "admin") {
      $$payload2.out.push("<!--[-->");
      Modal($$payload2, {
        show: showCreateModal,
        title: t.createCommunityTitle,
        onClose: closeCreateModal,
        onSubmit: handleCreateCommunity,
        submitText: isCreating ? t.creating : t.create,
        cancelText: t.cancel,
        submitDisabled: isCreating || !communityName.some((item) => item.value.trim().length > 0) || !communityDescription.some((item) => item.value.trim().length > 0),
        isSubmitting: isCreating,
        children: ($$payload3) => {
          if (createError) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="alert alert-danger mb-3">${escape_html(createError)}</div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--> `);
          if (createSuccess) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="alert alert-success mb-3">${escape_html(createSuccess)}</div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--> <form>`);
          User_picker($$payload3, {
            locale,
            label: t.headUser,
            placeholder: t.headUserPlaceholder,
            get selectedUserId() {
              return headUserId;
            },
            set selectedUserId($$value) {
              headUserId = $$value;
              $$settled = false;
            }
          });
          $$payload3.out.push(`<!----> <div class="form-text mb-3">${escape_html(t.headUserPlaceholder)}</div> `);
          Reactor_hub_picker($$payload3, {
            locale,
            label: t.hub,
            placeholder: t.hubPlaceholder,
            get selectedHubId() {
              return hubId;
            },
            set selectedHubId($$value) {
              hubId = $$value;
              $$settled = false;
            }
          });
          $$payload3.out.push(`<!----> <div class="form-text mb-3">${escape_html(t.hubPlaceholder)}</div> `);
          Localized_input($$payload3, {
            locale,
            id: "community-name",
            label: t.communityName,
            placeholder: t.communityNamePlaceholder,
            required: true,
            get value() {
              return communityName;
            },
            set value($$value) {
              communityName = $$value;
              $$settled = false;
            }
          });
          $$payload3.out.push(`<!----> `);
          Localized_textarea($$payload3, {
            locale,
            id: "community-description",
            label: t.communityDescription,
            placeholder: t.communityDescriptionPlaceholder,
            rows: 4,
            required: true,
            get value() {
              return communityDescription;
            },
            set value($$value) {
              communityDescription = $$value;
              $$settled = false;
            }
          });
          $$payload3.out.push(`<!----></form>`);
        }
      });
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]-->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
export {
  _page as default
};
