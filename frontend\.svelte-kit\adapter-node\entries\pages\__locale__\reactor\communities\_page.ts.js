import { a as consts_exports } from "../../../../../chunks/current-user.js";
import { g as getClient } from "../../../../../chunks/acrpc.js";
const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  const searchQuery = url.searchParams.get("search");
  const [
    me,
    communities
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.reactor.community.list.get({ query: searchQuery ?? void 0 }, { fetch, ctx: { url } })
  ]);
  return {
    me,
    communities,
    searchQuery,
    isHasMoreCommunities: communities.length === consts_exports.PAGE_SIZE
  };
};
export {
  load
};
