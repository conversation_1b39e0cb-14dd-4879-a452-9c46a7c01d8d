import { error } from "@sveltejs/kit";
import { a as consts_exports } from "../../../../../../chunks/current-user.js";
import { g as getClient } from "../../../../../../chunks/acrpc.js";
const load = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();
  const [
    me,
    [hub],
    communities
  ] = await Promise.all([
    api.user.me.get({ fetch, skipInterceptor: true }).catch(() => null),
    api.reactor.hub.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
    api.reactor.community.list.get({ hubId: params.id }, { fetch, ctx: { url } })
  ]);
  if (!hub) {
    throw error(404, "Hub not found");
  }
  const canEdit = me && (me.role === "admin" || me.id === hub.headUser.id);
  return {
    me,
    hub,
    communities,
    canEdit,
    isHasMoreCommunities: communities.length === consts_exports.PAGE_SIZE
  };
};
export {
  load
};
