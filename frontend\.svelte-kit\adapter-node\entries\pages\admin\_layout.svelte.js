import { z as escape_html, w as pop, u as push } from "../../../chunks/index.js";
import "clsx";
function _layout($$payload, $$props) {
  push();
  const { data, children } = $$props;
  $$payload.out.push(`<div class="admin-layout svelte-e2baey"><div class="container-fluid"><div class="row"><nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar svelte-e2baey"><div class="position-sticky pt-3"><div class="sidebar-header mb-3 svelte-e2baey"><h5 class="text-primary"><i class="bi bi-gear-fill me-2"></i> Admin Panel</h5> <small class="text-muted">Welcome, ${escape_html(data.me.email)}</small></div> <ul class="nav flex-column"><li class="nav-item"><a class="nav-link svelte-e2baey" href="/admin" data-sveltekit-preload-data="hover"><i class="bi bi-house-door me-2"></i> Dashboard</a></li> <li class="nav-item"><a class="nav-link svelte-e2baey" href="/admin/invites" data-sveltekit-preload-data="hover"><i class="bi bi-envelope me-2"></i> User Invites</a></li></ul></div></nav> <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 svelte-e2baey"><div class="pt-3">`);
  children($$payload);
  $$payload.out.push(`<!----></div></main></div></div></div>`);
  pop();
}
export {
  _layout as default
};
