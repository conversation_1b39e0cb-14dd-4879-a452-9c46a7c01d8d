import { error } from "@sveltejs/kit";
import { g as getClient } from "../../../chunks/acrpc.js";
const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  const [
    me
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } })
  ]);
  if (me.role !== "admin") {
    throw error(403, "Access denied: Admin privileges required");
  }
  return {
    me
  };
};
export {
  load
};
