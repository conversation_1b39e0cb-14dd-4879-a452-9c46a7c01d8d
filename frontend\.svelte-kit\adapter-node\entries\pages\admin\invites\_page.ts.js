import { a as consts_exports } from "../../../../chunks/current-user.js";
import { g as getClient } from "../../../../chunks/acrpc.js";
const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  const invites = await api.user.invite.list.get(
    {
      pagination: {
        page: 1,
        size: consts_exports.PAGE_SIZE
      }
    },
    { fetch, ctx: { url } }
  );
  return {
    invites,
    isHasMoreInvites: invites.length === consts_exports.PAGE_SIZE
  };
};
export {
  load
};
