export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["favicon.png","images/default-avatar.png","images/full-v3-transparent-white.svg","images/full-v3-transparent.svg","images/hi-chat.gif","images/home-page-main-image-mobile.png","images/home-page-main-image.png","tinymce/icons/default/icons.js","tinymce/icons/default/icons.min.js","tinymce/icons/default/index.js","tinymce/models/dom/index.js","tinymce/models/dom/model.js","tinymce/models/dom/model.min.js","tinymce/plugins/image/index.js","tinymce/plugins/image/plugin.js","tinymce/plugins/image/plugin.min.js","tinymce/plugins/lists/index.js","tinymce/plugins/lists/plugin.js","tinymce/plugins/lists/plugin.min.js","tinymce/skins/content/dark/content.css","tinymce/skins/content/dark/content.js","tinymce/skins/content/dark/content.min.css","tinymce/skins/content/default/content.css","tinymce/skins/content/default/content.js","tinymce/skins/content/default/content.min.css","tinymce/skins/content/document/content.css","tinymce/skins/content/document/content.js","tinymce/skins/content/document/content.min.css","tinymce/skins/content/tinymce-5/content.css","tinymce/skins/content/tinymce-5/content.js","tinymce/skins/content/tinymce-5/content.min.css","tinymce/skins/content/tinymce-5-dark/content.css","tinymce/skins/content/tinymce-5-dark/content.js","tinymce/skins/content/tinymce-5-dark/content.min.css","tinymce/skins/content/writer/content.css","tinymce/skins/content/writer/content.js","tinymce/skins/content/writer/content.min.css","tinymce/skins/ui/oxide/content.css","tinymce/skins/ui/oxide/content.inline.css","tinymce/skins/ui/oxide/content.inline.js","tinymce/skins/ui/oxide/content.inline.min.css","tinymce/skins/ui/oxide/content.js","tinymce/skins/ui/oxide/content.min.css","tinymce/skins/ui/oxide/skin.css","tinymce/skins/ui/oxide/skin.js","tinymce/skins/ui/oxide/skin.min.css","tinymce/skins/ui/oxide/skin.shadowdom.css","tinymce/skins/ui/oxide/skin.shadowdom.js","tinymce/skins/ui/oxide/skin.shadowdom.min.css","tinymce/skins/ui/oxide-dark/content.css","tinymce/skins/ui/oxide-dark/content.inline.css","tinymce/skins/ui/oxide-dark/content.inline.js","tinymce/skins/ui/oxide-dark/content.inline.min.css","tinymce/skins/ui/oxide-dark/content.js","tinymce/skins/ui/oxide-dark/content.min.css","tinymce/skins/ui/oxide-dark/skin.css","tinymce/skins/ui/oxide-dark/skin.js","tinymce/skins/ui/oxide-dark/skin.min.css","tinymce/skins/ui/oxide-dark/skin.shadowdom.css","tinymce/skins/ui/oxide-dark/skin.shadowdom.js","tinymce/skins/ui/oxide-dark/skin.shadowdom.min.css","tinymce/skins/ui/tinymce-5/content.css","tinymce/skins/ui/tinymce-5/content.inline.css","tinymce/skins/ui/tinymce-5/content.inline.js","tinymce/skins/ui/tinymce-5/content.inline.min.css","tinymce/skins/ui/tinymce-5/content.js","tinymce/skins/ui/tinymce-5/content.min.css","tinymce/skins/ui/tinymce-5/skin.css","tinymce/skins/ui/tinymce-5/skin.js","tinymce/skins/ui/tinymce-5/skin.min.css","tinymce/skins/ui/tinymce-5/skin.shadowdom.css","tinymce/skins/ui/tinymce-5/skin.shadowdom.js","tinymce/skins/ui/tinymce-5/skin.shadowdom.min.css","tinymce/skins/ui/tinymce-5-dark/content.css","tinymce/skins/ui/tinymce-5-dark/content.inline.css","tinymce/skins/ui/tinymce-5-dark/content.inline.js","tinymce/skins/ui/tinymce-5-dark/content.inline.min.css","tinymce/skins/ui/tinymce-5-dark/content.js","tinymce/skins/ui/tinymce-5-dark/content.min.css","tinymce/skins/ui/tinymce-5-dark/skin.css","tinymce/skins/ui/tinymce-5-dark/skin.js","tinymce/skins/ui/tinymce-5-dark/skin.min.css","tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.css","tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.js","tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.min.css","tinymce/themes/silver/index.js","tinymce/themes/silver/theme.js","tinymce/themes/silver/theme.min.js","tinymce/tinymce.min.js"]),
	mimeTypes: {".png":"image/png",".svg":"image/svg+xml",".gif":"image/gif",".js":"text/javascript",".css":"text/css"},
	_: {
		client: {start:"_app/immutable/entry/start.CXlvP7iz.js",app:"_app/immutable/entry/app.DmcxNwr8.js",imports:["_app/immutable/entry/start.CXlvP7iz.js","_app/immutable/chunks/CKnuo8tw.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/B0MzmgHo.js","_app/immutable/entry/app.DmcxNwr8.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/B5DcI8qy.js","_app/immutable/chunks/CR3e0W7L.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:true},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js')),
			__memo(() => import('./nodes/3.js')),
			__memo(() => import('./nodes/4.js')),
			__memo(() => import('./nodes/5.js')),
			__memo(() => import('./nodes/6.js')),
			__memo(() => import('./nodes/7.js')),
			__memo(() => import('./nodes/8.js')),
			__memo(() => import('./nodes/9.js')),
			__memo(() => import('./nodes/10.js')),
			__memo(() => import('./nodes/11.js')),
			__memo(() => import('./nodes/12.js')),
			__memo(() => import('./nodes/13.js')),
			__memo(() => import('./nodes/14.js')),
			__memo(() => import('./nodes/15.js')),
			__memo(() => import('./nodes/16.js')),
			__memo(() => import('./nodes/17.js')),
			__memo(() => import('./nodes/18.js')),
			__memo(() => import('./nodes/19.js')),
			__memo(() => import('./nodes/20.js')),
			__memo(() => import('./nodes/21.js')),
			__memo(() => import('./nodes/22.js')),
			__memo(() => import('./nodes/23.js')),
			__memo(() => import('./nodes/24.js')),
			__memo(() => import('./nodes/25.js')),
			__memo(() => import('./nodes/26.js')),
			__memo(() => import('./nodes/27.js')),
			__memo(() => import('./nodes/28.js')),
			__memo(() => import('./nodes/29.js')),
			__memo(() => import('./nodes/30.js')),
			__memo(() => import('./nodes/31.js')),
			__memo(() => import('./nodes/32.js'))
		],
		routes: [
			{
				id: "/admin",
				pattern: /^\/admin\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 6 },
				endpoint: null
			},
			{
				id: "/admin/invites",
				pattern: /^\/admin\/invites\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 7 },
				endpoint: null
			},
			{
				id: "/api/[...slug]",
				pattern: /^\/api(?:\/(.*))?\/?$/,
				params: [{"name":"slug","optional":false,"rest":true,"chained":true}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/_...slug_/_server.ts.js'))
			},
			{
				id: "/[[locale]]/auth",
				pattern: /^(?:\/([^/]+))?\/auth\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 24 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/communes",
				pattern: /^(?:\/([^/]+))?\/communes\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 9 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/communes/invitations",
				pattern: /^(?:\/([^/]+))?\/communes\/invitations\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 10 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/communes/join-requests",
				pattern: /^(?:\/([^/]+))?\/communes\/join-requests\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 11 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/communes/[id]",
				pattern: /^(?:\/([^/]+))?\/communes\/([^/]+?)\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 12 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/communes/[id]/invitations",
				pattern: /^(?:\/([^/]+))?\/communes\/([^/]+?)\/invitations\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 13 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/communes/[id]/join-requests",
				pattern: /^(?:\/([^/]+))?\/communes\/([^/]+?)\/join-requests\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 14 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/new-calendar",
				pattern: /^(?:\/([^/]+))?\/new-calendar\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 15 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/new-english",
				pattern: /^(?:\/([^/]+))?\/new-english\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 16 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/profile",
				pattern: /^(?:\/([^/]+))?\/profile\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 17 },
				endpoint: null
			},
			{
				id: "/[[locale]]/reactor",
				pattern: /^(?:\/([^/]+))?\/reactor\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,5,], errors: [1,,,], leaf: 25 },
				endpoint: null
			},
			{
				id: "/[[locale]]/reactor/communities",
				pattern: /^(?:\/([^/]+))?\/reactor\/communities\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,5,], errors: [1,,,], leaf: 26 },
				endpoint: null
			},
			{
				id: "/[[locale]]/reactor/communities/[id]",
				pattern: /^(?:\/([^/]+))?\/reactor\/communities\/([^/]+?)\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,5,], errors: [1,,,], leaf: 27 },
				endpoint: null
			},
			{
				id: "/[[locale]]/reactor/hubs",
				pattern: /^(?:\/([^/]+))?\/reactor\/hubs\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,5,], errors: [1,,,], leaf: 28 },
				endpoint: null
			},
			{
				id: "/[[locale]]/reactor/hubs/[id]",
				pattern: /^(?:\/([^/]+))?\/reactor\/hubs\/([^/]+?)\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,5,], errors: [1,,,], leaf: 29 },
				endpoint: null
			},
			{
				id: "/[[locale]]/reactor/[id]",
				pattern: /^(?:\/([^/]+))?\/reactor\/([^/]+?)\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,5,], errors: [1,,,], leaf: 30 },
				endpoint: null
			},
			{
				id: "/robots.txt",
				pattern: /^\/robots\.txt\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/robots.txt/_server.ts.js'))
			},
			{
				id: "/[[locale]]/(index)/rules",
				pattern: /^(?:\/([^/]+))?\/rules\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 18 },
				endpoint: null
			},
			{
				id: "/sitemap.xml",
				pattern: /^\/sitemap\.xml\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/sitemap.xml/_server.ts.js'))
			},
			{
				id: "/[[locale]]/test/editor",
				pattern: /^(?:\/([^/]+))?\/test\/editor\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 31 },
				endpoint: null
			},
			{
				id: "/[[locale]]/test/tag",
				pattern: /^(?:\/([^/]+))?\/test\/tag\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 32 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/the-law",
				pattern: /^(?:\/([^/]+))?\/the-law\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 19 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/users",
				pattern: /^(?:\/([^/]+))?\/users\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 20 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/users/[id]",
				pattern: /^(?:\/([^/]+))?\/users\/([^/]+?)\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 21 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/users/[id]/feedback",
				pattern: /^(?:\/([^/]+))?\/users\/([^/]+?)\/feedback\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 22 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)/users/[id]/karma",
				pattern: /^(?:\/([^/]+))?\/users\/([^/]+?)\/karma\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true},{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 23 },
				endpoint: null
			},
			{
				id: "/[[locale]]/(index)",
				pattern: /^(?:\/([^/]+))?\/?$/,
				params: [{"name":"locale","optional":true,"rest":false,"chained":true}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 8 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();

export const prerendered = new Set([]);

export const base = "";