

export const index = 16;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/new-english/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/16.CiA_LQTK.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/C_sRNQCS.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/CaC9IHEK.js","_app/immutable/chunks/q36Eg1F8.js"];
export const stylesheets = [];
export const fonts = [];
