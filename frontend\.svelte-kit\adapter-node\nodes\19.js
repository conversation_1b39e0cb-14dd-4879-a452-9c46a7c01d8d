

export const index = 19;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/the-law/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/19.C_vlF50S.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/CaC9IHEK.js"];
export const stylesheets = [];
export const fonts = [];
