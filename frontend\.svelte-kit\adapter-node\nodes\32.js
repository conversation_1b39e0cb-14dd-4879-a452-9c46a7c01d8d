

export const index = 32;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/__locale__/test/tag/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/32.7TuhOY8l.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/CGZ87yZq.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js"];
export const stylesheets = [];
export const fonts = [];
