import{a4 as d,a5 as n,a6 as u,I as i,a7 as v,a8 as h,a9 as g}from"./RHWQbow4.js";const A=Symbol("is custom element"),N=Symbol("is html");function l(r){if(i){var s=!1,a=()=>{if(!s){if(s=!0,r.hasAttribute("value")){var o=r.value;t(r,"value",null),r.value=o}if(r.hasAttribute("checked")){var e=r.checked;t(r,"checked",null),r.checked=e}}};r.__on_r=a,h(a),g()}}function E(r,s){var a=c(r);a.value===(a.value=s??void 0)||r.value===s&&(s!==0||r.nodeName!=="PROGRESS")||(r.value=s??"")}function t(r,s,a,o){var e=c(r);i&&(e[s]=r.getAttribute(s),s==="src"||s==="srcset"||s==="href"&&r.nodeName==="LINK")||e[s]!==(e[s]=a)&&(s==="loading"&&(r[d]=a),a==null?r.removeAttribute(s):typeof a!="string"&&S(r).includes(s)?r[s]=a:r.setAttribute(s,a))}function c(r){return r.__attributes??(r.__attributes={[A]:r.nodeName.includes("-"),[N]:r.namespaceURI===n})}var f=new Map;function S(r){var s=f.get(r.nodeName);if(s)return s;f.set(r.nodeName,s=[]);for(var a,o=r,e=Element.prototype;e!==o;){a=v(o);for(var _ in a)a[_].set&&s.push(_);o=u(o)}return s}export{E as a,l as r,t as s};
