import{H as Mt,R as $t}from"./CYgJF_JY.js";function rr(a,e){throw new Mt(a,e)}function sr(a,e){throw new $t(a,e.toString())}new TextEncoder;var S;(function(a){a.assertEqual=r=>{};function e(r){}a.assertIs=e;function t(r){throw new Error}a.assertNever=t,a.arrayToEnum=r=>{const s={};for(const i of r)s[i]=i;return s},a.getValidEnumValues=r=>{const s=a.objectKeys(r).filter(o=>typeof r[r[o]]!="number"),i={};for(const o of s)i[o]=r[o];return a.objectValues(i)},a.objectValues=r=>a.objectKeys(r).map(function(s){return r[s]}),a.objectKeys=typeof Object.keys=="function"?r=>Object.keys(r):r=>{const s=[];for(const i in r)Object.prototype.hasOwnProperty.call(r,i)&&s.push(i);return s},a.find=(r,s)=>{for(const i of r)if(s(i))return i},a.isInteger=typeof Number.isInteger=="function"?r=>Number.isInteger(r):r=>typeof r=="number"&&Number.isFinite(r)&&Math.floor(r)===r;function n(r,s=" | "){return r.map(i=>typeof i=="string"?`'${i}'`:i).join(s)}a.joinValues=n,a.jsonStringifyReplacer=(r,s)=>typeof s=="bigint"?s.toString():s})(S||(S={}));var ut;(function(a){a.mergeShapes=(e,t)=>({...e,...t})})(ut||(ut={}));const m=S.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),P=a=>{switch(typeof a){case"undefined":return m.undefined;case"string":return m.string;case"number":return Number.isNaN(a)?m.nan:m.number;case"boolean":return m.boolean;case"function":return m.function;case"bigint":return m.bigint;case"symbol":return m.symbol;case"object":return Array.isArray(a)?m.array:a===null?m.null:a.then&&typeof a.then=="function"&&a.catch&&typeof a.catch=="function"?m.promise:typeof Map<"u"&&a instanceof Map?m.map:typeof Set<"u"&&a instanceof Set?m.set:typeof Date<"u"&&a instanceof Date?m.date:m.object;default:return m.unknown}},d=S.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class E extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(s){return s.message},n={_errors:[]},r=s=>{for(const i of s.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let o=n,p=0;for(;p<i.path.length;){const f=i.path[p];p===i.path.length-1?(o[f]=o[f]||{_errors:[]},o[f]._errors.push(t(i))):o[f]=o[f]||{_errors:[]},o=o[f],p++}}};return r(this),n}static assert(e){if(!(e instanceof E))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,S.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const r of this.issues)if(r.path.length>0){const s=r.path[0];t[s]=t[s]||[],t[s].push(e(r))}else n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}E.create=a=>new E(a);const Me=(a,e)=>{let t;switch(a.code){case d.invalid_type:a.received===m.undefined?t="Required":t=`Expected ${a.expected}, received ${a.received}`;break;case d.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(a.expected,S.jsonStringifyReplacer)}`;break;case d.unrecognized_keys:t=`Unrecognized key(s) in object: ${S.joinValues(a.keys,", ")}`;break;case d.invalid_union:t="Invalid input";break;case d.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${S.joinValues(a.options)}`;break;case d.invalid_enum_value:t=`Invalid enum value. Expected ${S.joinValues(a.options)}, received '${a.received}'`;break;case d.invalid_arguments:t="Invalid function arguments";break;case d.invalid_return_type:t="Invalid function return type";break;case d.invalid_date:t="Invalid date";break;case d.invalid_string:typeof a.validation=="object"?"includes"in a.validation?(t=`Invalid input: must include "${a.validation.includes}"`,typeof a.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${a.validation.position}`)):"startsWith"in a.validation?t=`Invalid input: must start with "${a.validation.startsWith}"`:"endsWith"in a.validation?t=`Invalid input: must end with "${a.validation.endsWith}"`:S.assertNever(a.validation):a.validation!=="regex"?t=`Invalid ${a.validation}`:t="Invalid";break;case d.too_small:a.type==="array"?t=`Array must contain ${a.exact?"exactly":a.inclusive?"at least":"more than"} ${a.minimum} element(s)`:a.type==="string"?t=`String must contain ${a.exact?"exactly":a.inclusive?"at least":"over"} ${a.minimum} character(s)`:a.type==="number"?t=`Number must be ${a.exact?"exactly equal to ":a.inclusive?"greater than or equal to ":"greater than "}${a.minimum}`:a.type==="bigint"?t=`Number must be ${a.exact?"exactly equal to ":a.inclusive?"greater than or equal to ":"greater than "}${a.minimum}`:a.type==="date"?t=`Date must be ${a.exact?"exactly equal to ":a.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(a.minimum))}`:t="Invalid input";break;case d.too_big:a.type==="array"?t=`Array must contain ${a.exact?"exactly":a.inclusive?"at most":"less than"} ${a.maximum} element(s)`:a.type==="string"?t=`String must contain ${a.exact?"exactly":a.inclusive?"at most":"under"} ${a.maximum} character(s)`:a.type==="number"?t=`Number must be ${a.exact?"exactly":a.inclusive?"less than or equal to":"less than"} ${a.maximum}`:a.type==="bigint"?t=`BigInt must be ${a.exact?"exactly":a.inclusive?"less than or equal to":"less than"} ${a.maximum}`:a.type==="date"?t=`Date must be ${a.exact?"exactly":a.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(a.maximum))}`:t="Invalid input";break;case d.custom:t="Invalid input";break;case d.invalid_intersection_types:t="Intersection results could not be merged";break;case d.not_multiple_of:t=`Number must be a multiple of ${a.multipleOf}`;break;case d.not_finite:t="Number must be finite";break;default:t=e.defaultError,S.assertNever(a)}return{message:t}};let Dt=Me;function zt(){return Dt}const Vt=a=>{const{data:e,path:t,errorMaps:n,issueData:r}=a,s=[...t,...r.path||[]],i={...r,path:s};if(r.message!==void 0)return{...r,path:s,message:r.message};let o="";const p=n.filter(f=>!!f).slice().reverse();for(const f of p)o=f(i,{data:e,defaultError:o}).message;return{...r,path:s,message:o}};function l(a,e){const t=zt(),n=Vt({issueData:e,data:a.data,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,t,t===Me?void 0:Me].filter(r=>!!r)});a.common.issues.push(n)}class O{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if(r.status==="aborted")return v;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const r of t){const s=await r.key,i=await r.value;n.push({key:s,value:i})}return O.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:s,value:i}=r;if(s.status==="aborted"||i.status==="aborted")return v;s.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),s.value!=="__proto__"&&(typeof i.value<"u"||r.alwaysSet)&&(n[s.value]=i.value)}return{status:e.value,value:n}}}const v=Object.freeze({status:"aborted"}),oe=a=>({status:"dirty",value:a}),A=a=>({status:"valid",value:a}),ct=a=>a.status==="aborted",dt=a=>a.status==="dirty",Y=a=>a.status==="valid",ge=a=>typeof Promise<"u"&&a instanceof Promise;var h;(function(a){a.errToObj=e=>typeof e=="string"?{message:e}:e||{},a.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(h||(h={}));class M{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const lt=(a,e)=>{if(Y(e))return{success:!0,data:e.value};if(!a.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new E(a.common.issues);return this._error=t,this._error}}};function y(a){if(!a)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:r}=a;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(i,o)=>{const{message:p}=a;return i.code==="invalid_enum_value"?{message:p??o.defaultError}:typeof o.data>"u"?{message:p??n??o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:p??t??o.defaultError}},description:r}}class _{get description(){return this._def.description}_getType(e){return P(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:P(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new O,ctx:{common:e.parent.common,data:e.data,parsedType:P(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(ge(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){const n={common:{issues:[],async:(t==null?void 0:t.async)??!1,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:P(e)},r=this._parseSync({data:e,path:n.path,parent:n});return lt(n,r)}"~validate"(e){var n,r;const t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:P(e)};if(!this["~standard"].async)try{const s=this._parseSync({data:e,path:[],parent:t});return Y(s)?{value:s.value}:{issues:t.common.issues}}catch(s){(r=(n=s==null?void 0:s.message)==null?void 0:n.toLowerCase())!=null&&r.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(s=>Y(s)?{value:s.value}:{issues:t.common.issues})}async parseAsync(e,t){const n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){const n={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:P(e)},r=this._parse({data:e,path:n.path,parent:n}),s=await(ge(r)?r:Promise.resolve(r));return lt(n,s)}refine(e,t){const n=r=>typeof t=="string"||typeof t>"u"?{message:t}:typeof t=="function"?t(r):t;return this._refinement((r,s)=>{const i=e(r),o=()=>s.addIssue({code:d.custom,...n(r)});return typeof Promise<"u"&&i instanceof Promise?i.then(p=>p?!0:(o(),!1)):i?!0:(o(),!1)})}refinement(e,t){return this._refinement((n,r)=>e(n)?!0:(r.addIssue(typeof t=="function"?t(n,r):t),!1))}_refinement(e){return new X({schema:this,typeName:g.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return L.create(this,this._def)}nullable(){return ee.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return N.create(this)}promise(){return be.create(this,this._def)}or(e){return _e.create([this,e],this._def)}and(e){return Se.create(this,e,this._def)}transform(e){return new X({...y(this._def),schema:this,typeName:g.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new De({...y(this._def),innerType:this,defaultValue:t,typeName:g.ZodDefault})}brand(){return new ma({typeName:g.ZodBranded,type:this,...y(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new ze({...y(this._def),innerType:this,catchValue:t,typeName:g.ZodCatch})}describe(e){const t=this.constructor;return new t({...this._def,description:e})}pipe(e){return We.create(this,e)}readonly(){return Ve.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Ft=/^c[^\s-]{8,}$/i,qt=/^[0-9a-z]+$/,Bt=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Wt=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Jt=/^[a-z0-9_-]{21}$/i,Ht=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Yt=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Qt=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Kt="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let Pe;const Xt=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ea=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ta=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,aa=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,na=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,ra=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,kt="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",sa=new RegExp(`^${kt}$`);function It(a){let e="[0-5]\\d";a.precision?e=`${e}\\.\\d{${a.precision}}`:a.precision==null&&(e=`${e}(\\.\\d+)?`);const t=a.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${t}`}function ia(a){return new RegExp(`^${It(a)}$`)}function oa(a){let e=`${kt}T${It(a)}`;const t=[];return t.push(a.local?"Z?":"Z"),a.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function ua(a,e){return!!((e==="v4"||!e)&&Xt.test(a)||(e==="v6"||!e)&&ta.test(a))}function ca(a,e){if(!Ht.test(a))return!1;try{const[t]=a.split(".");if(!t)return!1;const n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),r=JSON.parse(atob(n));return!(typeof r!="object"||r===null||"typ"in r&&(r==null?void 0:r.typ)!=="JWT"||!r.alg||e&&r.alg!==e)}catch{return!1}}function da(a,e){return!!((e==="v4"||!e)&&ea.test(a)||(e==="v6"||!e)&&aa.test(a))}class Z extends _{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==m.string){const s=this._getOrReturnCtx(e);return l(s,{code:d.invalid_type,expected:m.string,received:s.parsedType}),v}const n=new O;let r;for(const s of this._def.checks)if(s.kind==="min")e.data.length<s.value&&(r=this._getOrReturnCtx(e,r),l(r,{code:d.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="max")e.data.length>s.value&&(r=this._getOrReturnCtx(e,r),l(r,{code:d.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="length"){const i=e.data.length>s.value,o=e.data.length<s.value;(i||o)&&(r=this._getOrReturnCtx(e,r),i?l(r,{code:d.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}):o&&l(r,{code:d.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}),n.dirty())}else if(s.kind==="email")Qt.test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"email",code:d.invalid_string,message:s.message}),n.dirty());else if(s.kind==="emoji")Pe||(Pe=new RegExp(Kt,"u")),Pe.test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"emoji",code:d.invalid_string,message:s.message}),n.dirty());else if(s.kind==="uuid")Wt.test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"uuid",code:d.invalid_string,message:s.message}),n.dirty());else if(s.kind==="nanoid")Jt.test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"nanoid",code:d.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid")Ft.test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"cuid",code:d.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid2")qt.test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"cuid2",code:d.invalid_string,message:s.message}),n.dirty());else if(s.kind==="ulid")Bt.test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"ulid",code:d.invalid_string,message:s.message}),n.dirty());else if(s.kind==="url")try{new URL(e.data)}catch{r=this._getOrReturnCtx(e,r),l(r,{validation:"url",code:d.invalid_string,message:s.message}),n.dirty()}else s.kind==="regex"?(s.regex.lastIndex=0,s.regex.test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"regex",code:d.invalid_string,message:s.message}),n.dirty())):s.kind==="trim"?e.data=e.data.trim():s.kind==="includes"?e.data.includes(s.value,s.position)||(r=this._getOrReturnCtx(e,r),l(r,{code:d.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),n.dirty()):s.kind==="toLowerCase"?e.data=e.data.toLowerCase():s.kind==="toUpperCase"?e.data=e.data.toUpperCase():s.kind==="startsWith"?e.data.startsWith(s.value)||(r=this._getOrReturnCtx(e,r),l(r,{code:d.invalid_string,validation:{startsWith:s.value},message:s.message}),n.dirty()):s.kind==="endsWith"?e.data.endsWith(s.value)||(r=this._getOrReturnCtx(e,r),l(r,{code:d.invalid_string,validation:{endsWith:s.value},message:s.message}),n.dirty()):s.kind==="datetime"?oa(s).test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{code:d.invalid_string,validation:"datetime",message:s.message}),n.dirty()):s.kind==="date"?sa.test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{code:d.invalid_string,validation:"date",message:s.message}),n.dirty()):s.kind==="time"?ia(s).test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{code:d.invalid_string,validation:"time",message:s.message}),n.dirty()):s.kind==="duration"?Yt.test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"duration",code:d.invalid_string,message:s.message}),n.dirty()):s.kind==="ip"?ua(e.data,s.version)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"ip",code:d.invalid_string,message:s.message}),n.dirty()):s.kind==="jwt"?ca(e.data,s.alg)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"jwt",code:d.invalid_string,message:s.message}),n.dirty()):s.kind==="cidr"?da(e.data,s.version)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"cidr",code:d.invalid_string,message:s.message}),n.dirty()):s.kind==="base64"?na.test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"base64",code:d.invalid_string,message:s.message}),n.dirty()):s.kind==="base64url"?ra.test(e.data)||(r=this._getOrReturnCtx(e,r),l(r,{validation:"base64url",code:d.invalid_string,message:s.message}),n.dirty()):S.assertNever(s);return{status:n.value,value:e.data}}_regex(e,t,n){return this.refinement(r=>e.test(r),{validation:t,code:d.invalid_string,...h.errToObj(n)})}_addCheck(e){return new Z({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...h.errToObj(e)})}url(e){return this._addCheck({kind:"url",...h.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...h.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...h.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...h.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...h.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...h.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...h.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...h.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...h.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...h.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...h.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...h.errToObj(e)})}datetime(e){return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,offset:(e==null?void 0:e.offset)??!1,local:(e==null?void 0:e.local)??!1,...h.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,...h.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...h.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...h.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...h.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...h.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...h.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...h.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...h.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...h.errToObj(t)})}nonempty(e){return this.min(1,h.errToObj(e))}trim(){return new Z({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Z({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Z({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Z.create=a=>new Z({checks:[],typeName:g.ZodString,coerce:(a==null?void 0:a.coerce)??!1,...y(a)});function la(a,e){const t=(a.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=t>n?t:n,s=Number.parseInt(a.toFixed(r).replace(".","")),i=Number.parseInt(e.toFixed(r).replace(".",""));return s%i/10**r}class F extends _{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==m.number){const s=this._getOrReturnCtx(e);return l(s,{code:d.invalid_type,expected:m.number,received:s.parsedType}),v}let n;const r=new O;for(const s of this._def.checks)s.kind==="int"?S.isInteger(e.data)||(n=this._getOrReturnCtx(e,n),l(n,{code:d.invalid_type,expected:"integer",received:"float",message:s.message}),r.dirty()):s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(n=this._getOrReturnCtx(e,n),l(n,{code:d.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),r.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(n=this._getOrReturnCtx(e,n),l(n,{code:d.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),r.dirty()):s.kind==="multipleOf"?la(e.data,s.value)!==0&&(n=this._getOrReturnCtx(e,n),l(n,{code:d.not_multiple_of,multipleOf:s.value,message:s.message}),r.dirty()):s.kind==="finite"?Number.isFinite(e.data)||(n=this._getOrReturnCtx(e,n),l(n,{code:d.not_finite,message:s.message}),r.dirty()):S.assertNever(s);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,h.toString(t))}gt(e,t){return this.setLimit("min",e,!1,h.toString(t))}lte(e,t){return this.setLimit("max",e,!0,h.toString(t))}lt(e,t){return this.setLimit("max",e,!1,h.toString(t))}setLimit(e,t,n,r){return new F({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:h.toString(r)}]})}_addCheck(e){return new F({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:h.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:h.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:h.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:h.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:h.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:h.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:h.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:h.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:h.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&S.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}F.create=a=>new F({checks:[],typeName:g.ZodNumber,coerce:(a==null?void 0:a.coerce)||!1,...y(a)});class q extends _{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==m.bigint)return this._getInvalidInput(e);let n;const r=new O;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(n=this._getOrReturnCtx(e,n),l(n,{code:d.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),r.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(n=this._getOrReturnCtx(e,n),l(n,{code:d.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),r.dirty()):s.kind==="multipleOf"?e.data%s.value!==BigInt(0)&&(n=this._getOrReturnCtx(e,n),l(n,{code:d.not_multiple_of,multipleOf:s.value,message:s.message}),r.dirty()):S.assertNever(s);return{status:r.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return l(t,{code:d.invalid_type,expected:m.bigint,received:t.parsedType}),v}gte(e,t){return this.setLimit("min",e,!0,h.toString(t))}gt(e,t){return this.setLimit("min",e,!1,h.toString(t))}lte(e,t){return this.setLimit("max",e,!0,h.toString(t))}lt(e,t){return this.setLimit("max",e,!1,h.toString(t))}setLimit(e,t,n,r){return new q({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:h.toString(r)}]})}_addCheck(e){return new q({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:h.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:h.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:h.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:h.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:h.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}q.create=a=>new q({checks:[],typeName:g.ZodBigInt,coerce:(a==null?void 0:a.coerce)??!1,...y(a)});class ye extends _{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==m.boolean){const n=this._getOrReturnCtx(e);return l(n,{code:d.invalid_type,expected:m.boolean,received:n.parsedType}),v}return A(e.data)}}ye.create=a=>new ye({typeName:g.ZodBoolean,coerce:(a==null?void 0:a.coerce)||!1,...y(a)});class Q extends _{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==m.date){const s=this._getOrReturnCtx(e);return l(s,{code:d.invalid_type,expected:m.date,received:s.parsedType}),v}if(Number.isNaN(e.data.getTime())){const s=this._getOrReturnCtx(e);return l(s,{code:d.invalid_date}),v}const n=new O;let r;for(const s of this._def.checks)s.kind==="min"?e.data.getTime()<s.value&&(r=this._getOrReturnCtx(e,r),l(r,{code:d.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),n.dirty()):s.kind==="max"?e.data.getTime()>s.value&&(r=this._getOrReturnCtx(e,r),l(r,{code:d.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),n.dirty()):S.assertNever(s);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new Q({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:h.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:h.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}Q.create=a=>new Q({checks:[],coerce:(a==null?void 0:a.coerce)||!1,typeName:g.ZodDate,...y(a)});class mt extends _{_parse(e){if(this._getType(e)!==m.symbol){const n=this._getOrReturnCtx(e);return l(n,{code:d.invalid_type,expected:m.symbol,received:n.parsedType}),v}return A(e.data)}}mt.create=a=>new mt({typeName:g.ZodSymbol,...y(a)});class ht extends _{_parse(e){if(this._getType(e)!==m.undefined){const n=this._getOrReturnCtx(e);return l(n,{code:d.invalid_type,expected:m.undefined,received:n.parsedType}),v}return A(e.data)}}ht.create=a=>new ht({typeName:g.ZodUndefined,...y(a)});class pt extends _{_parse(e){if(this._getType(e)!==m.null){const n=this._getOrReturnCtx(e);return l(n,{code:d.invalid_type,expected:m.null,received:n.parsedType}),v}return A(e.data)}}pt.create=a=>new pt({typeName:g.ZodNull,...y(a)});class ft extends _{constructor(){super(...arguments),this._any=!0}_parse(e){return A(e.data)}}ft.create=a=>new ft({typeName:g.ZodAny,...y(a)});class vt extends _{constructor(){super(...arguments),this._unknown=!0}_parse(e){return A(e.data)}}vt.create=a=>new vt({typeName:g.ZodUnknown,...y(a)});class $ extends _{_parse(e){const t=this._getOrReturnCtx(e);return l(t,{code:d.invalid_type,expected:m.never,received:t.parsedType}),v}}$.create=a=>new $({typeName:g.ZodNever,...y(a)});class gt extends _{_parse(e){if(this._getType(e)!==m.undefined){const n=this._getOrReturnCtx(e);return l(n,{code:d.invalid_type,expected:m.void,received:n.parsedType}),v}return A(e.data)}}gt.create=a=>new gt({typeName:g.ZodVoid,...y(a)});class N extends _{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==m.array)return l(t,{code:d.invalid_type,expected:m.array,received:t.parsedType}),v;if(r.exactLength!==null){const i=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(i||o)&&(l(t,{code:i?d.too_big:d.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(l(t,{code:d.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(l(t,{code:d.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>r.type._parseAsync(new M(t,i,t.path,o)))).then(i=>O.mergeArray(n,i));const s=[...t.data].map((i,o)=>r.type._parseSync(new M(t,i,t.path,o)));return O.mergeArray(n,s)}get element(){return this._def.type}min(e,t){return new N({...this._def,minLength:{value:e,message:h.toString(t)}})}max(e,t){return new N({...this._def,maxLength:{value:e,message:h.toString(t)}})}length(e,t){return new N({...this._def,exactLength:{value:e,message:h.toString(t)}})}nonempty(e){return this.min(1,e)}}N.create=(a,e)=>new N({type:a,minLength:null,maxLength:null,exactLength:null,typeName:g.ZodArray,...y(e)});function H(a){if(a instanceof I){const e={};for(const t in a.shape){const n=a.shape[t];e[t]=L.create(H(n))}return new I({...a._def,shape:()=>e})}else return a instanceof N?new N({...a._def,type:H(a.element)}):a instanceof L?L.create(H(a.unwrap())):a instanceof ee?ee.create(H(a.unwrap())):a instanceof B?B.create(a.items.map(e=>H(e))):a}class I extends _{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=S.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==m.object){const f=this._getOrReturnCtx(e);return l(f,{code:d.invalid_type,expected:m.object,received:f.parsedType}),v}const{status:n,ctx:r}=this._processInputParams(e),{shape:s,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof $&&this._def.unknownKeys==="strip"))for(const f in r.data)i.includes(f)||o.push(f);const p=[];for(const f of i){const b=s[f],ie=r.data[f];p.push({key:{status:"valid",value:f},value:b._parse(new M(r,ie,r.path,f)),alwaysSet:f in r.data})}if(this._def.catchall instanceof $){const f=this._def.unknownKeys;if(f==="passthrough")for(const b of o)p.push({key:{status:"valid",value:b},value:{status:"valid",value:r.data[b]}});else if(f==="strict")o.length>0&&(l(r,{code:d.unrecognized_keys,keys:o}),n.dirty());else if(f!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const f=this._def.catchall;for(const b of o){const ie=r.data[b];p.push({key:{status:"valid",value:b},value:f._parse(new M(r,ie,r.path,b)),alwaysSet:b in r.data})}}return r.common.async?Promise.resolve().then(async()=>{const f=[];for(const b of p){const ie=await b.key,Lt=await b.value;f.push({key:ie,value:Lt,alwaysSet:b.alwaysSet})}return f}).then(f=>O.mergeObjectSync(n,f)):O.mergeObjectSync(n,p)}get shape(){return this._def.shape()}strict(e){return h.errToObj,new I({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var s,i;const r=((i=(s=this._def).errorMap)==null?void 0:i.call(s,t,n).message)??n.defaultError;return t.code==="unrecognized_keys"?{message:h.errToObj(e).message??r}:{message:r}}}:{}})}strip(){return new I({...this._def,unknownKeys:"strip"})}passthrough(){return new I({...this._def,unknownKeys:"passthrough"})}extend(e){return new I({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new I({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:g.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new I({...this._def,catchall:e})}pick(e){const t={};for(const n of S.objectKeys(e))e[n]&&this.shape[n]&&(t[n]=this.shape[n]);return new I({...this._def,shape:()=>t})}omit(e){const t={};for(const n of S.objectKeys(this.shape))e[n]||(t[n]=this.shape[n]);return new I({...this._def,shape:()=>t})}deepPartial(){return H(this)}partial(e){const t={};for(const n of S.objectKeys(this.shape)){const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}return new I({...this._def,shape:()=>t})}required(e){const t={};for(const n of S.objectKeys(this.shape))if(e&&!e[n])t[n]=this.shape[n];else{let s=this.shape[n];for(;s instanceof L;)s=s._def.innerType;t[n]=s}return new I({...this._def,shape:()=>t})}keyof(){return Ct(S.objectKeys(this.shape))}}I.create=(a,e)=>new I({shape:()=>a,unknownKeys:"strip",catchall:$.create(),typeName:g.ZodObject,...y(e)});I.strictCreate=(a,e)=>new I({shape:()=>a,unknownKeys:"strict",catchall:$.create(),typeName:g.ZodObject,...y(e)});I.lazycreate=(a,e)=>new I({shape:a,unknownKeys:"strip",catchall:$.create(),typeName:g.ZodObject,...y(e)});class _e extends _{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;function r(s){for(const o of s)if(o.result.status==="valid")return o.result;for(const o of s)if(o.result.status==="dirty")return t.common.issues.push(...o.ctx.common.issues),o.result;const i=s.map(o=>new E(o.ctx.common.issues));return l(t,{code:d.invalid_union,unionErrors:i}),v}if(t.common.async)return Promise.all(n.map(async s=>{const i={...t,common:{...t.common,issues:[]},parent:null};return{result:await s._parseAsync({data:t.data,path:t.path,parent:i}),ctx:i}})).then(r);{let s;const i=[];for(const p of n){const f={...t,common:{...t.common,issues:[]},parent:null},b=p._parseSync({data:t.data,path:t.path,parent:f});if(b.status==="valid")return b;b.status==="dirty"&&!s&&(s={result:b,ctx:f}),f.common.issues.length&&i.push(f.common.issues)}if(s)return t.common.issues.push(...s.ctx.common.issues),s.result;const o=i.map(p=>new E(p));return l(t,{code:d.invalid_union,unionErrors:o}),v}}get options(){return this._def.options}}_e.create=(a,e)=>new _e({options:a,typeName:g.ZodUnion,...y(e)});function $e(a,e){const t=P(a),n=P(e);if(a===e)return{valid:!0,data:a};if(t===m.object&&n===m.object){const r=S.objectKeys(e),s=S.objectKeys(a).filter(o=>r.indexOf(o)!==-1),i={...a,...e};for(const o of s){const p=$e(a[o],e[o]);if(!p.valid)return{valid:!1};i[o]=p.data}return{valid:!0,data:i}}else if(t===m.array&&n===m.array){if(a.length!==e.length)return{valid:!1};const r=[];for(let s=0;s<a.length;s++){const i=a[s],o=e[s],p=$e(i,o);if(!p.valid)return{valid:!1};r.push(p.data)}return{valid:!0,data:r}}else return t===m.date&&n===m.date&&+a==+e?{valid:!0,data:a}:{valid:!1}}class Se extends _{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=(s,i)=>{if(ct(s)||ct(i))return v;const o=$e(s.value,i.value);return o.valid?((dt(s)||dt(i))&&t.dirty(),{status:t.value,value:o.data}):(l(n,{code:d.invalid_intersection_types}),v)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([s,i])=>r(s,i)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Se.create=(a,e,t)=>new Se({left:a,right:e,typeName:g.ZodIntersection,...y(t)});class B extends _{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==m.array)return l(n,{code:d.invalid_type,expected:m.array,received:n.parsedType}),v;if(n.data.length<this._def.items.length)return l(n,{code:d.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),v;!this._def.rest&&n.data.length>this._def.items.length&&(l(n,{code:d.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const s=[...n.data].map((i,o)=>{const p=this._def.items[o]||this._def.rest;return p?p._parse(new M(n,i,n.path,o)):null}).filter(i=>!!i);return n.common.async?Promise.all(s).then(i=>O.mergeArray(t,i)):O.mergeArray(t,s)}get items(){return this._def.items}rest(e){return new B({...this._def,rest:e})}}B.create=(a,e)=>{if(!Array.isArray(a))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new B({items:a,typeName:g.ZodTuple,rest:null,...y(e)})};class yt extends _{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==m.map)return l(n,{code:d.invalid_type,expected:m.map,received:n.parsedType}),v;const r=this._def.keyType,s=this._def.valueType,i=[...n.data.entries()].map(([o,p],f)=>({key:r._parse(new M(n,o,n.path,[f,"key"])),value:s._parse(new M(n,p,n.path,[f,"value"]))}));if(n.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const p of i){const f=await p.key,b=await p.value;if(f.status==="aborted"||b.status==="aborted")return v;(f.status==="dirty"||b.status==="dirty")&&t.dirty(),o.set(f.value,b.value)}return{status:t.value,value:o}})}else{const o=new Map;for(const p of i){const f=p.key,b=p.value;if(f.status==="aborted"||b.status==="aborted")return v;(f.status==="dirty"||b.status==="dirty")&&t.dirty(),o.set(f.value,b.value)}return{status:t.value,value:o}}}}yt.create=(a,e,t)=>new yt({valueType:e,keyType:a,typeName:g.ZodMap,...y(t)});class ce extends _{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==m.set)return l(n,{code:d.invalid_type,expected:m.set,received:n.parsedType}),v;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(l(n,{code:d.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(l(n,{code:d.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const s=this._def.valueType;function i(p){const f=new Set;for(const b of p){if(b.status==="aborted")return v;b.status==="dirty"&&t.dirty(),f.add(b.value)}return{status:t.value,value:f}}const o=[...n.data.values()].map((p,f)=>s._parse(new M(n,p,n.path,f)));return n.common.async?Promise.all(o).then(p=>i(p)):i(o)}min(e,t){return new ce({...this._def,minSize:{value:e,message:h.toString(t)}})}max(e,t){return new ce({...this._def,maxSize:{value:e,message:h.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}ce.create=(a,e)=>new ce({valueType:a,minSize:null,maxSize:null,typeName:g.ZodSet,...y(e)});class _t extends _{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}_t.create=(a,e)=>new _t({getter:a,typeName:g.ZodLazy,...y(e)});class St extends _{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return l(t,{received:t.data,code:d.invalid_literal,expected:this._def.value}),v}return{status:"valid",value:e.data}}get value(){return this._def.value}}St.create=(a,e)=>new St({value:a,typeName:g.ZodLiteral,...y(e)});function Ct(a,e){return new K({values:a,typeName:g.ZodEnum,...y(e)})}class K extends _{_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return l(t,{expected:S.joinValues(n),received:t.parsedType,code:d.invalid_type}),v}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return l(t,{received:t.data,code:d.invalid_enum_value,options:n}),v}return A(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return K.create(e,{...this._def,...t})}exclude(e,t=this._def){return K.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}}K.create=Ct;class bt extends _{_parse(e){const t=S.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==m.string&&n.parsedType!==m.number){const r=S.objectValues(t);return l(n,{expected:S.joinValues(r),received:n.parsedType,code:d.invalid_type}),v}if(this._cache||(this._cache=new Set(S.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const r=S.objectValues(t);return l(n,{received:n.data,code:d.invalid_enum_value,options:r}),v}return A(e.data)}get enum(){return this._def.values}}bt.create=(a,e)=>new bt({values:a,typeName:g.ZodNativeEnum,...y(e)});class be extends _{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==m.promise&&t.common.async===!1)return l(t,{code:d.invalid_type,expected:m.promise,received:t.parsedType}),v;const n=t.parsedType===m.promise?t.data:Promise.resolve(t.data);return A(n.then(r=>this._def.type.parseAsync(r,{path:t.path,errorMap:t.common.contextualErrorMap})))}}be.create=(a,e)=>new be({type:a,typeName:g.ZodPromise,...y(e)});class X extends _{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===g.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=this._def.effect||null,s={addIssue:i=>{l(n,i),i.fatal?t.abort():t.dirty()},get path(){return n.path}};if(s.addIssue=s.addIssue.bind(s),r.type==="preprocess"){const i=r.transform(n.data,s);if(n.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return v;const p=await this._def.schema._parseAsync({data:o,path:n.path,parent:n});return p.status==="aborted"?v:p.status==="dirty"||t.value==="dirty"?oe(p.value):p});{if(t.value==="aborted")return v;const o=this._def.schema._parseSync({data:i,path:n.path,parent:n});return o.status==="aborted"?v:o.status==="dirty"||t.value==="dirty"?oe(o.value):o}}if(r.type==="refinement"){const i=o=>{const p=r.refinement(o,s);if(n.common.async)return Promise.resolve(p);if(p instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?v:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>o.status==="aborted"?v:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(r.type==="transform")if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Y(i))return v;const o=r.transform(i.value,s);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>Y(i)?Promise.resolve(r.transform(i.value,s)).then(o=>({status:t.value,value:o})):v);S.assertNever(r)}}X.create=(a,e,t)=>new X({schema:a,typeName:g.ZodEffects,effect:e,...y(t)});X.createWithPreprocess=(a,e,t)=>new X({schema:e,effect:{type:"preprocess",transform:a},typeName:g.ZodEffects,...y(t)});class L extends _{_parse(e){return this._getType(e)===m.undefined?A(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}L.create=(a,e)=>new L({innerType:a,typeName:g.ZodOptional,...y(e)});class ee extends _{_parse(e){return this._getType(e)===m.null?A(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ee.create=(a,e)=>new ee({innerType:a,typeName:g.ZodNullable,...y(e)});class De extends _{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===m.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}De.create=(a,e)=>new De({innerType:a,typeName:g.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...y(e)});class ze extends _{_parse(e){const{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return ge(r)?r.then(s=>({status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new E(n.common.issues)},input:n.data})})):{status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new E(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}ze.create=(a,e)=>new ze({innerType:a,typeName:g.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...y(e)});class xt extends _{_parse(e){if(this._getType(e)!==m.nan){const n=this._getOrReturnCtx(e);return l(n,{code:d.invalid_type,expected:m.nan,received:n.parsedType}),v}return{status:"valid",value:e.data}}}xt.create=a=>new xt({typeName:g.ZodNaN,...y(a)});class ma extends _{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class We extends _{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?v:s.status==="dirty"?(t.dirty(),oe(s.value)):this._def.out._parseAsync({data:s.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?v:r.status==="dirty"?(t.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,t){return new We({in:e,out:t,typeName:g.ZodPipeline})}}class Ve extends _{_parse(e){const t=this._def.innerType._parse(e),n=r=>(Y(r)&&(r.value=Object.freeze(r.value)),r);return ge(t)?t.then(r=>n(r)):n(t)}unwrap(){return this._def.innerType}}Ve.create=(a,e)=>new Ve({innerType:a,typeName:g.ZodReadonly,...y(e)});var g;(function(a){a.ZodString="ZodString",a.ZodNumber="ZodNumber",a.ZodNaN="ZodNaN",a.ZodBigInt="ZodBigInt",a.ZodBoolean="ZodBoolean",a.ZodDate="ZodDate",a.ZodSymbol="ZodSymbol",a.ZodUndefined="ZodUndefined",a.ZodNull="ZodNull",a.ZodAny="ZodAny",a.ZodUnknown="ZodUnknown",a.ZodNever="ZodNever",a.ZodVoid="ZodVoid",a.ZodArray="ZodArray",a.ZodObject="ZodObject",a.ZodUnion="ZodUnion",a.ZodDiscriminatedUnion="ZodDiscriminatedUnion",a.ZodIntersection="ZodIntersection",a.ZodTuple="ZodTuple",a.ZodRecord="ZodRecord",a.ZodMap="ZodMap",a.ZodSet="ZodSet",a.ZodFunction="ZodFunction",a.ZodLazy="ZodLazy",a.ZodLiteral="ZodLiteral",a.ZodEnum="ZodEnum",a.ZodEffects="ZodEffects",a.ZodNativeEnum="ZodNativeEnum",a.ZodOptional="ZodOptional",a.ZodNullable="ZodNullable",a.ZodDefault="ZodDefault",a.ZodCatch="ZodCatch",a.ZodPromise="ZodPromise",a.ZodBranded="ZodBranded",a.ZodPipeline="ZodPipeline",a.ZodReadonly="ZodReadonly"})(g||(g={}));const k=Z.create,w=F.create;q.create;const ae=ye.create,D=Q.create,Le=$.create,x=N.create,c=I.create,Je=_e.create;Se.create;B.create;const z=K.create;be.create;L.create;ee.create;const ue={string:a=>Z.create({...a,coerce:!0}),number:a=>F.create({...a,coerce:!0}),boolean:a=>ye.create({...a,coerce:!0}),bigint:a=>q.create({...a,coerce:!0}),date:a=>Q.create({...a,coerce:!0})};var ha=Object.defineProperty,j=(a,e)=>{for(var t in e)ha(a,t,{get:e[t],enumerable:!0})},pa={};j(pa,{GetSitemapGenerationDataOutputSchema:()=>Ta});var Fe={};j(Fe,{FormDataToObject:()=>ba,ImageSchema:()=>Tt,ImagesSchema:()=>Ca,JsonStringToObject:()=>wt,LocalizationLocaleSchema:()=>le,LocalizationLocalesSchema:()=>Ia,LocalizationSchema:()=>Ot,LocalizationsSchema:()=>C,ObjectWithIdSchema:()=>xa,PaginationSchema:()=>T,WebsiteLocaleSchema:()=>ka,createdAt:()=>U,deletedAt:()=>R,email:()=>V,id:()=>u,idOrNull:()=>ya,imageUrl:()=>de,maybeImageUrl:()=>te,pagination:()=>Be,parseInput:()=>wa,parseUnknown:()=>Oa,query:()=>Sa,searchIds:()=>W,searchQuery:()=>ne,stringToDate:()=>qe,updatedAt:()=>G,url:()=>_a});var fa={};j(fa,{ALLOWED_IMAGE_FILE_TYPES:()=>va,MAX_IMAGE_FILE_SIZE:()=>ga,PAGE_SIZE:()=>xe});var xe=20,va=["image/jpeg","image/png","image/webp"],ga=5*1024*1024,u=k().nanoid(),ya=u.nullable().default(null),_a=k().url(),V=k().email(),Sa=k().nonempty(),de=k().nonempty(),te=de.nullable(),U=D(),G=D(),R=D().nullable(),W=x(u).min(1),ne=k().nonempty(),qe=Je([w(),k(),D()]).pipe(ue.date());function wt(a){return k().transform(e=>JSON.parse(e)).pipe(c(a))}function ba(a){return c({data:wt(a)})}var xa=c({id:u}),ka=z(["en","ru"]),le=z(["en","ru"]),Ia=x(le).min(1),Ot=c({locale:le,value:k().nonempty()}),C=x(Ot),Tt=c({id:u,url:k(),createdAt:qe,updatedAt:qe}),Ca=x(Tt),Be={offset:ue.number().int().default(0),limit:ue.number().int().positive().max(100).default(xe),page:ue.number().int().positive().default(1),size:ue.number().int().positive().max(100).default(xe)},T=c({page:Be.page,size:Be.size}).default({page:1,size:xe});function wa(a,e){return a.parse(e)}function Oa(a,e){return a.parse(e)}var Ta=c({communeIds:x(u),reactorPostIds:x(u),reactorHubIds:x(u),reactorCommunityIds:x(u)}),Aa={};j(Aa,{SendOtpInputSchema:()=>qa,SendOtpOutputSchema:()=>Ba,SigninInputSchema:()=>Ja,SignupInputSchema:()=>Wa,SuccessfulOutputSchema:()=>Ha,otp:()=>Qe});var At={};j(At,{CreateUserTitleInputSchema:()=>Ea,DeleteUserInviteInputSchema:()=>Fa,GetMeOutputSchema:()=>Na,GetUserInvitesInputSchema:()=>Da,GetUserInvitesOutputSchema:()=>za,GetUserNoteInputSchema:()=>La,GetUserNoteOutputSchema:()=>Ma,GetUserOutputSchema:()=>Nt,GetUserTitlesInputSchema:()=>Ga,GetUserTitlesOutputSchema:()=>Pa,GetUsersInputSchema:()=>Ua,GetUsersOutputSchema:()=>Ra,SimpleUserSchema:()=>J,UpdateUserInputSchema:()=>Za,UpdateUserNoteInputSchema:()=>$a,UpdateUserTitleInputSchema:()=>ja,UpsertUserInviteInputSchema:()=>Va,UserRoleSchema:()=>we,userDescription:()=>ke,userImage:()=>He,userName:()=>re,userNoteText:()=>Ye,userTitleColor:()=>Ce,userTitleIsActive:()=>me,userTitleName:()=>Ie});var re=C.min(1),ke=C,He=de.nullable(),Ie=C.min(1),me=ae(),Ce=k().nonempty().nullable(),Ye=k().nonempty(),we=z(["admin","moderator","user"]),J=c({id:u,name:re,image:He}),Na=c({id:u,email:V,role:we,name:re,description:ke,image:de.nullable(),createdAt:U,updatedAt:G}),Ua=c({pagination:T,ids:W,query:ne}).partial(),Nt=c({id:u,role:we,name:re,description:ke,image:He,createdAt:U,updatedAt:G,deletedAt:R.optional()}),Ra=x(Nt),Za=c({id:u,name:re.optional(),description:ke.optional()}),Ea=c({userId:u,name:Ie,isActive:me,color:Ce}),ja=c({id:u,name:Ie.optional(),isActive:me.optional(),color:Ce.optional()}),Ga=c({userId:u,ids:W.optional(),isActive:me.optional()}),Pa=x(c({id:u,userId:u,name:Ie,isActive:me,color:Ce,createdAt:U,updatedAt:G,deletedAt:R.optional()})),La=c({userId:u}),Ma=c({text:Ye.nullable()}),$a=c({userId:u,text:Ye.nullable()}),Da=c({pagination:T}),za=x(c({id:u,email:V,name:k().nonempty().nullable(),locale:le,isUsed:ae()})),Va=c({email:V,name:k().nonempty().nullable(),locale:le}),Fa=c({id:u}),Qe=k().nonempty().length(6),qa=c({email:V}),Ba=c({isSent:ae()}),Wa=c({referrerId:u.nullable(),email:V,otp:Qe}),Ja=c({email:V,otp:Qe}),Ha=c({id:u,email:V,role:we}),Ya={};j(Ya,{CommuneInvitationStatusSchema:()=>Et,CommuneJoinRequestStatusSchema:()=>jt,CommuneMemberTypeSchema:()=>Ut,CreateCommuneInputSchema:()=>en,CreateCommuneInvitationInputSchema:()=>un,CreateCommuneJoinRequestInputSchema:()=>ln,CreateCommuneMemberInputSchema:()=>rn,GetCommuneInvitationsInputSchema:()=>sn,GetCommuneInvitationsOutputSchema:()=>on,GetCommuneJoinRequestsInputSchema:()=>cn,GetCommuneJoinRequestsOutputSchema:()=>dn,GetCommuneMemberOutputSchema:()=>Zt,GetCommuneMembersInputSchema:()=>an,GetCommuneMembersOutputSchema:()=>nn,GetCommuneOutputSchema:()=>Rt,GetCommunesInputSchema:()=>Ka,GetCommunesOutputSchema:()=>Xa,TransferHeadStatusInputSchema:()=>Qa,UpdateCommuneInputSchema:()=>tn,communeDescription:()=>Oe,communeMemberActorType:()=>Ke,communeMemberName:()=>Xe,communeName:()=>he});var Ut=z(["user"]),he=C.min(1),Oe=C,Ke=Ut,Xe=Je([re,he]),Qa=c({communeId:u,newHeadUserId:u}),Ka=c({pagination:T,ids:W,query:ne,userId:u}).partial(),Rt=c({id:u,name:he,description:Oe,headMember:c({actorType:Ke,actorId:u,name:Xe,image:te}),memberCount:w().int().positive(),image:te,createdAt:U,updatedAt:G,deletedAt:R.optional()}),Xa=x(Rt),en=c({headUserId:u.optional(),name:he,description:Oe}),tn=c({id:u,name:he.optional(),description:Oe.optional()}),an=c({pagination:T,communeId:u}),Zt=c({id:u,actorType:Ke,actorId:u,name:Xe,image:te,createdAt:U,deletedAt:R.nullable()}),nn=x(Zt),rn=c({communeId:u,userId:u}),Et=z(["pending","accepted","rejected","expired"]),sn=c({pagination:T,communeId:u.optional()}),on=x(c({id:u,communeId:u,userId:u,status:Et,createdAt:D(),updatedAt:D()})),un=c({communeId:u,userId:u}),jt=z(["pending","accepted","rejected"]),cn=c({pagination:T,communeId:u.optional()}),dn=x(c({id:u,communeId:u,userId:u,status:jt,createdAt:D(),updatedAt:D()})),ln=c({communeId:u,userId:u}),mn={};j(mn,{AnonimifyCommentInputSchema:()=>Gn,CommentEntityTypeSchema:()=>nt,CreateCommentInputSchema:()=>Un,CreateCommunityInputSchema:()=>Bn,CreateHubInputSchema:()=>zn,CreateLensInputSchema:()=>Ln,CreatePostInputSchema:()=>xn,DeleteCommentInputSchema:()=>Zn,DeletePostInputSchema:()=>In,GetCommentsInputSchema:()=>An,GetCommentsOutputSchema:()=>Nn,GetCommunitiesInputSchema:()=>Fn,GetCommunitiesOutputSchema:()=>qn,GetHubsInputSchema:()=>$n,GetHubsOutputSchema:()=>Dn,GetLensesOutputSchema:()=>Pn,GetPostImagesInputSchema:()=>Sn,GetPostImagesOutputSchema:()=>bn,GetPostOutputSchema:()=>Pt,GetPostsInputSchema:()=>yn,GetPostsOutputSchema:()=>_n,PostImageSchema:()=>Gt,PostUsefulnessSchema:()=>at,RatingSchema:()=>fe,RatingTypeSchema:()=>Te,UpdateCommentInputSchema:()=>Rn,UpdateCommentRatingInputSchema:()=>En,UpdateCommentRatingOutputSchema:()=>jn,UpdateCommunityInputSchema:()=>Wn,UpdateHubInputSchema:()=>Vn,UpdateLensInputSchema:()=>Mn,UpdatePostInputSchema:()=>kn,UpdatePostRatingInputSchema:()=>Cn,UpdatePostRatingOutputSchema:()=>wn,UpdatePostUsefulnessInputSchema:()=>On,UpdatePostUsefulnessOutputSchema:()=>Tn,commentBody:()=>Ee,communityDescription:()=>Ue,communityImage:()=>et,communityName:()=>ve,hubDescription:()=>Ae,hubImage:()=>Ne,hubName:()=>se,lensCode:()=>Ge,lensName:()=>je,postBody:()=>Ze,postTitle:()=>Re,postUsefulness:()=>tt});var hn={};j(hn,{CreateTagInputSchema:()=>vn,GetTagsInputSchema:()=>pn,GetTagsOutputSchema:()=>fn,UpdateTagInputSchema:()=>gn,tagName:()=>pe});var pe=C.min(1),pn=c({pagination:T,ids:W,query:ne}).partial(),fn=x(c({id:u,name:pe,deletedAt:R.optional()})),vn=c({name:pe}),gn=c({id:u,name:pe}),Te=z(["like","dislike"]),fe=c({likes:w().int().nonnegative(),dislikes:w().int().nonnegative(),status:Te.nullable()}),se=C.min(1),Ae=C.min(1),Ne=te,ve=C.min(1),Ue=C.min(1),et=te,tt=w().int().min(0).max(10),at=c({value:tt.nullable(),count:w().int().nonnegative(),totalValue:w().min(0).max(10).nullable()}),Re=C.min(1),Ze=C.min(1),Gt=c({id:u,url:de}),Pt=c({id:u,hub:c({id:u,name:se,image:Ne}).nullable(),community:c({id:u,name:ve,image:et}).nullable(),author:J,rating:fe,usefulness:at,title:Re,body:Ze,tags:x(c({id:u,name:pe})),createdAt:U,updatedAt:G,deletedAt:R.optional()}),yn=c({pagination:T,id:u.optional(),lensId:u.nullable()}),_n=x(Pt),Sn=c({id:u}),bn=x(Gt),xn=c({hubId:u.nullable(),communityId:u.nullable(),title:Re,body:Ze,tagIds:x(u),imageIds:x(u)}),kn=c({id:u,title:Re.optional(),body:Ze.optional(),tagIds:x(u).optional(),imageIds:x(u).optional()}),In=c({id:u,reason:k().nonempty().nullable()}),Cn=c({id:u,type:Te}),wn=fe,On=c({id:u,value:tt.nullable()}),Tn=at,nt=z(["post","comment"]),Ee=C.min(1),An=Je([c({id:u,entityType:Le().optional(),entityId:Le().optional()}),c({id:Le().optional(),entityType:nt,entityId:u})]),Nn=x(c({id:u,path:k().nonempty(),author:J.nullable(),isAnonymous:ae(),anonimityReason:k().nonempty().nullable(),rating:fe,body:Ee.nullable(),childrenCount:w().int().nonnegative(),deleteReason:k().nonempty().nullable(),createdAt:U,updatedAt:G,deletedAt:R})),Un=c({entityType:nt,entityId:u,body:Ee}),Rn=c({id:u,body:Ee.optional()}),Zn=c({id:u,reason:k().nonempty().nullable()}),En=c({id:u,type:Te}),jn=fe,Gn=c({id:u,reason:k().nonempty().nullable()}),je=k().nonempty(),Ge=k().nonempty(),Pn=x(c({id:u,name:je,code:Ge})),Ln=c({name:je,code:Ge}),Mn=c({id:u,name:je.optional(),code:Ge.optional()}),$n=c({pagination:T,ids:W,query:ne}).partial(),Dn=x(c({id:u,headUser:J,image:Ne,name:se,description:Ae,createdAt:U,updatedAt:G,deletedAt:R.optional()})),zn=c({headUserId:u.nullable(),name:se,description:Ae}),Vn=c({id:u,name:se.optional(),description:Ae.optional()}),Fn=c({pagination:T,ids:W,query:ne,hubId:u}).partial(),qn=x(c({id:u,hub:c({id:u,name:se,image:Ne}).nullable(),headUser:J,image:et,name:ve,description:Ue,createdAt:U,updatedAt:G,deletedAt:R.optional()})),Bn=c({hubId:u.nullable(),headUserId:u.nullable(),name:ve,description:Ue}),Wn=c({id:u,name:ve.optional(),description:Ue.optional()}),Jn={};j(Jn,{CreateUserFeedbackInputSchema:()=>er,GetKarmaPointsInputSchema:()=>Hn,GetKarmaPointsOutputSchema:()=>Yn,GetUserFeedbacksInputSchema:()=>Kn,GetUserFeedbacksOutputSchema:()=>Xn,GetUserSummaryInputSchema:()=>tr,GetUserSummaryOutputSchema:()=>ar,SpendKarmaPointInputSchema:()=>Qn,karmaPointComment:()=>st,karmaPointQuantity:()=>rt,userFeedbackText:()=>ot,userFeedbackValue:()=>it});var rt=w().int(),st=C.min(1),Hn=c({pagination:T,userId:u}),Yn=x(c({id:u,author:J,quantity:rt,comment:st})),Qn=c({sourceUserId:u,targetUserId:u,quantity:rt,comment:st}),it=w().int().min(0).max(10),ot=C.min(1),Kn=c({pagination:T,userId:u}),Xn=x(c({id:u,author:J.nullable(),isAnonymous:ae(),value:it,text:ot})),er=c({sourceUserId:u,targetUserId:u,value:it,isAnonymous:ae(),text:ot}),tr=c({userId:u}),ar=c({rating:w().int(),karma:w().int(),rate:w().min(0).max(10).nullable()});const ir=c({id:Fe.id,email:Fe.email,role:At.UserRoleSchema});function or(a){localStorage.setItem("user",JSON.stringify(a))}function ur(){localStorage.removeItem("user")}export{ir as L,_ as Z,Fe as a,mn as b,fa as c,Jn as d,rr as e,Ya as f,Aa as g,sr as h,ur as r,or as s,hn as t,At as u};
