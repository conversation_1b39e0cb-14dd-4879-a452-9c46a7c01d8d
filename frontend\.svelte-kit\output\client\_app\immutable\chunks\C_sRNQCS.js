import{t as v,I as o,J as l,ar as g,as as y,P as h,ac as p,an as w,at as O,au as R,L as c,U as b,K as E,Q as f}from"./RHWQbow4.js";function C(m,u,i=!1,_=!1,N=!1){var n=m,t="";v(()=>{var s=g;if(t===(t=u()??"")){o&&l();return}if(s.nodes_start!==null&&(y(s.nodes_start,s.nodes_end),s.nodes_start=s.nodes_end=null),t!==""){if(o){h.data;for(var a=l(),d=a;a!==null&&(a.nodeType!==p||a.data!=="");)d=a,a=w(a);if(a===null)throw O(),R;c(h,d),n=b(a);return}var r=t+"";i?r=`<svg>${r}</svg>`:_&&(r=`<math>${r}</math>`);var e=E(r);if((i||_)&&(e=f(e)),c(f(e),e.lastChild),i||_)for(;f(e);)n.before(f(e));else n.before(e)}})}export{C as h};
