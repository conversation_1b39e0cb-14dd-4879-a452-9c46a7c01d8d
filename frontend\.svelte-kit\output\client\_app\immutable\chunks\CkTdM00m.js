import{H as _,I as i,J as d,K as l,L as u,l as h,M as m,N as v,n as y,O as g,P as c,Q as b}from"./RHWQbow4.js";function N(t,n,...s){var a=t,e=y,r;_(()=>{e!==(e=n())&&(r&&(g(r),r=null),r=v(()=>e(a,...s)))},m),i&&(a=c)}function R(t){return(n,...s)=>{var o;var a=t(...s),e;if(i)e=c,d();else{var r=a.render().trim(),p=l(r);e=b(p),n.before(e)}const f=(o=a.setup)==null?void 0:o.call(a,e);u(e,e),typeof f=="function"&&h(f)}}export{R as c,N as s};
