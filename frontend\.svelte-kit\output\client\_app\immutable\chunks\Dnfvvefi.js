import{W as j,H as se,aa as ee,U as X,I as D,Q as ue,J as te,g as K,ab as ve,R as de,S as _e,T as $,V as Y,P as O,ac as oe,ad as ce,N as J,Z as he,ae as q,af as V,X as pe,ag as k,ah as Ee,ai as y,aj as ae,i as me,_ as re,a0 as Te,ak as Ae,al as B,a2 as Ie,O as ne,am as Ne,an as we,ao as xe,ap as Ce,aq as Me,ar as Se}from"./RHWQbow4.js";function Oe(l,r){return r}function De(l,r,e){for(var u=l.items,v=[],d=r.length,s=0;s<d;s++)xe(r[s].e,v,!0);var h=d>0&&v.length===0&&e!==null;if(h){var T=e.parentNode;Ce(T),T.append(e),u.clear(),w(l,r[0].prev,r[d-1].next)}Me(v,()=>{for(var m=0;m<d;m++){var o=r[m];h||(u.delete(o.k),w(l,o.prev,o.next)),ne(o.e,!h)}})}function be(l,r,e,u,v,d=null){var s=l,h={flags:r,items:new Map,first:null},T=(r&ee)!==0;if(T){var m=l;s=D?X(ue(m)):m.appendChild(j())}D&&te();var o=null,C=!1,A=new Map,M=ve(()=>{var _=e();return me(_)?_:_==null?[]:ae(_)}),n,t;function i(){He(t,n,h,A,s,v,r,u,e),d!==null&&(n.length===0?o?re(o):o=J(()=>d(s)):o!==null&&Te(o,()=>{o=null}))}se(()=>{t??(t=Se),n=K(M);var _=n.length;if(C&&_===0)return;C=_===0;let E=!1;if(D){var I=de(s)===_e;I!==(_===0)&&(s=$(),X(s),Y(!1),E=!0)}if(D){for(var x=null,c,a=0;a<_;a++){if(O.nodeType===oe&&O.data===ce){s=O,E=!0,Y(!1);break}var f=n[a],p=u(f,a);c=P(O,h,x,null,f,p,a,v,r,e),h.items.set(p,c),x=c}_>0&&X($())}if(D)_===0&&d&&(o=J(()=>d(s)));else if(he()){var H=new Set,b=pe;for(a=0;a<_;a+=1){f=n[a],p=u(f,a);var S=h.items.get(p)??A.get(p);S?(r&(q|V))!==0&&fe(S,f,a,r):(c=P(null,h,null,null,f,p,a,v,r,e,!0),A.set(p,c)),H.add(p)}for(const[N,L]of h.items)H.has(N)||b.skipped_effects.add(L.e);b.add_callback(i)}else i();E&&Y(!0),K(M)}),D&&(s=O)}function He(l,r,e,u,v,d,s,h,T){var Q,W,Z,z;var m=(s&Ne)!==0,o=(s&(q|V))!==0,C=r.length,A=e.items,M=e.first,n=M,t,i=null,_,E=[],I=[],x,c,a,f;if(m)for(f=0;f<C;f+=1)x=r[f],c=h(x,f),a=A.get(c),a!==void 0&&((Q=a.a)==null||Q.measure(),(_??(_=new Set)).add(a));for(f=0;f<C;f+=1){if(x=r[f],c=h(x,f),a=A.get(c),a===void 0){var p=u.get(c);if(p!==void 0){u.delete(c),A.set(c,p);var H=i?i.next:n;w(e,i,p),w(e,p,H),F(p,H,v),i=p}else{var b=n?n.e.nodes_start:v;i=P(b,e,i,i===null?e.first:i.next,x,c,f,d,s,T)}A.set(c,i),E=[],I=[],n=i.next;continue}if(o&&fe(a,x,f,s),(a.e.f&B)!==0&&(re(a.e),m&&((W=a.a)==null||W.unfix(),(_??(_=new Set)).delete(a))),a!==n){if(t!==void 0&&t.has(a)){if(E.length<I.length){var S=I[0],N;i=S.prev;var L=E[0],g=E[E.length-1];for(N=0;N<E.length;N+=1)F(E[N],S,v);for(N=0;N<I.length;N+=1)t.delete(I[N]);w(e,L.prev,g.next),w(e,i,L),w(e,g,S),n=S,i=g,f-=1,E=[],I=[]}else t.delete(a),F(a,n,v),w(e,a.prev,a.next),w(e,a,i===null?e.first:i.next),w(e,i,a),i=a;continue}for(E=[],I=[];n!==null&&n.k!==c;)(n.e.f&B)===0&&(t??(t=new Set)).add(n),I.push(n),n=n.next;if(n===null)continue;a=n}E.push(a),i=a,n=a.next}if(n!==null||t!==void 0){for(var R=t===void 0?[]:ae(t);n!==null;)(n.e.f&B)===0&&R.push(n),n=n.next;var U=R.length;if(U>0){var ie=(s&ee)!==0&&C===0?v:null;if(m){for(f=0;f<U;f+=1)(Z=R[f].a)==null||Z.measure();for(f=0;f<U;f+=1)(z=R[f].a)==null||z.fix()}De(e,R,ie)}}m&&Ie(()=>{var G;if(_!==void 0)for(a of _)(G=a.a)==null||G.apply()}),l.first=e.first&&e.first.e,l.last=i&&i.e;for(var le of u.values())ne(le.e);u.clear()}function fe(l,r,e,u){(u&q)!==0&&k(l.v,r),(u&V)!==0?k(l.i,e):l.i=e}function P(l,r,e,u,v,d,s,h,T,m,o){var C=(T&q)!==0,A=(T&Ae)===0,M=C?A?Ee(v,!1,!1):y(v):v,n=(T&V)===0?s:y(s),t={i:n,v:M,k:d,a:null,e:null,prev:e,next:u};try{if(l===null){var i=document.createDocumentFragment();i.append(l=j())}return t.e=J(()=>h(l,M,n,m),D),t.e.prev=e&&e.e,t.e.next=u&&u.e,e===null?o||(r.first=t):(e.next=t,e.e.next=t.e),u!==null&&(u.prev=t,u.e.prev=t.e),t}finally{}}function F(l,r,e){for(var u=l.next?l.next.e.nodes_start:e,v=r?r.e.nodes_start:e,d=l.e.nodes_start;d!==null&&d!==u;){var s=we(d);v.before(d),d=s}}function w(l,r,e){r===null?l.first=e:(r.next=e,r.e.next=e&&e.e),e!==null&&(e.prev=r,e.e.prev=r&&r.e)}export{be as e,Oe as i};
