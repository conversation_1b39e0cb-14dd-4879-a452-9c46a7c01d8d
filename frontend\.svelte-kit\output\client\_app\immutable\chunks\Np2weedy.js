import{e as s,i as _,j as d,k as o,l as c}from"./RHWQbow4.js";import{l as b}from"./BlWcudmi.js";function v(e,r,t=!1){if(e.multiple){if(r==null)return;if(!_(r))return d();for(var u of e.options)u.selected=r.includes(n(u));return}for(u of e.options){var a=n(u);if(o(a,r)){u.selected=!0;return}}(!t||r!==void 0)&&(e.selectedIndex=-1)}function m(e){var r=new MutationObserver(()=>{v(e,e.__value)});r.observe(e,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),c(()=>{r.disconnect()})}function h(e,r,t=r){var u=!0;b(e,"change",a=>{var i=a?"[selected]":":checked",l;if(e.multiple)l=[].map.call(e.querySelectorAll(i),n);else{var f=e.querySelector(i)??e.querySelector("option:not([disabled])");l=f&&n(f)}t(l)}),s(()=>{var a=r();if(v(e,a,u),u&&a===void 0){var i=e.querySelector(":checked");i!==null&&(a=n(i),t(a))}e.__value=a,u=!1}),m(e)}function n(e){return"__value"in e?e.__value:e.value}export{h as b,m as i,v as s};
