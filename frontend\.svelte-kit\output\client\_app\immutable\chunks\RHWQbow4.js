var dn=Object.defineProperty;var ge=t=>{throw TypeError(t)};var pn=(t,e,n)=>e in t?dn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var Et=(t,e,n)=>pn(t,typeof e!="symbol"?e+"":e,n),zt=(t,e,n)=>e.has(t)||ge("Cannot "+n);var h=(t,e,n)=>(zt(t,e,"read from private field"),n?n.call(t):e.get(t)),k=(t,e,n)=>e.has(t)?ge("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),O=(t,e,n,r)=>(zt(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n),z=(t,e,n)=>(zt(t,e,"access private method"),n);var wn=Array.isArray,yn=Array.prototype.indexOf,hr=Array.from,Ee=Object.defineProperty,xt=Object.getOwnPropertyDescriptor,mn=Object.getOwnPropertyDescriptors,gn=Object.prototype,En=Array.prototype,Ie=Object.getPrototypeOf,be=Object.isExtensible;const dr=()=>{};function pr(t){return t()}function Pe(t){for(var e=0;e<t.length;e++)t[e]()}function bn(){var t,e,n=new Promise((r,s)=>{t=r,e=s});return{promise:n,resolve:t,reject:e}}function wr(t,e){if(Array.isArray(t))return t;if(!(Symbol.iterator in t))return Array.from(t);const n=[];for(const r of t)if(n.push(r),n.length===e)break;return n}const C=2,se=4,Vt=8,Dt=16,j=32,lt=64,Me=128,I=256,Lt=512,g=1024,M=2048,Y=4096,K=8192,yt=16384,ae=32768,Fe=65536,Te=1<<17,qe=1<<18,ie=1<<19,fe=1<<20,Jt=1<<21,le=1<<22,J=1<<23,Q=Symbol("$state"),yr=Symbol("legacy props"),mr=Symbol(""),ue=new class extends Error{constructor(){super(...arguments);Et(this,"name","StaleReactionError");Et(this,"message","The reaction that called `getAbortSignal()` was re-run or destroyed")}},oe=3,ce=8,Tn=11;function xn(){throw new Error("https://svelte.dev/e/await_outside_boundary")}function An(t){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}function Rn(){throw new Error("https://svelte.dev/e/async_derived_orphan")}function kn(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}function Sn(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function Cn(t){throw new Error("https://svelte.dev/e/effect_orphan")}function On(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function Er(){throw new Error("https://svelte.dev/e/get_abort_signal_outside_reaction")}function br(){throw new Error("https://svelte.dev/e/hydration_failed")}function Tr(t){throw new Error("https://svelte.dev/e/lifecycle_legacy_only")}function xr(t){throw new Error("https://svelte.dev/e/props_invalid_value")}function Nn(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function Dn(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function In(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}const Ar=1,Rr=2,kr=4,Sr=8,Cr=16,Or=1,Nr=2,Dr=4,Ir=8,Pr=16,Pn=1,Mn=2,Le="[",Fn="[!",qn="]",_e={},E=Symbol(),Mr="http://www.w3.org/1999/xhtml";function ve(t){console.warn("https://svelte.dev/e/hydration_mismatch")}function Fr(){console.warn("https://svelte.dev/e/select_multiple_invalid_value")}let b=!1;function xe(t){b=t}let m;function W(t){if(t===null)throw ve(),_e;return m=t}function Ln(){return W(L(m))}function qr(t){if(b){if(L(m)!==null)throw ve(),_e;m=t}}function Lr(t=1){if(b){for(var e=t,n=m;e--;)n=L(n);m=n}}function jr(){for(var t=0,e=m;;){if(e.nodeType===ce){var n=e.data;if(n===qn){if(t===0)return e;t-=1}else(n===Le||n===Fn)&&(t+=1)}var r=L(e);e.remove(),e=r}}function Yr(t){if(!t||t.nodeType!==ce)throw ve(),_e;return t.data}function je(t){return t===this.v}function jn(t,e){return t!=t?e==e:t!==e||t!==null&&typeof t=="object"||typeof t=="function"}function Ye(t){return!jn(t,this.v)}let $t=!1;function Hr(){$t=!0}let y=null;function jt(t){y=t}function Ur(t){return Gt().get(t)}function Br(t,e){return Gt().set(t,e),e}function Vr(t){return Gt().has(t)}function $r(){return Gt()}function Gr(t,e=!1,n){y={p:y,c:null,e:null,s:t,x:null,l:$t&&!e?{s:null,u:null,$:[]}:null}}function Kr(t){var e=y,n=e.e;if(n!==null){e.e=null;for(var r of n)tn(r)}return y=e.p,{}}function It(){return!$t||y!==null&&y.l===null}function Gt(t){return y===null&&An(),y.c??(y.c=new Map(Yn(y)||void 0))}function Yn(t){let e=t.p;for(;e!==null;){const n=e.c;if(n!==null)return n;e=e.p}return null}const Hn=new WeakMap;function Un(t){var e=v;if(e===null)return _.f|=J,t;if((e.f&ae)===0){if((e.f&Me)===0)throw!e.parent&&t instanceof Error&&He(t),t;e.b.error(t)}else he(t,e)}function he(t,e){for(;e!==null;){if((e.f&Me)!==0)try{e.b.error(t);return}catch(n){t=n}e=e.parent}throw t instanceof Error&&He(t),t}function He(t){const e=Hn.get(t);e&&(Ee(t,"message",{value:e.message}),Ee(t,"stack",{value:e.stack}))}const Bn=typeof requestIdleCallback>"u"?t=>setTimeout(t,1):requestIdleCallback;let At=[],Rt=[];function Ue(){var t=At;At=[],Pe(t)}function Be(){var t=Rt;Rt=[],Pe(t)}function Wr(t){At.length===0&&queueMicrotask(Ue),At.push(t)}function zr(t){Rt.length===0&&Bn(Be),Rt.push(t)}function Vn(){At.length>0&&Ue(),Rt.length>0&&Be()}function $n(){for(var t=v.b;t!==null&&!t.has_pending_snippet();)t=t.parent;return t===null&&xn(),t}function de(t){var e=C|M,n=_!==null&&(_.f&C)!==0?_:null;return v===null||n!==null&&(n.f&I)!==0?e|=I:v.f|=ie,{ctx:y,deps:null,effects:null,equals:je,f:e,fn:t,reactions:null,rv:0,v:E,wv:0,parent:n??v,ac:null}}function Gn(t,e){let n=v;n===null&&Rn();var r=n.b,s=void 0,a=we(E),i=null,l=!_;return nr(()=>{try{var f=t()}catch(d){f=Promise.reject(d)}var u=()=>f;s=(i==null?void 0:i.then(u,u))??Promise.resolve(f),i=s;var o=w,c=r.pending;l&&(r.update_pending_count(1),c||o.increment());const p=(d,T=void 0)=>{i=null,c||o.activate(),T?T!==ue&&(a.f|=J,ne(a,T)):((a.f&J)!==0&&(a.f^=J),ne(a,d)),l&&(r.update_pending_count(-1),c||o.decrement()),Ge()};if(s.then(p,d=>p(null,d||"unknown")),o)return()=>{queueMicrotask(()=>o.neuter())}}),new Promise(f=>{function u(o){function c(){o===s?f(a):u(s)}o.then(c,c)}u(s)})}function Xr(t){const e=de(t);return ln(e),e}function Kn(t){const e=de(t);return e.equals=Ye,e}function Ve(t){var e=t.effects;if(e!==null){t.effects=null;for(var n=0;n<e.length;n+=1)ft(e[n])}}function Wn(t){for(var e=t.parent;e!==null;){if((e.f&C)===0)return e;e=e.parent}return null}function pe(t){var e,n=v;Ht(Wn(t));try{Ve(t),e=_n(t)}finally{Ht(n)}return e}function $e(t){var e=pe(t);if(t.equals(e)||(t.v=e,t.wv=on()),!mt)if($!==null)$.set(t,t.v);else{var n=(G||(t.f&I)!==0)&&t.deps!==null?Y:g;R(t,n)}}function zn(t,e,n){const r=It()?de:Kn;if(e.length===0){n(t.map(r));return}var s=w,a=v,i=Xn(),l=$n();Promise.all(e.map(f=>Gn(f))).then(f=>{s==null||s.activate(),i();try{n([...t.map(r),...f])}catch(u){(a.f&yt)===0&&he(u,a)}s==null||s.deactivate(),Ge()}).catch(f=>{l.error(f)})}function Xn(){var t=v,e=_,n=y;return function(){Ht(t),wt(e),jt(n)}}function Ge(){Ht(null),wt(null),jt(null)}const bt=new Set;let w=null,Xt=null,$=null,Ae=new Set,Yt=[];function Ke(){const t=Yt.shift();Yt.length>0&&queueMicrotask(Ke),t()}let rt=[],Kt=null,Qt=!1,Ft=!1;var _t,vt,B,St,Ct,X,ht,Z,V,dt,Ot,Nt,P,We,qt,te;const Bt=class Bt{constructor(){k(this,P);Et(this,"current",new Map);k(this,_t,new Map);k(this,vt,new Set);k(this,B,0);k(this,St,null);k(this,Ct,!1);k(this,X,[]);k(this,ht,[]);k(this,Z,[]);k(this,V,[]);k(this,dt,[]);k(this,Ot,[]);k(this,Nt,[]);Et(this,"skipped_effects",new Set)}process(e){var a;rt=[],Xt=null;var n=null;if(bt.size>1){n=new Map,$=new Map;for(const[i,l]of this.current)n.set(i,{v:i.v,wv:i.wv}),i.v=l;for(const i of bt)if(i!==this)for(const[l,f]of h(i,_t))n.has(l)||(n.set(l,{v:l.v,wv:l.wv}),l.v=f)}for(const i of e)z(this,P,We).call(this,i);if(h(this,X).length===0&&h(this,B)===0){z(this,P,te).call(this);var r=h(this,Z),s=h(this,V);O(this,Z,[]),O(this,V,[]),O(this,dt,[]),Xt=w,w=null,Re(r),Re(s),w===null?w=this:bt.delete(this),(a=h(this,St))==null||a.resolve()}else z(this,P,qt).call(this,h(this,Z)),z(this,P,qt).call(this,h(this,V)),z(this,P,qt).call(this,h(this,dt));if(n){for(const[i,{v:l,wv:f}]of n)i.wv<=f&&(i.v=l);$=null}for(const i of h(this,X))ct(i);for(const i of h(this,ht))ct(i);O(this,X,[]),O(this,ht,[])}capture(e,n){h(this,_t).has(e)||h(this,_t).set(e,n),this.current.set(e,e.v)}activate(){w=this}deactivate(){w=null,Xt=null;for(const e of Ae)if(Ae.delete(e),e(),w!==null)break}neuter(){O(this,Ct,!0)}flush(){rt.length>0?ee():z(this,P,te).call(this),w===this&&(h(this,B)===0&&bt.delete(this),this.deactivate())}increment(){O(this,B,h(this,B)+1)}decrement(){if(O(this,B,h(this,B)-1),h(this,B)===0){for(const e of h(this,Ot))R(e,M),st(e);for(const e of h(this,Nt))R(e,Y),st(e);O(this,Z,[]),O(this,V,[]),this.flush()}else this.deactivate()}add_callback(e){h(this,vt).add(e)}settled(){return(h(this,St)??O(this,St,bn())).promise}static ensure(){if(w===null){const e=w=new Bt;bt.add(w),Ft||Bt.enqueue(()=>{w===e&&e.flush()})}return w}static enqueue(e){Yt.length===0&&queueMicrotask(Ke),Yt.unshift(e)}};_t=new WeakMap,vt=new WeakMap,B=new WeakMap,St=new WeakMap,Ct=new WeakMap,X=new WeakMap,ht=new WeakMap,Z=new WeakMap,V=new WeakMap,dt=new WeakMap,Ot=new WeakMap,Nt=new WeakMap,P=new WeakSet,We=function(e){var o;e.f^=g;for(var n=e.first;n!==null;){var r=n.f,s=(r&(j|lt))!==0,a=s&&(r&g)!==0,i=a||(r&K)!==0||this.skipped_effects.has(n);if(!i&&n.fn!==null){if(s)n.f^=g;else if((r&g)===0)if((r&se)!==0)h(this,V).push(n);else if((r&le)!==0){var l=(o=n.b)!=null&&o.pending?h(this,ht):h(this,X);l.push(n)}else Pt(n)&&((n.f&Dt)!==0&&h(this,dt).push(n),ct(n));var f=n.first;if(f!==null){n=f;continue}}var u=n.parent;for(n=n.next;n===null&&u!==null;)n=u.next,u=u.parent}},qt=function(e){for(const n of e)((n.f&M)!==0?h(this,Ot):h(this,Nt)).push(n),R(n,g);e.length=0},te=function(){if(!h(this,Ct))for(const e of h(this,vt))e();h(this,vt).clear()};let pt=Bt;function Zn(t){var e=Ft;Ft=!0;try{var n;for(t&&(ee(),n=t());;){if(Vn(),rt.length===0&&(w==null||w.flush(),rt.length===0))return Kt=null,n;ee()}}finally{Ft=e}}function ee(){var t=ot;Qt=!0;try{var e=0;for(Ce(!0);rt.length>0;){var n=pt.ensure();if(e++>1e3){var r,s;Jn()}n.process(rt),tt.clear()}}finally{Qt=!1,Ce(t),Kt=null}}function Jn(){try{On()}catch(t){he(t,Kt)}}function Re(t){var e=t.length;if(e!==0){for(var n=0;n<e;){var r=t[n++];if((r.f&(yt|K))===0&&Pt(r)){var s=w?w.current.size:0;if(ct(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null&&r.ac===null?sn(r):r.fn=null),w!==null&&w.current.size>s&&(r.f&fe)!==0)break}}for(;n<e;)st(t[n++])}}function st(t){for(var e=Kt=t;e.parent!==null;){e=e.parent;var n=e.f;if(Qt&&e===v&&(n&Dt)!==0)return;if((n&(lt|j))!==0){if((n&g)===0)return;e.f^=g}}rt.push(e)}const tt=new Map;function we(t,e){var n={f:0,v:t,reactions:null,equals:je,rv:0,wv:0};return n}function H(t,e){const n=we(t);return ln(n),n}function Zr(t,e=!1,n=!0){var s;const r=we(t);return e||(r.equals=Ye),$t&&n&&y!==null&&y.l!==null&&((s=y.l).s??(s.s=[])).push(r),r}function Jr(t,e){return U(t,hn(()=>ut(t))),e}function U(t,e,n=!1){_!==null&&(!F||(_.f&Te)!==0)&&It()&&(_.f&(C|Dt|le|Te))!==0&&!(A!=null&&A.includes(t))&&In();let r=n?Tt(e):e;return ne(t,r)}function ne(t,e){if(!t.equals(e)){var n=t.v;mt?tt.set(t,e):tt.set(t,n),t.v=e;var r=pt.ensure();r.capture(t,n),(t.f&C)!==0&&((t.f&M)!==0&&pe(t),R(t,(t.f&I)===0?g:Y)),t.wv=on(),ze(t,M),It()&&v!==null&&(v.f&g)!==0&&(v.f&(j|lt))===0&&(D===null?lr([t]):D.push(t))}return e}function Zt(t){U(t,t.v+1)}function ze(t,e){var n=t.reactions;if(n!==null)for(var r=It(),s=n.length,a=0;a<s;a++){var i=n[a],l=i.f;if(!(!r&&i===v)){var f=(l&M)===0;f&&R(i,e),(l&C)!==0?ze(i,Y):f&&st(i)}}}function Tt(t){if(typeof t!="object"||t===null||Q in t)return t;const e=Ie(t);if(e!==gn&&e!==En)return t;var n=new Map,r=wn(t),s=H(0),a=et,i=l=>{if(et===a)return l();var f=_,u=et;wt(null),Ne(a);var o=l();return wt(f),Ne(u),o};return r&&n.set("length",H(t.length)),new Proxy(t,{defineProperty(l,f,u){(!("value"in u)||u.configurable===!1||u.enumerable===!1||u.writable===!1)&&Nn();var o=n.get(f);return o===void 0?o=i(()=>{var c=H(u.value);return n.set(f,c),c}):U(o,u.value,!0),!0},deleteProperty(l,f){var u=n.get(f);if(u===void 0){if(f in l){const o=i(()=>H(E));n.set(f,o),Zt(s)}}else U(u,E),Zt(s);return!0},get(l,f,u){var d;if(f===Q)return t;var o=n.get(f),c=f in l;if(o===void 0&&(!c||(d=xt(l,f))!=null&&d.writable)&&(o=i(()=>{var T=Tt(c?l[f]:E),gt=H(T);return gt}),n.set(f,o)),o!==void 0){var p=ut(o);return p===E?void 0:p}return Reflect.get(l,f,u)},getOwnPropertyDescriptor(l,f){var u=Reflect.getOwnPropertyDescriptor(l,f);if(u&&"value"in u){var o=n.get(f);o&&(u.value=ut(o))}else if(u===void 0){var c=n.get(f),p=c==null?void 0:c.v;if(c!==void 0&&p!==E)return{enumerable:!0,configurable:!0,value:p,writable:!0}}return u},has(l,f){var p;if(f===Q)return!0;var u=n.get(f),o=u!==void 0&&u.v!==E||Reflect.has(l,f);if(u!==void 0||v!==null&&(!o||(p=xt(l,f))!=null&&p.writable)){u===void 0&&(u=i(()=>{var d=o?Tt(l[f]):E,T=H(d);return T}),n.set(f,u));var c=ut(u);if(c===E)return!1}return o},set(l,f,u,o){var me;var c=n.get(f),p=f in l;if(r&&f==="length")for(var d=u;d<c.v;d+=1){var T=n.get(d+"");T!==void 0?U(T,E):d in l&&(T=i(()=>H(E)),n.set(d+"",T))}if(c===void 0)(!p||(me=xt(l,f))!=null&&me.writable)&&(c=i(()=>H(void 0)),U(c,Tt(u)),n.set(f,c));else{p=c.v!==E;var gt=i(()=>Tt(u));U(c,gt)}var Mt=Reflect.getOwnPropertyDescriptor(l,f);if(Mt!=null&&Mt.set&&Mt.set.call(o,u),!p){if(r&&typeof f=="string"){var ye=n.get("length"),Wt=Number(f);Number.isInteger(Wt)&&Wt>=ye.v&&U(ye,Wt+1)}Zt(s)}return!0},ownKeys(l){ut(s);var f=Reflect.ownKeys(l).filter(c=>{var p=n.get(c);return p===void 0||p.v!==E});for(var[u,o]of n)o.v!==E&&!(u in l)&&f.push(u);return f},setPrototypeOf(){Dn()}})}function ke(t){try{if(t!==null&&typeof t=="object"&&Q in t)return t[Q]}catch{}return t}function Qr(t,e){return Object.is(ke(t),ke(e))}var Se,Qn,Xe,Ze,Je;function ts(){if(Se===void 0){Se=window,Qn=document,Xe=/Firefox/.test(navigator.userAgent);var t=Element.prototype,e=Node.prototype,n=Text.prototype;Ze=xt(e,"firstChild").get,Je=xt(e,"nextSibling").get,be(t)&&(t.__click=void 0,t.__className=void 0,t.__attributes=null,t.__style=void 0,t.__e=void 0),be(n)&&(n.__t=void 0)}}function at(t=""){return document.createTextNode(t)}function it(t){return Ze.call(t)}function L(t){return Je.call(t)}function es(t,e){if(!b)return it(t);var n=it(m);if(n===null)n=m.appendChild(at());else if(e&&n.nodeType!==oe){var r=at();return n==null||n.before(r),W(r),r}return W(n),n}function ns(t,e){if(!b){var n=it(t);return n instanceof Comment&&n.data===""?L(n):n}return m}function rs(t,e=1,n=!1){let r=b?m:t;for(var s;e--;)s=r,r=L(r);if(!b)return r;if(n&&(r==null?void 0:r.nodeType)!==oe){var a=at();return r===null?s==null||s.after(a):r.before(a),W(a),a}return W(r),r}function tr(t){t.textContent=""}function ss(){return!1}function Qe(t){v===null&&_===null&&Cn(),_!==null&&(_.f&I)!==0&&v===null&&Sn(),mt&&kn()}function er(t,e){var n=e.last;n===null?e.last=e.first=t:(n.next=t,t.prev=n,e.last=t)}function q(t,e,n,r=!0){var s=v;s!==null&&(s.f&K)!==0&&(t|=K);var a={ctx:y,deps:null,nodes_start:null,nodes_end:null,f:t|M,first:null,fn:e,last:null,next:null,parent:s,b:s&&s.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(n)try{ct(a),a.f|=ae}catch(f){throw ft(a),f}else e!==null&&st(a);var i=n&&a.deps===null&&a.first===null&&a.nodes_start===null&&a.teardown===null&&(a.f&ie)===0;if(!i&&r&&(s!==null&&er(a,s),_!==null&&(_.f&C)!==0&&(t&lt)===0)){var l=_;(l.effects??(l.effects=[])).push(a)}return a}function as(t){const e=q(Vt,null,!1);return R(e,g),e.teardown=t,e}function is(t){Qe();var e=v.f,n=!_&&(e&j)!==0&&(e&ae)===0;if(n){var r=y;(r.e??(r.e=[])).push(t)}else return tn(t)}function tn(t){return q(se|fe,t,!1)}function fs(t){return Qe(),q(Vt|fe,t,!0)}function ls(t){pt.ensure();const e=q(lt,t,!0);return(n={})=>new Promise(r=>{n.outro?ir(e,()=>{ft(e),r(void 0)}):(ft(e),r(void 0))})}function us(t){return q(se,t,!1)}function os(t,e){var n=y,r={effect:null,ran:!1,deps:t};n.l.$.push(r),r.effect=en(()=>{t(),!r.ran&&(r.ran=!0,hn(e))})}function cs(){var t=y;en(()=>{for(var e of t.l.$){e.deps();var n=e.effect;(n.f&g)!==0&&R(n,Y),Pt(n)&&ct(n),e.ran=!1}})}function nr(t){return q(le|ie,t,!0)}function en(t,e=0){return q(Vt|e,t,!0)}function _s(t,e=[],n=[]){zn(e,n,r=>{q(Vt,()=>t(...r.map(ut)),!0)})}function rr(t,e=0){var n=q(Dt|e,t,!0);return n}function vs(t,e=!0){return q(j,t,!0,e)}function nn(t){var e=t.teardown;if(e!==null){const n=mt,r=_;Oe(!0),wt(null);try{e.call(null)}finally{Oe(n),wt(r)}}}function rn(t,e=!1){var s;var n=t.first;for(t.first=t.last=null;n!==null;){(s=n.ac)==null||s.abort(ue);var r=n.next;(n.f&lt)!==0?n.parent=null:ft(n,e),n=r}}function sr(t){for(var e=t.first;e!==null;){var n=e.next;(e.f&j)===0&&ft(e),e=n}}function ft(t,e=!0){var n=!1;(e||(t.f&qe)!==0)&&t.nodes_start!==null&&t.nodes_end!==null&&(ar(t.nodes_start,t.nodes_end),n=!0),rn(t,e&&!n),Ut(t,0),R(t,yt);var r=t.transitions;if(r!==null)for(const a of r)a.stop();nn(t);var s=t.parent;s!==null&&s.first!==null&&sn(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=t.ac=null}function ar(t,e){for(;t!==null;){var n=t===e?null:L(t);t.remove(),t=n}}function sn(t){var e=t.parent,n=t.prev,r=t.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),e!==null&&(e.first===t&&(e.first=r),e.last===t&&(e.last=n))}function ir(t,e){var n=[];an(t,n,!0),fr(n,()=>{ft(t),e&&e()})}function fr(t,e){var n=t.length;if(n>0){var r=()=>--n||e();for(var s of t)s.out(r)}else e()}function an(t,e,n){if((t.f&K)===0){if(t.f^=K,t.transitions!==null)for(const i of t.transitions)(i.is_global||n)&&e.push(i);for(var r=t.first;r!==null;){var s=r.next,a=(r.f&Fe)!==0||(r.f&j)!==0;an(r,e,a?n:!1),r=s}}}function hs(t){fn(t,!0)}function fn(t,e){if((t.f&K)!==0){t.f^=K,(t.f&g)===0&&(R(t,M),st(t));for(var n=t.first;n!==null;){var r=n.next,s=(n.f&Fe)!==0||(n.f&j)!==0;fn(n,s?e:!1),n=r}if(t.transitions!==null)for(const a of t.transitions)(a.is_global||e)&&a.in()}}let ot=!1;function Ce(t){ot=t}let mt=!1;function Oe(t){mt=t}let _=null,F=!1;function wt(t){_=t}let v=null;function Ht(t){v=t}let A=null;function ln(t){_!==null&&(A===null?A=[t]:A.push(t))}let x=null,S=0,D=null;function lr(t){D=t}let un=1,kt=0,et=kt;function Ne(t){et=t}let G=!1;function on(){return++un}function Pt(t){var c;var e=t.f;if((e&M)!==0)return!0;if((e&Y)!==0){var n=t.deps,r=(e&I)!==0;if(n!==null){var s,a,i=(e&Lt)!==0,l=r&&v!==null&&!G,f=n.length;if((i||l)&&(v===null||(v.f&yt)===0)){var u=t,o=u.parent;for(s=0;s<f;s++)a=n[s],(i||!((c=a==null?void 0:a.reactions)!=null&&c.includes(u)))&&(a.reactions??(a.reactions=[])).push(u);i&&(u.f^=Lt),l&&o!==null&&(o.f&I)===0&&(u.f^=I)}for(s=0;s<f;s++)if(a=n[s],Pt(a)&&$e(a),a.wv>t.wv)return!0}(!r||v!==null&&!G)&&R(t,g)}return!1}function cn(t,e,n=!0){var r=t.reactions;if(r!==null&&!(A!=null&&A.includes(t)))for(var s=0;s<r.length;s++){var a=r[s];(a.f&C)!==0?cn(a,e,!1):e===a&&(n?R(a,M):(a.f&g)!==0&&R(a,Y),st(a))}}function _n(t){var T;var e=x,n=S,r=D,s=_,a=G,i=A,l=y,f=F,u=et,o=t.f;x=null,S=0,D=null,G=(o&I)!==0&&(F||!ot||_===null),_=(o&(j|lt))===0?t:null,A=null,jt(t.ctx),F=!1,et=++kt,t.ac!==null&&(t.ac.abort(ue),t.ac=null);try{t.f|=Jt;var c=(0,t.fn)(),p=t.deps;if(x!==null){var d;if(Ut(t,S),p!==null&&S>0)for(p.length=S+x.length,d=0;d<x.length;d++)p[S+d]=x[d];else t.deps=p=x;if(!G||(o&C)!==0&&t.reactions!==null)for(d=S;d<p.length;d++)((T=p[d]).reactions??(T.reactions=[])).push(t)}else p!==null&&S<p.length&&(Ut(t,S),p.length=S);if(It()&&D!==null&&!F&&p!==null&&(t.f&(C|Y|M))===0)for(d=0;d<D.length;d++)cn(D[d],t);return s!==null&&s!==t&&(kt++,D!==null&&(r===null?r=D:r.push(...D))),(t.f&J)!==0&&(t.f^=J),c}catch(gt){return Un(gt)}finally{t.f^=Jt,x=e,S=n,D=r,_=s,G=a,A=i,jt(l),F=f,et=u}}function ur(t,e){let n=e.reactions;if(n!==null){var r=yn.call(n,t);if(r!==-1){var s=n.length-1;s===0?n=e.reactions=null:(n[r]=n[s],n.pop())}}n===null&&(e.f&C)!==0&&(x===null||!x.includes(e))&&(R(e,Y),(e.f&(I|Lt))===0&&(e.f^=Lt),Ve(e),Ut(e,0))}function Ut(t,e){var n=t.deps;if(n!==null)for(var r=e;r<n.length;r++)ur(t,n[r])}function ct(t){var e=t.f;if((e&yt)===0){R(t,g);var n=v,r=ot;v=t,ot=!0;try{(e&Dt)!==0?sr(t):rn(t),nn(t);var s=_n(t);t.teardown=typeof s=="function"?s:null,t.wv=un;var a}finally{ot=r,v=n}}}async function ds(){await Promise.resolve(),Zn()}function ps(){return pt.ensure().settled()}function ut(t){var e=t.f,n=(e&C)!==0;if(_!==null&&!F){var r=v!==null&&(v.f&yt)!==0;if(!r&&!(A!=null&&A.includes(t))){var s=_.deps;if((_.f&Jt)!==0)t.rv<kt&&(t.rv=kt,x===null&&s!==null&&s[S]===t?S++:x===null?x=[t]:(!G||!x.includes(t))&&x.push(t));else{(_.deps??(_.deps=[])).push(t);var a=t.reactions;a===null?t.reactions=[_]:a.includes(_)||a.push(_)}}}else if(n&&t.deps===null&&t.effects===null){var i=t,l=i.parent;l!==null&&(l.f&I)===0&&(i.f^=I)}if(mt){if(tt.has(t))return tt.get(t);if(n){i=t;var f=i.v;return((i.f&g)===0&&i.reactions!==null||vn(i))&&(f=pe(i)),tt.set(i,f),f}}else if(n){if(i=t,$!=null&&$.has(i))return $.get(i);Pt(i)&&$e(i)}if((t.f&J)!==0)throw t.v;return t.v}function vn(t){if(t.v===E)return!0;if(t.deps===null)return!1;for(const e of t.deps)if(tt.has(e)||(e.f&C)!==0&&vn(e))return!0;return!1}function hn(t){var e=F;try{return F=!0,t()}finally{F=e}}const or=-7169;function R(t,e){t.f=t.f&or|e}function ws(t){if(!(typeof t!="object"||!t||t instanceof EventTarget)){if(Q in t)re(t);else if(!Array.isArray(t))for(let e in t){const n=t[e];typeof n=="object"&&n&&Q in n&&re(n)}}}function re(t,e=new Set){if(typeof t=="object"&&t!==null&&!(t instanceof EventTarget)&&!e.has(t)){e.add(t),t instanceof Date&&t.getTime();for(let r in t)try{re(t[r],e)}catch{}const n=Ie(t);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=mn(n);for(let s in r){const a=r[s].get;if(a)try{a.call(t)}catch{}}}}}function ys(t){b&&it(t)!==null&&tr(t)}let De=!1;function ms(){De||(De=!0,document.addEventListener("reset",t=>{Promise.resolve().then(()=>{var e;if(!t.defaultPrevented)for(const n of t.target.elements)(e=n.__on_r)==null||e.call(n)})},{capture:!0}))}let N;function gs(){N=void 0}function Es(t){let e=null,n=b;var r;if(b){for(e=m,N===void 0&&(N=it(document.head));N!==null&&(N.nodeType!==ce||N.data!==Le);)N=L(N);N===null?xe(!1):N=W(L(N))}b||(r=document.head.appendChild(at()));try{rr(()=>t(r),qe)}finally{n&&(xe(!0),N=m,W(e))}}function cr(t){var e=document.createElement("template");return e.innerHTML=t.replaceAll("<!>","<!---->"),e.content}function nt(t,e){var n=v;n.nodes_start===null&&(n.nodes_start=t,n.nodes_end=e)}function bs(t,e){var n=(e&Pn)!==0,r=(e&Mn)!==0,s,a=!t.startsWith("<!>");return()=>{if(b)return nt(m,null),m;s===void 0&&(s=cr(a?t:"<!>"+t),n||(s=it(s)));var i=r||Xe?document.importNode(s,!0):s.cloneNode(!0);if(n){var l=it(i),f=i.lastChild;nt(l,f)}else nt(i,i);return i}}function Ts(t){return()=>_r(t())}function _r(t){if(b)return t;const e=t.nodeType===Tn,n=t.tagName==="SCRIPT"?[t]:t.querySelectorAll("script"),r=v;for(const a of n){const i=document.createElement("script");for(var s of a.attributes)i.setAttribute(s.name,s.value);i.textContent=a.textContent,(e?t.firstChild===a:t===a)&&(r.nodes_start=i),(e?t.lastChild===a:t===a)&&(r.nodes_end=i),a.replaceWith(i)}return t}function xs(t=""){if(!b){var e=at(t+"");return nt(e,e),e}var n=m;return n.nodeType!==oe&&(n.before(n=at()),W(n)),nt(n,n),n}function As(){if(b)return nt(m,null),m;var t=document.createDocumentFragment(),e=document.createComment(""),n=at();return t.append(e,n),nt(e,n),t}function Rs(t,e){if(b){v.nodes_end=m,Ln();return}t!==null&&t.before(e)}export{Qn as $,Zn as A,$r as B,Ur as C,Vr as D,Br as E,ps as F,ds as G,rr as H,b as I,Ln as J,cr as K,nt as L,Fe as M,vs as N,ft as O,m as P,it as Q,Yr as R,Fn as S,jr as T,W as U,xe as V,at as W,w as X,E as Y,ss as Z,hs as _,ns as a,wr as a$,ir as a0,en as a1,Wr as a2,Q as a3,mr as a4,Mr as a5,Ie as a6,mn as a7,zr as a8,ms as a9,xt as aA,xr as aB,Dr as aC,yt as aD,de as aE,Ir as aF,Nr as aG,Or as aH,Pr as aI,mt as aJ,yr as aK,fs as aL,Pe as aM,pr as aN,ws as aO,Hr as aP,Lr as aQ,ys as aR,Ts as aS,wt as aT,Ht as aU,Ee as aV,ts as aW,Le as aX,br as aY,ls as aZ,gs as a_,kr as aa,Kn as ab,ce as ac,qn as ad,Ar as ae,Rr as af,ne as ag,Zr as ah,we as ai,hr as aj,Cr as ak,K as al,Sr as am,L as an,an as ao,tr as ap,fr as aq,v as ar,ar as as,ve as at,_e as au,H as av,Tt as aw,U as ax,As as ay,xs as az,Rs as b,jn as b0,It as b1,Xt as b2,os as b3,cs as b4,Jr as b5,Kr as c,es as d,us as e,bs as f,ut as g,Es as h,wn as i,Fr as j,Qr as k,as as l,An as m,dr as n,is as o,Gr as p,y as q,qr as r,rs as s,_s as t,Xr as u,Er as v,_ as w,Tr as x,$t as y,hn as z};
