import{c as de}from"../chunks/CVTn1FV4.js";import{g as ve}from"../chunks/CGZ87yZq.js";import"../chunks/Bzak7iHL.js";import{o as je}from"../chunks/DeAm3Eed.js";import{p as Le,av as M,aw as re,f as _,h as Se,t as p,b as g,c as He,s as m,d as a,g as e,$ as Pe,u as N,r as t,ax as x,a as me,az as Te}from"../chunks/RHWQbow4.js";import{d as ze,s as i}from"../chunks/BlWcudmi.js";import{i as A}from"../chunks/CtoItwj4.js";import{e as We}from"../chunks/Dnfvvefi.js";import{s as U}from"../chunks/BdpLTtcP.js";import{s as Ge}from"../chunks/Cxg-bych.js";import{b as Ne}from"../chunks/B5DcI8qy.js";import"../chunks/B0MzmgHo.js";import{f as Ue}from"../chunks/CL12WlkV.js";const Ze=async({fetch:B,url:f})=>{const{fetcher:w}=ve(),C=await w.commune.joinRequest.list.get({},{fetch:B,ctx:{url:f}}),j=C.length?await w.commune.list.get({ids:C.map(({communeId:s})=>s)},{fetch:B,ctx:{url:f}}):[],L=new Map(j.map(s=>[s.id,s]));return{joinRequests:C.map(s=>({...s,commune:L.get(s.communeId)})),isHasMoreJoinRequests:C.length===de.PAGE_SIZE}},xu=Object.freeze(Object.defineProperty({__proto__:null,load:Ze},Symbol.toStringTag,{value:"Module"}));var Ve=_('<div class="text-center py-5"><p class="text-muted"> </p></div>'),Ke=_('<div class="image-container svelte-lafg1c"><img class="svelte-lafg1c"/></div>'),Qe=_('<div class="bg-light text-center d-flex align-items-center justify-content-center" style="height: 140px;"><span class="text-muted"> </span></div>'),Xe=(B,f,w)=>f(e(w).id),Ye=_('<span class="spinner-border spinner-border-sm me-2" role="status"></span> ',1),$e=_('<div class="mt-auto"><div class="alert alert-info small mb-2"> </div> <button class="btn btn-outline-danger w-100"><!></button></div>'),eu=_('<div class="mt-auto"><a class="btn btn-outline-primary w-100"> </a></div>'),uu=_('<div class="col"><div class="card h-100 shadow-sm"><!> <div class="card-body d-flex flex-column"><div class="d-flex justify-content-between align-items-start mb-2"><span> </span> <small class="text-muted"> </small></div> <h5 class="card-title fs-5 text-truncate mb-2"> </h5> <p class="card-text text-muted small mb-3" style="height: 3rem; overflow: hidden"> </p> <div class="mb-3"><span class="badge bg-primary mb-2"> </span> <div class="small text-muted"><div> </div> <div class="d-flex flex-column"> </div></div></div> <!></div></div></div>'),tu=_('<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4"></div>'),au=_('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden"> </span></div> <p class="text-muted mt-2 mb-0"> </p>',1),ru=_('<div class="text-center py-3"><!></div>'),su=_('<div class="alert alert-danger" role="alert"> </div>'),nu=_('<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4"><h1> </h1> <a class="btn btn-outline-secondary"> </a></div> <!> <!> <!></div>');function Cu(B,f){Le(f,!0);const w={en:{_page:{title:"Join Requests — Commune"},joinRequests:"Join Requests",loading:"Loading...",noJoinRequests:"No join requests found",member:"member",members:"members",headMember:"Head",errorFetchingJoinRequests:"Failed to fetch join requests",errorOccurred:"An error occurred while fetching join requests",loadingMore:"Loading more join requests...",cancel:"Cancel Request",pending:"Pending",accepted:"Accepted",rejected:"Rejected",requestedOn:"Requested on",cancelingRequest:"Canceling...",errorCancelingRequest:"Failed to cancel join request",requestCanceled:"Join request canceled",backToCommunes:"Back to Communes",viewCommune:"View Commune",awaitingApproval:"Awaiting approval from commune head",noImage:"No image",communeImageAlt:"Commune image"},ru:{_page:{title:"Заявки на вступление — Коммуна"},joinRequests:"Заявки на вступление",loading:"Загрузка...",noJoinRequests:"Заявки не найдены",member:"участник",members:"участников",headMember:"Глава",errorFetchingJoinRequests:"Не удалось загрузить заявки",errorOccurred:"Произошла ошибка при загрузке заявок",loadingMore:"Загружаем больше заявок...",cancel:"Отменить заявку",pending:"Ожидает",accepted:"Принято",rejected:"Отклонено",requestedOn:"Подана",cancelingRequest:"Отменяем...",errorCancelingRequest:"Не удалось отменить заявку",requestCanceled:"Заявка отменена",backToCommunes:"Назад к коммунам",viewCommune:"Посмотреть коммуну",awaitingApproval:"Ожидает одобрения главы коммуны",noImage:"Нет изображения",communeImageAlt:"Изображение коммуны"}},{fetcher:C}=ve(),j=N(()=>f.data.locale),L=N(()=>f.data.toLocaleHref),S=N(()=>f.data.getAppropriateLocalization),s=N(()=>w[e(j)]);let D=M(re(f.data.joinRequests)),y=M(null),R=M(!1),se=M(1),H=M(re(f.data.isHasMoreJoinRequests)),J=M(null),P=re({});async function ge(){if(!(e(R)||!e(H))){x(R,!0),x(y,null);try{const u=e(se)+1,r=await C.commune.joinRequest.list.get({pagination:{page:u}}),d=r.length?await C.commune.list.get({ids:r.map(v=>v.communeId)}):[],n=new Map(d.map(v=>[v.id,v])),b=r.map(v=>({...v,commune:n.get(v.communeId)}));x(D,[...e(D),...b],!0),x(se,u),x(H,r.length===de.PAGE_SIZE)}catch(u){x(y,u instanceof Error?u.message:e(s).errorOccurred,!0),console.error(u)}finally{x(R,!1)}}}async function pe(u){P[u]="canceling",x(y,null);try{await C.commune.joinRequest.delete({id:u}),x(D,e(D).filter(r=>r.id!==u),!0)}catch(r){x(y,r instanceof Error?r.message:e(s).errorCancelingRequest,!0),console.error(r)}finally{P[u]=null}}je(()=>{let u;const r=()=>{e(J)&&(u=new IntersectionObserver(d=>{d[0].isIntersecting&&e(H)&&!e(R)&&ge()},{rootMargin:"100px",threshold:.1}),u.observe(e(J)))};return e(J)?r():setTimeout(r,100),()=>{u&&u.disconnect()}});function _e(u){switch(u){case"pending":return"bg-warning text-dark";case"accepted":return"bg-success";case"rejected":return"bg-danger";default:return"bg-secondary"}}function fe(u){switch(u){case"pending":return e(s).pending;case"accepted":return e(s).accepted;case"rejected":return e(s).rejected;default:return u}}var Z=nu();Se(u=>{p(()=>Pe.title=e(s)._page.title)});var V=a(Z),K=a(V),be=a(K,!0);t(K);var Q=m(K,2),he=a(Q,!0);t(Q),t(V);var ne=m(V,2);{var xe=u=>{var r=Ve(),d=a(r),n=a(d,!0);t(d),t(r),p(()=>i(n,e(s).noJoinRequests)),g(u,r)},Ce=u=>{var r=tu();We(r,21,()=>e(D),d=>d.id,(d,n)=>{var b=uu(),v=a(b),q=a(v);{var T=o=>{var c=Ke(),l=a(c);t(c),p(()=>{U(l,"src",`/images/${e(n).commune.image}`),U(l,"alt",`${e(s).communeImageAlt}`)}),g(o,c)},X=o=>{var c=Qe(),l=a(c),E=a(l,!0);t(l),t(c),p(()=>i(E,e(s).noImage)),g(o,c)};A(q,o=>{e(n).commune.image?o(T):o(X,!1)})}var k=m(q,2),I=a(k),z=a(I),De=a(z,!0);t(z);var ie=m(z,2),ye=a(ie);t(ie),t(I);var Y=m(I,2),qe=a(Y,!0);t(Y);var $=m(Y,2),Fe=a($,!0);t($);var ee=m($,2),ue=a(ee),Me=a(ue);t(ue);var ce=m(ue,2),te=a(ce),Be=a(te);t(te);var le=m(te,2),Re=a(le,!0);t(le),t(ce),t(ee);var Je=m(ee,2);{var ke=o=>{var c=$e(),l=a(c),E=a(l,!0);t(l);var h=m(l,2);h.__click=[Xe,pe,n];var ae=a(h);{var W=F=>{var O=Ye(),Oe=m(me(O));p(()=>i(Oe,` ${e(s).cancelingRequest??""}`)),g(F,O)},G=F=>{var O=Te();p(()=>i(O,e(s).cancel)),g(F,O)};A(ae,F=>{P[e(n).id]==="canceling"?F(W):F(G,!1)})}t(h),t(c),p(()=>{i(E,e(s).awaitingApproval),h.disabled=P[e(n).id]==="canceling"}),g(o,c)},Ie=o=>{var c=eu(),l=a(c),E=a(l,!0);t(l),t(c),p(h=>{U(l,"href",h),i(E,e(s).viewCommune)},[()=>{var h;return e(L)(`/communes/${((h=e(n).commune)==null?void 0:h.id)||e(n).communeId}`)}]),g(o,c)};A(Je,o=>{e(n).status==="pending"?o(ke):o(Ie,!1)})}t(k),t(v),t(b),p((o,c,l,E,h,ae)=>{var W,G;Ge(z,1,o,"svelte-lafg1c"),i(De,c),i(ye,`${e(s).requestedOn??""}
                  ${l??""}`),i(qe,E),i(Fe,h),i(Me,`${(((W=e(n).commune)==null?void 0:W.memberCount)||0)??""}
                  ${((((G=e(n).commune)==null?void 0:G.memberCount)||0)===1?e(s).member:e(s).members)??""}`),i(Be,`${e(s).headMember??""}:`),i(Re,ae)},[()=>`badge ${_e(e(n).status)}`,()=>fe(e(n).status),()=>Ue(e(n).createdAt,e(j)),()=>{var o;return e(S)((o=e(n).commune)==null?void 0:o.name)||"Unknown Commune"},()=>e(S)(e(n).commune.description)||"",()=>e(S)(e(n).commune.headMember.name)||"Unknown"]),g(d,b)}),t(r),g(u,r)};A(ne,u=>{e(D).length===0?u(xe):u(Ce,!1)})}var oe=m(ne,2);{var Ee=u=>{var r=ru(),d=a(r);{var n=b=>{var v=au(),q=me(v),T=a(q),X=a(T,!0);t(T),t(q);var k=m(q,2),I=a(k,!0);t(k),p(()=>{i(X,e(s).loadingMore),i(I,e(s).loadingMore)}),g(b,v)};A(d,b=>{e(R)&&b(n)})}t(r),Ne(r,b=>x(J,b),()=>e(J)),g(u,r)};A(oe,u=>{e(H)&&u(Ee)})}var Ae=m(oe,2);{var we=u=>{var r=su(),d=a(r,!0);t(r),p(()=>i(d,e(y))),g(u,r)};A(Ae,u=>{e(y)&&u(we)})}t(Z),p(u=>{i(be,e(s).joinRequests),U(Q,"href",u),i(he,e(s).backToCommunes)},[()=>e(L)("/communes")]),g(B,Z),He()}ze(["click"]);export{Cu as component,xu as universal};
