import{e as Uu,c as pu}from"../chunks/CVTn1FV4.js";import{g as nu}from"../chunks/CGZ87yZq.js";import"../chunks/Bzak7iHL.js";import{p as lu,f as g,a as d4,d as a,s as n,r as e,g as u,av as h,t as _,u as K,b as d,c as du,aQ as qu,ax as r,az as $4,o as Cu,ay as iu,aw as su,h as ju,$ as Pu}from"../chunks/RHWQbow4.js";import{d as _u,s as i,e as Eu}from"../chunks/BlWcudmi.js";import{i as D}from"../chunks/CtoItwj4.js";import{e as Hu}from"../chunks/Dnfvvefi.js";import{s as G4}from"../chunks/BdpLTtcP.js";import{s as Lu}from"../chunks/CaC9IHEK.js";import{p as Ju}from"../chunks/hBp8sf9T.js";import{g as zu}from"../chunks/CKnuo8tw.js";import{U as Nu,M as Wu,e as Gu,a as Ou,f as hu}from"../chunks/iI8NM7bJ.js";import{a as Yu}from"../chunks/CL12WlkV.js";import{s as Ku}from"../chunks/Cxg-bych.js";import"../chunks/BiLRrsV0.js";import"../chunks/B0MzmgHo.js";import{p as Qu}from"../chunks/C_wziyCN.js";const Xu=async({fetch:c,params:t,url:f})=>{const{fetcher:b}=nu(),[o,[m],v]=await Promise.all([b.user.me.get({fetch:c,skipInterceptor:!0}).catch(()=>null),b.commune.list.get({ids:[t.id]},{fetch:c,ctx:{url:f}}),b.commune.member.list.get({communeId:t.id},{fetch:c,ctx:{url:f}})]);if(!m)throw Uu(404,"Commune not found");const l=!!o,s=(o==null?void 0:o.role)==="admin",S=l&&m.headMember.actorType==="user"&&m.headMember.actorId===o.id,y=l&&v.some(L=>L.actorType==="user"&&L.actorId===o.id&&!L.deletedAt);let t4=!1;return l&&!y&&(t4=(await b.commune.joinRequest.list.get({},{fetch:c,ctx:{url:f}})).some(({communeId:O,status:J})=>O===t.id&&J==="pending")),{commune:m,members:v,userPermissions:{isLoggedIn:l,isAdmin:s,isHeadMember:S,isMember:y,canInvite:s||S,canRequestJoin:l&&!y&&!t4,hasPendingJoinRequest:t4}}},Ue=Object.freeze(Object.defineProperty({__proto__:null,load:Xu},Symbol.toStringTag,{value:"Module"}));function Zu(c,t){c.preventDefault(),c.stopPropagation(),r(t,!0)}function Vu(c,t){c.preventDefault(),c.stopPropagation(),r(t,!0)}async function $u(c,t,f,b,o,m,v){r(t,!0),r(f,null);try{await b.commune.member.delete({id:o.id}),r(m,!1),o.onMemberRemoved()}catch(l){r(f,l instanceof Error?l.message:u(v).errorOccurred,!0),console.error(l)}finally{r(t,!1)}}async function u0(c,t,f,b,o,m,v){r(t,!0),r(f,null);try{await b.commune.transferHeadStatus.post({communeId:o.communeId,newHeadUserId:o.actorId}),alert(u(m).transferSuccess),r(v,!1),o.onHeadTransferred?o.onHeadTransferred():window.location.reload()}catch(l){r(f,l instanceof Error?l.message:u(m).errorTransferringHead,!0),console.error("Error transferring head status:",l)}finally{r(t,!1)}}var e0=g('<div class="image-container svelte-8za3j"><img class="svelte-8za3j"/></div>'),a0=g('<div class="bg-light text-center d-flex align-items-center justify-content-center" style="height: 140px;"><span class="text-muted"> </span></div>'),t0=g('<button class="btn btn-outline-warning btn-sm"> </button>'),r0=g('<button class="btn btn-outline-danger btn-sm"> </button>'),n0=g('<div class="mt-2 d-flex flex-column gap-1"><!> <!></div>'),o0=(c,t)=>c.key==="Escape"&&t(),i0=c=>c.stopPropagation(),s0=g('<div class="alert alert-danger"> </div>'),l0=g('<div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" aria-modal="true" role="dialog"><div class="modal-dialog modal-dialog-centered" role="document"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"> </h5> <button type="button" class="btn-close" aria-label="Close"></button></div> <div class="modal-body"><!> <p> <strong> </strong> </p> <p class="text-muted small"> </p></div> <div class="modal-footer"><button type="button" class="btn btn-secondary"> </button> <button type="button" class="btn btn-danger"> </button></div></div></div></div>'),d0=(c,t)=>c.key==="Escape"&&t(),c0=c=>c.stopPropagation(),v0=g('<div class="alert alert-danger" role="alert"> </div>'),m0=g('<span class="spinner-border spinner-border-sm me-2" role="status"></span> ',1),_0=g('<div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" aria-modal="true" role="dialog"><div class="modal-dialog modal-dialog-centered" role="document"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"> </h5> <button type="button" class="btn-close" aria-label="Close"></button></div> <div class="modal-body"><!> <p> <strong> </strong>?</p> <div class="alert alert-warning" role="alert"><i class="bi bi-exclamation-triangle me-2"></i> </div></div> <div class="modal-footer"><button type="button" class="btn btn-secondary"> </button> <button type="button" class="btn btn-warning"><!></button></div></div></div></div>'),g0=g('<div><!> <a class="text-decoration-none text-black"><div class="card-body d-flex flex-column"><h5 class="card-title fs-5 text-truncate"> </h5> <div class="mt-auto d-flex justify-content-between align-items-center"><small class="text-muted"> </small></div> <!></div></a></div> <!> <!>',1);function f0(c,t){lu(t,!0);const f={en:{head:"Head",joined:"Joined",remove:"Remove",removing:"Removing...",transferHead:"Make Head",transferring:"Transferring...",confirmRemoval:"Confirm Removal",confirmRemoveMessage:"Are you sure you want to remove",fromCommune:"from this commune?",cannotUndo:"This action cannot be undone.",confirmTransfer:"Transfer Head Status",confirmTransferMessage:"Are you sure you want to transfer head status to",transferWarning:"This will make them the new head of the commune and remove your head privileges. This action cannot be undone.",transferSuccess:"Head status transferred successfully",cancel:"Cancel",removeMember:"Remove Member",transferHeadStatus:"Transfer Head Status",errorRemovingMember:"Failed to remove member",errorTransferringHead:"Failed to transfer head status",errorOccurred:"An error occurred while removing member",dateFormatLocale:"en-US",noImage:"No image",userImageAlt:"User image"},ru:{head:"Глава",joined:"Присоединился",remove:"Удалить",removing:"Удаление...",transferHead:"Сделать главой",transferring:"Передача...",confirmRemoval:"Подтвердите удаление",confirmRemoveMessage:"Вы уверены, что хотите удалить",fromCommune:"из этой коммуны?",cannotUndo:"Это действие нельзя отменить.",confirmTransfer:"Передача статуса главы",confirmTransferMessage:"Вы уверены, что хотите передать статус главы пользователю",transferWarning:"Это сделает их новым главой коммуны и лишит вас привилегий главы. Это действие нельзя отменить.",transferSuccess:"Статус главы успешно передан",cancel:"Отмена",removeMember:"Удалить участника",transferHeadStatus:"Передать статус главы",errorRemovingMember:"Не удалось удалить участника",errorTransferringHead:"Не удалось передать статус главы",errorOccurred:"Произошла ошибка при удалении участника",dateFormatLocale:"ru-RU",noImage:"Нет изображения",userImageAlt:"Изображение пользователя"}},{fetcher:b}=nu(),o=K(()=>f[t.locale]);let m=h(!1),v=h(!1),l=h(!1),s=h(!1),S=h(null);const y=t.getAppropriateLocalization(t.name),t4=t.actorType==="user"?`/users/${t.actorId}`:`/communes/${t.actorId}`,L=K(()=>t.createdAt.toLocaleDateString(u(o).dateFormatLocale,{year:"numeric",month:"long",day:"numeric"})),O=(t.isCurrentUserHead||t.isCurrentUserAdmin)&&!t.isHead,J=(t.isCurrentUserHead||t.isCurrentUserAdmin)&&!t.isHead&&t.actorType==="user";function C4(){r(m,!1)}function r4(){r(v,!1)}var E4=g0(),g4=d4(E4),n4=a(g4);{var f4=B=>{var E=e0(),F=a(E);e(E),_(()=>{G4(F,"src",`/images/${t.image}`),G4(F,"alt",`${u(o).userImageAlt}`)}),d(B,E)},h4=B=>{var E=a0(),F=a(E),Q=a(F,!0);e(F),e(E),_(()=>i(Q,u(o).noImage)),d(B,E)};D(n4,B=>{t.image?B(f4):B(h4,!1)})}var c4=n(n4,2),o4=a(c4),A4=a(o4),M=a(A4,!0);e(A4);var U=n(A4,2),b4=a(U),y4=a(b4);e(b4),e(U);var Y=n(U,2);{var $=B=>{var E=n0(),F=a(E);{var Q=m4=>{var X=t0();X.__click=[Vu,v];var p4=a(X,!0);e(X),_(()=>i(p4,u(o).transferHead)),d(m4,X)};D(F,m4=>{J&&m4(Q)})}var e4=n(F,2);{var k4=m4=>{var X=r0();X.__click=[Zu,m];var p4=a(X,!0);e(X),_(()=>i(p4,u(o).remove)),d(m4,X)};D(e4,m4=>{O&&m4(k4)})}e(E),d(B,E)};D(Y,B=>{(J||O)&&B($)})}e(o4),e(c4),e(g4);var u4=n(g4,2);{var O4=B=>{var E=l0();E.__click=C4,E.__keydown=[o0,C4];var F=a(E);F.__click=[i0];var Q=a(F),e4=a(Q),k4=a(e4),m4=a(k4,!0);e(k4);var X=n(k4,2);X.__click=C4,e(e4);var p4=n(e4,2),Y4=a(p4);{var uu=V=>{var k=s0(),w=a(k,!0);e(k),_(()=>i(w,u(S))),d(V,k)};D(Y4,V=>{u(S)&&V(uu)})}var S4=n(Y4,2),K4=a(S4),P4=n(K4),eu=a(P4,!0);e(P4);var X4=n(P4);e(S4);var Z4=n(S4,2),A=a(Z4,!0);e(Z4),e(p4);var I=n(p4,2),q=a(I);q.__click=C4;var Z=a(q,!0);e(q);var T=n(q,2);T.__click=[$u,l,S,b,t,m,o];var j=a(T,!0);e(T),e(I),e(Q),e(F),e(E),_(()=>{i(m4,u(o).confirmRemoval),i(K4,`${u(o).confirmRemoveMessage??""} `),i(eu,y),i(X4,` ${u(o).fromCommune??""}`),i(A,u(o).cannotUndo),q.disabled=u(l),i(Z,u(o).cancel),T.disabled=u(l),i(j,u(l)?u(o).removing:u(o).removeMember)}),d(B,E)};D(u4,B=>{u(m)&&B(O4)})}var v4=n(u4,2);{var D4=B=>{var E=_0();E.__click=r4,E.__keydown=[d0,r4];var F=a(E);F.__click=[c0];var Q=a(F),e4=a(Q),k4=a(e4),m4=a(k4,!0);e(k4);var X=n(k4,2);X.__click=r4,e(e4);var p4=n(e4,2),Y4=a(p4);{var uu=k=>{var w=v0(),a4=a(w,!0);e(w),_(()=>i(a4,u(S))),d(k,w)};D(Y4,k=>{u(S)&&k(uu)})}var S4=n(Y4,2),K4=a(S4),P4=n(K4),eu=a(P4,!0);e(P4),qu(),e(S4);var X4=n(S4,2),Z4=n(a(X4));e(X4),e(p4);var A=n(p4,2),I=a(A);I.__click=r4;var q=a(I,!0);e(I);var Z=n(I,2);Z.__click=[u0,s,S,b,t,o,v];var T=a(Z);{var j=k=>{var w=m0(),a4=n(d4(w));_(()=>i(a4,` ${u(o).transferring??""}`)),d(k,w)},V=k=>{var w=$4();_(()=>i(w,u(o).transferHeadStatus)),d(k,w)};D(T,k=>{u(s)?k(j):k(V,!1)})}e(Z),e(A),e(Q),e(F),e(E),_(()=>{i(m4,u(o).confirmTransfer),i(K4,`${u(o).confirmTransferMessage??""} `),i(eu,y),i(Z4,` ${u(o).transferWarning??""}`),i(q,u(o).cancel),Z.disabled=u(s)}),d(B,E)};D(v4,B=>{u(v)&&B(D4)})}_(B=>{Ku(g4,1,`card h-100 shadow-sm ${t.isHead?"head-member":""}`,"svelte-8za3j"),G4(c4,"href",B),i(M,y),i(y4,`${u(o).joined??""}
          ${u(L)??""}`)},[()=>t.toLocaleHref(t4)]),d(c,E4),du()}_u(["click","keydown"]);function b0(c,t,f,b,o){c.key==="Enter"&&!u(t)&&u(f)?(c.preventDefault(),b()):c.key==="Escape"&&(c.preventDefault(),o())}var D0=g('<div class="alert alert-success" role="alert"><i class="bi bi-check-circle me-2"></i> </div>'),p0=g('<div class="alert alert-danger" role="alert"><i class="bi bi-exclamation-triangle me-2"></i> </div>'),C0=g("<form><!> <!></form>"),E0=g('<span class="spinner-border spinner-border-sm me-2" role="status"></span> ',1),h0=g('<div class="modal-footer"><button type="button" class="btn btn-secondary"> </button> <button type="button" class="btn btn-primary"><!></button></div>'),y0=g('<div class="modal-backdrop fade show svelte-7l2jmw"></div> <div class="modal fade show d-block svelte-7l2jmw" tabindex="-1" role="dialog"><div class="modal-dialog modal-dialog-centered" role="document"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"> </h5> <button type="button" class="btn-close" aria-label="Close"></button></div> <div class="modal-body"><!></div> <!></div></div></div>',1);function x0(c,t){lu(t,!0);const f={en:{title:"Invite User to Commune",selectUser:"Select User",invite:"Send Invitation",cancel:"Cancel",inviting:"Sending invitation...",success:"Invitation sent successfully!",errorInviting:"Failed to send invitation",userRequired:"Please select a user to invite"},ru:{title:"Пригласить пользователя в коммуну",selectUser:"Выбрать пользователя",invite:"Отправить приглашение",cancel:"Отмена",inviting:"Отправляем приглашение...",success:"Приглашение отправлено успешно!",errorInviting:"Не удалось отправить приглашение",userRequired:"Пожалуйста, выберите пользователя для приглашения"}},{fetcher:b}=nu(),o=K(()=>f[t.locale]);let m=h(null),v=h(!1),l=h(null),s=h(null);Cu(()=>{t.show&&(r(m,null),r(l,null),r(s,null),r(v,!1))});async function S(){if(!u(m)){r(l,u(o).userRequired,!0);return}r(v,!0),r(l,null),r(s,null);try{await b.commune.invitation.post({communeId:t.communeId,userId:u(m)}),r(s,u(o).success,!0),t.onInviteSent(),setTimeout(()=>{t.onHide()},1500)}catch(J){r(l,J instanceof Error?J.message:u(o).errorInviting,!0),console.error("Error sending invitation:",J)}finally{r(v,!1)}}function y(){t.onHide()}var t4=iu(),L=d4(t4);{var O=J=>{var C4=y0(),r4=n(d4(C4),2);r4.__keydown=[b0,v,m,S,y];var E4=a(r4),g4=a(E4),n4=a(g4),f4=a(n4),h4=a(f4,!0);e(f4);var c4=n(f4,2);c4.__click=y,e(n4);var o4=n(n4,2),A4=a(o4);{var M=Y=>{var $=D0(),u4=n(a($));e($),_(()=>i(u4,` ${u(s)??""}`)),d(Y,$)},U=Y=>{var $=C0(),u4=a($);Nu(u4,{get locale(){return t.locale},get label(){return u(o).selectUser},placeholder:"Search for users to invite...",get selectedUserId(){return u(m)},set selectedUserId(D4){r(m,D4,!0)}});var O4=n(u4,2);{var v4=D4=>{var B=p0(),E=n(a(B));e(B),_(()=>i(E,` ${u(l)??""}`)),d(D4,B)};D(O4,D4=>{u(l)&&D4(v4)})}e($),Eu("submit",$,D4=>{D4.preventDefault(),S()}),d(Y,$)};D(A4,Y=>{u(s)?Y(M):Y(U,!1)})}e(o4);var b4=n(o4,2);{var y4=Y=>{var $=h0(),u4=a($);u4.__click=y;var O4=a(u4,!0);e(u4);var v4=n(u4,2);v4.__click=S;var D4=a(v4);{var B=F=>{var Q=E0(),e4=n(d4(Q));_(()=>i(e4,` ${u(o).inviting??""}`)),d(F,Q)},E=F=>{var Q=$4();_(()=>i(Q,u(o).invite)),d(F,Q)};D(D4,F=>{u(v)?F(B):F(E,!1)})}e(v4),e($),_(()=>{u4.disabled=u(v),i(O4,u(o).cancel),v4.disabled=u(v)||!u(m)}),d(Y,$)};D(b4,Y=>{u(s)||Y(y4)})}e(g4),e(E4),e(r4),_(()=>{i(h4,u(o).title),c4.disabled=u(v)}),d(J,C4)};D(L,J=>{t.show&&J(O)})}d(c,t4),du()}_u(["keydown","click"]);var B0=g('<div class="alert alert-success mb-3"> </div>'),F0=g('<div class="alert alert-danger mb-3"> </div>'),I0=g("<!> <!> <form><!> <!></form>",1);function w0(c,t){lu(t,!0);const f={en:{editCommune:"Edit Commune",communeUpdatedSuccess:"Commune updated successfully!",name:"Name",enterCommuneName:"Enter commune name",description:"Description (optional)",enterCommuneDescription:"Enter commune description",cancel:"Cancel",save:"Save Changes",saving:"Saving...",provideName:"Please provide a name for the commune.",failedToUpdate:"Failed to update commune",unexpectedError:"An unexpected error occurred. Please try again."},ru:{editCommune:"Редактировать коммуну",communeUpdatedSuccess:"Коммуна успешно обновлена!",name:"Название",enterCommuneName:"Введите название коммуны",description:"Описание (опционально)",enterCommuneDescription:"Введите описание коммуны",cancel:"Отмена",save:"Сохранить изменения",saving:"Сохранение...",provideName:"Пожалуйста, укажите название коммуны.",failedToUpdate:"Не удалось обновить коммуну",unexpectedError:"Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова."}},{fetcher:b}=nu(),o=K(()=>f[t.locale]);let m=h(su([])),v=h(su([])),l=h(""),s=h(!1),S=h(!1);Cu(()=>{t.communeData&&t.show&&(r(m,t.communeData.name||[],!0),r(v,t.communeData.description||[],!0))});async function y(){var L;if(!u(m).some(O=>O.value.trim().length)){r(l,u(o).provideName,!0);return}r(s,!0),r(l,"");try{await b.commune.patch({id:(L=t.communeData)==null?void 0:L.id,name:u(m),description:u(v)}),r(S,!0),t.onCommuneUpdated(),setTimeout(()=>{t4()},1500)}catch(O){r(l,O instanceof Error?O.message:u(o).unexpectedError,!0),console.error(O)}finally{r(s,!1)}}function t4(){r(m,[],!0),r(v,[],!0),r(l,""),r(S,!1),t.onHide()}{let L=K(()=>u(s)?u(o).saving:u(o).save),O=K(()=>!u(m).some(J=>J.value.trim().length)||u(s));Wu(c,{get show(){return t.show},get title(){return u(o).editCommune},onClose:t4,onSubmit:y,get submitText(){return u(L)},get cancelText(){return u(o).cancel},get submitDisabled(){return u(O)},get cancelDisabled(){return u(s)},get isSubmitting(){return u(s)},children:(J,C4)=>{var r4=I0(),E4=d4(r4);{var g4=M=>{var U=B0(),b4=a(U,!0);e(U),_(()=>i(b4,u(o).communeUpdatedSuccess)),d(M,U)};D(E4,M=>{u(S)&&M(g4)})}var n4=n(E4,2);{var f4=M=>{var U=F0(),b4=a(U,!0);e(U),_(()=>i(b4,u(l))),d(M,U)};D(n4,M=>{u(l)&&M(f4)})}var h4=n(n4,2),c4=K(()=>Qu(y)),o4=a(h4);Gu(o4,{id:"communeName",get label(){return u(o).name},get placeholder(){return u(o).enterCommuneName},required:!0,get locale(){return t.locale},get value(){return u(m)},set value(M){r(m,M,!0)}});var A4=n(o4,2);Ou(A4,{id:"communeDescription",get label(){return u(o).description},get placeholder(){return u(o).enterCommuneDescription},rows:4,get locale(){return t.locale},get value(){return u(v)},set value(M){r(v,M,!0)}}),e(h4),Eu("submit",h4,function(...M){var U;(U=u(c4))==null||U.apply(this,M)}),d(J,r4)},$$slots:{default:!0}})}du()}function A0(c,t){r(t,!0)}function k0(c,t){r(t,!0)}function M0(c,t){r(t,!0)}async function T0(c,t,f,b,o,m,v,l){if(u(t)){r(f,!0),r(b,null);try{await o.commune.joinRequest.post({communeId:m,userId:u(t).id}),alert(u(v).joinRequestSent),l()}catch(s){r(b,s instanceof Error?s.message:u(v).errorSendingJoinRequest,!0),console.error("Error sending join request:",s)}finally{r(f,!1)}}}async function R0(c,t,f,b,o,m,v){r(t,!0),r(f,null);try{await b.commune.delete({id:o}),alert(u(m).communeDeleted),zu(u(v)("/communes"))}catch(l){r(f,l instanceof Error?l.message:u(m).errorDeletingCommune,!0),console.error("Error deleting commune:",l)}finally{r(t,!1)}}function S0(c,t,f){r(t,!0),f()}function U0(c,t,f,b,o){const v=c.target.files;if(r(t,null),!v||v.length===0){r(f,null),r(b,null);return}const l=v[0];if(!pu.ALLOWED_IMAGE_FILE_TYPES.includes(l.type)){r(t,u(o).invalidFileType,!0),r(f,null),r(b,null);return}if(l.size>pu.MAX_IMAGE_FILE_SIZE){r(t,u(o).fileTooLarge,!0),r(f,null),r(b,null);return}r(f,l,!0),u(b)&&URL.revokeObjectURL(u(b)),r(b,URL.createObjectURL(l),!0)}async function q0(c,t,f,b,o,m,v){if(!u(t)){r(f,u(b).pleaseSelectImage,!0);return}r(o,!0),r(f,null),r(m,null);try{const l=new FormData;l.append("image",u(t));const s=await hu(`/api/commune/${v}/image`,{method:"PUT",body:l});if(!s.ok)throw new Error(`${u(b).errorUploadingImage}: ${s.statusText}`);r(m,u(b).imageUploadedSuccess,!0),setTimeout(()=>{window.location.reload()},1500)}catch(l){r(f,l instanceof Error?l.message:u(b).errorUploadingImage,!0),console.error(l)}finally{r(o,!1)}}function j0(c,t,f){r(t,!0),f()}async function P0(c,t,f,b,o,m){r(t,!0),r(f,null),r(b,null);try{const v=await hu(`/api/commune/${o}/image`,{method:"DELETE"});if(!v.ok)throw new Error(`${u(m).errorDeletingImage}: ${v.statusText}`);r(b,u(m).imageDeletedSuccess,!0),setTimeout(()=>{window.location.reload()},1500)}catch(v){r(f,v instanceof Error?v.message:u(m).errorDeletingImage,!0),console.error(v)}finally{r(t,!1)}}var H0=g('<div class="alert alert-danger"> </div>'),L0=g('<img class="commune-image svelte-1qaz19a"/>'),J0=g('<div class="commune-image-placeholder svelte-1qaz19a"><span class="text-muted"> </span></div>'),z0=g('<button class="btn btn-outline-danger btn-sm"><i class="bi bi-trash me-1"></i> </button>'),N0=g('<div class="mt-3 d-flex gap-2"><button class="btn btn-outline-primary btn-sm"><i class="bi bi-upload me-1"></i> </button> <!></div>'),W0=g('<div class="d-flex gap-1"><button class="btn btn-outline-primary btn-sm"> </button> <button class="btn btn-outline-danger btn-sm"> </button></div>'),G0=g('<span class="spinner-border spinner-border-sm me-2" role="status"></span> ',1),O0=g('<div class="alert alert-info d-flex justify-content-between align-items-center mt-4"><div><strong> </strong> <p class="mb-0 small text-muted"> </p></div> <button class="btn btn-success"><!></button></div>'),Y0=g('<div class="alert alert-warning mt-4"><strong> </strong> <p class="mb-0 small"> </p></div>'),K0=g('<div class="d-flex gap-2 flex-wrap"><a class="btn btn-outline-info"> </a> <a class="btn btn-outline-warning"> </a> <button class="btn btn-outline-primary"> </button></div>'),Q0=g('<div class="alert alert-info"> </div>'),X0=g('<div class="col"><!></div>'),Z0=g('<div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4"></div>'),V0=g('<div class="row"><div class="col-lg-8"><div class="commune-image-container svelte-1qaz19a"><!></div> <!> <div class="mb-4"><div class="d-flex justify-content-between align-items-center mb-3"><h2 class="mb-0"> </h2></div> <p class="lead text-muted"> </p></div></div> <div class="col-lg-4"><div class="card shadow-sm mb-4"><div class="card-body"><div class="d-flex justify-content-between align-items-center mb-2"><h5 class="card-title mb-0"> </h5> <!></div> <hr/> <div class="d-flex justify-content-between mb-2"><span> </span> <span class="badge bg-primary"> </span></div> <div class="d-flex justify-content-between mb-2"><span> </span> <span class="text-muted"> </span></div> <div class="d-flex justify-content-between"><span> </span> <span class="text-muted"> </span></div></div></div></div></div> <!> <div class="d-flex justify-content-between align-items-center mt-5 mb-4"><h3 class="mb-0"> </h3> <!></div> <!> <!> <!>',1),$0=(c,t)=>c.key==="Escape"&&t(),ue=c=>c.stopPropagation(),ee=g('<div class="alert alert-success mb-3"> </div>'),ae=g('<div class="alert alert-danger mb-3"> </div>'),te=g('<div class="mt-3 text-center"><img alt="Preview" class="img-thumbnail"/></div>'),re=g('<span class="spinner-border spinner-border-sm me-2" role="status"></span> ',1),ne=g('<div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" aria-modal="true" role="dialog"><div class="modal-dialog modal-dialog-centered modal-lg" role="document"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"> </h5> <button type="button" class="btn-close" aria-label="Close"></button></div> <div class="modal-body"><!> <!> <form><div class="mb-3"><label for="imageInput" class="form-label"> </label> <input id="imageInput" type="file" class="form-control" accept=".jpg,.jpeg,.png,.webp"/> <p class="form-text text-muted"> </p> <!></div></form></div> <div class="modal-footer"><button type="button" class="btn btn-secondary"> </button> <button type="button" class="btn btn-primary"><!></button></div></div></div></div>'),oe=(c,t)=>c.key==="Escape"&&t(),ie=c=>c.stopPropagation(),se=g('<div class="alert alert-success mb-3"> </div>'),le=g('<div class="alert alert-danger mb-3"> </div>'),de=g('<span class="spinner-border spinner-border-sm me-2" role="status"></span> ',1),ce=g('<div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" aria-modal="true" role="dialog"><div class="modal-dialog modal-dialog-centered" role="document"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"> </h5> <button type="button" class="btn-close" aria-label="Close"></button></div> <div class="modal-body"><!> <!> <p> </p></div> <div class="modal-footer"><button type="button" class="btn btn-secondary"> </button> <button type="button" class="btn btn-danger"><!></button></div></div></div></div>'),ve=(c,t)=>c.key==="Escape"&&t(),me=c=>c.stopPropagation(),_e=g('<div class="alert alert-danger" role="alert"> </div>'),ge=g('<span class="spinner-border spinner-border-sm me-2" role="status"></span> ',1),fe=g('<div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" aria-modal="true" role="dialog"><div class="modal-dialog modal-dialog-centered" role="document"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"> </h5> <button type="button" class="btn-close" aria-label="Close"></button></div> <div class="modal-body"><!> <p> </p> <div class="alert alert-danger" role="alert"><i class="bi bi-exclamation-triangle me-2"></i> </div></div> <div class="modal-footer"><button type="button" class="btn btn-secondary"> </button> <button type="button" class="btn btn-danger"><!></button></div></div></div></div>'),be=g('<div class="container py-4"><!></div> <!> <!> <!>',1);function qe(c,t){lu(t,!0);const f={en:{_page:{title:"— Commune"},loading:"Loading commune details...",communeNotFound:"Commune not found",communeDetails:"Commune Details",edit:"Edit",delete:"Delete Commune",members:"Members",member:"member",members_plural:"members",headMember:"Head Member",created:"Created",addMember:"Add Member",invite:"Invite User",manageInvitations:"Manage Invitations",manageJoinRequests:"Manage Join Requests",requestToJoin:"Request to Join",requestPending:"Join Request Pending",noMembers:"No members found",errorFetchingCommune:"Failed to fetch commune",errorFetchingMembers:"Failed to fetch members",errorOccurred:"An error occurred while fetching data",errorSendingJoinRequest:"Failed to send join request",joinRequestSent:"Join request sent successfully!",sendingJoinRequest:"Sending request...",interestedInJoining:"Interested in joining this commune?",joinRequestDescription:"Send a join request to the commune head for approval.",joinRequestPendingDescription:"Your join request is awaiting approval from the commune head.",confirmDelete:"Delete Commune",confirmDeleteMessage:"Are you sure you want to delete this commune?",deleteWarning:"This will permanently delete the commune and all its data, including members, invitations, and join requests. This action cannot be undone.",deleting:"Deleting...",communeDeleted:"Commune deleted successfully",errorDeletingCommune:"Failed to delete commune",cancel:"Cancel",dateFormatLocale:"en-US",noImages:"No images available",communeImageAlt:"Commune image",uploadImage:"Upload Image",deleteImage:"Delete Image",uploadImageTitle:"Upload Commune Image",upload:"Upload",uploading:"Uploading...",imageUploadedSuccess:"Image uploaded successfully!",errorUploadingImage:"Failed to upload image",pleaseSelectImage:"Please select an image to upload",invalidFileType:"Invalid file type. Please upload a JPG, PNG, or WebP image.",fileTooLarge:"File is too large. Maximum size is 5MB.",uploadImageMaxSize:"Upload an image (JPG, PNG, WebP), max 5MB.",confirmDeleteImage:"Are you sure you want to delete this image?",deleteImageTitle:"Delete Image",deleteImageButton:"Delete",deletingImage:"Deleting...",imageDeletedSuccess:"Image deleted successfully!",errorDeletingImage:"Failed to delete image"},ru:{_page:{title:"— Коммуна"},loading:"Загрузка данных коммуны...",communeNotFound:"Коммуна не найдена",communeDetails:"Информация о коммуне",edit:"Редактировать",delete:"Удалить коммуну",members:"Участники",member:"участник",members_plural:"участников",headMember:"Глава",created:"Создана",addMember:"Добавить участника",invite:"Пригласить пользователя",manageInvitations:"Управление приглашениями",manageJoinRequests:"Управление заявками",requestToJoin:"Подать заявку",requestPending:"Заявка на рассмотрении",noMembers:"Участники не найдены",errorFetchingCommune:"Не удалось загрузить коммуну",errorFetchingMembers:"Не удалось загрузить участников",errorOccurred:"Произошла ошибка при загрузке данных",errorSendingJoinRequest:"Не удалось отправить заявку",joinRequestSent:"Заявка отправлена успешно!",sendingJoinRequest:"Отправляем заявку...",interestedInJoining:"Хотите присоединиться к этой коммуне?",joinRequestDescription:"Отправьте заявку главе коммуны для одобрения.",joinRequestPendingDescription:"Ваша заявка ожидает одобрения главы коммуны.",confirmDelete:"Удалить коммуну",confirmDeleteMessage:"Вы уверены, что хотите удалить эту коммуну?",deleteWarning:"Это навсегда удалит коммуну и все её данные, включая участников, приглашения и заявки. Это действие нельзя отменить.",deleting:"Удаление...",communeDeleted:"Коммуна успешно удалена",errorDeletingCommune:"Не удалось удалить коммуну",cancel:"Отмена",dateFormatLocale:"ru-RU",noImages:"Нет доступных изображений",communeImageAlt:"Изображение коммуны",uploadImage:"Загрузить изображение",deleteImage:"Удалить изображение",uploadImageTitle:"Загрузить изображение коммуны",upload:"Загрузить",uploading:"Загрузка...",imageUploadedSuccess:"Изображение загружено успешно!",errorUploadingImage:"Не удалось загрузить изображение",pleaseSelectImage:"Пожалуйста, выберите изображение для загрузки",invalidFileType:"Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",fileTooLarge:"Файл слишком большой. Максимальный размер - 5MB.",uploadImageMaxSize:"Загрузите изображение (JPG, PNG, WebP), максимальный размер - 5MB.",confirmDeleteImage:"Вы уверены, что хотите удалить это изображение?",deleteImageTitle:"Удалить изображение",deleteImageButton:"Удалить",deletingImage:"Удаление...",imageDeletedSuccess:"Изображение удалено успешно!",errorDeletingImage:"Не удалось удалить изображение"}},{fetcher:b}=nu(),o=K(()=>t.data.locale),m=K(()=>t.data.toLocaleHref),v=K(()=>t.data.getAppropriateLocalization),l=Ju.params.id,s=K(()=>f[u(o)]),S=K(()=>t.data.user);let y=su(t.data.commune),t4=su(t.data.members),L=h(null),O=h(!1),J=h(!1),C4=h(!1),r4=h(!1),E4=h(!1),g4=h(!1),n4=h(!1),f4=h(null),h4=h(null),c4=h(null),o4=h(null),A4=h(!1),M=h(!1),U=h(null),b4=h(null);const y4=K(()=>t.data.userPermissions);function Y(){window.location.reload()}function $(){r(O,!1),Y()}const u4=K(()=>u(v)(y.name)),O4=K(()=>u(v)(y.description)),v4=K(()=>u(S)?y.headMember.actorType==="user"&&y.headMember.actorId===u(S).id:!1);function D4(){r(J,!1)}function B(){console.log("Invitation sent successfully")}function E(){r(C4,!1)}function F(){r(g4,!1),r(c4,null),r(o4,null)}function Q(){r(f4,null),r(h4,null),r(n4,!1),r(c4,null),r(o4,null)}function e4(){r(A4,!1)}function k4(){r(U,null),r(b4,null),r(M,!1)}var m4=be();ju(A=>{_(()=>Pu.title=`${u(u4)??""} ${u(s)._page.title??""}`)});var X=d4(m4),p4=a(X);{var Y4=A=>{var I=H0(),q=a(I,!0);e(I),_(()=>i(q,u(L))),d(A,I)},uu=A=>{var I=V0(),q=d4(I),Z=a(q),T=a(Z),j=a(T);{var V=C=>{var p=L0();_(()=>{G4(p,"src",`/images/${y.image}`),G4(p,"alt",`${u(s).communeImageAlt}`)}),d(C,p)},k=C=>{var p=J0(),x=a(p),R=a(x,!0);e(x),e(p),_(()=>i(R,u(s).noImages)),d(C,p)};D(j,C=>{y.image?C(V):C(k,!1)})}e(T);var w=n(T,2);{var a4=C=>{var p=N0(),x=a(p);x.__click=[S0,g4,Q];var R=n(a(x));e(x);var W=n(x,2);{var _4=s4=>{var l4=z0();l4.__click=[j0,A4,k4];var W4=n(a(l4));e(l4),_(()=>i(W4,` ${u(s).deleteImage??""}`)),d(s4,l4)};D(W,s4=>{y.image&&s4(_4)})}e(p),_(()=>i(R,` ${u(s).uploadImage??""}`)),d(C,p)};D(w,C=>{(u(v4)||u(y4).isAdmin)&&C(a4)})}var J4=n(w,2),M4=a(J4),x4=a(M4),U4=a(x4,!0);e(x4),e(M4);var z4=n(M4,2),q4=a(z4,!0);e(z4),e(J4),e(Z);var T4=n(Z,2),j4=a(T4),B4=a(j4),F4=a(B4),I4=a(F4),H4=a(I4,!0);e(I4);var P=n(I4,2);{var i4=C=>{var p=W0(),x=a(p);x.__click=[A0,O];var R=a(x,!0);e(x);var W=n(x,2);W.__click=[k0,C4];var _4=a(W,!0);e(W),e(p),_(()=>{i(R,u(s).edit),i(_4,u(s).delete)}),d(C,p)};D(P,C=>{(u(v4)||u(y4).isAdmin)&&C(i4)})}e(F4);var R4=n(F4,4),Q4=a(R4),G=a(Q4);e(Q4);var H=n(Q4,2),L4=a(H,!0);e(H),e(R4);var N4=n(R4,2),au=a(N4),cu=a(au);e(au);var ou=n(au,2),z=a(ou,!0);e(ou),e(N4);var N=n(N4,2),w4=a(N),yu=a(w4);e(w4);var gu=n(w4,2),xu=a(gu,!0);e(gu),e(N),e(B4),e(j4),e(T4),e(q);var fu=n(q,2);{var Bu=C=>{var p=O0(),x=a(p),R=a(x),W=a(R,!0);e(R);var _4=n(R,2),s4=a(_4,!0);e(_4),e(x);var l4=n(x,2);l4.__click=[T0,S,r4,L,b,l,s,Y];var W4=a(l4);{var tu=V4=>{var ru=G0(),Su=n(d4(ru));_(()=>i(Su,` ${u(s).sendingJoinRequest??""}`)),d(V4,ru)},Ru=V4=>{var ru=$4();_(()=>i(ru,u(s).requestToJoin)),d(V4,ru)};D(W4,V4=>{u(r4)?V4(tu):V4(Ru,!1)})}e(l4),e(p),_(()=>{i(W,u(s).interestedInJoining),i(s4,u(s).joinRequestDescription),l4.disabled=u(r4)}),d(C,p)},Fu=C=>{var p=iu(),x=d4(p);{var R=W=>{var _4=Y0(),s4=a(_4),l4=a(s4,!0);e(s4);var W4=n(s4,2),tu=a(W4,!0);e(W4),e(_4),_(()=>{i(l4,u(s).requestPending),i(tu,u(s).joinRequestPendingDescription)}),d(W,_4)};D(x,W=>{u(y4).hasPendingJoinRequest&&W(R)},!0)}d(C,p)};D(fu,C=>{u(y4).canRequestJoin?C(Bu):C(Fu,!1)})}var vu=n(fu,2),mu=a(vu),Iu=a(mu);e(mu);var wu=n(mu,2);{var Au=C=>{var p=K0(),x=a(p),R=a(x,!0);e(x);var W=n(x,2),_4=a(W,!0);e(W);var s4=n(W,2);s4.__click=[M0,J];var l4=a(s4,!0);e(s4),e(p),_((W4,tu)=>{G4(x,"href",W4),i(R,u(s).manageInvitations),G4(W,"href",tu),i(_4,u(s).manageJoinRequests),i(l4,u(s).invite)},[()=>u(m)(`/communes/${l}/invitations`),()=>u(m)(`/communes/${l}/join-requests`)]),d(C,p)};D(wu,C=>{u(y4).canInvite&&C(Au)})}e(vu);var bu=n(vu,2);{var ku=C=>{var p=Q0(),x=a(p,!0);e(p),_(()=>i(x,u(s).noMembers)),d(C,p)},Mu=C=>{var p=Z0();Hu(p,21,()=>t4,x=>x.id,(x,R)=>{var W=X0(),_4=a(W);{let s4=K(()=>u(R).name??[]),l4=K(()=>u(R).actorType===y.headMember.actorType&&u(R).actorId===y.headMember.actorId);f0(_4,{get id(){return u(R).id},get actorType(){return u(R).actorType},get actorId(){return u(R).actorId},get name(){return u(s4)},get isHead(){return u(l4)},get createdAt(){return u(R).createdAt},get communeId(){return l},get isCurrentUserHead(){return u(v4)},get isCurrentUserAdmin(){return u(y4).isAdmin},get locale(){return u(o)},onMemberRemoved:Y,onHeadTransferred:Y,get image(){return u(R).image},get toLocaleHref(){return u(m)},get getAppropriateLocalization(){return u(v)}})}e(W),d(x,W)}),e(p),d(C,p)};D(bu,C=>{t4.length===0?C(ku):C(Mu,!1)})}var Du=n(bu,2);w0(Du,{get show(){return u(O)},onHide:$,get communeData(){return y},get locale(){return u(o)},onCommuneUpdated:Y});var Tu=n(Du,2);x0(Tu,{get show(){return u(J)},onHide:D4,get communeId(){return l},get locale(){return u(o)},onInviteSent:B}),_((C,p)=>{i(U4,u(u4)),i(q4,u(O4)||""),i(H4,u(s).communeDetails),i(G,`${u(s).members??""}:`),i(L4,y.memberCount),i(cu,`${u(s).headMember??""}:`),i(z,C),i(yu,`${u(s).created??""}:`),i(xu,p),i(Iu,`${u(s).members??""} (${t4.length??""})`)},[()=>u(v)(y.headMember.name),()=>Yu(new Date(y.createdAt),u(o))]),d(A,I)};D(p4,A=>{u(L)?A(Y4):A(uu,!1)})}e(X);var S4=n(X,2);{var K4=A=>{var I=iu(),q=d4(I);{var Z=T=>{var j=ne();j.__click=F,j.__keydown=[$0,F];var V=a(j);V.__click=[ue];var k=a(V),w=a(k),a4=a(w),J4=a(a4,!0);e(a4);var M4=n(a4,2);M4.__click=F,e(w);var x4=n(w,2),U4=a(x4);{var z4=z=>{var N=ee(),w4=a(N,!0);e(N),_(()=>i(w4,u(h4))),d(z,N)};D(U4,z=>{u(h4)&&z(z4)})}var q4=n(U4,2);{var T4=z=>{var N=ae(),w4=a(N,!0);e(N),_(()=>i(w4,u(f4))),d(z,N)};D(q4,z=>{u(f4)&&z(T4)})}var j4=n(q4,2),B4=a(j4),F4=a(B4),I4=a(F4,!0);e(F4);var H4=n(F4,2);H4.__change=[U0,f4,c4,o4,s];var P=n(H4,2),i4=a(P,!0);e(P);var R4=n(P,2);{var Q4=z=>{var N=te(),w4=a(N);Lu(w4,"",{},{"max-height":"200px"}),e(N),_(()=>G4(w4,"src",u(o4))),d(z,N)};D(R4,z=>{u(o4)&&z(Q4)})}e(B4),e(j4),e(x4);var G=n(x4,2),H=a(G);H.__click=F;var L4=a(H,!0);e(H);var N4=n(H,2);N4.__click=[q0,c4,f4,s,n4,h4,l];var au=a(N4);{var cu=z=>{var N=re(),w4=n(d4(N));_(()=>i(w4,` ${u(s).uploading??""}`)),d(z,N)},ou=z=>{var N=$4();_(()=>i(N,u(s).upload)),d(z,N)};D(au,z=>{u(n4)?z(cu):z(ou,!1)})}e(N4),e(G),e(k),e(V),e(j),_(()=>{i(J4,u(s).uploadImageTitle),i(I4,u(s).pleaseSelectImage),H4.disabled=u(n4),i(i4,u(s).uploadImageMaxSize),i(L4,u(s).cancel),N4.disabled=!u(c4)||u(n4)}),d(T,j)};D(q,T=>{u(g4)&&T(Z)})}d(A,I)};D(S4,A=>{(u(v4)||u(y4).isAdmin)&&A(K4)})}var P4=n(S4,2);{var eu=A=>{var I=iu(),q=d4(I);{var Z=T=>{var j=ce();j.__click=e4,j.__keydown=[oe,e4];var V=a(j);V.__click=[ie];var k=a(V),w=a(k),a4=a(w),J4=a(a4,!0);e(a4);var M4=n(a4,2);M4.__click=e4,e(w);var x4=n(w,2),U4=a(x4);{var z4=G=>{var H=se(),L4=a(H,!0);e(H),_(()=>i(L4,u(b4))),d(G,H)};D(U4,G=>{u(b4)&&G(z4)})}var q4=n(U4,2);{var T4=G=>{var H=le(),L4=a(H,!0);e(H),_(()=>i(L4,u(U))),d(G,H)};D(q4,G=>{u(U)&&G(T4)})}var j4=n(q4,2),B4=a(j4,!0);e(j4),e(x4);var F4=n(x4,2),I4=a(F4);I4.__click=e4;var H4=a(I4,!0);e(I4);var P=n(I4,2);P.__click=[P0,M,U,b4,l,s];var i4=a(P);{var R4=G=>{var H=de(),L4=n(d4(H));_(()=>i(L4,` ${u(s).deletingImage??""}`)),d(G,H)},Q4=G=>{var H=$4();_(()=>i(H,u(s).deleteImageButton)),d(G,H)};D(i4,G=>{u(M)?G(R4):G(Q4,!1)})}e(P),e(F4),e(k),e(V),e(j),_(()=>{i(J4,u(s).deleteImageTitle),i(B4,u(s).confirmDeleteImage),i(H4,u(s).cancel),P.disabled=u(M)}),d(T,j)};D(q,T=>{u(A4)&&T(Z)})}d(A,I)};D(P4,A=>{(u(v4)||u(y4).isAdmin)&&y.image&&A(eu)})}var X4=n(P4,2);{var Z4=A=>{var I=fe();I.__click=E,I.__keydown=[ve,E];var q=a(I);q.__click=[me];var Z=a(q),T=a(Z),j=a(T),V=a(j,!0);e(j);var k=n(j,2);k.__click=E,e(T);var w=n(T,2),a4=a(w);{var J4=P=>{var i4=_e(),R4=a(i4,!0);e(i4),_(()=>i(R4,u(L))),d(P,i4)};D(a4,P=>{u(L)&&P(J4)})}var M4=n(a4,2),x4=a(M4,!0);e(M4);var U4=n(M4,2),z4=n(a(U4));e(U4),e(w);var q4=n(w,2),T4=a(q4);T4.__click=E;var j4=a(T4,!0);e(T4);var B4=n(T4,2);B4.__click=[R0,E4,L,b,l,s,m];var F4=a(B4);{var I4=P=>{var i4=ge(),R4=n(d4(i4));_(()=>i(R4,` ${u(s).deleting??""}`)),d(P,i4)},H4=P=>{var i4=$4();_(()=>i(i4,u(s).confirmDelete)),d(P,i4)};D(F4,P=>{u(E4)?P(I4):P(H4,!1)})}e(B4),e(q4),e(Z),e(q),e(I),_(()=>{i(V,u(s).confirmDelete),i(x4,u(s).confirmDeleteMessage),i(z4,` ${u(s).deleteWarning??""}`),i(j4,u(s).cancel),B4.disabled=u(E4)}),d(A,I)};D(X4,A=>{u(C4)&&A(Z4)})}d(c,m4),du()}_u(["click","keydown","change"]);export{qe as component,Ue as universal};
