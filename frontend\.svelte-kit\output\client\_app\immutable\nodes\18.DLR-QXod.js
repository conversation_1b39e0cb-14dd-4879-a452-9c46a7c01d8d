import"../chunks/Bzak7iHL.js";import{f as $,h as V,n as D,t as b,b as P,d as i,s as o,r as u,g as t,$ as J,u as T}from"../chunks/RHWQbow4.js";import{s as c}from"../chunks/BlWcudmi.js";import{e as Q,i as q}from"../chunks/Dnfvvefi.js";import{s as M}from"../chunks/Cxg-bych.js";import{s as G}from"../chunks/CaC9IHEK.js";const A=(g,f=D,y=D,_=D,e=D,d=D,l=D)=>{var n=K(),s=i(n),E=i(s),r=o(E,2),x=i(r,!0);u(r),u(s);var a=o(s,2),p=i(a),m=i(p,!0);u(p),u(a);var C=o(a,2),F=i(C),B=i(F);Q(B,21,_,q,(v,h,U,W)=>{let I=()=>t(h).title,j=()=>t(h).description;var w=z(),k=i(w),S=i(k),R=i(S),H=i(R,!0);u(R);var L=o(R,2),N=i(L,!0);u(L),u(S),u(k),u(w),b(()=>{c(H,I()),c(N,j())}),P(v,w)}),u(B),u(F),u(C),u(n),b(()=>{M(s,1,`d-flex align-items-center p-3 rounded-top ${e()}`),M(E,1,`bi ${d()} text-${l()} me-3 fs-4`),c(x,f()),c(m,y())}),P(g,n)};var z=$('<li class="ps-2 mb-3"><div class="d-flex"><div><h5 class="fw-bold mb-1"> </h5> <p class="mb-0"> </p></div></div></li>'),K=$('<section class="mb-5"><div><i></i> <h2 class="h3 mb-0"> </h2></div> <div class="p-3 bg-light border-start border-end border-bottom"><p class="fst-italic"> </p></div> <div class="card shadow-sm border-success"><div class="card-body mt-3 p-4"><ul class="mb-0 ps-4"></ul></div></div></section>'),O=$('<div class="container my-5"><div class="mx-auto"><div class="row justify-content-center"><div class="col-12 col-md-10 col-lg-8"><div class="text-center mb-5"><h1 class="display-4 fw-bold mb-4"><i class="bi bi-book me-3 text-primary"></i> </h1> <div class="card shadow border-0 bg-light"><div class="card-body p-4"><p class="lead mb-0"> </p></div></div></div> <!> <!> <!> <!></div></div></div></div>');function e4(g,f){const y={ru:{_page:{title:"Правила — Коммуна"},title:"Правила сообщества",description:"Правила являются практическим руководством для участников Коммуны и детализируют применение фундаментальных принципов, изложенных в Праве. Они направлены на поддержание среды, способствующей реализации принципов Рациональности, Свободы, Ответственности, Справедливости и Взаимоуважения в цифровом пространстве. Соблюдение Правил и основополагающих принципов Права является ответственностью каждого участника сообщества.",sections:{recommended:{title:"Рекомендуется",description:"Эти пункты описывают желательное поведение, способствующее здоровой, продуктивной и дружелюбной атмосфере в сообществе, в полном соответствии с духом Права.",points:[{title:"Конструктивное общение",description:"Поддерживайте содержательный диалог, предлагайте обоснованную критику и полезные советы."},{title:"Ясность и релевантность",description:"Излагайте мысли четко, по существу обсуждаемой темы."},{title:"Качественный контент",description:"Делитесь проверенной информацией и опытом, при необходимости указывая источники."},{title:"Читабельность",description:"Используйте форматирование (абзацы, списки) для удобства восприятия ваших сообщений."},{title:"Взаимопомощь и сотрудничество",description:"Помогайте другим участникам, способствуйте развитию сообщества."},{title:"Поддержка новичков",description:"Помогайте новым участникам адаптироваться в сообществе."},{title:"Благодарность",description:"Выражайте признательность за помощь и ценный контент."}]},notRecommended:{title:"Не рекомендуется",description:"Действия, которые могут мешать конструктивному взаимодействию, создавать информационный шум или иным образом снижать качество общения, потенциально нарушая принципы Рациональности и Уважения. Систематическое нарушение может привести к ограничениям.",points:[{title:"Информационный шум",description:"Избегайте спама, флуда, чрезмерного оффтопа и многократного дублирования сообщений (кросспостинг)."},{title:"Навязчивая реклама",description:"Воздерживайтесь от саморекламы или скрытого маркетинга, не несущих ценности для обсуждения."},{title:"Злоупотребление оформлением",description:"Не используйте избыточное форматирование, мешающее чтению (кричащие заголовки, ЗАГЛАВНЫЕ БУКВЫ, обилие эмодзи)."},{title:"Неконструктивная лексика",description:"Используйте нецензурную лексику только в исключительных случаях, если это оправдано контекстом, избегая её для оскорблений или пустословия."},{title:"Низкокачественный контент",description:"Не распространяйте намеренно бессодержательный или нерелевантный контент."},{title:"Дублирование тем",description:"Перед созданием новой темы убедитесь (например, с помощью поиска), что подобная не обсуждается."}]},prohibited:{title:"Запрещено",description:"Действия, представляющие собой прямое и серьёзное нарушение прав других участников (Принципы Достоинства, Свободы, Ответственности, Справедливости), такие как посягательство на частную жизнь, травля и обман. Нарушения могут повлечь временные или постоянные ограничения.",points:[{title:"Имперсонация",description:"Выдача себя за другое лицо с целью обмана, мошенничества или нанесения вреда репутации."}]},strictlyProhibited:{title:"Строго запрещено",description:"Действия, грубо попирающие базовые принципы Права, представляющие угрозу безопасности участников, стабильности сообщества или нарушающие законодательство. Такие нарушения ведут к немедленному и, как правило, постоянному исключению из сообщества.",points:[{title:"Нелегальная деятельность",description:"Распространение или пропаганда запрещенных веществ, услуг или контента, нарушающего законодательство."},{title:"Пропаганда ненависти и насилия",description:"Распространение идеологий, основанных на ненависти, дискриминации, призывы к насилию, экстремизму, терроризму."},{title:"Обход ограничений",description:"Создание дополнительных аккаунтов или использование иных методов для обхода блокировок или ограничений, наложенных администрацией."},{title:"Вредоносная активность",description:"Распространение вредоносного ПО, фишинговых ссылок, использование уязвимостей платформы или иные действия, направленные на техническое нанесение ущерба сообществу или его участникам."},{title:"Манипуляции",description:"Использование автоматизированных скриптов (ботов) для нечестной манипуляции контентом, голосованиями или иными механизмами сообщества."},{title:"Эксплуатация уязвимостей",description:"Использование ошибок или недоработок системы для получения несправедливого преимущества или нанесения вреда."}]}}},en:{_page:{title:"Rules — Commune"},title:"Rules of Society",description:"Rules serve as a practical guide for members of Commune and detail the application of fundamental principles outlined in The Law. They aim to maintain an environment that promotes the principles of Rationality, Freedom, Responsibility, Justice, and Mutual Respect in the digital space. Adherence to these Rules and the fundamental principles of The Law is the responsibility of each community member.",sections:{recommended:{title:"Recommended",description:"These points describe desirable behavior that contributes to a healthy, productive, and friendly atmosphere in the community, fully aligned with the spirit of The Law.",points:[{title:"Constructive Communication",description:"Maintain meaningful dialogue, offer reasoned criticism and helpful advice."},{title:"Clarity and Relevance",description:"Express thoughts clearly and stay on topic in discussions."},{title:"Quality Content",description:"Share verified information and experiences, citing sources when appropriate."},{title:"Readability",description:"Use formatting (paragraphs, lists) to make your messages easier to read."},{title:"Mutual Help and Collaboration",description:"Assist other members and contribute to community development."},{title:"Supporting Newcomers",description:"Help new members adapt to the community."},{title:"Gratitude",description:"Express appreciation for help and valuable content."}]},notRecommended:{title:"Not Recommended",description:"Actions that may interfere with constructive interaction, create information noise, or otherwise reduce the quality of communication, potentially violating the principles of Rationality and Respect. Systematic violations may lead to restrictions.",points:[{title:"Information Noise",description:"Avoid spam, flooding, excessive off-topic content, and repeated cross-posting."},{title:"Intrusive Advertising",description:"Refrain from self-promotion or hidden marketing that adds no value to discussions."},{title:"Formatting Abuse",description:"Don't use excessive formatting that hinders readability (screaming headlines, ALL CAPS, emoji overuse)."},{title:"Unconstructive Language",description:"Use profanity only when contextually justified, avoiding it for insults or empty talk."},{title:"Low-Quality Content",description:"Don't deliberately share meaningless or irrelevant content."},{title:"Duplicate Topics",description:"Before creating a new topic, check (using search) that a similar one isn't already being discussed."}]},prohibited:{title:"Prohibited",description:"Actions that constitute a direct and serious violation of the rights of other participants (Principles of Dignity, Freedom, Responsibility, Justice), such as invasion of privacy, harassment, and deception. Violations may result in temporary or permanent restrictions.",points:[{title:"Impersonation",description:"Pretending to be someone else to deceive, defraud, or damage reputation."}]},strictlyProhibited:{title:"Strictly Prohibited",description:"Actions that grossly violate the basic principles of The Law, posing a threat to the safety of participants, community stability, or violating legislation. Such violations typically lead to immediate and permanent exclusion from the community.",points:[{title:"Illegal Activity",description:"Distribution or promotion of illegal substances, services, or content that violates legislation."},{title:"Hate Speech and Violence",description:"Spreading ideologies based on hatred, discrimination, or calls for violence, extremism, terrorism."},{title:"Circumventing Restrictions",description:"Creating additional accounts or using other methods to bypass blocks or limitations imposed by administration."},{title:"Malicious Activity",description:"Distributing malware, phishing links, exploiting platform vulnerabilities, or other actions aimed at technically harming the community or its members."},{title:"Manipulation",description:"Using automated scripts (bots) for unfair manipulation of content, voting, or other community mechanisms."},{title:"Exploiting Vulnerabilities",description:"Using errors or flaws in the system to gain unfair advantage or cause harm."}]}}}},_=T(()=>f.data.locale),e=T(()=>y[t(_)]);var d=O();V(U=>{b(()=>J.title=t(e)._page.title)});var l=i(d);G(l,"",{},{width:"100%","max-width":"100%"});var n=i(l),s=i(n),E=i(s),r=i(E),x=o(i(r));u(r);var a=o(r,2),p=i(a),m=i(p),C=i(m,!0);u(m),u(p),u(a),u(E);var F=o(E,2);A(F,()=>t(e).sections.recommended.title,()=>t(e).sections.recommended.description,()=>t(e).sections.recommended.points,()=>"bg-success-subtle",()=>"bi-check-circle-fill",()=>"success");var B=o(F,2);A(B,()=>t(e).sections.notRecommended.title,()=>t(e).sections.notRecommended.description,()=>t(e).sections.notRecommended.points,()=>"bg-warning-subtle",()=>"bi-exclamation-triangle-fill",()=>"warning");var v=o(B,2);A(v,()=>t(e).sections.prohibited.title,()=>t(e).sections.prohibited.description,()=>t(e).sections.prohibited.points,()=>"bg-danger-subtle",()=>"bi-x-octagon-fill",()=>"danger");var h=o(v,2);A(h,()=>t(e).sections.strictlyProhibited.title,()=>t(e).sections.strictlyProhibited.description,()=>t(e).sections.strictlyProhibited.points,()=>"bg-dark text-white",()=>"bi-slash-circle-fill",()=>"white"),u(s),u(n),u(l),u(d),b(()=>{c(x,` ${t(e).title??""}`),c(C,t(e).description)}),P(g,d)}export{e4 as component};
