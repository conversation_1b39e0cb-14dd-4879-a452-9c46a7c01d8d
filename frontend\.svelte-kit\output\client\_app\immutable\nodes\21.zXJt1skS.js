import{e as Pe}from"../chunks/CVTn1FV4.js";import{g as ne}from"../chunks/CGZ87yZq.js";import"../chunks/Bzak7iHL.js";import{p as Te,av as J,aw as Re,o as He,g as e,ax as h,f as w,h as $e,d as a,t as b,b as D,c as Je,s,u as n,$ as Ke,r as t,aR as Me}from"../chunks/RHWQbow4.js";import{d as We,e as qe,s as i}from"../chunks/BlWcudmi.js";import{i as K}from"../chunks/CtoItwj4.js";import{s as E,a as Ge}from"../chunks/BdpLTtcP.js";import{s as Qe}from"../chunks/Cxg-bych.js";import{s as Ve}from"../chunks/CaC9IHEK.js";import"../chunks/B0MzmgHo.js";import{g as Xe}from"../chunks/DGxS2cwR.js";const Ye=async({fetch:m,params:o,url:_})=>{const{fetcher:g}=ne(),[[l],C,N]=await Promise.all([g.user.list.get({ids:[o.id]},{fetch:m,ctx:{url:_}}),g.user.note.get({userId:o.id},{fetch:m,ctx:{url:_}}),g.rating.summary.get({userId:o.id},{fetch:m,ctx:{url:_}})]);if(!l)throw Pe(404,"User not found");return{user:l,userNote:C.text,ratingSummary:N}},fa=Object.freeze(Object.defineProperty({__proto__:null,load:Ye},Symbol.toStringTag,{value:"Module"})),Ze=(m,o,_)=>{const g=m.target;h(o,g.value,!0),_()};var ea=w('<div style="height: 300px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;"><img style="width: 100%; height: 100%; object-fit: contain;"/></div>'),aa=w('<div class="bg-light text-center rounded mb-4 d-flex align-items-center justify-content-center" style="height: 300px;"><span class="text-muted"> </span></div>'),ta=w('<div class="card shadow-sm mb-4"><div class="card-body"><h5 class="card-title"> </h5> <hr/> <div class="row g-3"><div class="col-4"><div class="rating-block border rounded p-3 text-center svelte-1hels8p" style="border-color: #fd7e14 !important;"><div class="rating-label text-muted small mb-1"> </div> <div class="rating-value fw-bold" style="color: #fd7e14; font-size: 1.5rem;"> </div></div></div> <div class="col-4"><a class="karma-block border rounded p-3 text-center d-block text-decoration-none svelte-1hels8p" style="border-color: #d63384 !important;"><div class="karma-label text-muted small mb-1"> </div> <div class="karma-value fw-bold" style="color: #d63384; font-size: 1.5rem;"> </div></a></div> <div class="col-4"><a class="rate-block border rounded p-3 text-center d-block text-decoration-none svelte-1hels8p" style="border-color: #6c757d !important;"><div class="rate-label text-muted small mb-1"> </div> <div> </div></a></div></div></div></div>'),ua=w('<small class="text-muted"> </small>'),ra=w('<div class="container py-4"><div class="row"><div class="col-lg-8"><!> <div class="mb-4"><div class="d-flex justify-content-between align-items-center mb-3"><h2 class="mb-0"> </h2></div> <p class="lead text-muted"> </p></div></div> <div class="col-lg-4"><div class="card shadow-sm mb-4"><div class="card-body"><h5 class="card-title"> </h5> <hr/> <div class="d-flex align-items-center"><i class="bi bi-calendar-date me-2 text-primary"></i> <span> </span></div></div></div> <!> <div class="card shadow-sm mb-4"><div class="card-body"><h5 class="card-title"> </h5> <hr/> <div class="mb-2"><textarea class="form-control" rows="4"></textarea></div> <!></div></div></div></div></div>');function pa(m,o){Te(o,!0);const _={en:{_page:{title:"— Commune"},userNotFound:"User not found",userDetails:"User Details",joinedOn:"Joined on",dateFormatLocale:"en-US",userNote:"Personal Note",userNotePlaceholder:"Write your personal note about this user...",saved:"Saved...",rating:"Rating",karma:"Karma",rate:"Rate",noImage:"No image available",userImageAlt:"User image",socialOpinion:"Social Opinion"},ru:{_page:{title:"— Коммуна"},userNotFound:"Пользователь не найден",userDetails:"Информация о пользователе",joinedOn:"Дата регистрации",dateFormatLocale:"ru-RU",userNote:"Личная заметка",userNotePlaceholder:"Напишите свою личную заметку об этом пользователе...",saved:"Сохранено...",rating:"Рейтинг",karma:"Карма",rate:"Оценка",noImage:"Нет доступных изображений",userImageAlt:"Изображение пользователя",socialOpinion:"Общественное мнение"}},{fetcher:g}=ne(),l=n(()=>o.data.user),C=n(()=>o.data.locale),N=n(()=>o.data.getAppropriateLocalization),c=n(()=>o.data.ratingSummary),M=n(()=>o.data.toLocaleHref),d=n(()=>_[e(C)]);let f=J(Re(o.data.userNote)),p=J(null),F=J(!1);He(()=>{e(f)!==void 0&&h(f,e(f)||"",!0)});const W=async()=>{var u;await g.user.note.put({userId:e(l).id,text:((u=e(f))==null?void 0:u.trim())||null}),h(F,!0),setTimeout(()=>{h(F,!1)},2e3)},ce=()=>{e(p)&&clearTimeout(e(p)),h(p,setTimeout(()=>{W()},3e3),!0)},me=()=>{e(p)&&(clearTimeout(e(p)),h(p,null)),W()},q=n(()=>e(N)(e(l).name)),_e=n(()=>e(N)(e(l).description)),ge=n(()=>e(l)?new Date(e(l).createdAt):new Date),fe=n(()=>e(ge).toLocaleDateString(e(d).dateFormatLocale,{year:"numeric",month:"long",day:"numeric"}));var A=ra();$e(u=>{b(()=>Ke.title=`${e(q)??""} ${e(d)._page.title??""}`)});var G=a(A),B=a(G),Q=a(B);{var pe=u=>{var r=ea(),v=a(r);t(r),b(()=>{E(v,"src",`/images/${e(l).image}`),E(v,"alt",`${e(d).userImageAlt}`)}),D(u,r)},be=u=>{var r=aa(),v=a(r),y=a(v,!0);t(v),t(r),b(()=>i(y,e(d).noImage)),D(u,r)};K(Q,u=>{e(l).image?u(pe):u(be,!1)})}var V=s(Q,2),S=a(V),X=a(S),he=a(X,!0);t(X),t(S);var Y=s(S,2),xe=a(Y,!0);t(Y),t(V),t(B);var Z=s(B,2),j=a(Z),ee=a(j),I=a(ee),ye=a(I,!0);t(I);var ae=s(I,4),te=s(a(ae),2),De=a(te);t(te),t(ae),t(ee),t(j);var ue=s(j,2);{var Ee=u=>{var r=ta(),v=a(r),y=a(v),Ce=a(y,!0);t(y);var ie=s(y,4),U=a(ie),oe=a(U),z=a(oe),Fe=a(z,!0);t(z);var de=s(z,2),Ae=a(de,!0);t(de),t(oe),t(U);var P=s(U,2),T=a(P),R=a(T),Be=a(R,!0);t(R);var le=s(R,2),Se=a(le,!0);t(le),t(T),t(P);var ve=s(P,2),H=a(ve),$=a(H),je=a($,!0);t($);var k=s($,2),Ie=a(k,!0);t(k),t(H),t(ve),t(ie),t(v),t(r),b((Oe,Le,Ue,ze)=>{i(Ce,e(d).socialOpinion),i(Fe,e(d).rating),i(Ae,e(c).rating),E(T,"href",Oe),i(Be,e(d).karma),i(Se,e(c).karma),E(H,"href",Le),i(je,e(d).rate),Qe(k,1,`rate-value fw-bold ${e(c).rate===null?"text-muted":""}`),Ve(k,`font-size: 1.5rem; ${Ue??""}`),i(Ie,ze)},[()=>e(M)(`/users/${e(l).id}/karma`),()=>e(M)(`/users/${e(l).id}/feedback`),()=>e(c).rate!==null?`color: ${Xe(e(c).rate)};`:"",()=>e(c).rate!==null?e(c).rate.toFixed(1):"N/A"]),D(u,r)};K(ue,u=>{e(c)&&u(Ee)})}var re=s(ue,2),se=a(re),O=a(se),we=a(O,!0);t(O);var L=s(O,4),x=a(L);Me(x),x.__input=[Ze,f,ce],t(L);var Ne=s(L,2);{var ke=u=>{var r=ua(),v=a(r,!0);t(r),b(()=>i(v,e(d).saved)),D(u,r)};K(Ne,u=>{e(F)&&u(ke)})}t(se),t(re),t(Z),t(G),t(A),b(()=>{i(he,e(q)),i(xe,e(_e)||""),i(ye,e(d).userDetails),i(De,`${e(d).joinedOn??""} ${e(fe)??""}`),i(we,e(d).userNote),E(x,"placeholder",e(d).userNotePlaceholder),Ge(x,e(f))}),qe("blur",x,me),D(m,A),Je()}We(["input"]);export{pa as component,fa as universal};
