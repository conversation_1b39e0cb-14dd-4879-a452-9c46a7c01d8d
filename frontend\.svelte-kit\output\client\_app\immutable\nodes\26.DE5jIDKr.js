import{c as Se}from"../chunks/CVTn1FV4.js";import{g as je}from"../chunks/CGZ87yZq.js";import"../chunks/Bzak7iHL.js";import{o as ou}from"../chunks/DeAm3Eed.js";import{p as nu,av as g,aw as ee,f as m,h as lu,a as Ce,d as a,t as p,g as e,b as d,c as cu,ax as s,s as n,u as G,$ as du,r}from"../chunks/RHWQbow4.js";import{d as mu,s as _}from"../chunks/BlWcudmi.js";import{i as E}from"../chunks/CtoItwj4.js";import{e as vu}from"../chunks/Dnfvvefi.js";import{r as hu,s as x}from"../chunks/BdpLTtcP.js";import{b as fu,M as gu,U as _u,R as pu,e as Eu,a as bu}from"../chunks/iI8NM7bJ.js";import{b as yu}from"../chunks/B5DcI8qy.js";import{r as Cu,g as xu}from"../chunks/CKnuo8tw.js";import{f as Du}from"../chunks/CL12WlkV.js";import"../chunks/BiLRrsV0.js";const wu=async({fetch:M,url:v})=>{const{fetcher:w}=je(),N=v.searchParams.get("search"),[b,I]=await Promise.all([w.user.me.get({fetch:M,ctx:{url:v}}),w.reactor.community.list.get({query:N??void 0},{fetch:M,ctx:{url:v}})]);return{me:b,communities:I,searchQuery:N,isHasMoreCommunities:I.length===Se.PAGE_SIZE}},s4=Object.freeze(Object.defineProperty({__proto__:null,load:wu},Symbol.toStringTag,{value:"Module"}));function Pu(M,v,w){const b=M.target.value;v(b),w(b)}function Fu(M,v,w){s(v,!0),w()}var Uu=m('<button class="btn btn-primary"> </button>'),Bu=m('<div class="text-center py-5"><p class="text-muted"> </p></div>'),Mu=m('<img class="community-image svelte-jyzss"/>'),Iu=m('<div class="community-image-placeholder svelte-jyzss"><i class="bi bi-people fs-1 text-muted"></i></div>'),Su=m('<img class="rounded" style="width: 32px; height: 32px; object-fit: cover;"/>'),ju=m('<div class="rounded bg-secondary d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;"><i class="bi bi-collection text-white"></i></div>'),Tu=m('<div class="mb-3"><div class="d-flex align-items-center"><div class="me-3"><!></div> <div><a class="fw-medium" style="text-decoration: none;"> </a></div></div></div>'),Au=m('<img class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;"/>'),Hu=m('<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;"><i class="bi bi-person-fill text-white"></i></div>'),Nu=m('<div class="col-12"><div class="card shadow-sm h-100 svelte-jyzss"><div class="row g-0 h-100"><div class="col-md-3 col-lg-2"><div class="community-image-container svelte-jyzss"><!></div></div> <div class="col-md-9 col-lg-10"><div class="card-body d-flex flex-column h-100 p-4"><div class="d-flex justify-content-between align-items-start mb-3"><h4 class="card-title mb-0 flex-grow-1"><a style="text-decoration: none;"> </a></h4> <small class="text-muted ms-3"> </small></div> <p class="card-text text-muted mb-3 flex-grow-1"> </p> <!> <div class="d-flex align-items-center"><div class="me-3"><!></div> <div><a class="fw-medium" style="text-decoration: none;"> </a></div></div></div></div></div></div></div>'),zu=m('<div class="row g-4"></div>'),Ou=m('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden"> </span></div> <p class="text-muted mt-2 mb-0"> </p>',1),Lu=m('<div class="text-center py-3"><!></div>'),qu=m('<div class="alert alert-danger" role="alert"> </div>'),ku=m('<div class="alert alert-danger mb-3"> </div>'),Ru=m('<div class="alert alert-success mb-3"> </div>'),Qu=m('<!> <!> <form><!> <div class="form-text mb-3"> </div> <!> <div class="form-text mb-3"> </div> <!> <!></form>',1),Gu=m('<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4 gap-3"><h1 class="mb-0"> </h1> <div class="d-flex align-items-center gap-3"><div class="search-container svelte-jyzss"><input type="text" class="form-control svelte-jyzss" style="min-width: 250px;"/></div> <!></div></div> <!> <!> <!></div> <!>',1);function o4(M,v){nu(v,!0);const w={en:{_page:{title:"Communities — Reactor of Commune"},communities:"Communities",createCommunity:"Create Community",noCommunities:"No communities found",head:"Head",errorFetchingCommunities:"Failed to fetch communities",errorOccurred:"An error occurred while fetching communities",loadingMore:"Loading more communities...",createdOn:"Created on",createCommunityTitle:"Create New Community",communityName:"Community Name",communityDescription:"Community Description",headUser:"Head User",headUserPlaceholder:"Leave empty to use current user",hub:"Hub",hubPlaceholder:"Leave empty for no hub",communityNamePlaceholder:"Enter community name",communityDescriptionPlaceholder:"Enter community description",create:"Create",cancel:"Cancel",creating:"Creating...",communityCreatedSuccess:"Community created successfully!",errorCreatingCommunity:"Failed to create community",required:"This field is required",searchPlaceholder:"Search communities...",noHub:"No hub"},ru:{_page:{title:"Сообщества — Реактор Коммуны"},communities:"Сообщества",createCommunity:"Создать сообщество",noCommunities:"Сообщества не найдены",head:"Глава",errorFetchingCommunities:"Не удалось загрузить сообщества",errorOccurred:"Произошла ошибка при загрузке сообществ",loadingMore:"Загружаем больше сообществ...",createdOn:"Создано",createCommunityTitle:"Создать новое сообщество",communityName:"Название сообщества",communityDescription:"Описание сообщества",headUser:"Главный пользователь",headUserPlaceholder:"Оставьте пустым для использования текущего пользователя",hub:"Хаб",hubPlaceholder:"Оставьте пустым для отсутствия хаба",communityNamePlaceholder:"Введите название сообщества",communityDescriptionPlaceholder:"Введите описание сообщества",create:"Создать",cancel:"Отмена",creating:"Создаем...",communityCreatedSuccess:"Сообщество успешно создано!",errorCreatingCommunity:"Не удалось создать сообщество",required:"Это поле обязательно",searchPlaceholder:"Поиск сообществ...",noHub:"Нет хаба"}},{fetcher:N}=je(),b=G(()=>v.data.locale),I=G(()=>v.data.toLocaleHref),S=G(()=>v.data.getAppropriateLocalization),o=G(()=>w[e(b)]);let Z=g(ee(v.data.communities)),ue=g(null),ce=g(!1),de=g(ee(v.data.searchQuery||"")),V=g(null),J=g(!1),me=g(1),te=g(ee(v.data.isHasMoreCommunities)),K=g(null),z=g(!1),j=g(null),W=g(null),O=g(ee([])),L=g(ee([])),re=g(null),ae=g(null);async function xe(u,t=1,c=!1){c||s(J,!0),s(ue,null);try{const i=await N.reactor.community.list.get({pagination:{page:t},query:u.trim()||void 0});c?(s(Z,[...e(Z),...i],!0),s(me,t,!0)):(s(Z,i,!0),s(me,1)),s(te,i.length===Se.PAGE_SIZE)}catch(i){s(ue,i instanceof Error?i.message:e(o).errorOccurred,!0),console.error(i)}finally{s(J,!1)}}async function Te(){e(J)||!e(te)||await xe(e(de),e(me)+1,!0)}function Ae(u){const t=new URL(window.location.href);u.trim()?t.searchParams.set("search",encodeURIComponent(u)):t.searchParams.delete("search"),Cu(t.pathname+t.search,{})}function He(u){e(V)&&clearTimeout(e(V)),s(V,setTimeout(()=>{xe(u)},1e3),!0)}function Ne(){s(ce,!1)}function ze(){s(O,[],!0),s(L,[],!0),s(re,null),s(ae,null),s(j,null),s(W,null),s(z,!1)}function Oe(){return e(O).some(u=>u.value.trim().length>0)?e(L).some(u=>u.value.trim().length>0)?!0:(s(j,e(o).required,!0),!1):(s(j,e(o).required,!0),!1)}async function Le(){if(Oe()){s(z,!0),s(j,null),s(W,null);try{const{id:u}=await N.reactor.community.post({hubId:e(ae)||null,headUserId:e(re),name:e(O),description:e(L)});s(W,e(o).communityCreatedSuccess,!0),setTimeout(()=>{xu(e(I)(`/reactor/communities/${u}`))},1500)}catch(u){s(j,u instanceof Error?u.message:e(o).errorCreatingCommunity,!0),console.error(u)}finally{s(z,!1)}}}function qe(u,t=200){return u.length<=t?u:u.slice(0,t)+"..."}function ke(){if(!e(K))return null;const u=new IntersectionObserver(t=>{t[0].isIntersecting&&e(te)&&!e(J)&&Te()},{rootMargin:"100px",threshold:.1});return u.observe(e(K)),u}ou(()=>{let u=null;const t=()=>{u=ke()};return e(K)?t():setTimeout(t,100),()=>{u==null||u.disconnect(),e(V)&&clearTimeout(e(V))}});var De=Gu();lu(u=>{p(()=>du.title=e(o)._page.title)});var ve=Ce(De),he=a(ve),fe=a(he),Re=a(fe,!0);r(fe);var we=n(fe,2),ge=a(we),ie=a(ge);hu(ie),ie.__input=[Pu,Ae,He],r(ge);var Qe=n(ge,2);{var Ge=u=>{var t=Uu();t.__click=[Fu,ce,ze];var c=a(t,!0);r(t),p(()=>_(c,e(o).createCommunity)),d(u,t)};E(Qe,u=>{var t;((t=v.data.user)==null?void 0:t.role)==="admin"&&u(Ge)})}r(we),r(he);var Pe=n(he,2);{var Ze=u=>{var t=Bu(),c=a(t),i=a(c,!0);r(c),r(t),p(()=>_(i,e(o).noCommunities)),d(u,t)},Ve=u=>{var t=zu();vu(t,21,()=>e(Z),c=>c.id,(c,i)=>{var C=Nu(),P=a(C),D=a(P),F=a(D),T=a(F),q=a(T);{var k=l=>{var f=Mu();p(U=>{x(f,"src",`/images/${e(i).image}`),x(f,"alt",U)},[()=>e(S)(e(i).name)||"Community"]),d(l,f)},se=l=>{var f=Iu();d(l,f)};E(q,l=>{e(i).image?l(k):l(se,!1)})}r(T),r(F);var R=n(F,2),oe=a(R),Q=a(oe),A=a(Q),X=a(A),ne=a(X,!0);r(X),r(A);var le=n(A,2),h=a(le);r(le),r(Q);var y=n(Q,2),Y=a(y,!0);r(y);var Ue=n(y,2);{var $e=l=>{var f=Tu(),U=a(f),$=a(U),Ee=a($);{var be=B=>{var H=Su();p(su=>{x(H,"src",`/images/${e(i).hub.image}`),x(H,"alt",su)},[()=>e(S)(e(i).hub.name)]),d(B,H)},au=B=>{var H=ju();d(B,H)};E(Ee,B=>{e(i).hub.image?B(be):B(au,!1)})}r($);var Ie=n($,2),ye=a(Ie),iu=a(ye,!0);r(ye),r(Ie),r(U),r(f),p((B,H)=>{x(ye,"href",B),_(iu,H)},[()=>e(I)(`/reactor/hubs/${e(i).hub.id}`),()=>e(S)(e(i).hub.name)]),d(l,f)};E(Ue,l=>{e(i).hub&&l($e)})}var Be=n(Ue,2),_e=a(Be),eu=a(_e);{var uu=l=>{var f=Au();p(U=>{x(f,"src",`/images/${e(i).headUser.image}`),x(f,"alt",U)},[()=>e(S)(e(i).headUser.name)]),d(l,f)},tu=l=>{var f=Hu();d(l,f)};E(eu,l=>{e(i).headUser.image?l(uu):l(tu,!1)})}r(_e);var Me=n(_e,2),pe=a(Me),ru=a(pe,!0);r(pe),r(Me),r(Be),r(oe),r(R),r(D),r(P),r(C),p((l,f,U,$,Ee,be)=>{x(X,"href",l),_(ne,f),_(h,`${e(o).createdOn??""}
                      ${U??""}`),_(Y,$),x(pe,"href",Ee),_(ru,be)},[()=>e(I)(`/reactor/communities/${e(i).id}`),()=>e(S)(e(i).name)||"No name?",()=>Du(e(i).createdAt,e(b)),()=>qe(e(S)(e(i).description)||""),()=>e(I)(`/users/${e(i).headUser.id}`),()=>e(S)(e(i).headUser.name)]),d(c,C)}),r(t),d(u,t)};E(Pe,u=>{e(Z).length===0?u(Ze):u(Ve,!1)})}var Fe=n(Pe,2);{var Je=u=>{var t=Lu(),c=a(t);{var i=C=>{var P=Ou(),D=Ce(P),F=a(D),T=a(F,!0);r(F),r(D);var q=n(D,2),k=a(q,!0);r(q),p(()=>{_(T,e(o).loadingMore),_(k,e(o).loadingMore)}),d(C,P)};E(c,C=>{e(J)&&C(i)})}r(t),yu(t,C=>s(K,C),()=>e(K)),d(u,t)};E(Fe,u=>{e(te)&&u(Je)})}var Ke=n(Fe,2);{var We=u=>{var t=qu(),c=a(t,!0);r(t),p(()=>_(c,e(ue))),d(u,t)};E(Ke,u=>{e(ue)&&u(We)})}r(ve);var Xe=n(ve,2);{var Ye=u=>{{let t=G(()=>e(z)?e(o).creating:e(o).create),c=G(()=>e(z)||!e(O).some(i=>i.value.trim().length>0)||!e(L).some(i=>i.value.trim().length>0));gu(u,{get show(){return e(ce)},get title(){return e(o).createCommunityTitle},onClose:Ne,onSubmit:Le,get submitText(){return e(t)},get cancelText(){return e(o).cancel},get submitDisabled(){return e(c)},get isSubmitting(){return e(z)},children:(i,C)=>{var P=Qu(),D=Ce(P);{var F=h=>{var y=ku(),Y=a(y,!0);r(y),p(()=>_(Y,e(j))),d(h,y)};E(D,h=>{e(j)&&h(F)})}var T=n(D,2);{var q=h=>{var y=Ru(),Y=a(y,!0);r(y),p(()=>_(Y,e(W))),d(h,y)};E(T,h=>{e(W)&&h(q)})}var k=n(T,2),se=a(k);_u(se,{get locale(){return e(b)},get label(){return e(o).headUser},get placeholder(){return e(o).headUserPlaceholder},get selectedUserId(){return e(re)},set selectedUserId(h){s(re,h,!0)}});var R=n(se,2),oe=a(R,!0);r(R);var Q=n(R,2);pu(Q,{get locale(){return e(b)},get label(){return e(o).hub},get placeholder(){return e(o).hubPlaceholder},get selectedHubId(){return e(ae)},set selectedHubId(h){s(ae,h,!0)}});var A=n(Q,2),X=a(A,!0);r(A);var ne=n(A,2);Eu(ne,{get locale(){return e(b)},id:"community-name",get label(){return e(o).communityName},get placeholder(){return e(o).communityNamePlaceholder},required:!0,get value(){return e(O)},set value(h){s(O,h,!0)}});var le=n(ne,2);bu(le,{get locale(){return e(b)},id:"community-description",get label(){return e(o).communityDescription},get placeholder(){return e(o).communityDescriptionPlaceholder},rows:4,required:!0,get value(){return e(L)},set value(h){s(L,h,!0)}}),r(k),p(()=>{_(oe,e(o).headUserPlaceholder),_(X,e(o).hubPlaceholder)}),d(i,P)},$$slots:{default:!0}})}};E(Xe,u=>{var t;((t=v.data.user)==null?void 0:t.role)==="admin"&&u(Ye)})}p(()=>{_(Re,e(o).communities),x(ie,"placeholder",e(o).searchPlaceholder)}),fu(ie,()=>e(de),u=>s(de,u)),d(M,De),cu()}mu(["input","click"]);export{o4 as component,s4 as universal};
