import{e as m4,c as Bu}from"../chunks/CVTn1FV4.js";import{g as Su}from"../chunks/CGZ87yZq.js";import"../chunks/Bzak7iHL.js";import{p as c4,aw as fu,av as h,f as v,h as v4,a as uu,t as g,b as c,c as g4,g as u,u as $,$ as f4,s as o,d as r,r as i,ax as t}from"../chunks/RHWQbow4.js";import{d as p4,s as f}from"../chunks/BlWcudmi.js";import{i as p}from"../chunks/CtoItwj4.js";import{s as N}from"../chunks/BdpLTtcP.js";import{s as _4}from"../chunks/CaC9IHEK.js";import{M as pu,e as E4,a as b4,f as Uu}from"../chunks/iI8NM7bJ.js";import"../chunks/B0MzmgHo.js";import{f as h4}from"../chunks/CL12WlkV.js";import"../chunks/BiLRrsV0.js";const y4=async({fetch:A,params:s,url:D})=>{const{fetcher:x}=Su(),[C,[L]]=await Promise.all([x.user.me.get({fetch:A,skipInterceptor:!0}).catch(()=>null),x.reactor.community.list.get({ids:[s.id]},{fetch:A,ctx:{url:D}})]);if(!L)throw m4(404,"Community not found");const I=C&&(C.role==="admin"||C.id===L.headUser.id);return{me:C,community:L,canEdit:I}},ie=Object.freeze(Object.defineProperty({__proto__:null,load:y4},Symbol.toStringTag,{value:"Module"}));function D4(A,s,D,x,C,L){t(s,!0),t(D,[...x.name],!0),t(C,[...x.description],!0),L()}function x4(A,s,D){t(s,!0),D()}function C4(A,s,D,x,C){const I=A.target.files;if(t(s,null),!I||I.length===0){t(D,null),t(x,null);return}const a=I[0];if(!Bu.ALLOWED_IMAGE_FILE_TYPES.includes(a.type)){t(s,u(C).invalidFileType,!0),t(D,null),t(x,null);return}if(a.size>Bu.MAX_IMAGE_FILE_SIZE){t(s,u(C).fileTooLarge,!0),t(D,null),t(x,null);return}t(D,a,!0),u(x)&&URL.revokeObjectURL(u(x)),t(x,URL.createObjectURL(a),!0)}function I4(A,s,D){t(s,!0),D()}var w4=v('<img class="community-image svelte-14fieot"/>'),B4=v('<div class="community-image-placeholder svelte-14fieot"><i class="bi bi-people fs-1 text-muted"></i></div>'),U4=v('<button class="btn btn-outline-danger btn-sm"><i class="bi bi-trash me-1"></i> </button>'),S4=v('<div class="mt-3 d-grid gap-2"><button class="btn btn-outline-primary btn-sm"><i class="bi bi-upload me-1"></i> </button> <!></div>'),T4=v('<button class="btn btn-primary"><i class="bi bi-pencil me-1"></i> </button>'),P4=v('<img class="rounded" style="width: 48px; height: 48px; object-fit: cover;"/>'),F4=v('<div class="rounded bg-secondary d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;"><i class="bi bi-collection text-white"></i></div>'),M4=v('<div class="col-sm-6"><div class="d-flex align-items-center"><div class="me-3"><!></div> <div><a class="fw-medium" style="text-decoration: none;"> </a></div></div></div>'),A4=v('<img class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;"/>'),L4=v('<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;"><i class="bi bi-person-fill text-white"></i></div>'),$4=v('<div class="alert alert-danger mb-3"> </div>'),N4=v('<div class="alert alert-success mb-3"> </div>'),j4=v("<!> <!> <form><!> <!></form>",1),z4=v('<div class="alert alert-success mb-3"> </div>'),k4=v('<div class="alert alert-danger mb-3"> </div>'),G4=v('<div class="mt-3 text-center"><img alt="Preview" class="img-thumbnail"/></div>'),O4=v('<!> <!> <form><div class="mb-3"><label for="imageInput" class="form-label"> </label> <input id="imageInput" type="file" class="form-control" accept=".jpg,.jpeg,.png,.webp"/> <p class="form-text text-muted"> </p> <!></div></form>',1),q4=v('<div class="alert alert-success mb-3"> </div>'),H4=v('<div class="alert alert-danger mb-3"> </div>'),W4=v("<!> <!> <p> </p>",1),R4=v('<div class="container my-4 mb-5"><div class="row mb-4"><div class="col-md-4 col-lg-3 mb-3"><div class="community-image-container svelte-14fieot"><!></div> <!></div> <div class="col-md-8 col-lg-9"><div class="d-flex justify-content-between align-items-start mb-3"><h1 class="mb-0"> </h1> <!></div> <p class="text-muted mb-3 fs-5"> </p> <div class="row g-3"><!> <div class="col-sm-6"><div class="d-flex align-items-center"><div class="me-3"><!></div> <div><a class="fw-medium" style="text-decoration: none;"> </a></div></div></div> <div class="col-sm-6"><div class="small text-muted"> </div> <div class="fw-medium"> </div></div></div></div></div> <!></div> <!> <!> <!>',1);function le(A,s){c4(s,!0);const D={en:{_page:{title:"Community — Reactor of Commune"},head:"Head",createdOn:"Created on",editCommunity:"Edit Community",uploadImage:"Upload Image",deleteImage:"Delete Image",hub:"Hub",noHub:"No hub",editCommunityTitle:"Edit Community",communityName:"Community Name",communityDescription:"Community Description",communityNamePlaceholder:"Enter community name",communityDescriptionPlaceholder:"Enter community description",save:"Save",cancel:"Cancel",saving:"Saving...",communityUpdatedSuccess:"Community updated successfully!",errorUpdatingCommunity:"Failed to update community",required:"This field is required",uploadImageTitle:"Upload Community Image",upload:"Upload",uploading:"Uploading...",imageUploadedSuccess:"Image uploaded successfully!",errorUploadingImage:"Failed to upload image",pleaseSelectImage:"Please select an image to upload",invalidFileType:"Invalid file type. Please upload a JPG, PNG, or WebP image.",fileTooLarge:"File is too large. Maximum size is 5MB.",uploadImageMaxSize:"Upload an image (JPG, PNG, WebP), max 5MB.",confirmDeleteImage:"Are you sure you want to delete this image?",deleteImageTitle:"Delete Image",delete:"Delete",deleting:"Deleting...",imageDeletedSuccess:"Image deleted successfully!",errorDeletingImage:"Failed to delete image"},ru:{_page:{title:"Сообщество — Реактор Коммуны"},head:"Глава",createdOn:"Создано",editCommunity:"Редактировать сообщество",uploadImage:"Загрузить изображение",deleteImage:"Удалить изображение",hub:"Хаб",noHub:"Нет хаба",editCommunityTitle:"Редактировать сообщество",communityName:"Название сообщества",communityDescription:"Описание сообщества",communityNamePlaceholder:"Введите название сообщества",communityDescriptionPlaceholder:"Введите описание сообщества",save:"Сохранить",cancel:"Отмена",saving:"Сохранение...",communityUpdatedSuccess:"Сообщество успешно обновлено!",errorUpdatingCommunity:"Не удалось обновить сообщество",required:"Это поле обязательно",uploadImageTitle:"Загрузить изображение сообщества",upload:"Загрузить",uploading:"Загрузка...",imageUploadedSuccess:"Изображение загружено успешно!",errorUploadingImage:"Не удалось загрузить изображение",pleaseSelectImage:"Пожалуйста, выберите изображение для загрузки",invalidFileType:"Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",fileTooLarge:"Файл слишком большой. Максимальный размер - 5MB.",uploadImageMaxSize:"Загрузите изображение (JPG, PNG, WebP), максимальный размер - 5MB.",confirmDeleteImage:"Вы уверены, что хотите удалить это изображение?",deleteImageTitle:"Удалить изображение",delete:"Удалить",deleting:"Удаление...",imageDeletedSuccess:"Изображение удалено успешно!",errorDeletingImage:"Не удалось удалить изображение"}},{fetcher:x}=Su(),C=$(()=>s.data.locale),L=$(()=>s.data.toLocaleHref),I=$(()=>s.data.getAppropriateLocalization),a=$(()=>D[u(C)]);let n=fu(s.data.community),eu=h(!1),q=h(!1),z=h(null),Y=h(null),H=h(fu([])),W=h(fu([])),tu=h(!1),k=h(!1),G=h(null),Z=h(null),R=h(null),K=h(null),au=h(!1),J=h(!1),Q=h(null),V=h(null);function Tu(){t(eu,!1)}function Pu(){t(z,null),t(Y,null),t(q,!1)}function Fu(){return u(H).some(e=>e.value.trim().length>0)?u(W).some(e=>e.value.trim().length>0)?!0:(t(z,u(a).required,!0),!1):(t(z,u(a).required,!0),!1)}async function Mu(){if(Fu()){t(q,!0),t(z,null),t(Y,null);try{await x.reactor.community.patch({id:n.id,name:u(H),description:u(W)}),t(Y,u(a).communityUpdatedSuccess,!0),setTimeout(()=>{ku()},1500)}catch(e){t(z,e instanceof Error?e.message:u(a).errorUpdatingCommunity,!0),console.error(e)}finally{t(q,!1)}}}function Au(){t(tu,!1),t(R,null),t(K,null)}function Lu(){t(G,null),t(Z,null),t(k,!1),t(R,null),t(K,null)}async function $u(){if(!u(R)){t(G,u(a).pleaseSelectImage,!0);return}t(k,!0),t(G,null),t(Z,null);try{const e=new FormData;e.append("image",u(R));const l=await Uu(`/api/reactor/community/${n.id}/image`,{method:"PUT",body:e});if(!l.ok)throw new Error(`${u(a).errorUploadingImage}: ${l.statusText}`);t(Z,u(a).imageUploadedSuccess,!0),setTimeout(()=>{window.location.reload()},1500)}catch(e){t(G,e instanceof Error?e.message:u(a).errorUploadingImage,!0),console.error(e)}finally{t(k,!1)}}function Nu(){t(au,!1)}function ju(){t(Q,null),t(V,null),t(J,!1)}async function zu(){t(J,!0),t(Q,null),t(V,null);try{const e=await Uu(`/api/reactor/community/${n.id}/image`,{method:"DELETE"});if(!e.ok)throw new Error(`${u(a).errorDeletingImage}: ${e.statusText}`);t(V,u(a).imageDeletedSuccess,!0),setTimeout(()=>{window.location.reload()},1500)}catch(e){t(Q,e instanceof Error?e.message:u(a).errorDeletingImage,!0),console.error(e)}finally{t(J,!1)}}function ku(){window.location.reload()}var _u=R4();v4(e=>{g(l=>f4.title=`${l??""} — ${u(a)._page.title??""}`,[()=>u(I)(n.name)||"Community"])});var ru=uu(_u),iu=r(ru),lu=r(iu),ou=r(lu),Gu=r(ou);{var Ou=e=>{var l=w4();g(d=>{N(l,"src",`/images/${n.image}`),N(l,"alt",d)},[()=>u(I)(n.name)||"Community"]),c(e,l)},qu=e=>{var l=B4();c(e,l)};p(Gu,e=>{n.image?e(Ou):e(qu,!1)})}i(ou);var Hu=o(ou,2);{var Wu=e=>{var l=S4(),d=r(l);d.__click=[x4,tu,Lu];var E=o(r(d));i(d);var M=o(d,2);{var S=B=>{var w=U4();w.__click=[I4,au,ju];var U=o(r(w));i(w),g(()=>f(U,` ${u(a).deleteImage??""}`)),c(B,w)};p(M,B=>{n.image&&B(S)})}i(l),g(()=>f(E,` ${u(a).uploadImage??""}`)),c(e,l)};p(Hu,e=>{s.data.canEdit&&e(Wu)})}i(lu);var Eu=o(lu,2),nu=r(Eu),su=r(nu),Ru=r(su,!0);i(su);var Ju=o(su,2);{var Xu=e=>{var l=T4();l.__click=[D4,eu,H,n,W,Pu];var d=o(r(l));i(l),g(()=>f(d,` ${u(a).editCommunity??""}`)),c(e,l)};p(Ju,e=>{s.data.canEdit&&e(Xu)})}i(nu);var du=o(nu,2),Yu=r(du,!0);i(du);var bu=o(du,2),hu=r(bu);{var Zu=e=>{var l=M4(),d=r(l),E=r(d),M=r(E);{var S=b=>{var m=P4();g(y=>{N(m,"src",`/images/${n.hub.image}`),N(m,"alt",y)},[()=>u(I)(n.hub.name)]),c(b,m)},B=b=>{var m=F4();c(b,m)};p(M,b=>{n.hub.image?b(S):b(B,!1)})}i(E);var w=o(E,2),U=r(w),j=r(U,!0);i(U),i(w),i(d),i(l),g((b,m)=>{N(U,"href",b),f(j,m)},[()=>u(L)(`/reactor/hubs/${n.hub.id}`),()=>u(I)(n.hub.name)]),c(e,l)};p(hu,e=>{n.hub&&e(Zu)})}var mu=o(hu,2),yu=r(mu),cu=r(yu),Ku=r(cu);{var Qu=e=>{var l=A4();g(d=>{N(l,"src",`/images/${n.headUser.image}`),N(l,"alt",d)},[()=>u(I)(n.headUser.name)]),c(e,l)},Vu=e=>{var l=L4();c(e,l)};p(Ku,e=>{n.headUser.image?e(Qu):e(Vu,!1)})}i(cu);var Du=o(cu,2),vu=r(Du),u4=r(vu,!0);i(vu),i(Du),i(yu),i(mu);var xu=o(mu,2),gu=r(xu),e4=r(gu);i(gu);var Cu=o(gu,2),t4=r(Cu,!0);i(Cu),i(xu),i(bu),i(Eu),i(iu);var a4=o(iu,2);p(a4,e=>{}),i(ru);var Iu=o(ru,2);{var r4=e=>{{let l=$(()=>u(q)?u(a).saving:u(a).save),d=$(()=>u(q)||!u(H).some(E=>E.value.trim().length>0)||!u(W).some(E=>E.value.trim().length>0));pu(e,{get show(){return u(eu)},get title(){return u(a).editCommunityTitle},onClose:Tu,onSubmit:Mu,get submitText(){return u(l)},get cancelText(){return u(a).cancel},get submitDisabled(){return u(d)},get isSubmitting(){return u(q)},children:(E,M)=>{var S=j4(),B=uu(S);{var w=_=>{var T=$4(),O=r(T,!0);i(T),g(()=>f(O,u(z))),c(_,T)};p(B,_=>{u(z)&&_(w)})}var U=o(B,2);{var j=_=>{var T=N4(),O=r(T,!0);i(T),g(()=>f(O,u(Y))),c(_,T)};p(U,_=>{u(Y)&&_(j)})}var b=o(U,2),m=r(b);E4(m,{get locale(){return u(C)},id:"community-name",get label(){return u(a).communityName},get placeholder(){return u(a).communityNamePlaceholder},required:!0,get value(){return u(H)},set value(_){t(H,_,!0)}});var y=o(m,2);b4(y,{get locale(){return u(C)},id:"community-description",get label(){return u(a).communityDescription},get placeholder(){return u(a).communityDescriptionPlaceholder},rows:4,required:!0,get value(){return u(W)},set value(_){t(W,_,!0)}}),i(b),c(E,S)},$$slots:{default:!0}})}};p(Iu,e=>{s.data.canEdit&&e(r4)})}var wu=o(Iu,2);{var i4=e=>{{let l=$(()=>u(k)?u(a).uploading:u(a).upload),d=$(()=>!u(R)||u(k));pu(e,{get show(){return u(tu)},get title(){return u(a).uploadImageTitle},onClose:Au,onSubmit:$u,get submitText(){return u(l)},get cancelText(){return u(a).cancel},get submitDisabled(){return u(d)},get isSubmitting(){return u(k)},size:"lg",children:(E,M)=>{var S=O4(),B=uu(S);{var w=P=>{var F=z4(),X=r(F,!0);i(F),g(()=>f(X,u(Z))),c(P,F)};p(B,P=>{u(Z)&&P(w)})}var U=o(B,2);{var j=P=>{var F=k4(),X=r(F,!0);i(F),g(()=>f(X,u(G))),c(P,F)};p(U,P=>{u(G)&&P(j)})}var b=o(U,2),m=r(b),y=r(m),_=r(y,!0);i(y);var T=o(y,2);T.__change=[C4,G,R,K,a];var O=o(T,2),n4=r(O,!0);i(O);var s4=o(O,2);{var d4=P=>{var F=G4(),X=r(F);_4(X,"",{},{"max-height":"200px"}),i(F),g(()=>N(X,"src",u(K))),c(P,F)};p(s4,P=>{u(K)&&P(d4)})}i(m),i(b),g(()=>{f(_,u(a).pleaseSelectImage),T.disabled=u(k),f(n4,u(a).uploadImageMaxSize)}),c(E,S)},$$slots:{default:!0}})}};p(wu,e=>{s.data.canEdit&&e(i4)})}var l4=o(wu,2);{var o4=e=>{{let l=$(()=>u(J)?u(a).deleting:u(a).delete);pu(e,{get show(){return u(au)},get title(){return u(a).deleteImageTitle},onClose:Nu,onSubmit:zu,get submitText(){return u(l)},get cancelText(){return u(a).cancel},get submitDisabled(){return u(J)},get isSubmitting(){return u(J)},children:(d,E)=>{var M=W4(),S=uu(M);{var B=m=>{var y=q4(),_=r(y,!0);i(y),g(()=>f(_,u(V))),c(m,y)};p(S,m=>{u(V)&&m(B)})}var w=o(S,2);{var U=m=>{var y=H4(),_=r(y,!0);i(y),g(()=>f(_,u(Q))),c(m,y)};p(w,m=>{u(Q)&&m(U)})}var j=o(w,2),b=r(j,!0);i(j),g(()=>f(b,u(a).confirmDeleteImage)),c(d,M)},$$slots:{default:!0}})}};p(l4,e=>{s.data.canEdit&&n.image&&e(o4)})}g((e,l,d,E,M)=>{f(Ru,e),f(Yu,l),N(vu,"href",d),f(u4,E),f(e4,`${u(a).createdOn??""}:`),f(t4,M)},[()=>u(I)(n.name)||"Unknown Community",()=>u(I)(n.description)||"",()=>u(L)(`/users/${n.headUser.id}`),()=>u(I)(n.headUser.name),()=>h4(n.createdAt,u(C))]),c(A,_u),g4()}p4(["click","change"]);export{le as component,ie as universal};
