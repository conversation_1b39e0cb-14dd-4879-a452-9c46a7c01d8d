import{c as Ee}from"../chunks/CVTn1FV4.js";import{g as Ce}from"../chunks/CGZ87yZq.js";import"../chunks/Bzak7iHL.js";import{o as Xe}from"../chunks/DeAm3Eed.js";import{p as Ye,av as m,aw as W,f,h as $e,a as me,d as a,t as _,g as e,b as h,c as eu,ax as s,s as d,u as j,$ as uu,r as t}from"../chunks/RHWQbow4.js";import{d as ru,s as g}from"../chunks/BlWcudmi.js";import{i as E}from"../chunks/CtoItwj4.js";import{e as tu}from"../chunks/Dnfvvefi.js";import{r as au,s as U}from"../chunks/BdpLTtcP.js";import{b as su,M as iu,U as lu,e as ou,a as nu}from"../chunks/iI8NM7bJ.js";import{b as cu}from"../chunks/B5DcI8qy.js";import{r as du,g as vu}from"../chunks/CKnuo8tw.js";import{f as mu}from"../chunks/CL12WlkV.js";import"../chunks/BiLRrsV0.js";const hu=async({fetch:P,url:o})=>{const{fetcher:H}=Ce(),M=o.searchParams.get("search"),[p,S]=await Promise.all([H.user.me.get({fetch:P,ctx:{url:o}}),H.reactor.hub.list.get({query:M??void 0},{fetch:P,ctx:{url:o}})]);return{me:p,hubs:S,searchQuery:M,isHasMoreHubs:S.length===Ee.PAGE_SIZE}},Zu=Object.freeze(Object.defineProperty({__proto__:null,load:hu},Symbol.toStringTag,{value:"Module"}));function fu(P,o,H){const p=P.target.value;o(p),H(p)}function bu(P,o,H){s(o,!0),H()}var gu=f('<button class="btn btn-primary"> </button>'),_u=f('<div class="text-center py-5"><p class="text-muted"> </p></div>'),pu=f('<img class="hub-image svelte-si10zv"/>'),xu=f('<div class="hub-image-placeholder svelte-si10zv"><i class="bi bi-collection fs-1 text-muted"></i></div>'),Eu=f('<img class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;"/>'),Cu=f('<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;"><i class="bi bi-person-fill text-white"></i></div>'),yu=f('<div class="col-12"><div class="card shadow-sm h-100 svelte-si10zv"><div class="row g-0 h-100"><div class="col-md-3 col-lg-2"><div class="hub-image-container svelte-si10zv"><!></div></div> <div class="col-md-9 col-lg-10"><div class="card-body d-flex flex-column h-100 p-4"><div class="d-flex justify-content-between align-items-start mb-3"><h4 class="card-title mb-0 flex-grow-1"><a style="text-decoration: none;"> </a></h4> <small class="text-muted ms-3"> </small></div> <p class="card-text text-muted mb-3 flex-grow-1"> </p> <div class="d-flex align-items-center"><div class="me-3"><!></div> <div><a class="fw-medium" style="text-decoration: none;"> </a></div></div></div></div></div></div></div>'),Hu=f('<div class="row g-4"></div>'),Du=f('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden"> </span></div> <p class="text-muted mt-2 mb-0"> </p>',1),wu=f('<div class="text-center py-3"><!></div>'),Pu=f('<div class="alert alert-danger" role="alert"> </div>'),Fu=f('<div class="alert alert-danger mb-3"> </div>'),Bu=f('<div class="alert alert-success mb-3"> </div>'),Uu=f('<!> <!> <form><!> <div class="form-text mb-3"> </div> <!> <!></form>',1),Mu=f('<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4 gap-3"><h1 class="mb-0"> </h1> <div class="d-flex align-items-center gap-3"><div class="search-container svelte-si10zv"><input type="text" class="form-control svelte-si10zv" style="min-width: 250px;"/></div> <!></div></div> <!> <!> <!></div> <!>',1);function Vu(P,o){Ye(o,!0);const H={en:{_page:{title:"Hubs — Reactor of Commune"},hubs:"Hubs",createHub:"Create Hub",noHubs:"No hubs found",head:"Head",errorFetchingHubs:"Failed to fetch hubs",errorOccurred:"An error occurred while fetching hubs",loadingMore:"Loading more hubs...",createdOn:"Created on",createHubTitle:"Create New Hub",hubName:"Hub Name",hubDescription:"Hub Description",headUserPlaceholder:"Leave empty to use current user",hubNamePlaceholder:"Enter hub name",hubDescriptionPlaceholder:"Enter hub description",create:"Create",cancel:"Cancel",creating:"Creating...",hubCreatedSuccess:"Hub created successfully!",errorCreatingHub:"Failed to create hub",required:"This field is required",searchPlaceholder:"Search hubs..."},ru:{_page:{title:"Хабы — Реактор Коммуны"},hubs:"Хабы",createHub:"Создать хаб",noHubs:"Хабы не найдены",head:"Глава",errorFetchingHubs:"Не удалось загрузить хабы",errorOccurred:"Произошла ошибка при загрузке хабов",loadingMore:"Загружаем больше хабов...",createdOn:"Создан",createHubTitle:"Создать новый хаб",hubName:"Название хаба",hubDescription:"Описание хаба",headUserPlaceholder:"Оставьте пустым для использования текущего пользователя",hubNamePlaceholder:"Введите название хаба",hubDescriptionPlaceholder:"Введите описание хаба",create:"Создать",cancel:"Отмена",creating:"Создаём...",hubCreatedSuccess:"Хаб успешно создан!",errorCreatingHub:"Не удалось создать хаб",required:"Это поле обязательно",searchPlaceholder:"Поиск хабов..."}},{fetcher:M}=Ce(),p=j(()=>o.data.locale),S=j(()=>o.data.toLocaleHref),k=j(()=>o.data.getAppropriateLocalization),l=j(()=>H[e(p)]);let Q=m(W(o.data.hubs)),X=m(null),te=m(!1),ae=m(W(o.data.searchQuery||"")),R=m(null),G=m(!1),se=m(1),Y=m(W(o.data.isHasMoreHubs)),Z=m(null),T=m(!1),F=m(null),V=m(null),A=m(W([])),z=m(W([])),$=m(null);async function he(u,r=1,n=!1){n||s(G,!0),s(X,null);try{const i=await M.reactor.hub.list.get({pagination:{page:r},query:u.trim()||void 0});n?(s(Q,[...e(Q),...i],!0),s(se,r,!0)):(s(Q,i,!0),s(se,1)),s(Y,i.length===Ee.PAGE_SIZE)}catch(i){s(X,i instanceof Error?i.message:e(l).errorOccurred,!0),console.error(i)}finally{s(G,!1)}}async function ye(){e(G)||!e(Y)||await he(e(ae),e(se)+1,!0)}function He(u){const r=new URL(window.location.href);u.trim()?r.searchParams.set("search",encodeURIComponent(u)):r.searchParams.delete("search"),du(r.pathname+r.search,{})}function De(u){e(R)&&clearTimeout(e(R)),s(R,setTimeout(()=>{he(u)},1e3),!0)}function we(){s(te,!1)}function Pe(){s(A,[],!0),s(z,[],!0),s($,null),s(F,null),s(V,null),s(T,!1)}function Fe(){return e(A).some(u=>u.value.trim().length>0)?e(z).some(u=>u.value.trim().length>0)?!0:(s(F,e(l).required,!0),!1):(s(F,e(l).required,!0),!1)}async function Be(){var u;if(Fe()){s(T,!0),s(F,null),s(V,null);try{const{id:r}=await M.reactor.hub.post({headUserId:e($)||((u=o.data.user)==null?void 0:u.id)||"",name:e(A),description:e(z)});s(V,e(l).hubCreatedSuccess,!0),setTimeout(()=>{vu(e(S)(`/reactor/hubs/${r}`))},1500)}catch(r){s(F,r instanceof Error?r.message:e(l).errorCreatingHub,!0),console.error(r)}finally{s(T,!1)}}}function Ue(u,r=200){return u.length<=r?u:u.slice(0,r)+"..."}function Me(){if(!e(Z))return null;const u=new IntersectionObserver(r=>{r[0].isIntersecting&&e(Y)&&!e(G)&&ye()},{rootMargin:"100px",threshold:.1});return u.observe(e(Z)),u}Xe(()=>{let u=null;const r=()=>{u=Me()};return e(Z)?r():setTimeout(r,100),()=>{u==null||u.disconnect(),e(R)&&clearTimeout(e(R))}});var fe=Mu();$e(u=>{_(()=>uu.title=e(l)._page.title)});var ie=me(fe),le=a(ie),oe=a(le),Se=a(oe,!0);t(oe);var be=d(oe,2),ne=a(be),ee=a(ne);au(ee),ee.__input=[fu,He,De],t(ne);var Te=d(ne,2);{var Ae=u=>{var r=gu();r.__click=[bu,te,Pe];var n=a(r,!0);t(r),_(()=>g(n,e(l).createHub)),h(u,r)};E(Te,u=>{var r;((r=o.data.user)==null?void 0:r.role)==="admin"&&u(Ae)})}t(be),t(le);var ge=d(le,2);{var ze=u=>{var r=_u(),n=a(r),i=a(n,!0);t(n),t(r),_(()=>g(i,e(l).noHubs)),h(u,r)},Ie=u=>{var r=Hu();tu(r,21,()=>e(Q),n=>n.id,(n,i)=>{var x=yu(),D=a(x),C=a(D),w=a(C),B=a(w),I=a(B);{var N=v=>{var b=pu();_(K=>{U(b,"src",`/images/${e(i).image}`),U(b,"alt",K)},[()=>e(k)(e(i).name)||"Hub"]),h(v,b)},ue=v=>{var b=xu();h(v,b)};E(I,v=>{e(i).image?v(N):v(ue,!1)})}t(B),t(w);var O=d(w,2),re=a(O),L=a(re),J=a(L),c=a(J),y=a(c,!0);t(c),t(J);var q=d(J,2),ke=a(q);t(q),t(L);var ce=d(L,2),Qe=a(ce,!0);t(ce);var pe=d(ce,2),de=a(pe),Re=a(de);{var Ge=v=>{var b=Eu();_(K=>{U(b,"src",`/images/${e(i).headUser.image}`),U(b,"alt",K)},[()=>e(k)(e(i).headUser.name)]),h(v,b)},Ze=v=>{var b=Cu();h(v,b)};E(Re,v=>{e(i).headUser.image?v(Ge):v(Ze,!1)})}t(de);var xe=d(de,2),ve=a(xe),Ve=a(ve,!0);t(ve),t(xe),t(pe),t(re),t(O),t(C),t(D),t(x),_((v,b,K,Je,Ke,We)=>{U(c,"href",v),g(y,b),g(ke,`${e(l).createdOn??""}
                      ${K??""}`),g(Qe,Je),U(ve,"href",Ke),g(Ve,We)},[()=>e(S)(`/reactor/hubs/${e(i).id}`),()=>e(k)(e(i).name)||"No name?",()=>mu(e(i).createdAt,e(p)),()=>Ue(e(k)(e(i).description)||""),()=>e(S)(`/users/${e(i).headUser.id}`),()=>e(k)(e(i).headUser.name)]),h(n,x)}),t(r),h(u,r)};E(ge,u=>{e(Q).length===0?u(ze):u(Ie,!1)})}var _e=d(ge,2);{var Ne=u=>{var r=wu(),n=a(r);{var i=x=>{var D=Du(),C=me(D),w=a(C),B=a(w,!0);t(w),t(C);var I=d(C,2),N=a(I,!0);t(I),_(()=>{g(B,e(l).loadingMore),g(N,e(l).loadingMore)}),h(x,D)};E(n,x=>{e(G)&&x(i)})}t(r),cu(r,x=>s(Z,x),()=>e(Z)),h(u,r)};E(_e,u=>{e(Y)&&u(Ne)})}var Oe=d(_e,2);{var Le=u=>{var r=Pu(),n=a(r,!0);t(r),_(()=>g(n,e(X))),h(u,r)};E(Oe,u=>{e(X)&&u(Le)})}t(ie);var qe=d(ie,2);{var je=u=>{{let r=j(()=>e(T)?e(l).creating:e(l).create),n=j(()=>e(T)||!e(A).some(i=>i.value.trim().length>0)||!e(z).some(i=>i.value.trim().length>0));iu(u,{get show(){return e(te)},get title(){return e(l).createHubTitle},onClose:we,onSubmit:Be,get submitText(){return e(r)},get cancelText(){return e(l).cancel},get submitDisabled(){return e(n)},get isSubmitting(){return e(T)},children:(i,x)=>{var D=Uu(),C=me(D);{var w=c=>{var y=Fu(),q=a(y,!0);t(y),_(()=>g(q,e(F))),h(c,y)};E(C,c=>{e(F)&&c(w)})}var B=d(C,2);{var I=c=>{var y=Bu(),q=a(y,!0);t(y),_(()=>g(q,e(V))),h(c,y)};E(B,c=>{e(V)&&c(I)})}var N=d(B,2),ue=a(N);lu(ue,{get locale(){return e(p)},get label(){return e(l).head},get placeholder(){return e(l).headUserPlaceholder},get selectedUserId(){return e($)},set selectedUserId(c){s($,c,!0)}});var O=d(ue,2),re=a(O,!0);t(O);var L=d(O,2);ou(L,{get locale(){return e(p)},id:"hub-name",get label(){return e(l).hubName},get placeholder(){return e(l).hubNamePlaceholder},required:!0,get value(){return e(A)},set value(c){s(A,c,!0)}});var J=d(L,2);nu(J,{get locale(){return e(p)},id:"hub-description",get label(){return e(l).hubDescription},get placeholder(){return e(l).hubDescriptionPlaceholder},rows:4,required:!0,get value(){return e(z)},set value(c){s(z,c,!0)}}),t(N),_(()=>g(re,e(l).headUserPlaceholder)),h(i,D)},$$slots:{default:!0}})}};E(qe,u=>{var r;((r=o.data.user)==null?void 0:r.role)==="admin"&&u(je)})}_(()=>{g(Se,e(l).hubs),U(ee,"placeholder",e(l).searchPlaceholder)}),su(ee,()=>e(ae),u=>s(ae,u)),h(P,fe),eu()}ru(["input","click"]);export{Vu as component,Zu as universal};
