import{c as R}from"../chunks/CVTn1FV4.js";import{g as pe}from"../chunks/CGZ87yZq.js";import"../chunks/Bzak7iHL.js";import{p as Le,av as y,aw as ce,f,a as ie,s,b,c as Ne,ax as t,d as i,r as a,g as e,ay as De,u as Ge,t as C,aQ as Q}from"../chunks/RHWQbow4.js";import{d as Ze,s as z,e as je}from"../chunks/BlWcudmi.js";import{i as F}from"../chunks/CtoItwj4.js";import{e as Ce}from"../chunks/Dnfvvefi.js";import{r as me}from"../chunks/BdpLTtcP.js";import{s as se}from"../chunks/Cxg-bych.js";import{M as ze,b as ue}from"../chunks/iI8NM7bJ.js";import{b as Fe}from"../chunks/Np2weedy.js";import"../chunks/BiLRrsV0.js";const Oe=async({fetch:I,url:v})=>{const{fetcher:d}=pe(),r=await d.user.invite.list.get({pagination:{page:1,size:R.PAGE_SIZE}},{fetch:I,ctx:{url:v}});return{invites:r,isHasMoreInvites:r.length===R.PAGE_SIZE}},bt=Object.freeze(Object.defineProperty({__proto__:null,load:Oe},Symbol.toStringTag,{value:"Module"}));async function qe(I,v,d,r,h){if(!(e(v)||!e(d))){t(v,!0);try{const x=await r.user.invite.list.get({pagination:{page:Math.floor(e(h).length/R.PAGE_SIZE)+1,size:R.PAGE_SIZE}});t(h,[...e(h),...x],!0),t(d,x.length===R.PAGE_SIZE)}catch(x){console.error("Error loading more invites:",x)}finally{t(v,!1)}}}function be(I,v,d,r,h,x,G){t(v,!0),t(d,""),t(r,""),t(h,"ru"),t(x,null),t(G,null)}function _e(I,v,d,r,h){I.key==="Enter"&&!e(v)&&e(d).trim()&&r.test(e(d).trim())&&(I.preventDefault(),h())}var He=f('<div class="text-center py-5"><div class="mb-3"><i class="bi bi-envelope display-1 text-muted"></i></div> <h4 class="text-muted">No invitations yet</h4> <p class="text-muted">Start by sending your first user invitation.</p> <button type="button" class="btn btn-primary"><i class="bi bi-plus-circle me-1"></i> Send First Invite</button></div>'),Ke=f('<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i> Used</span>'),Qe=f('<span class="badge bg-warning text-dark"><i class="bi bi-clock me-1"></i> Pending</span>'),Re=(I,v,d)=>v(e(d).id),Be=f('<tr><td><div class="d-flex align-items-center"><i class="bi bi-envelope me-2 text-muted svelte-17chgb9"></i> <span> </span></div></td><td><div class="d-flex align-items-center"><i class="bi bi-person me-2 text-muted svelte-17chgb9"></i> <span> </span></div></td><td><div class="d-flex align-items-center"><i class="bi bi-globe me-2 text-muted svelte-17chgb9"></i> <span class="badge bg-light text-dark border"> </span></div></td><td><!></td><td><button type="button" class="btn btn-sm btn-outline-danger" aria-label="Delete invitation"><i class="bi bi-trash"></i></button></td></tr>'),Je=f('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Loading...',1),Ve=f('<i class="bi bi-arrow-down-circle me-1"></i> Load More',1),We=f('<div class="text-center mt-4"><button type="button" class="btn btn-outline-primary"><!></button></div>'),Xe=f('<div class="card"><div class="card-header"><h5 class="mb-0"><i class="bi bi-list-ul me-2"></i> </h5></div> <div class="card-body p-0"><div class="table-responsive"><table class="table table-hover mb-0"><thead class="table-light"><tr><th scope="col">Email</th><th scope="col">Name</th><th scope="col">Language</th><th scope="col">Status</th><th scope="col">Actions</th></tr></thead><tbody></tbody></table></div></div></div> <!>',1),Ye=f('<div class="alert alert-success" role="alert"><i class="bi bi-check-circle me-2"></i> </div>'),$e=f('<div class="alert alert-danger" role="alert"><i class="bi bi-exclamation-triangle me-2"></i> </div>'),et=f(`<form><div class="mb-3"><label for="inviteEmail" class="form-label">Email Address *</label> <input type="email" class="form-control" id="inviteEmail" placeholder="Enter email address" required/> <div class="form-text">The user will receive an invitation to join the platform.</div></div> <div class="mb-3"><label for="inviteName" class="form-label">Name (Optional)</label> <input type="text" class="form-control" id="inviteName" placeholder="Enter user's name"/> <div class="form-text">This name will be used in the invitation email.</div></div> <div class="mb-3"><label for="inviteLocale" class="form-label">Language *</label> <select class="form-select" id="inviteLocale"><option>English</option><option>Русский</option></select> <div class="form-text">The language for the invitation email.</div></div> <!></form>`),tt=f('<div class="admin-invites svelte-17chgb9"><div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"><h1 class="h2">User Invites</h1> <button type="button" class="btn btn-primary"><i class="bi bi-plus-circle me-1"></i> Send Invite</button></div> <!></div> <!>',1);function _t(I,v){Le(v,!0);const{fetcher:d}=pe();let r=y(ce(v.data.invites)),h=y(ce(v.data.isHasMoreInvites)),x=y(!1),G=y(!1),c=y(""),M=y(""),U=y("ru"),E=y(!1),w=y(null),A=y(null);const B=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;async function J(){if(!e(c).trim()){t(w,"Email is required");return}if(!B.test(e(c).trim())){t(w,"Please enter a valid email address");return}t(E,!0),t(w,null),t(A,null);try{const n=await d.user.invite.put({email:e(c).trim(),name:e(M).trim()||null,locale:e(U)}),m=e(r).findIndex(P=>P.email===e(c).trim()),k={id:n.id,email:e(c).trim(),name:e(M).trim()||null,locale:e(U),isUsed:!1};m>=0?e(r)[m]=k:t(r,[k,...e(r)],!0),t(A,"Invitation sent successfully!"),t(c,""),t(M,""),t(U,"ru"),setTimeout(()=>{t(G,!1),t(A,null)},1500)}catch(n){t(w,n instanceof Error?n.message:"Failed to send invitation",!0),console.error("Error sending invitation:",n)}finally{t(E,!1)}}async function fe(n){if(confirm("Are you sure you want to delete this invitation?"))try{await d.user.invite.delete({id:n}),t(r,e(r).filter(m=>m.id!==n),!0)}catch(m){console.error("Error deleting invitation:",m),alert("Failed to delete invitation")}}function ge(){t(G,!1),t(c,""),t(M,""),t(U,"ru"),t(w,null),t(A,null)}var re=tt(),X=ie(re),Y=i(X),he=s(i(Y),2);he.__click=[be,G,c,M,U,w,A],a(Y);var xe=s(Y,2);{var ye=n=>{var m=He(),k=s(i(m),6);k.__click=[be,G,c,M,U,w,A],a(m),b(n,m)},Ie=n=>{var m=Xe(),k=ie(m),P=i(k),V=i(P),$=s(i(V));a(V),a(P);var W=s(P,2),S=i(W),g=i(S),T=s(i(g));Ce(T,21,()=>e(r),_=>_.email,(_,o)=>{var u=Be();let L;var N=i(u),j=i(N),p=s(i(j),2);let l;var q=i(p,!0);a(p),a(j),a(N);var H=s(N),le=i(H),ee=s(i(le),2);let ne;var we=i(ee,!0);a(ee),a(le),a(H);var te=s(H),oe=i(te),ve=s(i(oe),2),ke=i(ve,!0);a(ve),a(oe),a(te);var ae=s(te),Se=i(ae);{var Me=D=>{var K=Ke();b(D,K)},Ue=D=>{var K=Qe();b(D,K)};F(Se,D=>{e(o).isUsed?D(Me):D(Ue,!1)})}a(ae);var de=s(ae),Ae=i(de);Ae.__click=[Re,fe,o],a(de),a(u),C((D,K,Pe,Te)=>{L=se(u,1,"svelte-17chgb9",null,L,D),l=se(p,1,"svelte-17chgb9",null,l,K),z(q,e(o).email),ne=se(ee,1,"svelte-17chgb9",null,ne,Pe),z(we,e(o).name||"—"),z(ke,Te)},[()=>({"table-secondary":e(o).isUsed}),()=>({"text-muted":e(o).isUsed}),()=>({"text-muted":e(o).isUsed}),()=>e(o).locale.toUpperCase()]),b(_,u)}),a(T),a(g),a(S),a(W),a(k);var Z=s(k,2);{var O=_=>{var o=We(),u=i(o);u.__click=[qe,x,h,d,r];var L=i(u);{var N=p=>{var l=Je();Q(),b(p,l)},j=p=>{var l=Ve();Q(),b(p,l)};F(L,p=>{e(x)?p(N):p(j,!1)})}a(u),a(o),C(()=>u.disabled=e(x)),b(_,o)};F(Z,_=>{e(h)&&_(O)})}C(()=>z($,` All Invitations (${e(r).length??""})`)),b(n,m)};F(xe,n=>{e(r).length===0?n(ye):n(Ie,!1)})}a(X);var Ee=s(X,2);{let n=Ge(()=>e(E)||!e(c).trim()||!B.test(e(c).trim()));ze(Ee,{get show(){return e(G)},title:"Send User Invitation",onClose:ge,onSubmit:J,submitText:"Send Invite",cancelText:"Cancel",get submitDisabled(){return e(n)},get isSubmitting(){return e(E)},children:(m,k)=>{var P=De(),V=ie(P);{var $=S=>{var g=Ye(),T=s(i(g));a(g),C(()=>z(T,` ${e(A)??""}`)),b(S,g)},W=S=>{var g=et(),T=i(g),Z=s(i(T),2);me(Z),Z.__keydown=[_e,E,c,B,J],Q(2),a(T);var O=s(T,2),_=s(i(O),2);me(_),_.__keydown=[_e,E,c,B,J],Q(2),a(O);var o=s(O,2),u=s(i(o),2),L=i(u);L.value=L.__value="en";var N=s(L);N.value=N.__value="ru",a(u),Q(2),a(o);var j=s(o,2);{var p=l=>{var q=$e(),H=s(i(q));a(q),C(()=>z(H,` ${e(w)??""}`)),b(l,q)};F(j,l=>{e(w)&&l(p)})}a(g),C(()=>{Z.disabled=e(E),_.disabled=e(E),u.disabled=e(E)}),je("submit",g,l=>{l.preventDefault(),J()}),ue(Z,()=>e(c),l=>t(c,l)),ue(_,()=>e(M),l=>t(M,l)),Fe(u,()=>e(U),l=>t(U,l)),b(S,g)};F(V,S=>{e(A)?S($):S(W,!1)})}b(m,P)},$$slots:{default:!0}})}b(I,re),Ne()}Ze(["click","keydown"]);export{_t as component,bt as universal};
