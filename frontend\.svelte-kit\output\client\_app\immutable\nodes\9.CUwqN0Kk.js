import{c as fe}from"../chunks/CVTn1FV4.js";import{g as le}from"../chunks/CGZ87yZq.js";import"../chunks/Bzak7iHL.js";import{o as Ae}from"../chunks/DeAm3Eed.js";import{p as _e,av as b,aw as ne,c as Ce,f,a as me,s as m,ax as s,g as e,d as t,b as p,u as H,r as u,t as C,h as we,$ as Be,aQ as Me}from"../chunks/RHWQbow4.js";import{e as Ie,s as i,d as Ne}from"../chunks/BlWcudmi.js";import{i as B}from"../chunks/CtoItwj4.js";import{e as Le}from"../chunks/Dnfvvefi.js";import{s as ee}from"../chunks/BdpLTtcP.js";import{b as Se}from"../chunks/B5DcI8qy.js";import{g as Pe}from"../chunks/CKnuo8tw.js";import{p as qe}from"../chunks/C_wziyCN.js";import{M as He,e as je,a as Oe}from"../chunks/iI8NM7bJ.js";import"../chunks/BiLRrsV0.js";const Re=async({fetch:M,url:o})=>{const{fetcher:j}=le(),[z,l]=await Promise.all([j.user.me.get({fetch:M,ctx:{url:o}}),j.commune.list.get({},{fetch:M,ctx:{url:o}})]);let g=0;return z&&(g=(await j.commune.invitation.list.get({},{fetch:M,ctx:{url:o}})).filter(({status:r})=>r==="pending").length),{communes:l,isHasMoreCommunes:l.length===fe.PAGE_SIZE,pendingInvitationsCount:g,isLoggedIn:!!z}},fu=Object.freeze(Object.defineProperty({__proto__:null,load:Re},Symbol.toStringTag,{value:"Module"}));var Te=f('<div class="alert alert-success mb-3"> </div>'),ze=f('<div class="alert alert-danger mb-3"> </div>'),Je=f("<!> <!> <form><!> <!></form>",1);function ke(M,o){_e(o,!0);const j={en:{createNewCommune:"Create New Commune",communeCreatedSuccess:"Commune created successfully!",name:"Name",enterCommuneName:"Enter commune name",description:"Description (optional)",enterCommuneDescription:"Enter commune description",cancel:"Cancel",create:"Create",creating:"Creating...",provideName:"Please provide a name for the commune.",failedToCreate:"Failed to create commune",unexpectedError:"An unexpected error occurred. Please try again."},ru:{createNewCommune:"Создать новую коммуну",communeCreatedSuccess:"Коммуна успешно создана!",name:"Название",enterCommuneName:"Введите название коммуны",description:"Описание (опционально)",enterCommuneDescription:"Введите описание коммуны",cancel:"Отмена",create:"Создать",creating:"Создание...",provideName:"Пожалуйста, укажите название коммуны.",failedToCreate:"Не удалось создать коммуну",unexpectedError:"Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова."}},{fetcher:z}=le(),l=H(()=>j[o.locale]);let g=b(ne([])),D=b(ne([])),r=b(""),x=b(!1),I=b(!1);function Q(){o.onHide(),s(g,[],!0),s(D,[],!0),s(r,""),s(I,!1)}async function N(){if(s(r,""),!e(g).some(E=>E.value.trim().length)){s(r,e(l).provideName,!0);return}s(x,!0);try{const{id:E}=await z.commune.post({name:e(g),description:e(D)});s(I,!0),setTimeout(()=>{Pe(o.toLocaleHref(`/communes/${E}`))},1500)}catch(E){console.error("Error creating commune:",E),s(r,e(l).unexpectedError,!0)}finally{s(x,!1)}}{let E=H(()=>e(x)?e(l).creating:e(l).create),J=H(()=>!e(g).some(y=>y.value.trim().length)||e(x));He(M,{get show(){return o.show},get title(){return e(l).createNewCommune},onClose:Q,onSubmit:N,get submitText(){return e(E)},get cancelText(){return e(l).cancel},get submitDisabled(){return e(J)},get cancelDisabled(){return e(x)},get isSubmitting(){return e(x)},children:(y,ce)=>{var ue=Je(),k=me(ue);{var K=v=>{var _=Te(),V=t(_,!0);u(_),C(()=>i(V,e(l).communeCreatedSuccess)),p(v,_)};B(k,v=>{e(I)&&v(K)})}var G=m(k,2);{var oe=v=>{var _=ze(),V=t(_,!0);u(_),C(()=>i(V,e(r))),p(v,_)};B(G,v=>{e(r)&&v(oe)})}var Z=m(G,2),te=H(()=>qe(N)),ae=t(Z);je(ae,{id:"communeName",get label(){return e(l).name},get placeholder(){return e(l).enterCommuneName},required:!0,get locale(){return o.locale},get value(){return e(g)},set value(v){s(g,v,!0)}});var U=m(ae,2);Oe(U,{id:"communeDescription",get label(){return e(l).description},get placeholder(){return e(l).enterCommuneDescription},rows:4,get locale(){return o.locale},get value(){return e(D)},set value(v){s(D,v,!0)}}),u(Z),Ie("submit",Z,function(...v){var _;(_=e(te))==null||_.apply(this,v)}),p(y,ue)},$$slots:{default:!0}})}Ce()}function Ge(M,o){s(o,!0)}var Ze=f('<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"> <span class="visually-hidden">pending invitations</span></span>'),Qe=f('<a class="btn btn-outline-info position-relative"><span class="d-none d-sm-inline"> </span> <span class="d-sm-none"> </span> <!></a> <a class="btn btn-outline-secondary ms-sm-4"><span class="d-none d-sm-inline"> </span> <span class="d-sm-none"> </span></a>',1),Ke=f('<div class="text-center py-5"><p class="text-muted"> </p></div>'),Ue=f('<div class="image-container svelte-wfgbe0"><img class="svelte-wfgbe0"/></div>'),Ve=f('<div class="bg-light text-center d-flex align-items-center justify-content-center" style="height: 140px;"><span class="text-muted"> </span></div>'),We=f('<div class="col"><div class="card h-100 shadow-sm hover-card svelte-wfgbe0"><a class="text-decoration-none text-black"><!> <div class="card-body d-flex flex-column"><h5 class="card-title fs-5 text-truncate"> </h5> <p class="card-text text-muted small" style="height: 3rem; overflow: hidden"> </p> <div class="mt-auto"><span class="badge bg-primary mb-2"> </span> <div class="small text-muted"><div> </div> <div class="d-flex flex-column"> </div></div></div></div></a></div></div>'),Xe=f('<div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-4"></div>'),Ye=f('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden"> </span></div> <p class="text-muted mt-2 mb-0"> </p>',1),$e=f('<div class="text-center py-3"><!></div>'),eu=f('<div class="alert alert-danger" role="alert"> </div>'),uu=f('<div class="container my-4 mb-5"><div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center my-4 gap-3"><h1 class="mb-0"> </h1> <div class="d-flex flex-column flex-sm-row align-items-center gap-2"><!> <button class="btn btn-primary ms-sm-4"> </button></div></div> <!> <!> <!> <!></div>');function _u(M,o){_e(o,!0);const j={en:{_page:{title:"Communes — Commune"},communes:"Communes",create:"Create",invitations:"Invitations",joinRequests:"Join Requests",myInvitations:"My Invitations",myJoinRequests:"My Join Requests",loading:"Loading...",noCommunes:"No communes found",member:"member",members:"members",headMember:"Head",errorFetchingCommunes:"Failed to fetch communes",errorOccurred:"An error occurred while fetching communes",first:"First",previous:"Previous",next:"Next",last:"Last",page:"Page",loadingMore:"Loading more communes...",noImage:"No image",communeImageAlt:"Commune image"},ru:{_page:{title:"Коммуны — Коммуна"},communes:"Коммуны",create:"Создать",invitations:"Приглашения",joinRequests:"Заявки",myInvitations:"Мои приглашения",myJoinRequests:"Мои заявки",loading:"Загрузка...",noCommunes:"Коммуны не найдены",member:"участник",members:"участников",headMember:"Глава",errorFetchingCommunes:"Не удалось загрузить коммуны",errorOccurred:"Произошла ошибка при загрузке коммун",first:"Первая",previous:"Предыдущая",next:"Следующая",last:"Последняя",page:"Страница",loadingMore:"Загружаем больше коммун...",noImage:"Нет изображения",communeImageAlt:"Изображение коммуны"}},{fetcher:z}=le(),l=H(()=>o.data.locale),g=H(()=>o.data.toLocaleHref),D=H(()=>o.data.getAppropriateLocalization),r=H(()=>j[e(l)]);let x=b(ne(o.data.communes)),I=b(null),Q=b(!1),N=b(!1),E=b(1),J=b(ne(o.data.isHasMoreCommunes)),y=b(null);async function ce(){if(!(e(N)||!e(J))){s(N,!0),s(I,null);try{const a=e(E)+1,n=await z.commune.list.get({pagination:{page:a}});s(x,[...e(x),...n],!0),s(E,a),s(J,n.length===fe.PAGE_SIZE)}catch(a){s(I,a instanceof Error?a.message:e(r).errorOccurred,!0),console.error(a)}finally{s(N,!1)}}}function ue(){s(Q,!1)}Ae(()=>{let a;const n=()=>{e(y)&&(a=new IntersectionObserver(c=>{c[0].isIntersecting&&e(J)&&!e(N)&&ce()},{rootMargin:"100px",threshold:.1}),a.observe(e(y)))};return e(y)?n():setTimeout(n,100),()=>{a&&a.disconnect()}});var k=uu();we(a=>{C(()=>Be.title=e(r)._page.title)});var K=t(k),G=t(K),oe=t(G,!0);u(G);var Z=m(G,2),te=t(Z);{var ae=a=>{var n=Qe(),c=me(n),d=t(c),h=t(d,!0);u(d);var F=m(d,2),A=t(F,!0);u(F);var O=m(F,2);{var W=R=>{var P=Ze(),$=t(P);Me(),u(P),C(()=>i($,`${o.data.pendingInvitationsCount??""} `)),p(R,P)};B(O,R=>{o.data.pendingInvitationsCount>0&&R(W)})}u(c);var L=m(c,2),S=t(L),X=t(S,!0);u(S);var re=m(S,2),Y=t(re,!0);u(re),u(L),C((R,P)=>{ee(c,"href",R),i(h,e(r).myInvitations),i(A,e(r).invitations),ee(L,"href",P),i(X,e(r).myJoinRequests),i(Y,e(r).joinRequests)},[()=>e(g)("/communes/invitations"),()=>e(g)("/communes/join-requests")]),p(a,n)};B(te,a=>{o.data.isLoggedIn&&a(ae)})}var U=m(te,2);U.__click=[Ge,Q];var v=t(U,!0);u(U),u(Z),u(K);var _=m(K,2);{var V=a=>{var n=Ke(),c=t(n),d=t(c,!0);u(c),u(n),C(()=>i(d,e(r).noCommunes)),p(a,n)},be=a=>{var n=Xe();Le(n,21,()=>e(x),c=>c.id,(c,d)=>{var h=We(),F=t(h),A=t(F),O=t(A);{var W=q=>{var w=Ue(),T=t(w);u(w),C(()=>{ee(T,"src",`/images/${e(d).image}`),ee(T,"alt",`${e(r).communeImageAlt}`)}),p(q,w)},L=q=>{var w=Ve(),T=t(w),ie=t(T,!0);u(T),u(w),C(()=>i(ie,e(r).noImage)),p(q,w)};B(O,q=>{e(d).image?q(W):q(L,!1)})}var S=m(O,2),X=t(S),re=t(X,!0);u(X);var Y=m(X,2),R=t(Y,!0);u(Y);var P=m(Y,2),$=t(P),De=t($);u($);var ge=m($,2),se=t(ge),ye=t(se);u(se);var pe=m(se,2),Fe=t(pe,!0);u(pe),u(ge),u(P),u(S),u(A),u(F),u(h),C((q,w,T,ie)=>{ee(A,"href",q),i(re,w),i(R,T),i(De,`${e(d).memberCount??""}
                    ${(e(d).memberCount===1?e(r).member:e(r).members)??""}`),i(ye,`${e(r).headMember??""}:`),i(Fe,ie)},[()=>e(g)(`/communes/${e(d).id}`),()=>e(D)(e(d).name),()=>e(D)(e(d).description)||"",()=>e(D)(e(d).headMember.name)]),p(c,h)}),u(n),p(a,n)};B(_,a=>{e(x).length===0?a(V):a(be,!1)})}var de=m(_,2);{var xe=a=>{var n=$e(),c=t(n);{var d=h=>{var F=Ye(),A=me(F),O=t(A),W=t(O,!0);u(O),u(A);var L=m(A,2),S=t(L,!0);u(L),C(()=>{i(W,e(r).loadingMore),i(S,e(r).loadingMore)}),p(h,F)};B(c,h=>{e(N)&&h(d)})}u(n),Se(n,h=>s(y,h),()=>e(y)),p(a,n)};B(de,a=>{e(J)&&a(xe)})}var ve=m(de,2);{var he=a=>{var n=eu(),c=t(n,!0);u(n),C(()=>i(c,e(I))),p(a,n)};B(ve,a=>{e(I)&&a(he)})}var Ee=m(ve,2);ke(Ee,{get locale(){return e(l)},get show(){return e(Q)},onHide:ue,get toLocaleHref(){return e(g)}}),u(k),C(()=>{i(oe,e(r).communes),i(v,e(r).create)}),p(M,k),Ce()}Ne(["click"]);export{_u as component,fu as universal};
