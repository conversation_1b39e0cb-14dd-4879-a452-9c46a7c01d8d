import { O as copy_payload, P as assign_payload, w as pop, u as push, z as escape_html, y as attr, F as attr_style, x as head, G as attr_class } from "../../../../../chunks/index.js";
import "@sveltejs/kit/internal";
import "../../../../../chunks/exports.js";
import "../../../../../chunks/state.svelte.js";
import { g as getClient } from "../../../../../chunks/acrpc.js";
import { a as consts_exports } from "../../../../../chunks/current-user.js";
import "@formatjs/intl-localematcher";
import "@sveltejs/kit";
import { f as formatDate } from "../../../../../chunks/format-date.js";
import "clsx";
import { M as Modal } from "../../../../../chunks/modal.js";
import { L as Localized_input } from "../../../../../chunks/localized-input.js";
import { L as Localized_textarea } from "../../../../../chunks/localized-textarea.js";
/* empty css                                                                           */
import { f as fetchWithAuth } from "../../../../../chunks/fetch-with-auth.js";
import "../../../../../chunks/schema.js";
function Edit_profile_modal($$payload, $$props) {
  push();
  const i18n = {
    en: {
      editProfile: "Edit Profile",
      name: { label: "Name", placeholder: "Enter your name" },
      description: {
        label: "Description (optional)",
        placeholder: "Tell us about yourself"
      },
      saveChanges: "Save Changes",
      cancel: "Cancel",
      saving: "Saving...",
      nameRequired: "Name is required",
      failedToUpdateProfile: "Failed to update profile",
      profileUpdatedSuccessfully: "Profile updated successfully",
      errorOccurred: "An error occurred while updating profile"
    },
    ru: {
      editProfile: "Редактировать профиль",
      name: {
        label: "Имя",
        placeholder: "Введите ваше имя"
      },
      description: {
        label: "Описание (необязательно)",
        placeholder: "Расскажите о себе"
      },
      saveChanges: "Сохранить изменения",
      cancel: "Отменить",
      saving: "Сохранение...",
      nameRequired: "Имя обязательно",
      failedToUpdateProfile: "Не удалось обновить профиль",
      profileUpdatedSuccessfully: "Профиль обновлен успешно",
      errorOccurred: "Произошла ошибка при обновлении профиля"
    }
  };
  const { fetcher: api } = getClient();
  const { locale, show, onHide, onProfileUpdated, userData } = $$props;
  const t = i18n[locale];
  let error = "";
  let isSubmitting = false;
  let submitSuccess = false;
  let name = userData?.name || [];
  let description = userData?.description || [];
  const handleSubmit = async () => {
    if (!name.some((item) => item.value.trim().length)) {
      error = t.nameRequired;
      return;
    }
    isSubmitting = true;
    error = "";
    try {
      await api.user.patch({ id: userData.id, name, description });
      submitSuccess = true;
      setTimeout(
        () => {
          handleClose();
          onProfileUpdated();
        },
        1500
      );
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  };
  const handleClose = () => {
    error = "";
    submitSuccess = false;
    onHide();
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Modal($$payload2, {
      show,
      title: t.editProfile,
      onClose: handleClose,
      onSubmit: handleSubmit,
      submitText: isSubmitting ? t.saving : t.saveChanges,
      cancelText: t.cancel,
      submitDisabled: !name.some((item) => item.value.trim().length) || isSubmitting,
      cancelDisabled: isSubmitting,
      isSubmitting,
      children: ($$payload3) => {
        if (submitSuccess) {
          $$payload3.out.push("<!--[-->");
          $$payload3.out.push(`<div class="alert alert-success mb-3">${escape_html(t.profileUpdatedSuccessfully)}</div>`);
        } else {
          $$payload3.out.push("<!--[!-->");
        }
        $$payload3.out.push(`<!--]--> `);
        if (error) {
          $$payload3.out.push("<!--[-->");
          $$payload3.out.push(`<div class="alert alert-danger mb-3">${escape_html(error)}</div>`);
        } else {
          $$payload3.out.push("<!--[!-->");
        }
        $$payload3.out.push(`<!--]--> <form>`);
        Localized_input($$payload3, {
          id: "profileName",
          label: t.name.label,
          placeholder: t.name.placeholder,
          required: true,
          locale,
          get value() {
            return name;
          },
          set value($$value) {
            name = $$value;
            $$settled = false;
          }
        });
        $$payload3.out.push(`<!----> `);
        Localized_textarea($$payload3, {
          id: "profileDescription",
          label: t.description.label,
          placeholder: t.description.placeholder,
          rows: 4,
          locale,
          get value() {
            return description;
          },
          set value($$value) {
            description = $$value;
            $$settled = false;
          }
        });
        $$payload3.out.push(`<!----></form>`);
      }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
function Upload_image_modal($$payload, $$props) {
  push();
  const MAX_FILE_SIZE_MB = consts_exports.MAX_IMAGE_FILE_SIZE / (1024 * 1024);
  const i18n = {
    en: {
      uploadImage: "Upload Profile Image",
      upload: "Upload",
      cancel: "Cancel",
      uploading: "Uploading...",
      imageUploadedSuccessfully: "Image uploaded successfully!",
      pleaseSelectImage: "Please select an image to upload",
      invalidFileTypeError: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: `File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`,
      failedToUploadImage: "Failed to upload image",
      errorOccurred: "An error occurred while uploading the image",
      uploadImageMaxSize: `Upload an image (JPG, PNG, WebP), max ${MAX_FILE_SIZE_MB}MB.`
    },
    ru: {
      uploadImage: "Загрузить изображение профиля",
      upload: "Загрузить",
      cancel: "Отменить",
      uploading: "Загрузка...",
      imageUploadedSuccessfully: "Изображение загружено успешно!",
      pleaseSelectImage: "Пожалуйста, выберите изображение для загрузки",
      invalidFileTypeError: "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: `Файл слишком большой. Максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      failedToUploadImage: "Не удалось загрузить изображение",
      errorOccurred: "Произошла ошибка при загрузке изображения",
      uploadImageMaxSize: `Загрузите изображение (JPG, PNG, WebP), максимальный размер - ${MAX_FILE_SIZE_MB}MB.`
    }
  };
  const { locale, show, onHide, userId, onImageUploaded } = $$props;
  const t = i18n[locale];
  let selectedFile = null;
  let previewUrl = null;
  let error = "";
  let isSubmitting = false;
  let submitSuccess = false;
  const handleSubmit = async () => {
    if (!selectedFile) {
      error = t.pleaseSelectImage;
      return;
    }
    isSubmitting = true;
    error = "";
    try {
      const formData = new FormData();
      formData.append("image", selectedFile);
      const response = await fetchWithAuth(`/api/user/${userId}/image`, {
        method: "PUT",
        body: formData
        // Don't set Content-Type header, it will be set automatically with the boundary
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.failedToUploadImage);
      }
      submitSuccess = true;
      onImageUploaded();
      const imageInput = document.getElementById("imageInput");
      if (imageInput) {
        imageInput.files = null;
      }
      setTimeout(
        () => {
          handleClose();
        },
        1500
      );
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  };
  const handleClose = () => {
    selectedFile = null;
    previewUrl = null;
    error = "";
    submitSuccess = false;
    onHide();
  };
  Modal($$payload, {
    show,
    title: t.uploadImage,
    onClose: handleClose,
    onSubmit: handleSubmit,
    submitText: isSubmitting ? t.uploading : t.upload,
    cancelText: t.cancel,
    submitDisabled: !selectedFile || isSubmitting,
    cancelDisabled: isSubmitting,
    isSubmitting,
    size: "lg",
    children: ($$payload2) => {
      if (submitSuccess) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div class="alert alert-success mb-3">${escape_html(t.imageUploadedSuccessfully)}</div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (error) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div class="alert alert-danger mb-3">${escape_html(error)}</div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> <form><div class="mb-3"><label for="imageInput" class="form-label">${escape_html(t.pleaseSelectImage)}</label> <input id="imageInput" type="file" class="form-control" accept=".jpg,.jpeg,.png,.webp"${attr("disabled", isSubmitting, true)}/> <p class="form-text text-muted">${escape_html(t.uploadImageMaxSize)}</p> `);
      if (previewUrl) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div class="mt-3 text-center"><img${attr("src", previewUrl)} alt="Preview" class="img-thumbnail"${attr_style("", { "max-height": "200px" })}/></div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--></div></form>`);
    }
  });
  pop();
}
function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Profile — Commune" },
      loading: "Loading...",
      loadingProfile: "Loading your profile...",
      uploadImage: "Upload profile image",
      admin: "Administrator",
      user: "User",
      editProfile: "Edit Profile",
      signOut: "Sign Out",
      joined: "Joined",
      accountSummary: "Account Summary",
      accountType: {
        title: "Account Type",
        values: { admin: "Administrator", moderator: "Moderator", user: "User" }
      },
      daysAsMember: "Days as member",
      aboutMe: "About Me",
      noDescription: "No description available yet. Add one to tell others about yourself.",
      addDescription: "Add Description",
      dateFormatLocale: "en-US"
    },
    ru: {
      _page: {
        title: "Профиль — Коммуна"
      },
      loading: "Загрузка...",
      loadingProfile: "Загрузка вашего профиля...",
      uploadImage: "Загрузить изображение профиля",
      admin: "Администратор",
      user: "Пользователь",
      editProfile: "Редактировать профиль",
      signOut: "Выйти",
      joined: "Присоединился",
      accountSummary: "Информация об аккаунте",
      accountType: {
        title: "Тип аккаунта",
        values: {
          admin: "Администратор",
          moderator: "Модератор",
          user: "Пользователь"
        }
      },
      daysAsMember: "Дней в качестве участника",
      aboutMe: "Обо мне",
      noDescription: "Нет описания. Добавьте описание, чтобы рассказать другим о себе.",
      addDescription: "Добавить описание",
      dateFormatLocale: "ru-RU"
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const { locale, getAppropriateLocalization } = data;
  const t = i18n[locale];
  let user = data.me;
  let showEditModal = false;
  let showUploadModal = false;
  const name = getAppropriateLocalization(user.name);
  const description = getAppropriateLocalization(user.description);
  const joinDate = new Date(user.createdAt);
  function refresh() {
    window.location.reload();
  }
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;
  });
  $$payload.out.push(`<div class="container-fluid py-4"><div class="row g-4"><div class="col-lg-3"><div class="card border-0 shadow-sm h-100"><div class="text-center p-4"><div class="position-relative mx-auto mb-3"${attr_style("", { width: "120px", height: "120px" })}><div class="bg-light rounded-circle overflow-hidden border" role="img"${attr_style("", { width: "100%", height: "100%" })}><div class="position-relative w-100 h-100"><img${attr("src", user.image ? `/images/${user.image}` : "/images/default-avatar.png")}${attr("alt", `${name}'s avatar`)}${attr("width", 120)}${attr("height", 120)}${attr_style("", { width: "100%", height: "100%", "object-fit": "cover" })}/></div></div> <button class="position-absolute bottom-0 end-0 bg-primary text-white rounded-circle p-1 border-0"${attr("title", t.uploadImage)}${attr("aria-label", t.uploadImage)}${attr_style("", {
    width: "30px",
    height: "30px",
    display: "flex",
    "align-items": "center",
    "justify-content": "center"
  })}><i class="bi bi-plus"></i></button></div> <h4 class="fw-bold mb-1">${escape_html(name)}</h4> `);
  if (user.role === "admin") {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<span${attr_class(`badge bg-danger mb-3`)}>${escape_html(t.admin)}</span>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="d-grid gap-2 mt-3"><button class="btn btn-primary"><i class="bi bi-gear me-2"></i> ${escape_html(t.editProfile)}</button> <button class="btn btn-outline-danger"><i class="bi bi-box-arrow-right me-2"></i> ${escape_html(t.signOut)}</button></div></div> <div class="card-footer bg-light border-top p-3"><div class="d-flex align-items-center mb-2"><i class="bi bi-envelope text-muted me-2"></i> <div class="text-truncate"><small class="text-muted">${escape_html(user.email)}</small></div></div> <div class="d-flex align-items-center"><i class="bi bi-calendar3 text-muted me-2"></i> <div><small class="text-muted">${escape_html(t.joined)} ${escape_html(formatDate(joinDate, locale))}</small></div></div></div></div></div> <div class="col-lg-9"><div class="card border-0 shadow-sm mb-4"><div class="card-header bg-transparent border-bottom-0 pb-0"><h5 class="fw-bold">${escape_html(t.accountSummary)}</h5></div> <div class="card-body"><div class="row g-4"><div class="col-md-4"><div class="border rounded p-3 text-center h-100"><div class="mb-2"><i class="bi bi-calendar-check fs-3 text-primary"></i></div> <h2 class="mb-0 fw-bold">${escape_html(Math.floor(((/* @__PURE__ */ new Date()).getTime() - joinDate.getTime()) / (1e3 * 60 * 60 * 24)))}</h2> <p class="text-muted mb-0">${escape_html(t.daysAsMember)}</p></div></div></div></div></div> <div class="card border-0 shadow-sm mb-4"><div class="card-header d-flex justify-content-between align-items-center bg-transparent"><h5 class="fw-bold mb-0">${escape_html(t.aboutMe)}</h5></div> <div class="card-body">`);
  if (description) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<p class="mb-0">${escape_html(description)}</p>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div class="text-center text-muted py-4"><i class="bi bi-file-earmark-text fs-1 mb-2"></i> <p>${escape_html(t.noDescription)}</p> <button class="btn btn-sm btn-primary"${attr("aria-label", t.addDescription)}><i class="bi bi-plus-circle me-1"></i> ${escape_html(t.addDescription)}</button></div>`);
  }
  $$payload.out.push(`<!--]--></div></div></div></div> `);
  Edit_profile_modal($$payload, {
    locale,
    show: showEditModal,
    onHide: () => showEditModal = false,
    userData: user || null,
    onProfileUpdated: refresh
  });
  $$payload.out.push(`<!----> `);
  Upload_image_modal($$payload, {
    locale,
    show: showUploadModal,
    onHide: () => showUploadModal = false,
    userId: user.id,
    onImageUploaded: refresh
  });
  $$payload.out.push(`<!----></div>`);
  pop();
}
export {
  _page as default
};
