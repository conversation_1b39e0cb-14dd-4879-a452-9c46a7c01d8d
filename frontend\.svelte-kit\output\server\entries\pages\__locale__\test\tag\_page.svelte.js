import { K as ensure_array_like, z as escape_html, w as pop, u as push } from "../../../../../chunks/index.js";
import "@sveltejs/kit";
import "../../../../../chunks/schema.js";
import "../../../../../chunks/current-user.js";
function _page($$payload, $$props) {
  push();
  let tags = [];
  const each_array = ensure_array_like(tags);
  $$payload.out.push(`<div>Test!!</div> <button>Create new tag</button> <h2>Tags:</h2> <table><thead><tr><th>ID</th><th>Locale</th><th>Value</th></tr></thead><tbody><!--[-->`);
  for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
    let tag = each_array[$$index_1];
    const each_array_1 = ensure_array_like(tag.name);
    $$payload.out.push(`<!--[-->`);
    for (let i = 0, $$length2 = each_array_1.length; i < $$length2; i++) {
      let name = each_array_1[i];
      $$payload.out.push(`<tr><td>${escape_html(i === 0 ? tag.id : "")}</td><td>${escape_html(name.locale)}</td><td>${escape_html(name.value)}</td></tr>`);
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]--></tbody></table>`);
  pop();
}
export {
  _page as default
};
