import * as universal from '../entries/pages/__locale__/(index)/communes/_id_/invitations/_page.ts.js';

export const index = 13;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/communes/_id_/invitations/_page.svelte.js')).default;
export { universal };
export const universal_id = "src/routes/[[locale]]/(index)/communes/[id]/invitations/+page.ts";
export const imports = ["_app/immutable/nodes/13.DnrpY_UW.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CGZ87yZq.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/B5DcI8qy.js","_app/immutable/chunks/B0MzmgHo.js","_app/immutable/chunks/CL12WlkV.js"];
export const stylesheets = [];
export const fonts = [];
