import * as universal from '../entries/pages/__locale__/(index)/users/_id_/_page.ts.js';

export const index = 21;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/users/_id_/_page.svelte.js')).default;
export { universal };
export const universal_id = "src/routes/[[locale]]/(index)/users/[id]/+page.ts";
export const imports = ["_app/immutable/nodes/21.zXJt1skS.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CGZ87yZq.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/CaC9IHEK.js","_app/immutable/chunks/B0MzmgHo.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/DGxS2cwR.js"];
export const stylesheets = ["_app/immutable/assets/21.BU79Yo5H.css"];
export const fonts = [];
