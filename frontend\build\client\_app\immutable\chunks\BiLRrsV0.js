import{q as g,aL as d,o as c,z as m,aM as i,aN as b,g as p,aO as v,aE as h,aP as k}from"./RHWQbow4.js";function x(t=!1){const s=g,e=s.l.u;if(!e)return;let f=()=>v(s.s);if(t){let n=0,a={};const _=h(()=>{let l=!1;const r=s.s;for(const o in r)r[o]!==a[o]&&(a[o]=r[o],l=!0);return l&&n++,n});f=()=>p(_)}e.b.length&&d(()=>{u(s,f),i(e.b)}),c(()=>{const n=m(()=>e.m.map(b));return()=>{for(const a of n)typeof a=="function"&&a()}}),e.a.length&&c(()=>{u(s,f),i(e.a)})}function u(t,s){if(t.l.s)for(const e of t.l.s)p(e);s()}k();export{x as i};
