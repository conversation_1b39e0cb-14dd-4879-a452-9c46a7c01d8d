import{a9 as Y,aT as E,aU as T,w as A,ar as R,l as q,a2 as C,aV as j,i as B,aW as N,Q as U,ac as V,aX as $,an as J,au as L,V as w,U as m,J as Q,P as p,ad as X,at as Z,aY as z,ap as F,aj as G,aZ as K,W as x,N as rr,I as O,a_ as tr,p as ar,q as er,L as nr,c as sr}from"./RHWQbow4.js";const ir=["touchstart","touchmove"];function or(r){return ir.includes(r)}function I(r){var t=A,e=R;E(null),T(null);try{return r()}finally{E(t),T(e)}}function _r(r,t,e,i=e){r.addEventListener(t,()=>I(e));const n=r.__on_r;n?r.__on_r=()=>{n(),i(!0)}:r.__on_r=()=>i(!0),Y()}const W=new Set,S=new Set;function ur(r,t,e,i={}){function n(a){if(i.capture||y.call(t,a),!a.cancelBubble)return I(()=>e==null?void 0:e.call(this,a))}return r.startsWith("pointer")||r.startsWith("touch")||r==="wheel"?C(()=>{t.addEventListener(r,n,i)}):t.addEventListener(r,n,i),n}function lr(r,t,e,i,n){var a={capture:i,passive:n},u=ur(r,t,e,a);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&q(()=>{t.removeEventListener(r,u,a)})}function dr(r){for(var t=0;t<r.length;t++)W.add(r[t]);for(var e of S)e(r)}function y(r){var D;var t=this,e=t.ownerDocument,i=r.type,n=((D=r.composedPath)==null?void 0:D.call(r))||[],a=n[0]||r.target,u=0,d=r.__root;if(d){var _=n.indexOf(d);if(_!==-1&&(t===document||t===window)){r.__root=t;return}var h=n.indexOf(t);if(h===-1)return;_<=h&&(u=_)}if(a=n[u]||r.target,a!==t){j(r,"currentTarget",{configurable:!0,get(){return a||e}});var b=A,f=R;E(null),T(null);try{for(var s,o=[];a!==null;){var c=a.assignedSlot||a.parentNode||a.host||null;try{var l=a["__"+i];if(l!=null&&(!a.disabled||r.target===a))if(B(l)){var[H,...P]=l;H.apply(a,[r,...P])}else l.call(a,r)}catch(g){s?o.push(g):s=g}if(r.cancelBubble||c===t||c===null)break;a=c}if(s){for(let g of o)queueMicrotask(()=>{throw g});throw s}}finally{r.__root=t,delete r.currentTarget,E(b),T(f)}}}function hr(r,t){var e=t==null?"":typeof t=="object"?t+"":t;e!==(r.__t??(r.__t=r.nodeValue))&&(r.__t=e,r.nodeValue=e+"")}function fr(r,t){return k(r,t)}function vr(r,t){N(),t.intro=t.intro??!1;const e=t.target,i=O,n=p;try{for(var a=U(e);a&&(a.nodeType!==V||a.data!==$);)a=J(a);if(!a)throw L;w(!0),m(a),Q();const u=k(r,{...t,anchor:a});if(p===null||p.nodeType!==V||p.data!==X)throw Z(),L;return w(!1),u}catch(u){if(u===L)return t.recover===!1&&z(),N(),F(e),w(!1),fr(r,t);throw u}finally{w(i),m(n),tr()}}const v=new Map;function k(r,{target:t,anchor:e,props:i={},events:n,context:a,intro:u=!0}){N();var d=new Set,_=f=>{for(var s=0;s<f.length;s++){var o=f[s];if(!d.has(o)){d.add(o);var c=or(o);t.addEventListener(o,y,{passive:c});var l=v.get(o);l===void 0?(document.addEventListener(o,y,{passive:c}),v.set(o,1)):v.set(o,l+1)}}};_(G(W)),S.add(_);var h=void 0,b=K(()=>{var f=e??t.appendChild(x());return rr(()=>{if(a){ar({});var s=er;s.c=a}n&&(i.$$events=n),O&&nr(f,null),h=r(f,i)||{},O&&(R.nodes_end=p),a&&sr()}),()=>{var c;for(var s of d){t.removeEventListener(s,y);var o=v.get(s);--o===0?(document.removeEventListener(s,y),v.delete(s)):v.set(s,o)}S.delete(_),f!==e&&((c=f.parentNode)==null||c.removeChild(f))}});return M.set(h,b),h}let M=new WeakMap;function pr(r,t){const e=M.get(r);return e?(M.delete(r),e(t)):Promise.resolve()}export{dr as d,lr as e,vr as h,_r as l,fr as m,hr as s,pr as u};
