var At=Object.defineProperty;var Nt=(t,e,n)=>e in t?At(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var v=(t,e,n)=>Nt(t,typeof e!="symbol"?e+"":e,n);import{Z as it,u as m,a as c,t as q,b as i,d as R,f as S,g as _,r as _t,h as kt}from"./CVTn1FV4.js";globalThis.__sveltekit_4aiq1n.env;class jt{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(e,n){this.keyToValue.set(e,n),this.valueToKey.set(n,e)}getByKey(e){return this.keyToValue.get(e)}getByValue(e){return this.valueToKey.get(e)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}}class ft{constructor(e){this.generateIdentifier=e,this.kv=new jt}register(e,n){this.kv.getByValue(e)||(n||(n=this.generateIdentifier(e)),this.kv.set(n,e))}clear(){this.kv.clear()}getIdentifier(e){return this.kv.getByValue(e)}getValue(e){return this.kv.getByKey(e)}}class Mt extends ft{constructor(){super(e=>e.name),this.classToAllowedProps=new Map}register(e,n){typeof n=="object"?(n.allowProps&&this.classToAllowedProps.set(e,n.allowProps),super.register(e,n.identifier)):super.register(e,n)}getAllowedProps(e){return this.classToAllowedProps.get(e)}}function Gt(t){if("values"in Object)return Object.values(t);const e=[];for(const n in t)t.hasOwnProperty(n)&&e.push(t[n]);return e}function Wt(t,e){const n=Gt(t);if("find"in n)return n.find(e);const r=n;for(let a=0;a<r.length;a++){const s=r[a];if(e(s))return s}}function j(t,e){Object.entries(t).forEach(([n,r])=>e(r,n))}function K(t,e){return t.indexOf(e)!==-1}function ut(t,e){for(let n=0;n<t.length;n++){const r=t[n];if(e(r))return r}}class zt{constructor(){this.transfomers={}}register(e){this.transfomers[e.name]=e}findApplicable(e){return Wt(this.transfomers,n=>n.isApplicable(e))}findByName(e){return this.transfomers[e]}}const Lt=t=>Object.prototype.toString.call(t).slice(8,-1),mt=t=>typeof t>"u",Vt=t=>t===null,L=t=>typeof t!="object"||t===null||t===Object.prototype?!1:Object.getPrototypeOf(t)===null?!0:Object.getPrototypeOf(t)===Object.prototype,X=t=>L(t)&&Object.keys(t).length===0,P=t=>Array.isArray(t),xt=t=>typeof t=="string",Bt=t=>typeof t=="number"&&!isNaN(t),qt=t=>typeof t=="boolean",Kt=t=>t instanceof RegExp,V=t=>t instanceof Map,x=t=>t instanceof Set,dt=t=>Lt(t)==="Symbol",Ft=t=>t instanceof Date&&!isNaN(t.valueOf()),$t=t=>t instanceof Error,ct=t=>typeof t=="number"&&isNaN(t),Ht=t=>qt(t)||Vt(t)||mt(t)||Bt(t)||xt(t)||dt(t),Zt=t=>typeof t=="bigint",Jt=t=>t===1/0||t===-1/0,Qt=t=>ArrayBuffer.isView(t)&&!(t instanceof DataView),Yt=t=>t instanceof URL,St=t=>t.replace(/\./g,"\\."),Y=t=>t.map(String).map(St).join("."),z=t=>{const e=[];let n="";for(let a=0;a<t.length;a++){let s=t.charAt(a);if(s==="\\"&&t.charAt(a+1)==="."){n+=".",a++;continue}if(s==="."){e.push(n),n="";continue}n+=s}const r=n;return e.push(r),e};function b(t,e,n,r){return{isApplicable:t,annotation:e,transform:n,untransform:r}}const gt=[b(mt,"undefined",()=>null,()=>{}),b(Zt,"bigint",t=>t.toString(),t=>typeof BigInt<"u"?BigInt(t):(console.error("Please add a BigInt polyfill."),t)),b(Ft,"Date",t=>t.toISOString(),t=>new Date(t)),b($t,"Error",(t,e)=>{const n={name:t.name,message:t.message};return e.allowedErrorProps.forEach(r=>{n[r]=t[r]}),n},(t,e)=>{const n=new Error(t.message);return n.name=t.name,n.stack=t.stack,e.allowedErrorProps.forEach(r=>{n[r]=t[r]}),n}),b(Kt,"regexp",t=>""+t,t=>{const e=t.slice(1,t.lastIndexOf("/")),n=t.slice(t.lastIndexOf("/")+1);return new RegExp(e,n)}),b(x,"set",t=>[...t.values()],t=>new Set(t)),b(V,"map",t=>[...t.entries()],t=>new Map(t)),b(t=>ct(t)||Jt(t),"number",t=>ct(t)?"NaN":t>0?"Infinity":"-Infinity",Number),b(t=>t===0&&1/t===-1/0,"number",()=>"-0",Number),b(Yt,"URL",t=>t.toString(),t=>new URL(t))];function F(t,e,n,r){return{isApplicable:t,annotation:e,transform:n,untransform:r}}const It=F((t,e)=>dt(t)?!!e.symbolRegistry.getIdentifier(t):!1,(t,e)=>["symbol",e.symbolRegistry.getIdentifier(t)],t=>t.description,(t,e,n)=>{const r=n.symbolRegistry.getValue(e[1]);if(!r)throw new Error("Trying to deserialize unknown symbol");return r}),Xt=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((t,e)=>(t[e.name]=e,t),{}),yt=F(Qt,t=>["typed-array",t.constructor.name],t=>[...t],(t,e)=>{const n=Xt[e[1]];if(!n)throw new Error("Trying to deserialize unknown typed array");return new n(t)});function vt(t,e){return t!=null&&t.constructor?!!e.classRegistry.getIdentifier(t.constructor):!1}const bt=F(vt,(t,e)=>["class",e.classRegistry.getIdentifier(t.constructor)],(t,e)=>{const n=e.classRegistry.getAllowedProps(t.constructor);if(!n)return{...t};const r={};return n.forEach(a=>{r[a]=t[a]}),r},(t,e,n)=>{const r=n.classRegistry.getValue(e[1]);if(!r)throw new Error(`Trying to deserialize unknown class '${e[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(r.prototype),t)}),wt=F((t,e)=>!!e.customTransformerRegistry.findApplicable(t),(t,e)=>["custom",e.customTransformerRegistry.findApplicable(t).name],(t,e)=>e.customTransformerRegistry.findApplicable(t).serialize(t),(t,e,n)=>{const r=n.customTransformerRegistry.findByName(e[1]);if(!r)throw new Error("Trying to deserialize unknown custom value");return r.deserialize(t)}),te=[bt,It,wt,yt],lt=(t,e)=>{const n=ut(te,a=>a.isApplicable(t,e));if(n)return{value:n.transform(t,e),type:n.annotation(t,e)};const r=ut(gt,a=>a.isApplicable(t,e));if(r)return{value:r.transform(t,e),type:r.annotation}},Ct={};gt.forEach(t=>{Ct[t.annotation]=t});const ee=(t,e,n)=>{if(P(e))switch(e[0]){case"symbol":return It.untransform(t,e,n);case"class":return bt.untransform(t,e,n);case"custom":return wt.untransform(t,e,n);case"typed-array":return yt.untransform(t,e,n);default:throw new Error("Unknown transformation: "+e)}else{const r=Ct[e];if(!r)throw new Error("Unknown transformation: "+e);return r.untransform(t,n)}},k=(t,e)=>{if(e>t.size)throw new Error("index out of bounds");const n=t.keys();for(;e>0;)n.next(),e--;return n.next().value};function Ot(t){if(K(t,"__proto__"))throw new Error("__proto__ is not allowed as a property");if(K(t,"prototype"))throw new Error("prototype is not allowed as a property");if(K(t,"constructor"))throw new Error("constructor is not allowed as a property")}const ne=(t,e)=>{Ot(e);for(let n=0;n<e.length;n++){const r=e[n];if(x(t))t=k(t,+r);else if(V(t)){const a=+r,s=+e[++n]==0?"key":"value",o=k(t,a);switch(s){case"key":t=o;break;case"value":t=t.get(o);break}}else t=t[r]}return t},tt=(t,e,n)=>{if(Ot(e),e.length===0)return n(t);let r=t;for(let s=0;s<e.length-1;s++){const o=e[s];if(P(r)){const l=+o;r=r[l]}else if(L(r))r=r[o];else if(x(r)){const l=+o;r=k(r,l)}else if(V(r)){if(s===e.length-2)break;const p=+o,w=+e[++s]==0?"key":"value",I=k(r,p);switch(w){case"key":r=I;break;case"value":r=r.get(I);break}}}const a=e[e.length-1];if(P(r)?r[+a]=n(r[+a]):L(r)&&(r[a]=n(r[a])),x(r)){const s=k(r,+a),o=n(s);s!==o&&(r.delete(s),r.add(o))}if(V(r)){const s=+e[e.length-2],o=k(r,s);switch(+a==0?"key":"value"){case"key":{const p=n(o);r.set(p,r.get(o)),p!==o&&r.delete(o);break}case"value":{r.set(o,n(r.get(o)));break}}}return t};function et(t,e,n=[]){if(!t)return;if(!P(t)){j(t,(s,o)=>et(s,e,[...n,...z(o)]));return}const[r,a]=t;a&&j(a,(s,o)=>{et(s,e,[...n,...z(o)])}),e(r,n)}function re(t,e,n){return et(e,(r,a)=>{t=tt(t,a,s=>ee(s,r,n))}),t}function ae(t,e){function n(r,a){const s=ne(t,z(a));r.map(z).forEach(o=>{t=tt(t,o,()=>s)})}if(P(e)){const[r,a]=e;r.forEach(s=>{t=tt(t,z(s),()=>t)}),a&&j(a,n)}else j(e,n);return t}const se=(t,e)=>L(t)||P(t)||V(t)||x(t)||vt(t,e);function oe(t,e,n){const r=n.get(t);r?r.push(e):n.set(t,[e])}function ie(t,e){const n={};let r;return t.forEach(a=>{if(a.length<=1)return;e||(a=a.map(l=>l.map(String)).sort((l,p)=>l.length-p.length));const[s,...o]=a;s.length===0?r=o.map(Y):n[Y(s)]=o.map(Y)}),r?X(n)?[r]:[r,n]:X(n)?void 0:n}const Et=(t,e,n,r,a=[],s=[],o=new Map)=>{const l=Ht(t);if(!l){oe(t,a,e);const y=o.get(t);if(y)return r?{transformedValue:null}:y}if(!se(t,n)){const y=lt(t,n),d=y?{transformedValue:y.value,annotations:[y.type]}:{transformedValue:t};return l||o.set(t,d),d}if(K(s,t))return{transformedValue:null};const p=lt(t,n),w=(p==null?void 0:p.value)??t,I=P(w)?[]:{},E={};j(w,(y,d)=>{if(d==="__proto__"||d==="constructor"||d==="prototype")throw new Error(`Detected property ${d}. This is a prototype pollution risk, please remove it from your object.`);const C=Et(y,e,n,r,[...a,d],[...s,t],o);I[d]=C.transformedValue,P(C.annotations)?E[d]=C.annotations:L(C.annotations)&&j(C.annotations,(M,g)=>{E[St(d)+"."+g]=M})});const B=X(E)?{transformedValue:I,annotations:p?[p.type]:void 0}:{transformedValue:I,annotations:p?[p.type,E]:E};return l||o.set(t,B),B};function Rt(t){return Object.prototype.toString.call(t).slice(8,-1)}function pt(t){return Rt(t)==="Array"}function ue(t){if(Rt(t)!=="Object")return!1;const e=Object.getPrototypeOf(t);return!!e&&e.constructor===Object&&e===Object.prototype}function ce(t,e,n,r,a){const s={}.propertyIsEnumerable.call(r,e)?"enumerable":"nonenumerable";s==="enumerable"&&(t[e]=n),a&&s==="nonenumerable"&&Object.defineProperty(t,e,{value:n,enumerable:!1,writable:!0,configurable:!0})}function nt(t,e={}){if(pt(t))return t.map(a=>nt(a,e));if(!ue(t))return t;const n=Object.getOwnPropertyNames(t),r=Object.getOwnPropertySymbols(t);return[...n,...r].reduce((a,s)=>{if(pt(e.props)&&!e.props.includes(s))return a;const o=t[s],l=nt(o,e);return ce(a,s,l,t,e.nonenumerable),a},{})}class u{constructor({dedupe:e=!1}={}){this.classRegistry=new Mt,this.symbolRegistry=new ft(n=>n.description??""),this.customTransformerRegistry=new zt,this.allowedErrorProps=[],this.dedupe=e}serialize(e){const n=new Map,r=Et(e,n,this,this.dedupe),a={json:r.transformedValue};r.annotations&&(a.meta={...a.meta,values:r.annotations});const s=ie(n,this.dedupe);return s&&(a.meta={...a.meta,referentialEqualities:s}),a}deserialize(e){const{json:n,meta:r}=e;let a=nt(n);return r!=null&&r.values&&(a=re(a,r.values,this)),r!=null&&r.referentialEqualities&&(a=ae(a,r.referentialEqualities)),a}stringify(e){return JSON.stringify(this.serialize(e))}parse(e){return this.deserialize(JSON.parse(e))}registerClass(e,n){this.classRegistry.register(e,n)}registerSymbol(e,n){this.symbolRegistry.register(e,n)}registerCustom(e,n){this.customTransformerRegistry.register({name:n,...e})}allowErrorProps(...e){this.allowedErrorProps.push(...e)}}u.defaultInstance=new u;u.serialize=u.defaultInstance.serialize.bind(u.defaultInstance);u.deserialize=u.defaultInstance.deserialize.bind(u.defaultInstance);u.stringify=u.defaultInstance.stringify.bind(u.defaultInstance);u.parse=u.defaultInstance.parse.bind(u.defaultInstance);u.registerClass=u.defaultInstance.registerClass.bind(u.defaultInstance);u.registerSymbol=u.defaultInstance.registerSymbol.bind(u.defaultInstance);u.registerCustom=u.defaultInstance.registerCustom.bind(u.defaultInstance);u.allowErrorProps=u.defaultInstance.allowErrorProps.bind(u.defaultInstance);function le(t){var e;return((e=t[0])==null?void 0:e.toUpperCase())+t.slice(1).toLowerCase()}function pe(t,e){const n=(e==null?void 0:e.split(""))??[],r=t.map(a=>typeof a=="string"?a:(n.push(...a.flags.split("")),String(a).slice(1,-a.flags.length-1)));return new RegExp(r.join("|"),[...new Set(n)].join(""))}var he=/[^\p{L}\p{N}]/,fe=new RegExp("(?<=\\p{Lu})(?=\\p{Lu}\\p{Ll})"),me=new RegExp("(?<=\\p{Ll})(?=\\p{Lu})"),de=new RegExp("(?<=\\p{L})(?=\\p{N})"),Se=new RegExp("(?<=\\p{N})(?=\\p{L})"),T,ge=(T=class{constructor(...e){v(this,"preserveCase");v(this,"preserveCaps");v(this,"capitalizeAfter");typeof e[0]=="boolean"?(this.preserveCase=!0,this.preserveCaps=!0,this.capitalizeAfter=Number.POSITIVE_INFINITY):(this.preserveCase=!1,this.preserveCaps=e[1],this.capitalizeAfter=e[0])}parse(e){return e.replace(T.parser," ").trim().split(/\s+/)}join(e){const n=Math.max(0,this.capitalizeAfter),r=e.slice(0,n),a=e.slice(n),s=this.preserveCase?a:a.map(o=>!this.preserveCaps||new RegExp("\\p{Ll}","u").test(o)?le(o):o);return[...r,...s].join("")}},v(T,"parser",pe([fe,me,de,Se],"gu")),T),Ie=class{constructor(t,e){this.separator=t,this.lowercaseJoined=e}parse(t){return t.split(this.separator).filter(e=>!!e.trim())}join(t){const e=t.join(this.separator);return this.lowercaseJoined?e.toLowerCase():e}},ye=class extends Ie{constructor(){super("-",!0)}},D,ve=(D=class extends ge{constructor(){super(!0)}parse(e){return super.parse(e.replace(D.parser," "))}join(e){return e.join(" ")}},v(D,"parser",new RegExp(he,"gu")),D),be=class{constructor(t,e){this.parseStrategy=t,this.joinStrategy=e}transform(t){return this.joinStrategy.join(this.parseStrategy.parse(t))}};function we(...t){}function U(...t){}var Ce=new be(new ve,new ye),Oe={serialize:JSON.stringify,deserialize:JSON.parse},Ee={serialize:u.stringify,deserialize:u.parse};function Re(t){return t!=null&&typeof t=="object"&&"input"in t&&(t.input instanceof it||t.input===null||t.input===void 0)&&"output"in t&&(t.output instanceof it||t.output===null||t.output===void 0)}var Ue=class extends Error{constructor(e,n,r,a){super(`Fetch at ${e.toUpperCase()} ${n} failed, status ${r}, description: '${a}'`);v(this,"method");v(this,"url");v(this,"status");v(this,"description");this.method=e,this.url=n,this.status=r,this.description=a}};function rt(){return typeof window<"u"?window.localStorage??null:typeof globalThis!==void 0?globalThis.localStorage??null:null}function Pe(t){if(!t.length)return[];const e=t.split("/").slice(1),n=[];for(let r=0;r<e.length;r++)n.push("/"+e.slice(0,r+1).join("/"));return n}function Te(t){return function(n){const r=Pe(n);for(const a of r)t.has(a)||t.set(a,[]),t.get(a).push(n)}}function De(t){for(const[e,n]of t)t.set(e,[...new Set(n)])}function Ae(t,e){for(const[n,r]of t)for(const a of r)e.has(a)||e.set(a,[]),e.get(a).push(n)}function Ne(t){var n,r;const e=(n=rt())==null?void 0:n.getItem("acrpc:invalid-paths");if(e)try{const a=JSON.parse(e);for(const s of a)t.add(s)}catch(a){console.error("Error parsing invalid paths",a),(r=rt())==null||r.removeItem("acrpc:invalid-paths")}}function _e(t,e,n){return function(a,s){var w;const o=t.get(a)??[];U("invalidating path cache",{path:a,depth:s,masterPaths:o});const l=o[Math.max(0,o.length-s)],p=e.get(l)??[];U("invalidating path cache 2",{masterPath:l,paths:p});for(const I of p)n.add(I);(w=rt())==null||w.setItem("acrpc:invalid-paths",JSON.stringify([...n]))}}function ke(t,e){const n=e.transformer??Oe,r=e.entrypointUrl.endsWith("/")?e.entrypointUrl.slice(0,-1):e.entrypointUrl,a=new Map,s=new Map,o=new Set,l=Te(a),p=_e(a,s,o);U({invalidPathCacheSet:o});const w=e.fetch??fetch,I={...e.init};function E(y,d,C){for(const[M,g]of Object.entries(y)){const Ut=Ce.transform(M);if(Re(g)){let $=function(G){return g.input===null?[void 0,{...G[0]}]:[G[0],G[1]]};const O=["",...d].join("/"),A=M;l(O);const Pt={[A]:async function(...G){var st;const[H,h]=$(G);if(g.input!=null&&!H)throw new Error("Input data argument not provided.");we(`Performing ${A.toUpperCase()} ${O}...`),U({entrypointUrl:r,path:O});const Tt=o.has(O),Z={...I,...h,headers:{...I.headers,...h==null?void 0:h.headers,...Tt?{"Cache-Control":"reload"}:null},method:A.toUpperCase()};let J="";if(g.input!==null&&H!==void 0){const W=n.serialize(H);A==="get"?J=`__body=${encodeURIComponent(W)}`:(Z.headers["Content-Type"]="application/json",Z.body=W)}const Dt=(h==null?void 0:h.fetch)??w;h==null||delete h.fetch;const at=J?`?${J}`:"";U({fetchUrl:r+O+at});const N=await Dt(r+O+at,Z);if(h!=null&&h.skipInterceptor||await((st=e.interceptor)==null?void 0:st.call(e,{method:A,path:O,response:N,ctx:h==null?void 0:h.ctx})),N.ok){let W=null;if(g.output!==null){const Q=await N.text();W=n.deserialize(Q)}U({autoScopeInvalidationDepth:g.autoScopeInvalidationDepth,invalidate:g.invalidate}),U("before invalidations",{masterPathMap:a,invalidPathCacheSet:o});const ot=g.autoScopeInvalidationDepth??0;if(ot&&p(O,ot),g.invalidate)for(const Q of g.invalidate)p(Q,0);return U("after invalidations",{masterPathMap:a,invalidPathCacheSet:o}),W}throw new Ue(A,O,N.status,await N.text()||N.statusText)}};Object.assign(C,Pt)}else{const $=C[M]={};E(g,[...d,Ut],$)}}return C}const B=E(t,[],{});return De(a),Ae(a,s),Ne(o),{fetcher:B}}var je="no-cache",ht="no-cache",Me="no-cache",f=je,Ge={auth:{otp:{post:{input:_.SendOtpInputSchema,output:_.SendOtpOutputSchema,isMetadataUsed:!1}},signUp:{post:{input:_.SignupInputSchema,output:_.SuccessfulOutputSchema,isMetadataUsed:!1,invalidate:["/user/me"]}},signIn:{post:{input:_.SigninInputSchema,output:_.SuccessfulOutputSchema,isMetadataUsed:!1,invalidate:["/user/me"]}},signOut:{get:{input:null,output:null,isMetadataUsed:!1,invalidate:["/user/me"]}}},commune:{transferHeadStatus:{post:{input:S.TransferHeadStatusInputSchema,output:null,autoScopeInvalidationDepth:2}},list:{get:{input:S.GetCommunesInputSchema,output:S.GetCommunesOutputSchema,cacheControl:f,isMetadataRequired:!1}},post:{input:S.CreateCommuneInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:S.UpdateCommuneInputSchema,output:null,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1},member:{list:{get:{input:S.GetCommuneMembersInputSchema,output:S.GetCommuneMembersOutputSchema,cacheControl:f,isMetadataRequired:!1}},post:{input:S.CreateCommuneMemberInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1}},invitation:{list:{get:{input:S.GetCommuneInvitationsInputSchema,output:S.GetCommuneInvitationsOutputSchema,cacheControl:f}},post:{input:S.CreateCommuneInvitationInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1},accept:{post:{input:c.ObjectWithIdSchema,output:null,invalidate:["/commune"]}},reject:{post:{input:c.ObjectWithIdSchema,output:null,invalidate:["/commune"]}}},joinRequest:{list:{get:{input:S.GetCommuneJoinRequestsInputSchema,output:S.GetCommuneJoinRequestsOutputSchema,cacheControl:f}},post:{input:S.CreateCommuneJoinRequestInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1},accept:{post:{input:c.ObjectWithIdSchema,output:null,invalidate:["/commune"]}},reject:{post:{input:c.ObjectWithIdSchema,output:null,invalidate:["/commune"]}}}},rating:{karma:{list:{get:{input:R.GetKarmaPointsInputSchema,output:R.GetKarmaPointsOutputSchema,cacheControl:f}},post:{input:R.SpendKarmaPointInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1,invalidate:["/rating/summary"]}},feedback:{list:{get:{input:R.GetUserFeedbacksInputSchema,output:R.GetUserFeedbacksOutputSchema,cacheControl:f}},post:{input:R.CreateUserFeedbackInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1,invalidate:["/rating/summary"]}},summary:{get:{input:R.GetUserSummaryInputSchema,output:R.GetUserSummaryOutputSchema,cacheControl:f}}},reactor:{post:{list:{get:{input:i.GetPostsInputSchema,output:i.GetPostsOutputSchema,cacheControl:f,isMetadataRequired:!1}},post:{input:i.CreatePostInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:i.UpdatePostInputSchema,output:null,autoScopeInvalidationDepth:1},delete:{input:i.DeletePostInputSchema,output:null,autoScopeInvalidationDepth:1},rating:{post:{input:i.UpdatePostRatingInputSchema,output:i.UpdatePostRatingOutputSchema,autoScopeInvalidationDepth:2}},usefulness:{post:{input:i.UpdatePostUsefulnessInputSchema,output:i.UpdatePostUsefulnessOutputSchema,autoScopeInvalidationDepth:2}},image:{list:{get:{input:i.GetPostImagesInputSchema,output:i.GetPostImagesOutputSchema,cacheControl:f}}}},comment:{list:{get:{input:i.GetCommentsInputSchema,output:i.GetCommentsOutputSchema,cacheControl:f,isMetadataRequired:!1}},post:{input:i.CreateCommentInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:i.UpdateCommentInputSchema,output:null,autoScopeInvalidationDepth:1},delete:{input:i.DeleteCommentInputSchema,output:null,autoScopeInvalidationDepth:1},rating:{post:{input:i.UpdateCommentRatingInputSchema,output:i.UpdateCommentRatingOutputSchema,autoScopeInvalidationDepth:2}},anonimify:{post:{input:i.AnonimifyCommentInputSchema,output:null,autoScopeInvalidationDepth:2}}},lens:{list:{get:{input:null,output:i.GetLensesOutputSchema,cacheControl:Me}},post:{input:i.CreateLensInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:i.UpdateLensInputSchema,output:null,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1}},hub:{list:{get:{input:i.GetHubsInputSchema,output:i.GetHubsOutputSchema,cacheControl:f,isMetadataRequired:!1}},post:{input:i.CreateHubInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:i.UpdateHubInputSchema,output:null,autoScopeInvalidationDepth:1}},community:{list:{get:{input:i.GetCommunitiesInputSchema,output:i.GetCommunitiesOutputSchema,cacheControl:f,isMetadataRequired:!1}},post:{input:i.CreateCommunityInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:i.UpdateCommunityInputSchema,output:null,autoScopeInvalidationDepth:1}}},tag:{list:{get:{input:q.GetTagsInputSchema,output:q.GetTagsOutputSchema,cacheControl:ht}},post:{input:q.CreateTagInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:q.UpdateTagInputSchema,output:null,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1}},user:{list:{get:{input:m.GetUsersInputSchema,output:m.GetUsersOutputSchema,cacheControl:f}},me:{get:{input:null,output:m.GetMeOutputSchema,cacheControl:ht}},patch:{input:m.UpdateUserInputSchema,output:null,autoScopeInvalidationDepth:1},title:{list:{get:{input:m.GetUserTitlesInputSchema,output:m.GetUserTitlesOutputSchema,cacheControl:f}},post:{input:m.CreateUserTitleInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},patch:{input:m.UpdateUserTitleInputSchema,output:null,autoScopeInvalidationDepth:1},delete:{input:c.ObjectWithIdSchema,output:null,autoScopeInvalidationDepth:1}},note:{get:{input:m.GetUserNoteInputSchema,output:m.GetUserNoteOutputSchema,cacheControl:f},put:{input:m.UpdateUserNoteInputSchema,output:null,autoScopeInvalidationDepth:1}},invite:{list:{get:{input:m.GetUserInvitesInputSchema,output:m.GetUserInvitesOutputSchema,cacheControl:f}},put:{input:m.UpsertUserInviteInputSchema,output:c.ObjectWithIdSchema,autoScopeInvalidationDepth:1},delete:{input:m.DeleteUserInviteInputSchema,output:null,autoScopeInvalidationDepth:1}}}},We=Ee;let ze;function xe(){return ze??(ze=ke(Ge,{entrypointUrl:"/api",transformer:We,init:{credentials:"include"},interceptor:async({response:t,ctx:e})=>{if(t.status===401){_t();const n=e?e.url.pathname+e.url.search:window.location.pathname+window.location.search;kt(302,`/auth?redirectFrom=${encodeURIComponent(n)}`)}}}))}export{Ue as H,xe as g};
