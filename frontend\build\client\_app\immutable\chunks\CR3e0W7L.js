import{aA as A,aB as D,aC as m,g as S,aw as w,ax as L,ar as T,aD as B,aE as h,ab as y,aF as Y,z as K,y as M,aG as N,aH as U,aI as z,aJ as C,a3 as G,aK as $}from"./RHWQbow4.js";let c=!1;function q(e){var r=c;try{return c=!1,[e(),c]}finally{c=r}}const F={get(e,r){if(!e.exclude.includes(r))return e.props[r]},set(e,r){return!1},getOwnPropertyDescriptor(e,r){if(!e.exclude.includes(r)&&r in e.props)return{enumerable:!0,configurable:!0,value:e.props[r]}},has(e,r){return e.exclude.includes(r)?!1:r in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(r=>!e.exclude.includes(r))}};function J(e,r,a){return new Proxy({props:e,exclude:r},F)}function Z(e,r,a,_){var p;var u=!M||(a&N)!==0,l=(a&Y)!==0,E=(a&z)!==0,i=_,o=!0,g=()=>(o&&(o=!1,i=E?K(_):_),i),s;if(l){var O=G in e||$ in e;s=((p=A(e,r))==null?void 0:p.set)??(O&&r in e?n=>e[r]=n:void 0)}var f,I=!1;l?[f,I]=q(()=>e[r]):f=e[r],f===void 0&&_!==void 0&&(f=g(),s&&(u&&D(),s(f)));var t;if(u?t=()=>{var n=e[r];return n===void 0?g():(o=!0,n)}:t=()=>{var n=e[r];return n!==void 0&&(i=void 0),n===void 0?i:n},u&&(a&m)===0)return t;if(s){var R=e.$$legacy;return function(n,v){return arguments.length>0?((!u||!v||R||I)&&s(v?t():n),n):t()}}var P=!1,d=((a&U)!==0?h:y)(()=>(P=!1,t()));l&&S(d);var x=T;return function(n,v){if(arguments.length>0){const b=v?S(d):u&&l?w(n):n;return L(d,b),P=!0,i!==void 0&&(i=b),n}return C&&P||(x.f&B)!==0?d.v:S(d)}}export{Z as p,J as r};
