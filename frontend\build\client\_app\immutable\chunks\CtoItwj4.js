import{H as k,I as _,J as A,M as R,R as S,S as x,T as D,U as F,V as p,W as C,N as T,X as H,Y as L,Z as P,_ as U,a0 as Y,P as Z}from"./RHWQbow4.js";function J(b,g,y=!1){_&&A();var r=b,t=null,s=null,e=L,E=y?R:0,l=!1;const I=(n,a=!0)=>{l=!0,d(a,n)};var f=null;function o(){f!==null&&(f.lastChild.remove(),r.before(f),f=null);var n=e?t:s,a=e?s:t;n&&U(n),a&&Y(a,()=>{e?s=null:t=null})}const d=(n,a)=>{if(e===(e=n))return;let u=!1;if(_){const N=S(r)===x;!!e===N&&(r=D(),F(r),p(!1),u=!0)}var v=P(),i=r;if(v&&(f=document.createDocumentFragment(),f.append(i=C())),e?t??(t=a&&T(()=>a(i))):s??(s=a&&T(()=>a(i))),v){var c=H,h=e?t:s,m=e?s:t;h&&c.skipped_effects.delete(h),m&&c.skipped_effects.add(m),c.add_callback(o)}else o();u&&p(!0)};k(()=>{l=!1,g(I),l||d(null,null)},E),_&&(r=Z)}export{J as i};
