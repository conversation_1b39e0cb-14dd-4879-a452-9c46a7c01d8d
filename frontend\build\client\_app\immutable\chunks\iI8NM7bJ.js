import"./Bzak7iHL.js";import{b1 as ht,z as vt,a1 as ut,I as at,X as Ge,b2 as pt,k as yt,l as xt,a2 as ot,p as Ie,ay as Ee,a as xe,b as m,c as Se,f as g,s as b,d as a,r as u,t as k,g as e,u as ke,av as G,ax as r,aw as De,aR as wt,ah as ze,b3 as Et,b4 as Ct,aO as Je,b5 as kt,o as He,az as st,aQ as Dt}from"./RHWQbow4.js";import{l as rt,d as Le,s as E,e as Xe}from"./BlWcudmi.js";import{i as B}from"./CtoItwj4.js";import{e as Re}from"./Dnfvvefi.js";import{s as U,a as mt,r as Ke}from"./BdpLTtcP.js";import{s as _e}from"./Cxg-bych.js";import{g as lt}from"./CKnuo8tw.js";import{g as We}from"./CGZ87yZq.js";import{r as It,c as Ze}from"./CVTn1FV4.js";import{s as St}from"./CkTdM00m.js";import{c as _t}from"./q36Eg1F8.js";import{p as Z,r as it}from"./CR3e0W7L.js";import{o as bt,c as Bt,a as Ft}from"./DeAm3Eed.js";import{b as Ne}from"./B5DcI8qy.js";import"./B0MzmgHo.js";import{i as Tt}from"./BiLRrsV0.js";function Qe(l,t,d=t){var f=ht(),v=new WeakSet;rt(l,"input",x=>{var y=x?l.defaultValue:l.value;if(y=et(l)?tt(y):y,d(y),Ge!==null&&v.add(Ge),f&&y!==(y=t())){var I=l.selectionStart,D=l.selectionEnd;l.value=y??"",D!==null&&(l.selectionStart=I,l.selectionEnd=Math.min(D,l.value.length))}}),(at&&l.defaultValue!==l.value||vt(t)==null&&l.value)&&(d(et(l)?tt(l.value):l.value),Ge!==null&&v.add(Ge)),ut(()=>{var x=t();if(l===document.activeElement){var y=pt??Ge;if(v.has(y))return}et(l)&&x===tt(l.value)||l.type==="date"&&!x&&!l.value||x!==l.value&&(l.value=x??"")})}const $e=new Set;function Hr(l,t,d,f,v=f){var x=d.getAttribute("type")==="checkbox",y=l;let I=!1;if(t!==null)for(var D of t)y=y[D]??(y[D]=[]);y.push(d),rt(d,"change",()=>{var h=d.__value;x&&(h=dt(y,h,d.checked)),v(h)},()=>v(x?[]:null)),ut(()=>{var h=f();if(at&&d.defaultChecked!==d.checked){I=!0;return}x?(h=h||[],d.checked=h.includes(d.__value)):d.checked=yt(d.__value,h)}),xt(()=>{var h=y.indexOf(d);h!==-1&&y.splice(h,1)}),$e.has(y)||($e.add(y),ot(()=>{y.sort((h,L)=>h.compareDocumentPosition(L)===4?-1:1),$e.delete(y)})),ot(()=>{if(I){var h;if(x)h=dt(y,h,d.checked);else{var L=y.find(z=>z.checked);h=L==null?void 0:L.__value}v(h)}})}function Mr(l,t,d=t){rt(l,"change",f=>{var v=f?l.defaultChecked:l.checked;d(v)}),(at&&l.defaultChecked!==l.checked||vt(t)==null)&&d(l.checked),ut(()=>{var f=t();l.checked=!!f})}function dt(l,t,d){for(var f=new Set,v=0;v<l.length;v+=1)l[v].checked&&f.add(l[v].__value);return d||f.delete(t),Array.from(f)}function et(l){var t=l.type;return t==="number"||t==="range"}function tt(l){return l===""?null:+l}async function Pt(l,t){const d={...t,credentials:"include"},f=await fetch(l,d);return f.status===401?(It(),At()):f}function At(){const l=window.location.pathname+window.location.search,t=encodeURIComponent(l);return lt(`/auth?redirectFrom=${t}`),new Promise(()=>{})}function Lt(l,t){if(l===t)return;let d;switch(!0){case(l!==null&&t===null):d=location.pathname.slice(1+l.length)||"/";break;case(l===null&&t!==null):d=`/${t}`+location.pathname;break;case l!==t:d=`/${t}`+location.pathname.slice(1+l.length)||"/";break;default:throw new Error("Invalid locale change params")}return lt(d,{noScroll:!0})}function ct(l,t){t.onClose()}function Ut(l,t){t.onSubmit&&t.onSubmit()}var jt=g('<button type="button" class="btn-close" aria-label="Close"></button>'),Rt=g('<button type="button" class="btn btn-primary"> </button>'),Ht=g('<div class="modal-footer svelte-1swe5em"><button type="button" class="btn btn-secondary"> </button> <!></div>'),Mt=g('<div class="modal fade show" style="display: block;" tabindex="-1" aria-modal="true" role="dialog"><div class="modal-backdrop fade show svelte-1swe5em"></div> <div><div class="modal-content svelte-1swe5em"><div class="modal-header svelte-1swe5em"><h5 class="modal-title svelte-1swe5em"> </h5> <!></div> <div class="modal-body svelte-1swe5em"><!></div> <!></div></div></div>');function qt(l,t){Ie(t,!0);const d=Z(t,"show",3,!1),f=Z(t,"title",3,""),v=Z(t,"submitText",3,"Submit"),x=Z(t,"cancelText",3,"Cancel"),y=Z(t,"submitDisabled",3,!1),I=Z(t,"cancelDisabled",3,!1),D=Z(t,"isSubmitting",3,!1),h=Z(t,"size",3,"md"),L=Z(t,"centered",3,!0),z=Z(t,"showFooter",3,!0),T=Z(t,"showCloseButton",3,!0),M=Z(t,"showSubmitButton",3,!0),W=ke(()=>`modal-dialog modal-${h()} ${L()?"modal-dialog-centered":""}`);var ae=Ee(),q=xe(ae);{var O=K=>{var $=Mt(),re=b(a($),2),ee=a(re),te=a(ee),X=a(te),ie=a(X,!0);u(X);var se=b(X,2);{var ne=s=>{var c=jt();c.__click=[ct,t],m(s,c)};B(se,s=>{T()&&s(ne)})}u(te);var _=b(te,2),i=a(_);St(i,()=>t.children),u(_);var n=b(_,2);{var o=s=>{var c=Ht(),p=a(c);p.__click=[ct,t];var C=a(p,!0);u(p);var w=b(p,2);{var S=j=>{var R=Rt();R.__click=[Ut,t];var P=a(R,!0);u(R),k(()=>{R.disabled=y()||D(),E(P,D()?`${v()}...`:v())}),m(j,R)};B(w,j=>{M()&&j(S)})}u(c),k(()=>{p.disabled=I(),E(C,x())}),m(s,c)};B(n,s=>{z()&&s(o)})}u(ee),u(re),u($),k(()=>{_e(re,1,_t(e(W)),"svelte-1swe5em"),E(ie,f())}),m(K,$)};B(q,K=>{d()&&K(O)})}m(l,ae),Se()}Le(["click"]);function Ot(l,t){r(t,!e(t))}var zt=(l,t)=>t("en"),Gt=(l,t)=>t("ru"),Kt=(l,t)=>t(null),Wt=g('<div class="dropdown mx-2"><button class="btn btn-outline-secondary btn-sm dropdown-toggle d-flex align-items-center justify-content-center" style="width: 110px; min-width: 110px;" type="button" id="locale-dropdown"><i class="bi bi-globe me-1"></i> </button> <ul aria-labelledby="locale-dropdown"><li><button><i class="bi bi-translate me-2"></i> English</button></li> <li><button><i class="bi bi-translate me-2"></i> Русский</button></li> <li><button><i class="bi bi-globe me-2"></i> Auto</button></li></ul></div>');function qr(l,t){Ie(t,!0);let d=G(!1),f;bt(()=>{function q(O){f&&!f.contains(O.target)&&r(d,!1)}return document.addEventListener("mousedown",q),()=>{document.removeEventListener("mousedown",q)}});function v(q){Lt(t.currentLocale,q),r(d,!1)}function x(q){switch(q){case"en":return"English";case"ru":return"Русский";case null:return"Auto";default:return"Auto"}}var y=Wt(),I=a(y);I.__click=[Ot,d];var D=b(a(I));u(I);var h=b(I,2),L=a(h),z=a(L);z.__click=[zt,v],u(L);var T=b(L,2),M=a(T);M.__click=[Gt,v],u(T);var W=b(T,2),ae=a(W);ae.__click=[Kt,v],u(W),u(h),u(y),Ne(y,q=>f=q,()=>f),k(q=>{U(I,"aria-expanded",e(d)),E(D,` ${q??""}`),_e(h,1,`dropdown-menu ${e(d)?"show":""}`),_e(z,1,`dropdown-item ${t.currentLocale==="en"?"active":""}`),_e(M,1,`dropdown-item ${t.currentLocale==="ru"?"active":""}`),_e(ae,1,`dropdown-item ${t.currentLocale===null?"active":""}`)},[()=>x(t.currentLocale)]),m(l,y),Se()}Le(["click"]);var Nt=g('<span class="text-danger">*</span>'),Qt=(l,t)=>t(l.currentTarget.value),Vt=(l,t)=>t("en"),Yt=(l,t)=>t("ru"),Jt=g('<li class="badge bg-light text-dark me-1"> </li>'),Xt=g('<div class="mt-2 small text-muted"><div> </div> <ul class="list-unstyled mb-0 mt-1"></ul></div>'),Zt=g('<div class="mb-3"><label class="form-label"> <!></label> <div class="input-group"><input type="text" class="form-control"/> <div class="dropdown"><button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="width: 60px; display: flex; justify-content: space-between; align-items: center;"> </button> <ul class="dropdown-menu"><li><button type="button"> </button></li> <li><button type="button"> </button></li></ul></div></div> <!></div>');function $t(l,t){Ie(t,!0);const d={en:{languages:{en:"English",ru:"Russian"},providedTranslations:"Provided translations:"},ru:{languages:{en:"Английский",ru:"Русский"},providedTranslations:"Указанные переводы:"}};let f=Z(t,"value",15),v=it(t,["$$slots","$$events","$$legacy","value"]);const{id:x,label:y,placeholder:I,required:D=!1,locale:h}=v,L=ke(()=>d[h]);let z=G(De(h));function T(){const w=f().find(S=>S.locale===e(z));return(w==null?void 0:w.value)||""}function M(w){if(w.length){const S=[...f()],j=S.findIndex(R=>R.locale===e(z));j>=0?S[j]={locale:e(z),value:w}:S.push({locale:e(z),value:w}),f(S)}else f(f().filter(S=>S.locale!==e(z)))}function W(w){r(z,w,!0)}function ae(){return e(z).toUpperCase()}var q=Zt(),O=a(q),K=a(O),$=b(K);{var re=w=>{var S=Nt();m(w,S)};B($,w=>{D&&w(re)})}u(O);var ee=b(O,2),te=a(ee);Ke(te),te.__input=[Qt,M];var X=b(te,2),ie=a(X),se=a(ie,!0);u(ie);var ne=b(ie,2),_=a(ne),i=a(_);i.__click=[Vt,W];var n=a(i,!0);u(i),u(_);var o=b(_,2),s=a(o);s.__click=[Yt,W];var c=a(s,!0);u(s),u(o),u(ne),u(X),u(ee);var p=b(ee,2);{var C=w=>{var S=Xt(),j=a(S),R=a(j,!0);u(j);var P=b(j,2);Re(P,21,()=>f().filter(Boolean),F=>F.locale,(F,H)=>{var Y=Jt(),le=a(Y);u(Y),k(()=>E(le,`${e(L).languages[e(H).locale]??""}: ${e(H).value??""}`)),m(F,Y)}),u(P),u(S),k(()=>E(R,e(L).providedTranslations)),m(w,S)};B(p,w=>{f().length>0&&w(C)})}u(q),k((w,S)=>{U(O,"for",x),E(K,`${y??""} `),U(te,"id",x),U(te,"placeholder",I),mt(te,w),te.required=D,U(ie,"id",`dropdown-${x}`),E(se,S),U(ne,"aria-labelledby",`dropdown-${x}`),_e(i,1,`dropdown-item ${e(z)==="en"?"active":""}`),E(n,e(L).languages.en),_e(s,1,`dropdown-item ${e(z)==="ru"?"active":""}`),E(c,e(L).languages.ru)},[T,ae]),m(l,q),Se()}Le(["input","click"]);var eu=g('<span class="text-danger">*</span>'),tu=(l,t)=>t("en"),uu=(l,t)=>t("ru"),au=g('<div class="d-flex justify-content-between align-items-center mb-2"><label class="form-label mb-0"> <!></label> <div class="dropdown"><button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="width: 60px; display: flex; justify-content: space-between; align-items: center;"> </button> <ul class="dropdown-menu dropdown-menu-end"><li><button type="button"> </button></li> <li><button type="button"> </button></li></ul></div></div>'),ru=g('<span class="text-danger">*</span>'),lu=g('<label class="form-label mb-2"> <!></label>'),iu=(l,t)=>t(l.currentTarget.value),nu=(l,t)=>t("en"),ou=(l,t)=>t("ru"),su=g('<div class="d-flex justify-content-between align-items-center mt-2"><div class="d-flex align-items-center"><!></div> <div class="dropdown"><button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="width: 60px; display: flex; justify-content: space-between; align-items: center;"> </button> <ul class="dropdown-menu dropdown-menu-end"><li><button type="button"> </button></li> <li><button type="button"> </button></li></ul></div></div>'),du=g('<li class="badge bg-light text-dark me-1"> </li>'),cu=g('<div class="mt-2 small text-muted"><div> </div> <ul class="list-unstyled mb-0 mt-1"></ul></div>'),vu=g('<div class="mb-3"><!> <textarea class="form-control"></textarea> <!> <!></div>');function Or(l,t){Ie(t,!0);const d={en:{languages:{en:"English",ru:"Russian"},providedTranslations:"Provided translations:"},ru:{languages:{en:"Английский",ru:"Русский"},providedTranslations:"Указанные переводы:"}};let f=Z(t,"value",15),v=it(t,["$$slots","$$events","$$legacy","value"]);const{id:x,label:y,placeholder:I,rows:D=3,required:h=!1,locale:L,languageSelectPosition:z="top",children:T}=v,M=ke(()=>d[L]);let W=G(De(L));function ae(){const i=f().find(n=>n.locale===e(W));return(i==null?void 0:i.value)||""}function q(i){if(i.length){const n=[...f()],o=n.findIndex(s=>s.locale===e(W));o>=0?n[o]={locale:e(W),value:i}:n.push({locale:e(W),value:i}),f(n)}else f(f().filter(n=>n.locale!==e(W)))}function O(i){r(W,i,!0)}function K(){return e(W).toUpperCase()}var $=vu(),re=a($);{var ee=i=>{var n=au(),o=a(n),s=a(o),c=b(s);{var p=N=>{var V=eu();m(N,V)};B(c,N=>{h&&N(p)})}u(o);var C=b(o,2),w=a(C),S=a(w,!0);u(w);var j=b(w,2),R=a(j),P=a(R);P.__click=[tu,O];var F=a(P,!0);u(P),u(R);var H=b(R,2),Y=a(H);Y.__click=[uu,O];var le=a(Y,!0);u(Y),u(H),u(j),u(C),u(n),k(N=>{U(o,"for",x),E(s,`${y??""} `),U(w,"id",`dropdown-${x}`),E(S,N),U(j,"aria-labelledby",`dropdown-${x}`),_e(P,1,`dropdown-item ${e(W)==="en"?"active":""}`),E(F,e(M).languages.en),_e(Y,1,`dropdown-item ${e(W)==="ru"?"active":""}`),E(le,e(M).languages.ru)},[K]),m(i,n)},te=i=>{var n=lu(),o=a(n),s=b(o);{var c=p=>{var C=ru();m(p,C)};B(s,p=>{h&&p(c)})}u(n),k(()=>{U(n,"for",x),E(o,`${y??""} `)}),m(i,n)};B(re,i=>{z==="top"?i(ee):i(te,!1)})}var X=b(re,2);wt(X),X.__input=[iu,q];var ie=b(X,2);{var se=i=>{var n=su(),o=a(n),s=a(o);{var c=le=>{T(le)};B(s,le=>{T&&le(c)})}u(o);var p=b(o,2),C=a(p),w=a(C,!0);u(C);var S=b(C,2),j=a(S),R=a(j);R.__click=[nu,O];var P=a(R,!0);u(R),u(j);var F=b(j,2),H=a(F);H.__click=[ou,O];var Y=a(H,!0);u(H),u(F),u(S),u(p),u(n),k(le=>{U(C,"id",`dropdown-${x}`),E(w,le),U(S,"aria-labelledby",`dropdown-${x}`),_e(R,1,`dropdown-item ${e(W)==="en"?"active":""}`),E(P,e(M).languages.en),_e(H,1,`dropdown-item ${e(W)==="ru"?"active":""}`),E(Y,e(M).languages.ru)},[K]),m(i,n)};B(ie,i=>{z==="bottom"&&i(se)})}var ne=b(ie,2);{var _=i=>{var n=cu(),o=a(n),s=a(o,!0);u(o);var c=b(o,2);Re(c,21,()=>f().filter(Boolean),p=>p.locale,(p,C)=>{var w=du(),S=a(w);u(w),k(j=>E(S,`${e(M).languages[e(C).locale]??""}: ${j??""}`),[()=>e(C).value.length>50?e(C).value.slice(0,47)+"...":e(C).value]),m(p,w)}),u(c),u(n),k(()=>E(s,e(M).providedTranslations)),m(i,n)};B(ne,i=>{f().length>0&&i(_)})}u($),k(i=>{U(X,"id",x),U(X,"rows",D),U(X,"placeholder",I),mt(X,i),X.required=h},[ae]),m(l,$),Se()}Le(["click","input"]);const mu=["Activate","AddUndo","BeforeAddUndo","BeforeExecCommand","BeforeGetContent","BeforeRenderUI","BeforeSetContent","BeforePaste","Blur","Change","ClearUndos","Click","CommentChange","CompositionEnd","CompositionStart","CompositionUpdate","ContextMenu","Copy","Cut","Dblclick","Deactivate","Dirty","Drag","DragDrop","DragEnd","DragGesture","DragOver","Drop","ExecCommand","Focus","FocusIn","FocusOut","GetContent","Hide","Init","Input","KeyDown","KeyPress","KeyUp","LoadContent","MouseDown","MouseEnter","MouseLeave","MouseMove","MouseOut","MouseOver","MouseUp","NodeChange","ObjectResizeStart","ObjectResized","ObjectSelected","Paste","PostProcess","PostRender","PreProcess","ProgressState","Redo","Remove","Reset","ResizeEditor","SaveContent","SelectionChange","SetAttrib","SetContent","Show","Submit","Undo","VisualAid"],_u=(l,t)=>{mu.forEach(d=>{l.on(d,f=>{t(d.toLowerCase(),{eventName:d,event:f,editor:l})})})},ft=l=>l+"_"+Math.floor(Math.random()*1e9)+String(Date.now()),bu=l=>typeof l.options.set=="function"&&l.options.isRegistered("disabled"),fu=()=>{let l={listeners:[],scriptId:ft("tiny-script"),scriptLoaded:!1,injected:!1};const t=(f,v,x,y)=>{l.injected=!0;const I=v.createElement("script");I.referrerPolicy="origin",I.type="application/javascript",I.src=x,I.onload=()=>{y()},v.head&&v.head.appendChild(I)};return{load:(f,v,x)=>{l.scriptLoaded?x():(l.listeners.push(x),l.injected||t(l.scriptId,f,v,()=>{l.listeners.forEach(y=>y()),l.scriptLoaded=!0}))}}};let gu=fu();var hu=g("<div></div>"),pu=g('<textarea style="visibility:hidden"></textarea>'),yu=g("<div><!></div>");function xu(l,t){Ie(t,!1);let d=Z(t,"id",24,()=>ft("tinymce-svelte")),f=Z(t,"inline",8,void 0),v=Z(t,"disabled",8,!1),x=Z(t,"readonly",8,!1),y=Z(t,"apiKey",8,"no-api-key"),I=Z(t,"licenseKey",8,void 0),D=Z(t,"channel",8,"7"),h=Z(t,"scriptSrc",8,void 0),L=Z(t,"conf",24,()=>({})),z=Z(t,"modelEvents",8,"change input undo redo"),T=Z(t,"value",12,""),M=Z(t,"text",12,""),W=Z(t,"cssClass",8,"tinymce-wrapper"),ae=ze(),q=ze(),O=ze(),K=ze(T()),$=ze(v()),re=ze(x());const ee=(o,s)=>{var c;typeof((c=o.mode)===null||c===void 0?void 0:c.set)=="function"&&o.mode.set(s?"readonly":"design")},te=(o,s)=>{bu(o)?o.options.set("disabled",s):o.mode.set(s?"readonly":"design")},X=Bt(),ie=()=>{const s=typeof window<"u"?window:global;return s&&s.tinymce?s.tinymce:null},se=()=>{var o;const s=Object.assign(Object.assign({},L()),{target:e(q),inline:f()!==void 0?f():L().inline!==void 0?L().inline:!1,license_key:I(),setup:c=>{c.on("PreInit",()=>{te(c,v()),ee(c,x())}),r(O,c),c.on("init",()=>{c.setContent(T()),c.on(z(),()=>{r(K,c.getContent()),e(K)!==T()&&(T(e(K)),M(c.getContent({format:"text"})))})}),_u(c,X),typeof L().setup=="function"&&L().setup(c)}});kt(q,e(q).style.visibility=""),(o=ie())===null||o===void 0||o.init(s)};bt(()=>{if(ie()!==null)se();else{const o=h()?h():`https://cdn.tiny.cloud/1/${y()}/tinymce/${D()}/tinymce.min.js`;gu.load(e(ae).ownerDocument,o,()=>{se()})}}),Ft(()=>{var o;e(O)&&((o=ie())===null||o===void 0||o.remove(e(O)))}),Et(()=>(e(O),e(K),Je(T()),Je(x()),e(re),Je(v()),e($)),()=>{e(O)&&e(K)!==T()&&(e(O).setContent(T()),M(e(O).getContent({format:"text"}))),e(O)&&x()!==e(re)&&(r(re,x()),ee(e(O),x())),e(O)&&v()!==e($)&&(r($,v()),te(e(O),v()))}),Ct(),Tt();var ne=yu(),_=a(ne);{var i=o=>{var s=hu();Ne(s,c=>r(q,c),()=>e(q)),k(()=>U(s,"id",d())),m(o,s)},n=o=>{var s=pu();Ne(s,c=>r(q,c),()=>e(q)),k(()=>U(s,"id",d())),m(o,s)};B(_,o=>{f()?o(i):o(n,!1)})}u(ne),Ne(ne,o=>r(ae,o),()=>e(ae)),k(()=>_e(ne,1,_t(W()))),m(l,ne),Se()}function wu(l,t){Ie(t,!0);let d=Z(t,"content",15);const f={toolbar:"undo redo | bold italic strikethrough underline | alignleft aligncenter alignright",menubar:!1,height:300,promotion:!1,relative_urls:!1,init_instance_callback:v=>{var x;(x=t.onEditorInit)==null||x.call(t,v)}};xu(l,{licenseKey:"gpl",scriptSrc:"/tinymce/tinymce.min.js",get conf(){return f},get value(){return d()},set value(v){d(v)}}),Se()}var Eu=g('<span class="text-danger">*</span>'),Cu=(l,t)=>t("en"),ku=(l,t)=>t("ru"),Du=g('<div class="d-flex justify-content-between align-items-center mb-2"><p class="form-label mb-0 svelte-3fvz9w"> <!></p> <div class="dropdown"><button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="width: 60px; display: flex; justify-content: space-between; align-items: center;"> </button> <ul class="dropdown-menu dropdown-menu-end"><li><button type="button"> </button></li> <li><button type="button"> </button></li></ul></div></div>'),Iu=g('<span class="text-danger">*</span>'),Su=g('<p class="form-label mb-2 svelte-3fvz9w"> <!></p>'),Bu=(l,t)=>t("en"),Fu=(l,t)=>t("ru"),Tu=g('<div class="d-flex justify-content-between align-items-center mt-2"><div class="d-flex align-items-center"><!></div> <div class="dropdown"><button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="width: 60px; display: flex; justify-content: space-between; align-items: center;"> </button> <ul class="dropdown-menu dropdown-menu-end"><li><button type="button"> </button></li> <li><button type="button"> </button></li></ul></div></div>'),Pu=g('<li class="badge bg-light text-dark me-1"> </li>'),Au=g('<div class="mt-2 small text-muted"><div> </div> <ul class="list-unstyled mb-0 mt-1"></ul></div>'),Lu=g('<div class="mb-3"><!> <div><!></div> <!> <!></div>');function Uu(l,t){Ie(t,!0);const d={en:{languages:{en:"English",ru:"Russian"},providedTranslations:"Provided translations:"},ru:{languages:{en:"Английский",ru:"Русский"},providedTranslations:"Указанные переводы:"}};let f=Z(t,"value",15),v=it(t,["$$slots","$$events","$$legacy","value"]);const{id:x,label:y,required:I=!1,locale:D,languageSelectPosition:h="top",children:L,onEditorInit:z}=v,T=ke(()=>d[D]);let M=G(De(D));function W(){const n=f().find(o=>o.locale===e(M));return(n==null?void 0:n.value)||""}function ae(n){if(n.length){const o=[...f()],s=o.findIndex(c=>c.locale===e(M));s>=0?o[s]={locale:e(M),value:n}:o.push({locale:e(M),value:n}),f(o)}else f(f().filter(o=>o.locale!==e(M)))}function q(n){r(M,n,!0)}function O(){return e(M).toUpperCase()}let K=G("");He(()=>{r(K,W(),!0)}),He(()=>{e(K)!==W()&&ae(e(K))});var $=Lu(),re=a($);{var ee=n=>{var o=Du(),s=a(o),c=a(s),p=b(c);{var C=V=>{var oe=Eu();m(V,oe)};B(p,V=>{I&&V(C)})}u(s);var w=b(s,2),S=a(w),j=a(S,!0);u(S);var R=b(S,2),P=a(R),F=a(P);F.__click=[Cu,q];var H=a(F,!0);u(F),u(P);var Y=b(P,2),le=a(Y);le.__click=[ku,q];var N=a(le,!0);u(le),u(Y),u(R),u(w),u(o),k(V=>{E(c,`${y??""} `),U(S,"id",`dropdown-${x}`),E(j,V),U(R,"aria-labelledby",`dropdown-${x}`),_e(F,1,`dropdown-item ${e(M)==="en"?"active":""}`),E(H,e(T).languages.en),_e(le,1,`dropdown-item ${e(M)==="ru"?"active":""}`),E(N,e(T).languages.ru)},[O]),m(n,o)},te=n=>{var o=Su(),s=a(o),c=b(s);{var p=C=>{var w=Iu();m(C,w)};B(c,C=>{I&&C(p)})}u(o),k(()=>E(s,`${y??""} `)),m(n,o)};B(re,n=>{h==="top"?n(ee):n(te,!1)})}var X=b(re,2),ie=a(X);wu(ie,{get onEditorInit(){return z},get content(){return e(K)},set content(n){r(K,n,!0)}}),u(X);var se=b(X,2);{var ne=n=>{var o=Tu(),s=a(o),c=a(s);{var p=N=>{L(N)};B(c,N=>{L&&N(p)})}u(s);var C=b(s,2),w=a(C),S=a(w,!0);u(w);var j=b(w,2),R=a(j),P=a(R);P.__click=[Bu,q];var F=a(P,!0);u(P),u(R);var H=b(R,2),Y=a(H);Y.__click=[Fu,q];var le=a(Y,!0);u(Y),u(H),u(j),u(C),u(o),k(N=>{U(w,"id",`dropdown-${x}`),E(S,N),U(j,"aria-labelledby",`dropdown-${x}`),_e(P,1,`dropdown-item ${e(M)==="en"?"active":""}`),E(F,e(T).languages.en),_e(Y,1,`dropdown-item ${e(M)==="ru"?"active":""}`),E(le,e(T).languages.ru)},[O]),m(n,o)};B(se,n=>{h==="bottom"&&n(ne)})}var _=b(se,2);{var i=n=>{var o=Au(),s=a(o),c=a(s,!0);u(s);var p=b(s,2);Re(p,21,()=>f().filter(Boolean),C=>C.locale,(C,w)=>{var S=Pu(),j=a(S,!0);u(S),k(()=>E(j,e(T).languages[e(w).locale])),m(C,S)}),u(p),u(o),k(()=>E(c,e(T).providedTranslations)),m(n,o)};B(_,n=>{f().length>0&&n(i)})}u($),k(()=>U(X,"id",x)),m(l,$),Se()}Le(["click"]);function ju(l,t,d){const f=l.target;r(t,f.value,!0),d(e(t))}function Ru(l,t,d,f){r(t,!0),r(d,""),r(f,[],!0)}function Hu(l,t,d,f){r(t,!1),r(d,""),r(f,[],!0)}var Mu=g('<label for="tag-search-input" class="form-label"> </label>'),qu=(l,t,d)=>t(e(d).id),Ou=g('<span class="badge bg-primary d-flex align-items-center"> <button type="button" class="btn-close btn-close-white ms-1" style="font-size: 0.7em;" aria-label="Remove tag"></button></span>'),zu=g('<button type="button" class="btn btn-outline-secondary btn-sm"><i class="bi bi-plus"></i> </button>'),Gu=g('<div class="p-2 text-muted"><i class="bi bi-hourglass-split me-1"></i> </div>'),Ku=g('<div class="p-2 text-muted"> </div>'),Wu=(l,t,d)=>t(e(d)),Nu=g('<button type="button" class="dropdown-item d-flex align-items-center svelte-1snh682"><i class="bi bi-tag me-2"></i> </button>'),Qu=g('<div class="search-results position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm svelte-1snh682" style="z-index: 1000; max-height: 200px; overflow-y: auto;"><!></div>'),Vu=g('<div class="tag-search-container position-relative"><div class="input-group"><input type="text" id="tag-search-input" class="form-control"/> <button type="button" class="btn btn-outline-secondary" aria-label="Close tag search"><i class="bi bi-x"></i></button></div> <!></div>'),Yu=g('<div class="mb-3"><!> <div class="tag-picker svelte-1snh682"><div class="selected-tags d-flex flex-wrap gap-2 mb-2 svelte-1snh682"><!> <!></div> <!></div></div>');function Ju(l,t){Ie(t,!0);const d={en:{tags:"Tags",addTag:"Add tag",searchTags:"Search tags...",noTagsFound:"No tags found",loading:"Loading..."},ru:{tags:"Теги",addTag:"Добавить тег",searchTags:"Поиск тегов...",noTagsFound:"Теги не найдены",loading:"Загрузка..."}},{fetcher:f}=We();let v=Z(t,"selectedTagIds",15);const x=ke(()=>d[t.locale]);let y=G(!1),I=G(""),D=G(De([])),h=G(De([])),L=G(!1),z=G(null);function T(i){var o;const n=i.find(s=>s.locale===t.locale);return(n==null?void 0:n.value)||((o=i[0])==null?void 0:o.value)||""}He(()=>{v().length>0?M():r(h,[],!0)});async function M(){try{r(h,await f.tag.list.get({ids:v()}),!0)}catch(i){console.error("Failed to load selected tags:",i)}}async function W(i){const n=i.trim();if(!n){r(D,[],!0);return}r(L,!0);try{r(D,await f.tag.list.get({query:n}).then(o=>o.filter(s=>!v().includes(s.id))),!0)}catch(o){console.error("Failed to search tags:",o),r(D,[],!0)}finally{r(L,!1)}}function ae(i){e(z)&&clearTimeout(e(z)),r(z,setTimeout(()=>{W(i)},300),!0)}function q(i){v().includes(i.id)||(v([...v(),i.id]),r(h,[...e(h),i],!0),r(D,e(D).filter(n=>n.id!==i.id),!0),r(I,""),r(D,[],!0),r(y,!1))}function O(i){v(v().filter(n=>n!==i)),r(h,e(h).filter(n=>n.id!==i),!0)}var K=Yu(),$=a(K);{var re=i=>{var n=Mu(),o=a(n,!0);u(n),k(()=>E(o,t.label)),m(i,n)};B($,i=>{t.label&&e(y)&&i(re)})}var ee=b($,2),te=a(ee),X=a(te);Re(X,17,()=>e(h),i=>i.id,(i,n)=>{var o=Ou(),s=a(o),c=b(s);c.__click=[qu,O,n],u(o),k(p=>E(s,`${p??""} `),[()=>T(e(n).name)]),m(i,o)});var ie=b(X,2);{var se=i=>{var n=zu();n.__click=[Ru,y,I,D];var o=b(a(n));u(n),k(()=>E(o,` ${e(x).addTag??""}`)),m(i,n)};B(ie,i=>{e(y)||i(se)})}u(te);var ne=b(te,2);{var _=i=>{var n=Vu(),o=a(n),s=a(o);Ke(s),s.__input=[ju,I,ae];var c=b(s,2);c.__click=[Hu,y,I,D],u(o);var p=b(o,2);{var C=w=>{var S=Qu(),j=a(S);{var R=F=>{var H=Gu(),Y=b(a(H));u(H),k(()=>E(Y,` ${e(x).loading??""}`)),m(F,H)},P=F=>{var H=Ee(),Y=xe(H);{var le=V=>{var oe=Ku(),ve=a(oe,!0);u(oe),k(()=>E(ve,e(x).noTagsFound)),m(V,oe)},N=V=>{var oe=Ee(),ve=xe(oe);Re(ve,17,()=>e(D),J=>J.id,(J,de)=>{var he=Nu();he.__click=[Wu,q,de];var Ue=b(a(he));u(he),k(Pe=>E(Ue,` ${Pe??""}`),[()=>T(e(de).name)]),m(J,he)}),m(V,oe)};B(Y,V=>{e(D).length===0?V(le):V(N,!1)},!0)}m(F,H)};B(j,F=>{e(L)?F(R):F(P,!1)})}u(S),m(w,S)};B(p,w=>{e(I).trim()&&(e(L)||e(D).length>0)&&w(C)})}u(n),k(()=>U(s,"placeholder",t.placeholder||e(x).searchTags)),Qe(s,()=>e(I),w=>r(I,w)),m(i,n)};B(ne,i=>{e(y)&&i(_)})}u(ee),u(K),m(l,K),Se()}Le(["click","input"]);function Xu(l,t,d){const f=l.target;r(t,f.value,!0),d(e(t))}function Zu(l,t,d){t(null),r(d,null)}function $u(l,t,d,f){r(t,!0),r(d,""),r(f,[],!0)}function ea(l,t,d,f){r(t,!1),r(d,""),r(f,[],!0)}var ta=g('<label for="user-search-input" class="form-label"> </label>'),ua=g('<img class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;"/>'),aa=g('<img class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;"/>'),ra=g('<div class="d-flex align-items-center bg-light border rounded p-2 flex-grow-1"><!> <div class="flex-grow-1"><div class="fw-medium"> </div></div> <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-x"></i></button></div>'),la=g('<button type="button" class="btn btn-outline-secondary"><i class="bi bi-person-plus"></i> </button>'),ia=g('<div class="p-2 text-muted"><i class="bi bi-hourglass-split me-1"></i> </div>'),na=g('<div class="p-2 text-muted"> </div>'),oa=(l,t,d)=>t(e(d)),sa=g('<img class="rounded-circle me-2" style="width: 24px; height: 24px; object-fit: cover;"/>'),da=g('<img class="rounded-circle me-2" style="width: 24px; height: 24px; object-fit: cover;"/>'),ca=g('<button type="button" class="dropdown-item d-flex align-items-center p-2 svelte-fbacwr"><!> <div><div class="fw-medium"> </div></div></button>'),va=g('<div class="search-results position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm svelte-fbacwr" style="z-index: 1000; max-height: 200px; overflow-y: auto;"><!></div>'),ma=g('<div class="user-search-container position-relative"><div class="input-group"><input type="text" id="user-search-input" class="form-control"/> <button type="button" class="btn btn-outline-secondary" aria-label="Close user search"><i class="bi bi-x"></i></button></div> <!></div>'),_a=g('<div class="mb-3"><!> <div class="user-picker svelte-fbacwr"><div class="selected-user d-flex align-items-center gap-2 mb-2 svelte-fbacwr"><!></div> <!></div></div>');function zr(l,t){Ie(t,!0);const d={en:{user:"User",selectUser:"Select user",searchUsers:"Search users...",noUsersFound:"No users found",loading:"Loading...",clearSelection:"Clear selection"},ru:{user:"Пользователь",selectUser:"Выбрать пользователя",searchUsers:"Поиск пользователей...",noUsersFound:"Пользователи не найдены",loading:"Загрузка...",clearSelection:"Очистить выбор"}},{fetcher:f}=We();let v=Z(t,"selectedUserId",15);const x=ke(()=>d[t.locale]);let y=G(!1),I=G(""),D=G(De([])),h=G(null),L=G(!1),z=G(null);function T(_){var n;const i=_.find(o=>o.locale===t.locale);return(i==null?void 0:i.value)||((n=_[0])==null?void 0:n.value)||""}He(()=>{v()?M():r(h,null)});async function M(){var _;if(v()&&v()!==((_=e(h))==null?void 0:_.id))try{const i=await f.user.list.get({ids:[v()]});r(h,i[0]||null,!0)}catch(i){console.error("Failed to load selected user:",i),r(h,null)}}async function W(_){const i=_.trim();if(!i){r(D,[],!0);return}r(L,!0);try{r(D,await f.user.list.get({query:i}),!0)}catch(n){console.error("Failed to search users:",n),r(D,[],!0)}finally{r(L,!1)}}function ae(_){e(z)&&clearTimeout(e(z)),r(z,setTimeout(()=>{W(_)},300),!0)}function q(_){v(_.id),r(h,_,!0),r(I,""),r(D,[],!0),r(y,!1)}var O=_a(),K=a(O);{var $=_=>{var i=ta(),n=a(i,!0);u(i),k(()=>E(n,t.label)),m(_,i)};B(K,_=>{t.label&&e(y)&&_($)})}var re=b(K,2),ee=a(re),te=a(ee);{var X=_=>{var i=ra(),n=a(i);{var o=S=>{var j=ua();k(R=>{U(j,"src",`/images/${e(h).image}`),U(j,"alt",R)},[()=>T(e(h).name)]),m(S,j)},s=S=>{var j=aa();U(j,"src","/images/default-avatar.png"),k(R=>U(j,"alt",R),[()=>T(e(h).name)]),m(S,j)};B(n,S=>{e(h).image?S(o):S(s,!1)})}var c=b(n,2),p=a(c),C=a(p,!0);u(p),u(c);var w=b(c,2);w.__click=[Zu,v,h],u(i),k(S=>{E(C,S),U(w,"aria-label",e(x).clearSelection)},[()=>T(e(h).name)]),m(_,i)},ie=_=>{var i=Ee(),n=xe(i);{var o=s=>{var c=la();c.__click=[$u,y,I,D];var p=b(a(c));u(c),k(()=>E(p,` ${e(x).selectUser??""}`)),m(s,c)};B(n,s=>{e(y)||s(o)})}m(_,i)};B(te,_=>{e(h)?_(X):_(ie,!1)})}u(ee);var se=b(ee,2);{var ne=_=>{var i=ma(),n=a(i),o=a(n);Ke(o),o.__input=[Xu,I,ae];var s=b(o,2);s.__click=[ea,y,I,D],u(n);var c=b(n,2);{var p=C=>{var w=va(),S=a(w);{var j=P=>{var F=ia(),H=b(a(F));u(F),k(()=>E(H,` ${e(x).loading??""}`)),m(P,F)},R=P=>{var F=Ee(),H=xe(F);{var Y=N=>{var V=na(),oe=a(V,!0);u(V),k(()=>E(oe,e(x).noUsersFound)),m(N,V)},le=N=>{var V=Ee(),oe=xe(V);Re(oe,17,()=>e(D),ve=>ve.id,(ve,J)=>{var de=ca();de.__click=[oa,q,J];var he=a(de);{var Ue=me=>{var pe=sa();k(be=>{U(pe,"src",`/images/${e(J).image}`),U(pe,"alt",be)},[()=>T(e(J).name)]),m(me,pe)},Pe=me=>{var pe=da();U(pe,"src","/images/default-avatar.png"),k(be=>U(pe,"alt",be),[()=>T(e(J).name)]),m(me,pe)};B(he,me=>{e(J).image?me(Ue):me(Pe,!1)})}var Ae=b(he,2),Ce=a(Ae),Me=a(Ce,!0);u(Ce),u(Ae),u(de),k(me=>E(Me,me),[()=>T(e(J).name)]),m(ve,de)}),m(N,V)};B(H,N=>{e(D).length===0?N(Y):N(le,!1)},!0)}m(P,F)};B(S,P=>{e(L)?P(j):P(R,!1)})}u(w),m(C,w)};B(c,C=>{e(I).trim()&&(e(L)||e(D).length>0)&&C(p)})}u(i),k(()=>U(o,"placeholder",t.placeholder||e(x).searchUsers)),Qe(o,()=>e(I),C=>r(I,C)),m(_,i)};B(se,_=>{e(y)&&_(ne)})}u(re),u(O),m(l,O),Se()}Le(["click","input"]);function ba(l,t,d){const f=l.target;r(t,f.value,!0),d(e(t))}function fa(l,t,d){t(null),r(d,null)}function ga(l,t,d,f){r(t,!0),r(d,""),r(f,[],!0)}function ha(l,t,d,f){r(t,!1),r(d,""),r(f,[],!0)}var pa=g('<label for="hub-search-input" class="form-label"> </label>'),ya=g('<img class="rounded me-2" style="width: 32px; height: 32px; object-fit: cover;"/>'),xa=g('<div class="rounded bg-primary d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; color: white; font-size: 14px;"><i class="bi bi-collection"></i></div>'),wa=g('<div class="d-flex align-items-center bg-light border rounded p-2 flex-grow-1"><!> <div class="flex-grow-1"><div class="fw-medium"> </div> <small class="text-muted"> </small></div> <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-x"></i></button></div>'),Ea=g('<button type="button" class="btn btn-outline-secondary"><i class="bi bi-collection-fill"></i> </button>'),Ca=g('<div class="p-2 text-muted"><i class="bi bi-hourglass-split me-1"></i> </div>'),ka=g('<div class="p-2 text-muted"> </div>'),Da=(l,t,d)=>t(e(d)),Ia=g('<img class="rounded me-2" style="width: 24px; height: 24px; object-fit: cover;"/>'),Sa=g('<div class="rounded bg-primary d-flex align-items-center justify-content-center me-2" style="width: 24px; height: 24px; color: white; font-size: 12px;"><i class="bi bi-collection"></i></div>'),Ba=g('<button type="button" class="dropdown-item d-flex align-items-center p-2 svelte-11tmxf3"><!> <div><div class="fw-medium"> </div> <small class="text-muted"> </small></div></button>'),Fa=g('<div class="search-results position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm svelte-11tmxf3" style="z-index: 1000; max-height: 200px; overflow-y: auto;"><!></div>'),Ta=g('<div class="hub-search-container position-relative"><div class="input-group"><input type="text" id="hub-search-input" class="form-control"/> <button type="button" class="btn btn-outline-secondary" aria-label="Close hub search"><i class="bi bi-x"></i></button></div> <!></div>'),Pa=g('<div class="mb-3"><!> <div class="hub-picker svelte-11tmxf3"><div class="selected-hub d-flex align-items-center gap-2 mb-2 svelte-11tmxf3"><!></div> <!></div></div>');function Aa(l,t){Ie(t,!0);const d={en:{hub:"Hub",selectHub:"Select hub",searchHubs:"Search hubs...",noHubsFound:"No hubs found",loading:"Loading...",clearSelection:"Clear selection"},ru:{hub:"Хаб",selectHub:"Выбрать хаб",searchHubs:"Поиск хабов...",noHubsFound:"Хабы не найдены",loading:"Загрузка...",clearSelection:"Очистить выбор"}},{fetcher:f}=We();let v=Z(t,"selectedHubId",15);const x=ke(()=>d[t.locale]);let y=G(!1),I=G(""),D=G(De([])),h=G(null),L=G(!1),z=G(null);function T(_){var n;const i=_.find(o=>o.locale===t.locale);return(i==null?void 0:i.value)||((n=_[0])==null?void 0:n.value)||""}He(()=>{v()?M():r(h,null)});async function M(){if(v())try{const _=await f.reactor.hub.list.get({ids:[v()]});r(h,_[0]||null,!0)}catch(_){console.error("Failed to load selected hub:",_),r(h,null)}}async function W(_){const i=_.trim();if(!i){r(D,[],!0);return}r(L,!0);try{r(D,await f.reactor.hub.list.get({query:i}),!0)}catch(n){console.error("Failed to search hubs:",n),r(D,[],!0)}finally{r(L,!1)}}function ae(_){e(z)&&clearTimeout(e(z)),r(z,setTimeout(()=>{W(_)},300),!0)}function q(_){v(_.id),r(h,_,!0),r(I,""),r(D,[],!0),r(y,!1)}var O=Pa(),K=a(O);{var $=_=>{var i=pa(),n=a(i,!0);u(i),k(()=>E(n,t.label)),m(_,i)};B(K,_=>{t.label&&e(y)&&_($)})}var re=b(K,2),ee=a(re),te=a(ee);{var X=_=>{var i=wa(),n=a(i);{var o=R=>{var P=ya();k(F=>{U(P,"src",`/images/${e(h).image}`),U(P,"alt",F)},[()=>T(e(h).name)]),m(R,P)},s=R=>{var P=xa();m(R,P)};B(n,R=>{e(h).image?R(o):R(s,!1)})}var c=b(n,2),p=a(c),C=a(p,!0);u(p);var w=b(p,2),S=a(w,!0);u(w),u(c);var j=b(c,2);j.__click=[fa,v,h],u(i),k((R,P)=>{E(C,R),E(S,P),U(j,"aria-label",e(x).clearSelection)},[()=>T(e(h).name),()=>T(e(h).description)]),m(_,i)},ie=_=>{var i=Ee(),n=xe(i);{var o=s=>{var c=Ea();c.__click=[ga,y,I,D];var p=b(a(c));u(c),k(()=>E(p,` ${e(x).selectHub??""}`)),m(s,c)};B(n,s=>{e(y)||s(o)})}m(_,i)};B(te,_=>{e(h)?_(X):_(ie,!1)})}u(ee);var se=b(ee,2);{var ne=_=>{var i=Ta(),n=a(i),o=a(n);Ke(o),o.__input=[ba,I,ae];var s=b(o,2);s.__click=[ha,y,I,D],u(n);var c=b(n,2);{var p=C=>{var w=Fa(),S=a(w);{var j=P=>{var F=Ca(),H=b(a(F));u(F),k(()=>E(H,` ${e(x).loading??""}`)),m(P,F)},R=P=>{var F=Ee(),H=xe(F);{var Y=N=>{var V=ka(),oe=a(V,!0);u(V),k(()=>E(oe,e(x).noHubsFound)),m(N,V)},le=N=>{var V=Ee(),oe=xe(V);Re(oe,17,()=>e(D),ve=>ve.id,(ve,J)=>{var de=Ba();de.__click=[Da,q,J];var he=a(de);{var Ue=be=>{var Be=Ia();k(fe=>{U(Be,"src",`/images/${e(J).image}`),U(Be,"alt",fe)},[()=>T(e(J).name)]),m(be,Be)},Pe=be=>{var Be=Sa();m(be,Be)};B(he,be=>{e(J).image?be(Ue):be(Pe,!1)})}var Ae=b(he,2),Ce=a(Ae),Me=a(Ce,!0);u(Ce);var me=b(Ce,2),pe=a(me,!0);u(me),u(Ae),u(de),k((be,Be)=>{E(Me,be),E(pe,Be)},[()=>T(e(J).name),()=>T(e(J).description)]),m(ve,de)}),m(N,V)};B(H,N=>{e(D).length===0?N(Y):N(le,!1)},!0)}m(P,F)};B(S,P=>{e(L)?P(j):P(R,!1)})}u(w),m(C,w)};B(c,C=>{e(I).trim()&&(e(L)||e(D).length>0)&&C(p)})}u(i),k(()=>U(o,"placeholder",t.placeholder||e(x).searchHubs)),Qe(o,()=>e(I),C=>r(I,C)),m(_,i)};B(se,_=>{e(y)&&_(ne)})}u(re),u(O),m(l,O),Se()}Le(["click","input"]);function La(l,t,d){const f=l.target;r(t,f.value,!0),d(e(t))}function Ua(l,t,d){t(null),r(d,null)}function ja(l,t,d,f){r(t,!0),r(d,""),r(f,[],!0)}function Ra(l,t,d,f){r(t,!1),r(d,""),r(f,[],!0)}var Ha=g('<label for="community-search-input" class="form-label"> </label>'),Ma=g('<img class="rounded me-2" style="width: 32px; height: 32px; object-fit: cover;"/>'),qa=g('<div class="rounded bg-success d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; color: white; font-size: 14px;"><i class="bi bi-people"></i></div>'),Oa=g('<div class="d-flex align-items-center bg-light border rounded p-2 flex-grow-1"><!> <div class="flex-grow-1"><div class="fw-medium"> </div> <small class="text-muted"> <!></small></div> <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-x"></i></button></div>'),za=g('<button type="button" class="btn btn-outline-secondary"><i class="bi bi-people-fill"></i> </button>'),Ga=g('<div class="p-2 text-muted"><i class="bi bi-hourglass-split me-1"></i> </div>'),Ka=g('<div class="p-2 text-muted"> </div>'),Wa=(l,t,d)=>t(e(d)),Na=g('<img class="rounded me-2" style="width: 24px; height: 24px; object-fit: cover;"/>'),Qa=g('<div class="rounded bg-success d-flex align-items-center justify-content-center me-2" style="width: 24px; height: 24px; color: white; font-size: 12px;"><i class="bi bi-people"></i></div>'),Va=g('<button type="button" class="dropdown-item d-flex align-items-center p-2 svelte-njlx63"><!> <div><div class="fw-medium"> </div> <small class="text-muted"> <!></small></div></button>'),Ya=g('<div class="search-results position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm svelte-njlx63" style="z-index: 1000; max-height: 200px; overflow-y: auto;"><!></div>'),Ja=g('<div class="community-search-container position-relative"><div class="input-group"><input type="text" id="community-search-input" class="form-control"/> <button type="button" class="btn btn-outline-secondary" aria-label="Close community search"><i class="bi bi-x"></i></button></div> <!></div>'),Xa=g('<div class="mb-3"><!> <div class="community-picker svelte-njlx63"><div class="selected-community d-flex align-items-center gap-2 mb-2 svelte-njlx63"><!></div> <!></div></div>');function Za(l,t){Ie(t,!0);const d={en:{community:"Community",selectCommunity:"Select community",searchCommunities:"Search communities...",noCommunitiesFound:"No communities found",loading:"Loading...",clearSelection:"Clear selection"},ru:{community:"Сообщество",selectCommunity:"Выбрать сообщество",searchCommunities:"Поиск сообществ...",noCommunitiesFound:"Сообщества не найдены",loading:"Загрузка...",clearSelection:"Очистить выбор"}},{fetcher:f}=We();let v=Z(t,"selectedCommunityId",15);const x=ke(()=>d[t.locale]);let y=G(!1),I=G(""),D=G(De([])),h=G(null),L=G(!1),z=G(null);function T(_){var n;const i=_.find(o=>o.locale===t.locale);return(i==null?void 0:i.value)||((n=_[0])==null?void 0:n.value)||""}He(()=>{v()?M():r(h,null)});async function M(){if(v())try{const _=await f.reactor.community.list.get({ids:[v()]});r(h,_[0]||null,!0)}catch(_){console.error("Failed to load selected community:",_),r(h,null)}}async function W(_){const i=_.trim();if(!i){r(D,[],!0);return}r(L,!0);try{const n={query:i};t.hubId&&(n.hubId=t.hubId),r(D,await f.reactor.community.list.get(n),!0)}catch(n){console.error("Failed to search communities:",n),r(D,[],!0)}finally{r(L,!1)}}function ae(_){e(z)&&clearTimeout(e(z)),r(z,setTimeout(()=>{W(_)},300),!0)}function q(_){v(_.id),r(h,_,!0),r(I,""),r(D,[],!0),r(y,!1)}var O=Xa(),K=a(O);{var $=_=>{var i=Ha(),n=a(i,!0);u(i),k(()=>E(n,t.label)),m(_,i)};B(K,_=>{t.label&&e(y)&&_($)})}var re=b(K,2),ee=a(re),te=a(ee);{var X=_=>{var i=Oa(),n=a(i);{var o=F=>{var H=Ma();k(Y=>{U(H,"src",`/images/${e(h).image}`),U(H,"alt",Y)},[()=>T(e(h).name)]),m(F,H)},s=F=>{var H=qa();m(F,H)};B(n,F=>{e(h).image?F(o):F(s,!1)})}var c=b(n,2),p=a(c),C=a(p,!0);u(p);var w=b(p,2),S=a(w),j=b(S);{var R=F=>{var H=st();k(Y=>E(H,`• ${Y??""}`),[()=>T(e(h).hub.name)]),m(F,H)};B(j,F=>{e(h).hub&&F(R)})}u(w),u(c);var P=b(c,2);P.__click=[Ua,v,h],u(i),k((F,H)=>{E(C,F),E(S,`${H??""} `),U(P,"aria-label",e(x).clearSelection)},[()=>T(e(h).name),()=>T(e(h).description)]),m(_,i)},ie=_=>{var i=Ee(),n=xe(i);{var o=s=>{var c=za();c.__click=[ja,y,I,D];var p=b(a(c));u(c),k(()=>E(p,` ${e(x).selectCommunity??""}`)),m(s,c)};B(n,s=>{e(y)||s(o)})}m(_,i)};B(te,_=>{e(h)?_(X):_(ie,!1)})}u(ee);var se=b(ee,2);{var ne=_=>{var i=Ja(),n=a(i),o=a(n);Ke(o),o.__input=[La,I,ae];var s=b(o,2);s.__click=[Ra,y,I,D],u(n);var c=b(n,2);{var p=C=>{var w=Ya(),S=a(w);{var j=P=>{var F=Ga(),H=b(a(F));u(F),k(()=>E(H,` ${e(x).loading??""}`)),m(P,F)},R=P=>{var F=Ee(),H=xe(F);{var Y=N=>{var V=Ka(),oe=a(V,!0);u(V),k(()=>E(oe,e(x).noCommunitiesFound)),m(N,V)},le=N=>{var V=Ee(),oe=xe(V);Re(oe,17,()=>e(D),ve=>ve.id,(ve,J)=>{var de=Va();de.__click=[Wa,q,J];var he=a(de);{var Ue=fe=>{var A=Na();k(Q=>{U(A,"src",`/images/${e(J).image}`),U(A,"alt",Q)},[()=>T(e(J).name)]),m(fe,A)},Pe=fe=>{var A=Qa();m(fe,A)};B(he,fe=>{e(J).image?fe(Ue):fe(Pe,!1)})}var Ae=b(he,2),Ce=a(Ae),Me=a(Ce,!0);u(Ce);var me=b(Ce,2),pe=a(me),be=b(pe);{var Be=fe=>{var A=st();k(Q=>E(A,`• ${Q??""}`),[()=>T(e(J).hub.name)]),m(fe,A)};B(be,fe=>{e(J).hub&&fe(Be)})}u(me),u(Ae),u(de),k((fe,A)=>{E(Me,fe),E(pe,`${A??""} `)},[()=>T(e(J).name),()=>T(e(J).description)]),m(ve,de)}),m(N,V)};B(H,N=>{e(D).length===0?N(Y):N(le,!1)},!0)}m(P,F)};B(S,P=>{e(L)?P(j):P(R,!1)})}u(w),m(C,w)};B(c,C=>{e(I).trim()&&(e(L)||e(D).length>0)&&C(p)})}u(i),k(()=>U(o,"placeholder",t.placeholder||e(x).searchCommunities)),Qe(o,()=>e(I),C=>r(I,C)),m(_,i)};B(se,_=>{e(y)&&_(ne)})}u(re),u(O),m(l,O),Se()}Le(["click","input"]);var $a=g('<div class="form-text text-muted"> </div>'),er=g('<div class="mb-3"><!></div>'),tr=g('<div class="form-text text-muted"> </div>'),ur=g('<div class="mb-3"><!></div>'),ar=g("<!> <!>",1),rr=(l,t)=>{var d;return(d=document.getElementById(t.post?"edit-image-upload-input":"image-upload-input"))==null?void 0:d.click()},lr=g('<button type="button" class="btn btn-outline-secondary btn-sm"><i class="bi bi-image"></i> </button>'),ir=g('<div class="mt-3"><h6> </h6> <div class="text-center py-3"><div class="spinner-border spinner-border-sm me-2" role="status"></div> <span class="text-muted"> </span></div></div>'),nr=(l,t,d)=>t(e(d)),or=(l,t,d)=>t(e(d).id),sr=g('<div class="col-6 col-md-4 col-lg-3"><div class="position-relative"><button type="button" class="btn p-0 border-0 bg-transparent w-100"><img alt="" class="img-thumbnail w-100" style="height: 120px; object-fit: cover;"/></button> <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1" style="padding: 0.25rem 0.5rem; font-size: 0.75rem;"><i class="bi bi-x"></i></button></div></div>'),dr=g('<div class="mt-3"><h6> </h6> <div class="row g-2"></div></div>'),cr=(l,t)=>{var d;return(d=document.getElementById(t.post?"edit-image-upload-input":"image-upload-input"))==null?void 0:d.click()},vr=(l,t)=>{var d;return l.key==="Enter"&&((d=document.getElementById(t.post?"edit-image-upload-input":"image-upload-input"))==null?void 0:d.click())},mr=g('<div class="text-muted"><div class="spinner-border spinner-border-sm me-2" role="status"></div> </div>'),_r=g('<div class="text-muted"><i class="bi bi-cloud-upload fs-2 mb-2"></i> <div> </div> <small class="text-muted"> </small></div>'),br=g("<br/><small>You can still upload new images.</small>",1),fr=g('<div class="alert alert-danger mt-2 mb-0" role="alert"> <!></div>'),gr=g('<div class="alert alert-success mt-2 mb-0" role="alert"> </div>'),hr=g('<div class="alert alert-danger mt-3" role="alert"> </div>'),pr=g('<div class="alert alert-success mt-3" role="alert"> </div>'),yr=g('<form><!> <!> <!> <input type="file" multiple style="display: none;"/> <div class="mb-3"><!> <div class="form-label"> </div> <div role="button" tabindex="0" style="cursor: pointer; min-height: 100px; display: flex; flex-direction: column; justify-content: center;"><!></div> <!> <!></div> <!> <!> <!></form>');function Gr(l,t){Ie(t,!0);const{fetcher:d}=We(),f={en:{createPostTitle:"Create New Post",editPostTitle:"Edit Post",cancel:"Cancel",create:"Create",save:"Save Changes",hub:"Hub",hubPlaceholder:"Select hub (optional)...",community:"Community",communityPlaceholder:"Select community (optional)...",hubDisabledByCommunity:"Hub selection is disabled when Community is specified",communityDisabledByHub:"Community selection is disabled when Hub is specified",tags:"Tags",title:"Title",titlePlaceholder:"Enter post title...",body:"Body",bodyPlaceholder:"Write your post content...",titleRequired:"Title is required",bodyRequired:"Body is required",createSuccess:"Post created successfully!",createError:"Failed to create post",updateSuccess:"Post updated successfully!",updateError:"Failed to update post",images:"Images",uploadImages:"Upload Images",dragDropImages:"Drag & drop images here or click to select",maxImages:"Maximum 10 images",uploading:"Uploading...",imageUploadSuccess:"Images uploaded successfully!",imageUploadError:"Failed to upload images",invalidFileType:"Invalid file type. Please upload JPG, PNG, or WebP images.",fileTooLarge:"File is too large. Maximum size is 5MB.",tooManyFiles:"Too many files. Maximum 10 images per post.",insertImage:"Insert image",removeImage:"Remove image",imageGallery:"Image Gallery",loadingImages:"Loading images..."},ru:{createPostTitle:"Создать новый пост",editPostTitle:"Редактировать пост",cancel:"Отмена",create:"Создать",save:"Сохранить изменения",hub:"Хаб",hubPlaceholder:"Выберите хаб (необязательно)...",community:"Сообщество",communityPlaceholder:"Выберите сообщество (необязательно)...",hubDisabledByCommunity:"Выбор хаба отключен, когда указано сообщество",communityDisabledByHub:"Выбор сообщества отключен, когда указан хаб",tags:"Теги",title:"Заголовок",titlePlaceholder:"Введите заголовок поста...",body:"Содержание",bodyPlaceholder:"Напишите содержание поста...",titleRequired:"Заголовок обязателен",bodyRequired:"Содержание обязательно",createSuccess:"Пост успешно создан!",createError:"Не удалось создать пост",updateSuccess:"Пост успешно обновлен!",updateError:"Не удалось обновить пост",images:"Изображения",uploadImages:"Загрузить изображения",dragDropImages:"Перетащите изображения сюда или нажмите для выбора",maxImages:"Максимум 10 изображений",uploading:"Загрузка...",imageUploadSuccess:"Изображения загружены успешно!",imageUploadError:"Не удалось загрузить изображения",invalidFileType:"Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображения.",fileTooLarge:"Файл слишком большой. Максимальный размер - 5MB.",tooManyFiles:"Слишком много файлов. Максимум 10 изображений на пост.",insertImage:"Вставить изображение",removeImage:"Удалить изображение",imageGallery:"Галерея изображений",loadingImages:"Загрузка изображений..."}},v=ke(()=>f[t.locale]);let x=G(De([])),y=G(De([])),I=G(null),D=G(null),h=G(De([])),L=G(!1),z=G(null),T=G(null),M=G(De([])),W=G(!1),ae=G(!1),q=G(null),O=G(null),K=G(!1),$=G(null);He(()=>{e(I)&&r(D,null),e(D)&&r(I,null)}),He(()=>{var s,c;t.show&&(re(),t.post&&(r(x,[...t.post.title],!0),r(y,[...t.post.body],!0),r(h,t.post.tags.map(p=>p.id),!0),r(I,((s=t.post.hub)==null?void 0:s.id)||null,!0),r(D,((c=t.post.community)==null?void 0:c.id)||null,!0),o(t.post.id)))});function re(){r(x,[],!0),r(y,[],!0),r(I,null),r(D,null),r(h,[],!0),r(z,null),r(T,null),r(L,!1),r(M,[],!0),r(W,!1),r(ae,!1),r(q,null),r(O,null),r(K,!1)}async function ee(){r(z,null),r(T,null);const s=e(x).some(p=>p.value.trim().length>0),c=e(y).some(p=>p.value.trim().length>0);if(!s){r(z,e(v).titleRequired,!0);return}if(!c){r(z,e(v).bodyRequired,!0);return}r(L,!0);try{if(t.post)await d.reactor.post.patch({id:t.post.id,title:e(x).filter(p=>p.value.trim().length>0),body:e(y).filter(p=>p.value.trim().length>0),tagIds:e(h),imageIds:e(M).map(p=>p.id)}),r(T,e(v).updateSuccess,!0),setTimeout(()=>{t.onClose(),window.location.reload()},1500);else{const{id:p}=await d.reactor.post.post({hubId:e(I),communityId:e(D),title:e(x).filter(C=>C.value.trim().length>0),body:e(y).filter(C=>C.value.trim().length>0),tagIds:e(h),imageIds:e(M).map(C=>C.id)});r(T,e(v).createSuccess,!0),t.onPostCreated&&t.onPostCreated(),setTimeout(()=>{lt(t.toLocaleHref(`/reactor/${p}`))},1500)}}catch(p){console.error("Error submitting post:",p),r(z,p instanceof Error?p.message:t.post?e(v).updateError:e(v).createError,!0)}finally{r(L,!1)}}function te(s){if(s.length+e(M).length>10)return e(v).tooManyFiles;for(const c of s){if(!Ze.ALLOWED_IMAGE_FILE_TYPES.includes(c.type))return e(v).invalidFileType;if(c.size>Ze.MAX_IMAGE_FILE_SIZE)return e(v).fileTooLarge}return null}async function X(s){const c=te(s);if(c){r(q,c,!0);return}r(W,!0),r(q,null),r(O,null);try{const p=new FormData;for(const S of s)p.append("images",S);const C=await Pt("/api/reactor/post/image",{method:"POST",body:p});if(!C.ok)throw new Error(`${e(v).imageUploadError}: ${C.statusText}`);const w=await C.json();r(M,[...e(M),...w],!0),r(O,e(v).imageUploadSuccess,!0),setTimeout(()=>{r(O,null)},3e3)}catch(p){r(q,p instanceof Error?p.message:e(v).imageUploadError,!0),console.error(p)}finally{r(W,!1)}}function ie(s){const c=s.target;c.files&&c.files.length>0&&(X(c.files),c.value="")}function se(s){s.preventDefault(),r(K,!0)}function ne(s){s.preventDefault(),r(K,!1)}function _(s){var c;s.preventDefault(),r(K,!1),(c=s.dataTransfer)!=null&&c.files&&X(s.dataTransfer.files)}function i(s){if(e($)){const c=`<img src="/images/${s.url}" alt="" style="max-width: 100%; height: auto;" />`;e($).insertContent(c)}}function n(s){r(M,e(M).filter(c=>c.id!==s),!0)}async function o(s){r(ae,!0),r(q,null);try{const c=await d.reactor.post.image.list.get({id:s});r(M,c,!0)}catch(c){console.error("Failed to load post images:",c),r(q,"Failed to load existing images")}finally{r(ae,!1)}}{let s=ke(()=>t.post?e(v).editPostTitle:e(v).createPostTitle),c=ke(()=>t.post?e(v).save:e(v).create),p=ke(()=>e(L)||!e(x).some(C=>C.value.trim().length>0)||!e(y).some(C=>C.value.trim().length>0));qt(l,{get show(){return t.show},get title(){return e(s)},get onClose(){return t.onClose},onSubmit:ee,get submitText(){return e(c)},get cancelText(){return e(v).cancel},get submitDisabled(){return e(p)},get isSubmitting(){return e(L)},children:(C,w)=>{var S=yr(),j=a(S);{var R=A=>{var Q=ar(),ue=xe(Q);{var Fe=ge=>{var ce=$a(),Te=a(ce,!0);u(ce),k(()=>E(Te,e(v).hubDisabledByCommunity)),m(ge,ce)},we=ge=>{var ce=er(),Te=a(ce);Aa(Te,{get locale(){return t.locale},get label(){return e(v).hub},get placeholder(){return e(v).hubPlaceholder},get selectedHubId(){return e(I)},set selectedHubId(qe){r(I,qe,!0)}}),u(ce),m(ge,ce)};B(ue,ge=>{e(D)?ge(Fe):ge(we,!1)})}var ye=b(ue,2);{var je=ge=>{var ce=tr(),Te=a(ce,!0);u(ce),k(()=>E(Te,e(v).communityDisabledByHub)),m(ge,ce)},Ve=ge=>{var ce=ur(),Te=a(ce);Za(Te,{get hubId(){return e(I)},get locale(){return t.locale},get label(){return e(v).community},get placeholder(){return e(v).communityPlaceholder},get selectedCommunityId(){return e(D)},set selectedCommunityId(qe){r(D,qe,!0)}}),u(ce),m(ge,ce)};B(ye,ge=>{e(I)?ge(je):ge(Ve,!1)})}m(A,Q)};B(j,A=>{t.post||A(R)})}var P=b(j,2);$t(P,{get locale(){return t.locale},id:"post-title",get label(){return e(v).title},get placeholder(){return e(v).titlePlaceholder},required:!0,get value(){return e(x)},set value(A){r(x,A,!0)}});var F=b(P,2);Uu(F,{get locale(){return t.locale},id:"post-body",get label(){return e(v).body},required:!0,onEditorInit:A=>{r($,A,!0)},languageSelectPosition:"top",get value(){return e(y)},set value(A){r(y,A,!0)},children:(A,Q)=>{var ue=lr();ue.__click=[rr,t];var Fe=b(a(ue));u(ue),k(()=>{ue.disabled=e(W)||e(M).length>=10,U(ue,"title",e(v).uploadImages),E(Fe,` ${e(v).uploadImages??""}`)}),m(A,ue)},$$slots:{default:!0}});var H=b(F,2);H.__change=ie;var Y=b(H,2),le=a(Y);{var N=A=>{var Q=ir(),ue=a(Q),Fe=a(ue,!0);u(ue);var we=b(ue,2),ye=b(a(we),2),je=a(ye,!0);u(ye),u(we),u(Q),k(()=>{E(Fe,e(v).imageGallery),E(je,e(v).loadingImages)}),m(A,Q)},V=A=>{var Q=Ee(),ue=xe(Q);{var Fe=we=>{var ye=dr(),je=a(ye),Ve=a(je,!0);u(je);var ge=b(je,2);Re(ge,21,()=>e(M),ce=>ce.id,(ce,Te)=>{var qe=sr(),nt=a(qe),Oe=a(nt);Oe.__click=[nr,i,Te];var gt=a(Oe);u(Oe);var Ye=b(Oe,2);Ye.__click=[or,n,Te],u(nt),u(qe),k(()=>{U(Oe,"title",e(v).insertImage),U(Oe,"aria-label",e(v).insertImage),U(gt,"src",`/images/${e(Te).url}`),U(Ye,"title",e(v).removeImage),U(Ye,"aria-label",e(v).removeImage)}),m(ce,qe)}),u(ge),u(ye),k(()=>E(Ve,e(v).imageGallery)),m(we,ye)};B(ue,we=>{e(M).length>0&&we(Fe)},!0)}m(A,Q)};B(le,A=>{e(ae)?A(N):A(V,!1)})}var oe=b(le,2),ve=a(oe,!0);u(oe);var J=b(oe,2);J.__click=[cr,t],J.__keydown=[vr,t];var de=a(J);{var he=A=>{var Q=mr(),ue=b(a(Q));u(Q),k(()=>E(ue,` ${e(v).uploading??""}`)),m(A,Q)},Ue=A=>{var Q=_r(),ue=b(a(Q),2),Fe=a(ue,!0);u(ue);var we=b(ue,2),ye=a(we,!0);u(we),u(Q),k(()=>{E(Fe,e(v).dragDropImages),E(ye,e(v).maxImages)}),m(A,Q)};B(de,A=>{e(W)?A(he):A(Ue,!1)})}u(J);var Pe=b(J,2);{var Ae=A=>{var Q=fr(),ue=a(Q),Fe=b(ue);{var we=ye=>{var je=br();Dt(),m(ye,je)};B(Fe,ye=>{e(q).includes("Failed to load existing images")&&ye(we)})}u(Q),k(()=>E(ue,`${e(q)??""} `)),m(A,Q)};B(Pe,A=>{e(q)&&A(Ae)})}var Ce=b(Pe,2);{var Me=A=>{var Q=gr(),ue=a(Q,!0);u(Q),k(()=>E(ue,e(O))),m(A,Q)};B(Ce,A=>{e(O)&&A(Me)})}u(Y);var me=b(Y,2);Ju(me,{get locale(){return t.locale},get label(){return e(v).tags},get selectedTagIds(){return e(h)},set selectedTagIds(A){r(h,A,!0)}});var pe=b(me,2);{var be=A=>{var Q=hr(),ue=a(Q,!0);u(Q),k(()=>E(ue,e(z))),m(A,Q)};B(pe,A=>{e(z)&&A(be)})}var Be=b(pe,2);{var fe=A=>{var Q=pr(),ue=a(Q,!0);u(Q),k(()=>E(ue,e(T))),m(A,Q)};B(Be,A=>{e(T)&&A(fe)})}u(S),k(A=>{U(H,"id",t.post?"edit-image-upload-input":"image-upload-input"),U(H,"accept",A),E(ve,e(v).images),_e(J,1,`border border-2 border-dashed rounded p-4 text-center ${e(K)?"border-primary bg-light":"border-secondary"}`)},[()=>Ze.ALLOWED_IMAGE_FILE_TYPES.join(",")]),Xe("dragover",J,se),Xe("dragleave",J,ne),Xe("drop",J,_),m(C,S)},$$slots:{default:!0}})}Se()}Le(["click","change","keydown"]);export{Gr as C,wu as E,qr as L,qt as M,Aa as R,zr as U,Or as a,Qe as b,Mr as c,Hr as d,$t as e,Pt as f};
