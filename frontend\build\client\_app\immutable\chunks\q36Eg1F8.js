function j(r){var u,i,f="";if(typeof r=="string"||typeof r=="number")f+=r;else if(typeof r=="object")if(Array.isArray(r)){var t=r.length;for(u=0;u<t;u++)r[u]&&(i=j(r[u]))&&(f&&(f+=" "),f+=i)}else for(i in r)r[i]&&(f&&(f+=" "),f+=i);return f}function C(){for(var r,u,i=0,f="",t=arguments.length;i<t;i++)(r=arguments[i])&&(u=j(r))&&(f&&(f+=" "),f+=u);return f}function L(r){return typeof r=="object"?C(r):r??""}const v=[...` 	
\r\f \v\uFEFF`];function $(r,u,i){var f=r==null?"":""+r;if(u&&(f=f?f+" "+u:u),i){for(var t in i)if(i[t])f=f?f+" "+t:t;else if(f.length)for(var g=t.length,n=0;(n=f.indexOf(t,n))>=0;){var c=n+g;(n===0||v.includes(f[n-1]))&&(c===f.length||v.includes(f[c]))?f=(n===0?"":f.substring(0,n))+f.substring(c+1):n=c}}return f===""?null:f}function A(r,u=!1){var i=u?" !important;":";",f="";for(var t in r){var g=r[t];g!=null&&g!==""&&(f+=" "+t+": "+g+i)}return f}function b(r){return r[0]!=="-"||r[1]!=="-"?r.toLowerCase():r}function q(r,u){if(u){var i="",f,t;if(Array.isArray(u)?(f=u[0],t=u[1]):f=u,r){r=String(r).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var g=!1,n=0,c=!1,a=[];f&&a.push(...Object.keys(f).map(b)),t&&a.push(...Object.keys(t).map(b));var p=0,l=-1;const h=r.length;for(var s=0;s<h;s++){var o=r[s];if(c?o==="/"&&r[s-1]==="*"&&(c=!1):g?g===o&&(g=!1):o==="/"&&r[s+1]==="*"?c=!0:o==='"'||o==="'"?g=o:o==="("?n++:o===")"&&n--,!c&&g===!1&&n===0){if(o===":"&&l===-1)l=s;else if(o===";"||s===h-1){if(l!==-1){var O=b(r.substring(p,l).trim());if(!a.includes(O)){o!==";"&&s++;var S=r.substring(p,s).trim();i+=" "+S+";"}}p=s+1,l=-1}}}}return f&&(i+=A(f)),t&&(i+=A(t,!0)),i=i.trim(),i===""?null:i}return r==null?null:String(r)}export{q as a,L as c,$ as t};
