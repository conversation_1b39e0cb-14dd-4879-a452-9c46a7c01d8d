import{c as ge}from"../chunks/CVTn1FV4.js";import{g as pe}from"../chunks/CGZ87yZq.js";import"../chunks/Bzak7iHL.js";import{o as We}from"../chunks/DeAm3Eed.js";import{p as Ge,av as O,aw as ne,f as x,h as Ne,t as g,b as l,c as Ue,s as d,d as n,g as e,$ as Ze,u as Z,r as a,ax as b,a as ie,az as ve}from"../chunks/RHWQbow4.js";import{d as Ve,s}from"../chunks/BlWcudmi.js";import{i as w}from"../chunks/CtoItwj4.js";import{e as qe}from"../chunks/Dnfvvefi.js";import{s as V}from"../chunks/BdpLTtcP.js";import{s as Je}from"../chunks/Cxg-bych.js";import{b as Ke}from"../chunks/B5DcI8qy.js";import{g as Qe}from"../chunks/CKnuo8tw.js";import{f as Xe}from"../chunks/CL12WlkV.js";const Ye=async({fetch:B,url:p})=>{const{fetcher:I}=pe(),h=await I.commune.invitation.list.get({},{fetch:B,ctx:{url:p}}),T=h.length?await I.commune.list.get({ids:h.map(({communeId:r})=>r)},{fetch:B,ctx:{url:p}}):[],L=new Map(T.map(r=>[r.id,r]));return{invitations:h.map(r=>({...r,commune:L.get(r.communeId)})),isHasMoreInvitations:h.length===ge.PAGE_SIZE}},yu=Object.freeze(Object.defineProperty({__proto__:null,load:Ye},Symbol.toStringTag,{value:"Module"}));var $e=x('<div class="text-center py-5"><p class="text-muted"> </p></div>'),eu=x('<div class="image-container svelte-lafg1c"><img class="svelte-lafg1c"/></div>'),uu=x('<div class="bg-light text-center d-flex align-items-center justify-content-center" style="height: 140px;"><span class="text-muted"> </span></div>'),tu=(B,p,I)=>{var h;return p(e(I).id,((h=e(I).commune)==null?void 0:h.id)||e(I).communeId)},au=x('<span class="spinner-border spinner-border-sm me-2" role="status"></span> ',1),ru=(B,p,I)=>p(e(I).id),nu=x('<span class="spinner-border spinner-border-sm me-2" role="status"></span> ',1),iu=x('<div class="mt-auto"><div class="d-grid gap-2"><button class="btn btn-success"><!></button> <button class="btn btn-outline-danger"><!></button></div></div>'),su=x('<div class="mt-auto"><a class="btn btn-outline-primary w-100"> </a></div>'),ou=x('<div class="col"><div class="card h-100 shadow-sm"><!> <div class="card-body d-flex flex-column"><div class="d-flex justify-content-between align-items-start mb-2"><span> </span> <small class="text-muted"> </small></div> <h5 class="card-title fs-5 text-truncate mb-2"> </h5> <p class="card-text text-muted small mb-3" style="height: 3rem; overflow: hidden"> </p> <div class="mb-3"><span class="badge bg-primary mb-2"> </span> <div class="small text-muted"><div> </div> <div class="d-flex flex-column"> </div></div></div> <!></div></div></div>'),cu=x('<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4"></div>'),mu=x('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden"> </span></div> <p class="text-muted mt-2 mb-0"> </p>',1),du=x('<div class="text-center py-3"><!></div>'),lu=x('<div class="alert alert-danger" role="alert"> </div>'),vu=x('<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4"><h1> </h1> <a class="btn btn-outline-secondary"> </a></div> <!> <!> <!></div>');function wu(B,p){Ge(p,!0);const I={en:{_page:{title:"Commune Invitations — Commune"},invitations:"Commune Invitations",loading:"Loading...",noInvitations:"No invitations found",member:"member",members:"members",headMember:"Head",errorFetchingInvitations:"Failed to fetch invitations",errorOccurred:"An error occurred while fetching invitations",loadingMore:"Loading more invitations...",accept:"Accept",reject:"Reject",pending:"Pending",accepted:"Accepted",rejected:"Rejected",expired:"Expired",invitedOn:"Invited on",acceptingInvitation:"Accepting...",rejectingInvitation:"Rejecting...",errorAcceptingInvitation:"Failed to accept invitation",errorRejectingInvitation:"Failed to reject invitation",invitationAccepted:"Invitation accepted! Redirecting to commune...",backToCommunes:"Back to Communes",viewCommune:"View Commune",noImage:"No image",communeImageAlt:"Commune image"},ru:{_page:{title:"Приглашения в коммуны — Коммуна"},invitations:"Приглашения в коммуны",loading:"Загрузка...",noInvitations:"Приглашения не найдены",member:"участник",members:"участников",headMember:"Глава",errorFetchingInvitations:"Не удалось загрузить приглашения",errorOccurred:"Произошла ошибка при загрузке приглашений",loadingMore:"Загружаем больше приглашений...",accept:"Принять",reject:"Отклонить",pending:"Ожидает",accepted:"Принято",rejected:"Отклонено",expired:"Истекло",invitedOn:"Приглашен",acceptingInvitation:"Принимаем...",rejectingInvitation:"Отклоняем...",errorAcceptingInvitation:"Не удалось принять приглашение",errorRejectingInvitation:"Не удалось отклонить приглашение",invitationAccepted:"Приглашение принято! Перенаправляем в коммуну...",backToCommunes:"Назад к коммунам",viewCommune:"Посмотреть коммуну",noImage:"Нет изображения",communeImageAlt:"Изображение коммуны"}},{fetcher:h}=pe(),T=Z(()=>p.data.locale),L=Z(()=>p.data.toLocaleHref),z=Z(()=>p.data.getAppropriateLocalization),r=Z(()=>I[e(T)]);let M=O(ne(p.data.invitations)),A=O(null),R=O(!1),se=O(1),W=O(ne(p.data.isHasMoreInvitations)),S=O(null),F=ne({});async function _e(){if(!(e(R)||!e(W))){b(R,!0),b(A,null);try{const u=e(se)+1,t=await h.commune.invitation.list.get({pagination:{page:u}}),o=t.length?await h.commune.list.get({ids:t.map(_=>_.communeId)}):[],i=new Map(o.map(_=>[_.id,_])),C=t.map(_=>({..._,commune:i.get(_.communeId)}));b(M,[...e(M),...C],!0),b(se,u),b(W,t.length===ge.PAGE_SIZE)}catch(u){b(A,u instanceof Error?u.message:e(r).errorOccurred,!0),console.error(u)}finally{b(R,!1)}}}async function fe(u,t){F[u]="accepting",b(A,null);try{await h.commune.invitation.accept.post({id:u}),alert(e(r).invitationAccepted),Qe(e(L)(`/communes/${t}`))}catch(o){b(A,o instanceof Error?o.message:e(r).errorAcceptingInvitation,!0),console.error(o)}finally{F[u]=null}}async function be(u){F[u]="rejecting",b(A,null);try{await h.commune.invitation.reject.post({id:u}),b(M,e(M).map(t=>t.id===u?{...t,status:"rejected"}:t),!0)}catch(t){b(A,t instanceof Error?t.message:e(r).errorRejectingInvitation,!0),console.error(t)}finally{F[u]=null}}We(()=>{let u;const t=()=>{e(S)&&(u=new IntersectionObserver(o=>{o[0].isIntersecting&&e(W)&&!e(R)&&_e()},{rootMargin:"100px",threshold:.1}),u.observe(e(S)))};return e(S)?t():setTimeout(t,100),()=>{u&&u.disconnect()}});function xe(u){switch(u){case"pending":return"bg-warning text-dark";case"accepted":return"bg-success";case"rejected":return"bg-danger";case"expired":return"bg-secondary";default:return"bg-secondary"}}function he(u){switch(u){case"pending":return e(r).pending;case"accepted":return e(r).accepted;case"rejected":return e(r).rejected;case"expired":return e(r).expired;default:return u}}var q=vu();Ne(u=>{g(()=>Ze.title=e(r)._page.title)});var J=n(q),K=n(J),Ce=n(K,!0);a(K);var Q=d(K,2),De=n(Q,!0);a(Q),a(J);var oe=d(J,2);{var Ee=u=>{var t=$e(),o=n(t),i=n(o,!0);a(o),a(t),g(()=>s(i,e(r).noInvitations)),l(u,t)},Ie=u=>{var t=cu();qe(t,21,()=>e(M),o=>o.id,(o,i)=>{var C=ou(),_=n(C),k=n(_);{var G=c=>{var m=eu(),v=n(m);a(m),g(()=>{V(v,"src",`/images/${e(i).commune.image}`),V(v,"alt",`${e(r).communeImageAlt}`)}),l(c,m)},X=c=>{var m=uu(),v=n(m),D=n(v,!0);a(v),a(m),g(()=>s(D,e(r).noImage)),l(c,m)};w(k,c=>{e(i).commune.image?c(G):c(X,!1)})}var H=d(k,2),P=n(H),N=n(P),we=n(N,!0);a(N);var me=d(N,2),je=n(me);a(me),a(P);var Y=d(P,2),Be=n(Y,!0);a(Y);var $=d(Y,2),Me=n($,!0);a($);var ee=d($,2),ue=n(ee),ke=n(ue);a(ue);var de=d(ue,2),te=n(de),Oe=n(te);a(te);var le=d(te,2),Le=n(le,!0);a(le),a(de),a(ee);var Re=d(ee,2);{var Se=c=>{var m=iu(),v=n(m),D=n(v);D.__click=[tu,fe,i];var y=n(D);{var ae=f=>{var E=au(),re=d(ie(E));g(()=>s(re,` ${e(r).acceptingInvitation??""}`)),l(f,E)},U=f=>{var E=ve();g(()=>s(E,e(r).accept)),l(f,E)};w(y,f=>{F[e(i).id]==="accepting"?f(ae):f(U,!1)})}a(D);var j=d(D,2);j.__click=[ru,be,i];var Pe=n(j);{var Te=f=>{var E=nu(),re=d(ie(E));g(()=>s(re,` ${e(r).rejectingInvitation??""}`)),l(f,E)},ze=f=>{var E=ve();g(()=>s(E,e(r).reject)),l(f,E)};w(Pe,f=>{F[e(i).id]==="rejecting"?f(Te):f(ze,!1)})}a(j),a(v),a(m),g(()=>{D.disabled=F[e(i).id]==="accepting",j.disabled=F[e(i).id]==="rejecting"}),l(c,m)},He=c=>{var m=su(),v=n(m),D=n(v,!0);a(v),a(m),g(y=>{V(v,"href",y),s(D,e(r).viewCommune)},[()=>{var y;return e(L)(`/communes/${((y=e(i).commune)==null?void 0:y.id)||e(i).communeId}`)}]),l(c,m)};w(Re,c=>{e(i).status==="pending"?c(Se):c(He,!1)})}a(H),a(_),a(C),g((c,m,v,D,y,ae)=>{var U,j;Je(N,1,c,"svelte-lafg1c"),s(we,m),s(je,`${e(r).invitedOn??""}
                  ${v??""}`),s(Be,D),s(Me,y),s(ke,`${(((U=e(i).commune)==null?void 0:U.memberCount)||0)??""}
                  ${((((j=e(i).commune)==null?void 0:j.memberCount)||0)===1?e(r).member:e(r).members)??""}`),s(Oe,`${e(r).headMember??""}:`),s(Le,ae)},[()=>`badge ${xe(e(i).status)}`,()=>he(e(i).status),()=>Xe(e(i).createdAt,e(T)),()=>{var c;return e(z)((c=e(i).commune)==null?void 0:c.name)||"Unknown Commune"},()=>e(z)(e(i).commune.description)||"",()=>e(z)(e(i).commune.headMember.name)||"Unknown"]),l(o,C)}),a(t),l(u,t)};w(oe,u=>{e(M).length===0?u(Ee):u(Ie,!1)})}var ce=d(oe,2);{var Ae=u=>{var t=du(),o=n(t);{var i=C=>{var _=mu(),k=ie(_),G=n(k),X=n(G,!0);a(G),a(k);var H=d(k,2),P=n(H,!0);a(H),g(()=>{s(X,e(r).loadingMore),s(P,e(r).loadingMore)}),l(C,_)};w(o,C=>{e(R)&&C(i)})}a(t),Ke(t,C=>b(S,C),()=>e(S)),l(u,t)};w(ce,u=>{e(W)&&u(Ae)})}var Fe=d(ce,2);{var ye=u=>{var t=lu(),o=n(t,!0);a(t),g(()=>s(o,e(A))),l(u,t)};w(Fe,u=>{e(A)&&u(ye)})}a(q),g(u=>{s(Ce,e(r).invitations),V(Q,"href",u),s(De,e(r).backToCommunes)},[()=>e(L)("/communes")]),l(B,q),Ue()}Ve(["click"]);export{wu as component,yu as universal};
