var y0=Object.defineProperty;var _0=(c,s,o)=>s in c?y0(c,s,{enumerable:!0,configurable:!0,writable:!0,value:o}):c[s]=o;var b4=(c,s,o)=>_0(c,typeof s!="symbol"?s+"":s,o);import"../chunks/Bzak7iHL.js";import{p as T4,av as a0,aw as r0,f as p,d as t,r as u,s as a,g as e,t as h,b as B,c as N4,ax as i0,u as r4,h as A0,$ as f0,ay as o0,a as t0}from"../chunks/RHWQbow4.js";import{d as d0,s as r}from"../chunks/BlWcudmi.js";import{e as q,i as M}from"../chunks/Dnfvvefi.js";import"../chunks/CVTn1FV4.js";import"../chunks/B0MzmgHo.js";import{s as E0}from"../chunks/CkTdM00m.js";import{i as e0}from"../chunks/CtoItwj4.js";import{s as l0,a as x0,r as b0}from"../chunks/BdpLTtcP.js";import{s as w0}from"../chunks/CaC9IHEK.js";import{o as S0}from"../chunks/DeAm3Eed.js";function c4(c){return c.toString().padStart(2,"0")}const I=class I extends Date{getIsLeapYear(s=this.getFullYear()){return s%400===0||s%4===0&&s%100!==0}getDayOfYear(s=this.getFullYear()){const o=this.getTime()-new Date(s,0,0).getTime();return Math.floor(o/I.MS_PER_DAY)}getDayPosition(s){return s===I.LEAP_DAY?{month:14,day:2}:s===I.PEACE_DAY?{month:14,day:1}:{month:Math.ceil(s/I.DAYS_PER_MONTH),day:s%I.DAYS_PER_MONTH||28}}getParsed(){const s=this.getFullYear(),o=this.getDayOfYear(s),{month:n,day:d}=this.getDayPosition(o);return{year:s,month:n,day:d}}toDateString(){const{year:s,month:o,day:n}=this.getParsed();return`${c4(n)}.${c4(o)}.${s}`}toString(){const o=super.toISOString().split("T")[1].slice(0,8),{year:n,month:d,day:i}=this.getParsed();return`${c4(i)}.${c4(d)}.${n} ${o}`}toISODateString(){const{year:s,month:o,day:n}=this.getParsed();return`${s}-${c4(o)}-${c4(n)}`}toISOString(){const o=super.toISOString().split("T")[1];return`${this.toISODateString()}T${o}`}};b4(I,"MS_PER_DAY",1e3*60*60*24),b4(I,"DAYS_PER_MONTH",28),b4(I,"PEACE_DAY",365),b4(I,"LEAP_DAY",366);let w4=I;function I0(c,s,o){var n;i0(s,!e(s)),(n=o.onToggle)==null||n.call(o)}var $0=p('<div class="card-body"><!></div>'),Y0=p('<div class="card mb-2"><div class="card-header d-flex justify-content-between align-items-center" role="switch" tabindex="0"><div> </div> <button class="btn btn-link" type="button"> </button></div> <!></div>');function D0(c,s){T4(s,!0);let o=a0(r0(s.isOpen??!1));var n=Y0(),d=t(n);d.__click=[I0,o,s],w0(d,"",{},{cursor:"pointer"});var i=t(d),D=t(i,!0);u(i);var g=a(i,2),b=t(g,!0);u(g),u(d);var w=a(d,2);{var $=F=>{var A=$0(),Y=t(A);E0(Y,()=>s.children),u(A),B(F,A)};e0(w,F=>{e(o)&&F($)})}u(n),h(()=>{l0(d,"aria-checked",e(o)),r(D,s.title),l0(g,"aria-expanded",e(o)),r(b,e(o)?"−":"+")}),B(c,n),N4()}d0(["click"]);var P0=p('<div class="accordion"><!></div>');function c0(c,s){var o=P0(),n=t(o);E0(n,()=>s.children),u(o),B(c,o)}function k0(c,s){const o=c.target,n=new Date(o.value);Number.isNaN(n.getTime())||i0(s,n,!0)}var O0=p('<div class="special-day-info"><h5> </h5> <p> </p></div>'),L0=p('<div class="row"><div class="col-md-12"><div class="alert alert-success"><h4 class="alert-heading"> </h4> <hr/> <div class="row"><div class="col-md-6"><ul class="list-unstyled"><li><strong> </strong> </li> <li><strong> </strong> </li> <li><strong> </strong> </li> <li><strong> </strong> </li> <li><strong> </strong> </li> <li><strong> </strong> </li> <li><strong> </strong> </li></ul></div> <div class="col-md-6"><!></div></div></div></div></div>'),T0=p('<div class="card"><div class="card-body"><p class="lead"> </p> <div class="row mb-4"><div class="col-md-6"><label for="date-picker" class="form-label"> </label> <input id="date-picker" class="form-control" type="date"/></div></div> <!></div></div>');function N0(c,s){T4(s,!0);const o={en:{description:"Convert any date to our new calendar system",selectDate:"Select a date",isLeapYear:{title:"Is Leap Year",yes:"yes",no:"no"},dayOfYear:"Day of Year",day:"Day",month:"Month",year:"Year",commonString:"Common Format",isoString:"ISO Format",peaceDay:{title:"Peace Day",description:"The 365th day of the year that falls outside the regular month structure."},leapDay:{title:"Leap Day",description:"The 366th day of the year that only occurs in leap years."}},ru:{description:"Конвертировать любую дату в наш новый календарь",selectDate:"Выберите дату",isLeapYear:{title:"Високосный год",yes:"да",no:"нет"},dayOfYear:"День года",day:"День",month:"Месяц",year:"Год",commonString:"Бытовой формат",isoString:"Формат ISO",peaceDay:{title:"День мира",description:"365-й день года, который выпадает за пределы регулярной структуры месяцев."},leapDay:{title:"Високосный день",description:"366-й день года, который встречается только в високосных годах."}}},n=r4(()=>o[s.locale]);function d(f){const C=new w4(f),S=C.getIsLeapYear(),x=C.getDayOfYear(),L=C.getParsed(),P=C.toString().slice(0,10);return{newCalendarDate:C,isLeapYear:S,dayOfYear:x,parsed:L,commonString:P,isoString:C.toISOString().slice(0,10)}}let i=a0(r0(new Date));const D=r4(()=>d(e(i)));var g=T0(),b=t(g),w=t(b),$=t(w,!0);u(w);var F=a(w,2),A=t(F),Y=t(A),N=t(Y);u(Y);var O=a(Y,2);b0(O),O.__change=[k0,i],u(A),u(F);var i4=a(F,2);{var H=f=>{var C=L0(),S=t(C),x=t(S),L=t(x),P=t(L);u(L);var R=a(L,4),k=t(R),s4=t(k),y=t(s4),W=t(y),S4=t(W);u(W);var d4=a(W);u(y);var j=a(y,2),E4=t(j),U4=t(E4);u(E4);var v4=a(E4);u(j);var z=a(j,2),B4=t(z),I4=t(B4);u(B4);var $4=a(B4);u(z);var G=a(z,2),C4=t(G),m4=t(C4);u(C4);var q4=a(C4);u(G);var X=a(G,2),V=t(X),M4=t(V);u(V);var Y4=a(V);u(X);var J=a(X,2),p4=t(J),P4=t(p4);u(p4);var h4=a(p4);u(J);var n4=a(J,2),g4=t(n4),H4=t(g4);u(g4);var F4=a(g4);u(n4),u(s4),u(k);var o4=a(k,2),R4=t(o4);{var W4=U=>{var K=O0(),y4=t(K),j4=t(y4,!0);u(y4);var E=a(y4,2),v=t(E,!0);u(E),u(K),h(()=>{r(j4,e(D).parsed.day===1?e(n).peaceDay.title:e(n).leapDay.title),r(v,e(D).parsed.day===1?e(n).peaceDay.description:e(n).leapDay.description)}),B(U,K)};e0(R4,U=>{e(D).parsed.month===14&&U(W4)})}u(o4),u(R),u(x),u(S),u(C),h(U=>{r(P,`${U??""} → ${e(D).commonString??""}`),r(S4,`${e(n).isLeapYear.title??""}:`),r(d4,`  
                    ${(e(D).isLeapYear?e(n).isLeapYear.yes:e(n).isLeapYear.no)??""}`),r(U4,`${e(n).dayOfYear??""}:`),r(v4,`  
                    ${e(D).dayOfYear??""}`),r(I4,`${e(n).day??""}:`),r($4,`  
                    ${e(D).parsed.day??""}`),r(m4,`${e(n).month??""}:`),r(q4,`  
                    ${e(D).parsed.month??""}`),r(M4,`${e(n).year??""}:`),r(Y4,`  
                    ${e(D).parsed.year??""}`),r(P4,`${e(n).commonString??""}:`),r(h4,`  
                    ${e(D).commonString??""}`),r(H4,`${e(n).isoString??""}:`),r(F4,`  
                    ${e(D).isoString??""}`)},[()=>e(i).toLocaleDateString()]),B(f,C)};e0(i4,f=>{e(D)&&f(H)})}u(b),u(g),h(f=>{r($,e(n).description),r(N,`${e(n).selectDate??""}:`),x0(O,f)},[()=>e(i).toISOString().split("T")[0]]),B(c,g),N4()}d0(["change"]);var U0=p('<div class="my-4"><div class="row"><div class="col-md-6"><div class="card mb-3"><div class="card-body"><h5 class="card-title text-center"> </h5> <div class="display-4 text-center"> </div> <div class="display-6 text-center font-monospace"> </div></div></div></div> <div class="col-md-6"><div class="card mb-3"><div class="card-body"><h5 class="card-title text-center"> </h5> <div class="display-4 text-center"> </div> <div class="display-6 text-center font-monospace"> </div></div></div></div></div></div>');function q0(c,s){T4(s,!0);const o={en:{gregorianCalendar:"Gregorian Calendar",newCalendar:"New Calendar"},ru:{gregorianCalendar:"Григорианский календарь",newCalendar:"Новый календарь"}},n=r4(()=>o[s.locale]);function d(y){return y.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",second:"2-digit"})}let i=a0(r0(new Date(0)));const D=r4(()=>d(e(i)));S0(()=>{const y=setInterval(()=>{i0(i,new Date,!0)},1e3);return()=>{clearInterval(y)}});var g=U0(),b=t(g),w=t(b),$=t(w),F=t($),A=t(F),Y=t(A,!0);u(A);var N=a(A,2),O=t(N,!0);u(N);var i4=a(N,2),H=t(i4,!0);u(i4),u(F),u($),u(w);var f=a(w,2),C=t(f),S=t(C),x=t(S),L=t(x,!0);u(x);var P=a(x,2),R=t(P,!0);u(P);var k=a(P,2),s4=t(k,!0);u(k),u(S),u(C),u(f),u(b),u(g),h((y,W)=>{r(Y,e(n).gregorianCalendar),r(O,y),r(H,e(D)),r(L,e(n).newCalendar),r(R,W),r(s4,e(D))},[()=>e(i).toLocaleDateString([],{year:"numeric",month:"numeric",day:"numeric"}),()=>new w4(e(i)).toDateString()]),B(c,g),N4()}var M0=p("<li> </li>"),H0=p("<li> </li>"),R0=p("<li> </li>"),W0=p("<li> </li>"),j0=p("<p><strong> </strong> </p> <p><strong> </strong></p> <ul></ul> <p><strong> </strong></p> <ul></ul>",1),z0=p("<li> </li>"),G0=p("<li> </li>"),X0=p('<div class="row"><div class="col-md-6"><ul class="list-unstyled"><li><strong> </strong> </li> <li><strong> </strong> </li> <li><strong> </strong> </li> <li><strong> </strong> </li> <li><strong> </strong> </li> <li><strong> </strong> </li> <li><strong> </strong> </li></ul></div></div>'),V0=p('<div class="container my-5"><div class="responsive-container"><h1 class="mb-4"> </h1> <section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><p class="lead"> </p> <p> </p> <ul></ul></div></div></section> <section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><p class="lead"> </p> <p> </p> <ul></ul></div></div></section> <section class="mb-5"><h2> </h2> <!></section> <section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><p class="lead"> </p> <p> </p> <ul></ul> <p> </p> <div class="mt-4"><h4> </h4> <ul></ul></div></div></div></section> <section class="mb-5"><h2> </h2> <!></section> <section class="mb-5"><h2> </h2> <!></section> <section class="mb-5"><h2> </h2> <!></section></div></div>');function ou(c,s){T4(s,!0);const o=[new Date(2025,0,1),new Date(2024,1,29),new Date(2025,7,23),new Date(2024,7,23),new Date(2025,11,30),new Date(2025,11,31),new Date(2024,11,30),new Date(2024,11,31)].map(E=>{const v=new w4(E),l=v.getIsLeapYear(),_=v.getDayOfYear(),_4=v.getParsed(),m=v.toString().slice(0,10),A4=v.toISOString().slice(0,10);return{currentCalendarDate:E,newCalendarDate:v,isLeapYear:l,dayOfYear:_,parsed:_4,commonString:m,isoString:A4}}),n={en:{_page:{title:"New Calendar — Commune"},title:"Future of Time: New Calendar",problem:{title:"The Challenge",description:"Our current calendar is fundamentally flawed with inconsistent month lengths and unpredictable date patterns.",inefficiencies:{description:"The Gregorian calendar creates significant inefficiencies in modern society",list:["Irregular month lengths (28/29, 30, or 31 days) make planning inconsistent","Dates shift to different weekdays each year, disrupting scheduling patterns","Business quarters contain unequal numbers of days and weeks","Leap year calculations add unnecessary complexity to date management"]}},solution:{title:"Our Solution",description:"Introducing a revolutionary fixed calendar designed for the modern digital age.",benefits:{description:"Our calendar system delivers these essential benefits",list:["Consistent, predictable structure that eliminates date confusion","Intuitive design that allows for instant mental calculations","Perfect alignment with business quarters for streamlined planning","Flexible framework that adapts to contemporary scheduling requirements"]}},historicalPrecedents:{title:"Historical Precedents",description:"Description",inspirations:"What inspired us from the project",whyNotUseAsIs:"Why not use as is",list:[{title:"International Fixed Calendar",description:"The International Fixed Calendar revolutionized timekeeping with its groundbreaking 13-month structure, each containing exactly 28 days. Pioneered by visionary Moses B. Cotsworth and championed by business leader George Eastman, this system directly addressed the inefficiencies of traditional calendars. Despite gaining significant momentum in early 20th century business circles, entrenched cultural resistance prevented its full adoption—a barrier our modern approach now overcomes.",inspirations:["Perfect 28-day month symmetry for optimal scheduling","Strategic 13-month yearly framework that aligns with lunar cycles","Innovative concept of special days outside the standard week structure"],whyNotUseAsIs:["Outdated month naming convention that preserves Gregorian inconsistencies","Problematic leap day placement disrupting mid-year planning cycles","Suboptimal week structure with Sunday start that misaligns with modern work patterns"]},{title:"World Calendar",description:"Elisabeth Achelis's World Calendar (1930) delivered a transformative approach to time organization with its quarterly structure. Each 91-day quarter featured a balanced three-month pattern, while introducing the revolutionary concept of 'World Day' and 'Leap Day' as special days outside the standard week cycle—creating perfect year-to-year alignment while honoring the astronomical reality of Earth's orbit.",inspirations:["Breakthrough concept of special days outside the weekly cycle for perfect alignment","Precisely balanced 13-week quarters for optimal business planning"],whyNotUseAsIs:["Inconsistent month lengths that perpetuate calculation difficulties","Suboptimal placement of special days that disrupts natural year transitions"]},{title:"Positivist Calendar",description:"Auguste Comte's visionary Positivist Calendar (1849) established a perfectly symmetrical time structure with 13 identical 28-day months plus a special year-end day. This system transcended mere timekeeping by connecting each month to humanity's greatest achievements and institutions—creating not just a calendar but a celebration of human progress and intellectual advancement.",inspirations:["Mathematically perfect 28-day month structure for complete predictability","Optimal 13-month yearly framework that aligns with natural cycles","Innovative special day concept that honors the astronomical year"],whyNotUseAsIs:["Culturally specific month naming system that limits global adoption","Philosophical and religious associations that restrict universal acceptance","Problematic leap day placement disrupting yearly planning","Suboptimal week structure with Sunday start that conflicts with modern work patterns"]},{title:"Symmetry454 Calendar",description:"Dr. Irv Bromberg's Symmetry454 Calendar represents a mathematical breakthrough in temporal organization. This ingenious system creates perfect quarterly symmetry through a precise pattern: two 28-day months followed by one 35-day month in each quarter. This pattern delivers consistent month starts while maintaining perfect alignment with the solar year through a leap week mechanism in December.",inspirations:["Mathematically elegant approach to quarter and month design that balances regularity with astronomical reality"],whyNotUseAsIs:["Variable month lengths that complicate scheduling and planning","Full-week leap year adjustments that create excessive disruption to yearly patterns"]},{title:"Hanke-Henry Permanent Calendar",description:"The Hanke-Henry Permanent Calendar, developed by economists Steve Hanke and Richard Henry, delivers remarkable consistency through its ingenious design. By implementing a fixed 30-30-31 day quarterly pattern and adding a leap week every 5-6 years, this system ensures dates always fall on the same weekday year after year—dramatically simplifying business planning and personal scheduling across decades.",inspirations:["Perfect date-to-weekday consistency that eliminates year-to-year variations"],whyNotUseAsIs:["Inconsistent month lengths that perpetuate mental calculation challenges"]}]},ourDesign:{title:"Our Design",description:"A perfect balance: 13 uniform months of 28 days with special celebration days",structure:{description:"Our calendar features an elegant, mathematically sound structure",list:["13 identical months of exactly 28 days (4 complete weeks) for perfect symmetry","Every month begins on the same weekday, creating consistent patterns","Peace Day: A special celebration at year's end (day 365)","Leap Day: A bonus celebration in leap years (day 366)"]},celebrations:"The final celebrations (Peace Day and optional Leap Day) create a special period outside the regular month structure—a dedicated time for global celebration and renewal as we transition to a new year.",advantages:{title:"Transformative Advantages",list:["Perfect monthly symmetry with exactly 4 weeks in every month","Precise quarterly alignment with exactly 91 days (13 weeks) per quarter","Permanent weekday-date alignment that never changes year to year","Effortless date calculations that can be done mentally in seconds","Revolutionary improvement for business planning and financial cycles"]}},examples:{title:"See It In Action",isLeapYear:{title:"Is Leap Year",yes:"yes",no:"no"},dayOfYear:"Day of Year",day:"Day",month:"Month",year:"Year",commonString:"Common Format",isoString:"ISO Format"},realtimeWatches:{title:"Live Calendar Comparison"},dateCalculator:{title:"Date Calculator"}},ru:{_page:{title:"Новый календарь — Коммуна"},title:"Будущее времени: Новый календарь",problem:{title:"Проблема",description:"Наш текущий календарь имеет фундаментальные недостатки: нерегулярная длина месяцев и непредсказуемость дат.",inefficiencies:{description:"Григорианский календарь создает значительные неэффективности в современном обществе",list:["Нерегулярная длина месяцев (28/29, 30 или 31 день) делает планирование нестабильным","Даты сдвигаются на разные дни недели каждый год, нарушая графики","Бизнес-кварталы содержат неравное количество дней и недель","Високосные годы добавляют ненужную сложность в управление датами"]}},solution:{title:"Наше решение",description:"Представляем революционный фиксированный календарь, созданный для цифровой эпохи.",benefits:{description:"Наша система календаря обеспечивает следующие ключевые преимущества",list:["Последовательная и предсказуемая структура, исключающая путаницу в датах","Интуитивный дизайн, позволяющий мгновенно производить расчёты в уме","Идеальное совпадение с бизнес-кварталами для упрощенного планирования","Гибкая система, адаптированная к современным требованиям расписания"]}},historicalPrecedents:{title:"Исторические прецеденты",description:"Описание",inspirations:"Что вдохновило нас в этом проекте",whyNotUseAsIs:"Почему не использовать как есть",list:[{title:"Международный фиксированный календарь",description:"Международный фиксированный календарь произвел революцию во временном учете, введя инновационную структуру из 13 месяцев по 28 дней. Разработанный Моисеем Б. Котсвортом и поддержанный бизнес-лидером Джорджем Истменом, этот календарь решал основные проблемы традиционных систем. Несмотря на активное распространение в деловых кругах XX века, культурное сопротивление помешало его полному внедрению — проблему, которую наше современное решение успешно преодолевает.",inspirations:["Идеальная симметрия месяцев по 28 дней для оптимального планирования","Стратегическая структура из 13 месяцев, согласованная с лунными циклами","Инновационная концепция специальных дней вне стандартной недели"],whyNotUseAsIs:["Устаревшие названия месяцев, сохраняющие григорианские несоответствия","Неподходящее размещение високосного дня, нарушающее середину года","Нерациональная недельная структура с началом в воскресенье, не соответствующая современным трудовым стандартам"]},{title:"Мировой календарь",description:"Мировой календарь Элизабет Ахелис (1930) предложил радикальный подход к организации времени с квартальной структурой. Каждый квартал состоял из сбалансированных трёх месяцев (по 91 дню), включая уникальные «Мировой день» и «Високосный день» вне недели, обеспечивая идеальное совпадение года с годом и астрономической реальностью орбиты Земли.",inspirations:["Прорывная идея специальных дней вне недельного цикла для точного выравнивания","Точно сбалансированные кварталы по 13 недель для оптимального бизнес-планирования"],whyNotUseAsIs:["Нерегулярная длина месяцев сохраняет сложности расчётов","Неудачное расположение специальных дней нарушает естественный переход года"]},{title:"Позитивистский календарь",description:"Позитивистский календарь Огюста Конта (1849) установил симметричную структуру времени: 13 идентичных месяцев по 28 дней и один специальный день в конце года. Календарь выходил за рамки простой хронологии, связывая каждый месяц с достижениями человечества — превращая календарь в праздник человеческого прогресса и разума.",inspirations:["Математически идеальная структура месяцев по 28 дней для полной предсказуемости","Оптимальный год из 13 месяцев, согласованный с природными циклами","Идея специального дня, отражающего астрономический год"],whyNotUseAsIs:["Культурно-специфичная система названий месяцев затрудняет глобальное внедрение","Философские и религиозные ассоциации ограничивают универсальное принятие","Проблемное размещение високосного дня нарушает годовое планирование","Нерациональное начало недели (воскресенье) противоречит современным трудовым нормам"]},{title:"Календарь Symmetry454",description:"Календарь Symmetry454, разработанный доктором Ирвом Бромбергом, представляет собой математический прорыв в организации времени. Он создает симметрию кварталов с чёткой последовательностью: два месяца по 28 дней и один — 35 дней. Такая структура сохраняет точное начало месяцев и выравнивание с солнечным годом благодаря добавлению 'високосной недели' в декабре.",inspirations:["Элегантный математический подход к квартальной и месячной структуре, сочетающий регулярность с астрономической точностью"],whyNotUseAsIs:["Переменная длина месяцев усложняет планирование","Добавление целой недели в високосные годы нарушает годовой ритм"]},{title:"Постоянный календарь Ханке-Генри",description:"Постоянный календарь Ханке-Генри, разработанный экономистами Стивом Ханке и Ричардом Генри, предлагает поразительную регулярность благодаря своей структуре: месяцы по 30, 30 и 31 дню в каждом квартале и добавление 'високосной недели' каждые 5–6 лет. Это обеспечивает постоянное совпадение даты и дня недели из года в год — упрощая планирование на десятилетия вперёд.",inspirations:["Постоянное совпадение даты и дня недели, устраняющее межгодовую путаницу"],whyNotUseAsIs:["Нерегулярная длина месяцев сохраняет сложности при вычислениях"]}]},ourDesign:{title:"Наш дизайн",description:"Идеальный баланс: 13 равных месяцев по 28 дней и особые праздничные дни",structure:{description:"Наш календарь имеет элегантную и математически точную структуру",list:["13 идентичных месяцев по ровно 28 дней (4 полные недели) для идеальной симметрии","Каждый месяц начинается в один и тот же день недели, создавая стабильные шаблоны","День Мира — особый праздник в конце года (365-й день)","Високосный день — дополнительный праздник в високосные годы (366-й день)"]},celebrations:"Финальные праздники (День Мира и дополнительный Високосный день) образуют особый период вне стандартной структуры месяцев — время для глобального празднования и обновления перед новым годом.",advantages:{title:"Преимущества преобразования",list:["Идеальная симметрия месяцев: ровно 4 недели в каждом месяце","Точное совпадение кварталов: ровно 91 день (13 недель) в каждом","Постоянное совпадение дня недели и даты, не меняющееся с годами","Моментальные расчеты дат в уме без усилий","Революционное улучшение бизнес-планирования и финансовых циклов"]}},examples:{title:"Примеры",isLeapYear:{title:"Високосный год",yes:"да",no:"нет"},dayOfYear:"День года",day:"День",month:"Месяц",year:"Год",commonString:"Бытовой формат",isoString:"Формат ISO"},realtimeWatches:{title:"Сравнение календарей в реальном времени"},dateCalculator:{title:"Калькулятор дат"}}},d=r4(()=>s.data.locale),i=r4(()=>n[e(d)]);var D=V0();A0(E=>{h(()=>f0.title=e(i)._page.title)});var g=t(D),b=t(g),w=t(b,!0);u(b);var $=a(b,2),F=t($),A=t(F,!0);u(F);var Y=a(F,2),N=t(Y),O=t(N),i4=t(O,!0);u(O);var H=a(O,2),f=t(H);u(H);var C=a(H,2);q(C,21,()=>e(i).problem.inefficiencies.list,M,(E,v)=>{var l=M0(),_=t(l,!0);u(l),h(()=>r(_,e(v))),B(E,l)}),u(C),u(N),u(Y),u($);var S=a($,2),x=t(S),L=t(x,!0);u(x);var P=a(x,2),R=t(P),k=t(R),s4=t(k,!0);u(k);var y=a(k,2),W=t(y);u(y);var S4=a(y,2);q(S4,21,()=>e(i).solution.benefits.list,M,(E,v)=>{var l=H0(),_=t(l,!0);u(l),h(()=>r(_,e(v))),B(E,l)}),u(S4),u(R),u(P),u(S);var d4=a(S,2),j=t(d4),E4=t(j,!0);u(j);var U4=a(j,2);c0(U4,{children:(E,v)=>{var l=o0(),_=t0(l);q(_,17,()=>e(i).historicalPrecedents.list,M,(_4,m)=>{D0(_4,{get title(){return e(m).title},children:(A4,s0)=>{var z4=j0(),Q=t0(z4),l4=t(Q),k4=t(l4,!0);u(l4);var f4=a(l4);u(Q);var Z=a(Q,2),O4=t(Z),G4=t(O4,!0);u(O4),u(Z);var u4=a(Z,2);q(u4,21,()=>e(m).inspirations,M,(e4,x4)=>{var T=R0(),a4=t(T,!0);u(T),h(()=>r(a4,e(x4))),B(e4,T)}),u(u4);var t4=a(u4,2),L4=t(t4),X4=t(L4,!0);u(L4),u(t4);var D4=a(t4,2);q(D4,21,()=>e(m).whyNotUseAsIs,M,(e4,x4)=>{var T=W0(),a4=t(T,!0);u(T),h(()=>r(a4,e(x4))),B(e4,T)}),u(D4),h(()=>{r(k4,e(i).historicalPrecedents.description),r(f4,`  
              ${e(m).description??""}`),r(G4,e(i).historicalPrecedents.inspirations),r(X4,e(i).historicalPrecedents.whyNotUseAsIs)}),B(A4,z4)},$$slots:{default:!0}})}),B(E,l)}}),u(d4);var v4=a(d4,2),z=t(v4),B4=t(z,!0);u(z);var I4=a(z,2),$4=t(I4),G=t($4),C4=t(G,!0);u(G);var m4=a(G,2),q4=t(m4,!0);u(m4);var X=a(m4,2);q(X,21,()=>e(i).ourDesign.structure.list,M,(E,v)=>{var l=z0(),_=t(l,!0);u(l),h(()=>r(_,e(v))),B(E,l)}),u(X);var V=a(X,2),M4=t(V,!0);u(V);var Y4=a(V,2),J=t(Y4),p4=t(J,!0);u(J);var P4=a(J,2);q(P4,21,()=>e(i).ourDesign.advantages.list,M,(E,v)=>{var l=G0(),_=t(l,!0);u(l),h(()=>r(_,e(v))),B(E,l)}),u(P4),u(Y4),u($4),u(I4),u(v4);var h4=a(v4,2),n4=t(h4),g4=t(n4,!0);u(n4);var H4=a(n4,2);c0(H4,{children:(E,v)=>{var l=o0(),_=t0(l);q(_,17,()=>o,M,(_4,m)=>{{let A4=r4(()=>`${e(m).currentCalendarDate.toLocaleDateString()} → ${e(m).commonString}`);D0(_4,{get title(){return e(A4)},children:(s0,z4)=>{var Q=X0(),l4=t(Q),k4=t(l4),f4=t(k4),Z=t(f4),O4=t(Z);u(Z);var G4=a(Z);u(f4);var u4=a(f4,2),t4=t(u4),L4=t(t4);u(t4);var X4=a(t4);u(u4);var D4=a(u4,2),e4=t(D4),x4=t(e4);u(e4);var T=a(e4);u(D4);var a4=a(D4,2),V4=t(a4),v0=t(V4);u(V4);var B0=a(V4);u(a4);var J4=a(a4,2),K4=t(J4),C0=t(K4);u(K4);var m0=a(K4);u(J4);var Q4=a(J4,2),Z4=t(Q4),p0=t(Z4);u(Z4);var h0=a(Z4);u(Q4);var n0=a(Q4,2),u0=t(n0),g0=t(u0);u(u0);var F0=a(u0);u(n0),u(k4),u(l4),u(Q),h(()=>{r(O4,`${e(i).examples.isLeapYear.title??""}:`),r(G4,`  
                    ${(e(m).isLeapYear?e(i).examples.isLeapYear.yes:e(i).examples.isLeapYear.no)??""}`),r(L4,`${e(i).examples.dayOfYear??""}:`),r(X4,`  
                    ${e(m).dayOfYear??""}`),r(x4,`${e(i).examples.day??""}:`),r(T,`  
                    ${e(m).parsed.day??""}`),r(v0,`${e(i).examples.month??""}:`),r(B0,`  
                    ${e(m).parsed.month??""}`),r(C0,`${e(i).examples.year??""}:`),r(m0,`  
                    ${e(m).parsed.year??""}`),r(p0,`${e(i).examples.commonString??""}:`),r(h0,`  
                    ${e(m).commonString??""}`),r(g0,`${e(i).examples.isoString??""}:`),r(F0,`  
                    ${e(m).isoString??""}`)}),B(s0,Q)},$$slots:{default:!0}})}}),B(E,l)}}),u(h4);var F4=a(h4,2),o4=t(F4),R4=t(o4,!0);u(o4);var W4=a(o4,2);q0(W4,{get locale(){return e(d)}}),u(F4);var U=a(F4,2),K=t(U),y4=t(K,!0);u(K);var j4=a(K,2);N0(j4,{get locale(){return e(d)}}),u(U),u(g),u(D),h(()=>{r(w,e(i).title),r(A,e(i).problem.title),r(i4,e(i).problem.description),r(f,`${e(i).problem.inefficiencies.description??""}:`),r(L,e(i).solution.title),r(s4,e(i).solution.description),r(W,`${e(i).solution.benefits.description??""}:`),r(E4,e(i).historicalPrecedents.title),r(B4,e(i).ourDesign.title),r(C4,e(i).ourDesign.description),r(q4,e(i).ourDesign.structure.description),r(M4,e(i).ourDesign.celebrations),r(p4,e(i).ourDesign.advantages.title),r(g4,e(i).examples.title),r(R4,e(i).realtimeWatches.title),r(y4,e(i).dateCalculator.title)}),B(c,D),N4()}export{ou as component};
