import"../chunks/Bzak7iHL.js";import{p as o0,f as r,t as d,b as t,c as d0,s as a,d as e,r as u,g as n,u as f4,a as v4,av as L0,az as V4,ay as S4,ax as s0,a$ as t0,n as H,h as R0,$ as T0}from"../chunks/RHWQbow4.js";import{d as W0,s}from"../chunks/BlWcudmi.js";import{i as o4}from"../chunks/CtoItwj4.js";import{e as y,i as b}from"../chunks/Dnfvvefi.js";import{h as q0}from"../chunks/C_sRNQCS.js";import{s as R4}from"../chunks/BdpLTtcP.js";import{s as S}from"../chunks/CaC9IHEK.js";const L4={cambridge:"A8CF92",opendict:"F5BE6A",any:"EBBACB"},wu={cambridge:"7B966B",opendict:"B58C4E",any:"B08B98"},M0=[{english:"approach",transcriptions:{cambridge:"əˈproʊtʃ",opendict:"əˈpɹoʊtʃ"},newEnglish:"aprōč",breakdown:[{sounds:["ə"],mapping:"a"},{sounds:["p"],mapping:"p"},[{dictionaryKey:"cambridge",sounds:["r"],mapping:"r"},{dictionaryKey:"opendict",sounds:["ɹ"],mapping:"r"}],{sounds:["oʊ"],mapping:"ō"},{sounds:["tʃ"],mapping:"č"}]},{english:"pig",transcriptions:{cambridge:"pɪɡ",opendict:"ˈpɪɡ"},newEnglish:"pik",breakdown:[{sounds:["p"],mapping:"p"},{sounds:["ɪ"],mapping:"i"},{sounds:["ɡ$"],mapping:"k"}]},{english:"dog",transcriptions:{cambridge:"dɑːɡ",opendict:"ˈdɔɡ"},newEnglish:"dak",breakdown:[{sounds:["d"],mapping:"d"},[{dictionaryKey:"cambridge",sounds:["ɑː"],mapping:"a"},{dictionaryKey:"opendict",sounds:["ɔ"],mapping:"a"}],{sounds:["ɡ$"],mapping:"k"}]},{english:"red",transcriptions:{cambridge:"red",opendict:"ˈɹɛd"},newEnglish:"ret",breakdown:[[{dictionaryKey:"cambridge",sounds:["r"],mapping:"r"},{dictionaryKey:"opendict",sounds:["ɹ"],mapping:"r"}],[{dictionaryKey:"cambridge",sounds:["e"],mapping:"e"},{dictionaryKey:"opendict",sounds:["ɛ"],mapping:"e"}],{sounds:["d$"],mapping:"t"}]},{english:"rat",transcriptions:{cambridge:"ræt",opendict:"ˈɹæt"},newEnglish:"rāt",breakdown:[[{dictionaryKey:"cambridge",sounds:["r"],mapping:"r"},{dictionaryKey:"opendict",sounds:["ɹ"],mapping:"r"}],{sounds:["æ"],mapping:"ā"},{sounds:["t"],mapping:"t"}]},{english:"turtle",transcriptions:{cambridge:"ˈtɝː.t̬əl",opendict:"ˈtɝtəɫ"},newEnglish:"tëtl",breakdown:[{sounds:["t"],mapping:"t"},{sounds:["ɝ"],mapping:"ë"},[{dictionaryKey:"cambridge",sounds:["t̬"],mapping:"t"},{dictionaryKey:"opendict",sounds:["t"],mapping:"t"}],[{dictionaryKey:"cambridge",sounds:["əl$"],mapping:"l"},{dictionaryKey:"opendict",sounds:["əɫ$"],mapping:"l"}]]},{english:"goat",transcriptions:{cambridge:"ɡoʊt",opendict:"ˈɡoʊt"},newEnglish:"gōt",breakdown:[{sounds:["ɡ"],mapping:"g"},{sounds:["oʊ"],mapping:"ō"},{sounds:["t"],mapping:"t"}]},{english:"bear",transcriptions:{cambridge:"ber",opendict:"ˈbɛɹ"},newEnglish:"be",breakdown:[{sounds:["b"],mapping:"b"},[{dictionaryKey:"cambridge",sounds:["e"],mapping:"e"},{dictionaryKey:"opendict",sounds:["ɛ"],mapping:"e"}],[{dictionaryKey:"cambridge",sounds:["r$"],mapping:""},{dictionaryKey:"opendict",sounds:["ɹ$"],mapping:""}]]},{english:"panther",transcriptions:{cambridge:"ˈpæn.θɚ",opendict:"ˈpænθɝ"},newEnglish:"pānfe",breakdown:[{sounds:["p"],mapping:"p"},{sounds:["æ"],mapping:"ā"},{sounds:["n"],mapping:"n"},{sounds:["θ"],mapping:"f"},[{dictionaryKey:"cambridge",sounds:["ɚ"],mapping:"e"},{dictionaryKey:"opendict",sounds:["ɝ$"],mapping:"e"}]]},{english:"frog",transcriptions:{cambridge:"frɑːɡ",opendict:"ˈfɹɑɡ"},newEnglish:"frak",breakdown:[{sounds:["f"],mapping:"f"},[{dictionaryKey:"cambridge",sounds:["r"],mapping:"r"},{dictionaryKey:"opendict",sounds:["ɹ"],mapping:"r"}],{sounds:["ɑ"],mapping:"a"},{sounds:["ɡ$"],mapping:"k"}]},{english:"feather",transcriptions:{cambridge:"ˈfeð.ɚ",opendict:"ˈfɛðɝ"},newEnglish:"feve",breakdown:[{sounds:["f"],mapping:"f"},[{dictionaryKey:"cambridge",sounds:["e"],mapping:"e"},{dictionaryKey:"opendict",sounds:["ɛ"],mapping:"e"}],{sounds:["ð"],mapping:"v"},[{dictionaryKey:"cambridge",sounds:["ɚ"],mapping:"e"},{dictionaryKey:"opendict",sounds:["ɝ"],mapping:"e"}]]},{english:"beaver",transcriptions:{cambridge:"ˈbiː.vɚ",opendict:"ˈbivɝ"},newEnglish:"bive",breakdown:[{sounds:["b"],mapping:"b"},{sounds:["i"],mapping:"i"},{sounds:["v"],mapping:"v"},[{dictionaryKey:"cambridge",sounds:["ɚ"],mapping:"e"},{dictionaryKey:"opendict",sounds:["ɝ"],mapping:"e"}]]},{english:"snake",transcriptions:{cambridge:"sneɪk",opendict:"ˈsneɪk"},newEnglish:"sneík",breakdown:[{sounds:["s"],mapping:"s"},{sounds:["n"],mapping:"n"},{sounds:["eɪ"],mapping:"eí"},{sounds:["k"],mapping:"k"}]},{english:"sheep",transcriptions:{cambridge:"ʃiːp",opendict:"ˈʃip"},newEnglish:"šip",breakdown:[{sounds:["ʃ"],mapping:"š"},{sounds:["i"],mapping:"i"},{sounds:["p"],mapping:"p"}]},{english:"chicken",transcriptions:{cambridge:"ˈtʃɪk.ɪn",opendict:"ˈtʃɪkən"},newEnglish:"čikn",breakdown:[{sounds:["tʃ"],mapping:"č"},{sounds:["ɪ"],mapping:"i"},{sounds:["k"],mapping:"k"},[{dictionaryKey:"cambridge",sounds:["ɪn$"],mapping:"n"},{dictionaryKey:"any",sounds:["ən"],mapping:"n"}]]},{english:"zebra",transcriptions:{cambridge:"ˈziː.brə",opendict:"ˈzibɹə"},newEnglish:"zibra",breakdown:[{sounds:["z"],mapping:"z"},{sounds:["i"],mapping:"i"},{sounds:["b"],mapping:"b"},[{dictionaryKey:"cambridge",sounds:["r"],mapping:"r"},{dictionaryKey:"opendict",sounds:["ɹ"],mapping:"r"}],{sounds:["ə$"],mapping:"a"}]},{english:"television",transcriptions:{cambridge:"ˈtel.ə.vɪʒ.ən",opendict:"ˈtɛɫəˌvɪʒən"},newEnglish:"televijn",breakdown:[{sounds:["t"],mapping:"t"},[{dictionaryKey:"cambridge",sounds:["e"],mapping:"e"},{dictionaryKey:"opendict",sounds:["ɛ"],mapping:"e"}],[{dictionaryKey:"cambridge",sounds:["lə"],mapping:"le"},{dictionaryKey:"opendict",sounds:["ɫə"],mapping:"le"}],{sounds:["v"],mapping:"v"},{sounds:["ɪ"],mapping:"i"},{sounds:["ʒ"],mapping:"j"},{sounds:["ən"],mapping:"n"}]},{english:"giraffe",transcriptions:{cambridge:"dʒɪˈræf",opendict:"dʒɝˈæf"},newEnglish:"djirāf",breakdown:[{sounds:["d"],mapping:"d"},{sounds:["ʒ"],mapping:"j"},[{dictionaryKey:"cambridge",sounds:["ɪˈr"],mapping:"ir"},{dictionaryKey:"opendict",sounds:["ɝˈ"],mapping:"ir"}],{sounds:["æ"],mapping:"ā"},{sounds:["f"],mapping:"f"}]},{english:"wolf",transcriptions:{cambridge:"wʊlf",opendict:"ˈwʊɫf"},newEnglish:"wolf",breakdown:[{sounds:["w"],mapping:"w"},{sounds:["ʊ"],mapping:"o"},[{dictionaryKey:"cambridge",sounds:["l"],mapping:"l"},{dictionaryKey:"opendict",sounds:["ɫ"],mapping:"l"}],{sounds:["f"],mapping:"f"}]},{english:"lion",transcriptions:{cambridge:"ˈlaɪ.ən",opendict:"ˈɫaɪən"},newEnglish:"laín",breakdown:[[{dictionaryKey:"cambridge",sounds:["l"],mapping:"l"},{dictionaryKey:"opendict",sounds:["ɫ"],mapping:"l"}],{sounds:["aɪ"],mapping:"aí"},{sounds:["ən"],mapping:"n"}]},{english:"mouse",transcriptions:{cambridge:"maʊs",opendict:"ˈmaʊs"},newEnglish:"mays",breakdown:[{sounds:["m"],mapping:"m"},{sounds:["aʊ"],mapping:"ay"},{sounds:["s"],mapping:"s"}]},{english:"dinosaur",transcriptions:{cambridge:"ˈdaɪ.nə.sɔːr",opendict:"ˈdaɪnəˌsɔɹ"},newEnglish:"daínaso",breakdown:[{sounds:["d"],mapping:"d"},{sounds:["aɪ"],mapping:"aí"},{sounds:["n"],mapping:"n"},{sounds:["ə"],mapping:"a"},{sounds:["s"],mapping:"s"},[{dictionaryKey:"cambridge",sounds:["ɔːr"],mapping:"o"},{dictionaryKey:"opendict",sounds:["ɔɹ"],mapping:"o"}]]},{english:"penguin",transcriptions:{cambridge:"ˈpeŋ.ɡwɪn",opendict:"ˈpɛŋɡwən"},newEnglish:"pengwn",breakdown:[{sounds:["p"],mapping:"p"},[{dictionaryKey:"cambridge",sounds:["e"],mapping:"e"},{dictionaryKey:"opendict",sounds:["ɛ"],mapping:"e"}],{sounds:["ŋ"],mapping:"n"},{sounds:["ɡ"],mapping:"g"},{sounds:["w"],mapping:"w"},[{dictionaryKey:"cambridge",sounds:["ɪn$"],mapping:"n"},{dictionaryKey:"opendict",sounds:["ən"],mapping:"n"}]]},{english:"rabbit",transcriptions:{cambridge:"ˈræb.ɪt",opendict:"ˈɹæbət"},newEnglish:"rābit",breakdown:[[{dictionaryKey:"cambridge",sounds:["r"],mapping:"r"},{dictionaryKey:"opendict",sounds:["ɹ"],mapping:"r"}],{sounds:["æ"],mapping:"ā"},[{dictionaryKey:"cambridge",sounds:["bi"],mapping:"bi"},{dictionaryKey:"opendict",sounds:["bə"],mapping:"bi"}],{sounds:["t"],mapping:"t"}]},{english:"yak",transcriptions:{cambridge:"jæk",opendict:"ˈjæk"},newEnglish:"íāk",breakdown:[{sounds:["j"],mapping:"í"},{sounds:["æ"],mapping:"ā"},{sounds:["k"],mapping:"k"}]},{english:"horse",transcriptions:{cambridge:"hɔːrs",opendict:"ˈhɔɹs"},newEnglish:"hos",breakdown:[{sounds:["h"],mapping:"h"},[{dictionaryKey:"cambridge",sounds:["ɔːr"],mapping:"o"},{dictionaryKey:"opendict",sounds:["ɔɹ"],mapping:"o"}],{sounds:["s"],mapping:"s"}]},{english:"green",transcriptions:{cambridge:"ɡriːn",opendict:"ˈɡɹin"},newEnglish:"grin",breakdown:[{sounds:["ɡ"],mapping:"g"},[{dictionaryKey:"cambridge",sounds:["r"],mapping:"r"},{dictionaryKey:"opendict",sounds:["ɹ"],mapping:"r"}],{sounds:["i"],mapping:"i"},{sounds:["n"],mapping:"n"}]},{english:"pink",transcriptions:{cambridge:"pɪŋk",opendict:"ˈpɪŋk"},newEnglish:"pink",breakdown:[{sounds:["p"],mapping:"p"},{sounds:["ɪ"],mapping:"i"},{sounds:["ŋ"],mapping:"n"},{sounds:["k"],mapping:"k"}]},{english:"wood",transcriptions:{cambridge:"wʊd",opendict:"ˈwʊd"},newEnglish:"wot",breakdown:[{sounds:["w"],mapping:"w"},{sounds:["ʊ"],mapping:"o"},{sounds:["d$"],mapping:"t"}]},{english:"blue",transcriptions:{cambridge:"bluː",opendict:"ˈbɫu"},newEnglish:"blu",breakdown:[{sounds:["b"],mapping:"b"},[{dictionaryKey:"cambridge",sounds:["l"],mapping:"l"},{dictionaryKey:"opendict",sounds:["ɫ"],mapping:"l"}],{sounds:["u"],mapping:"u"}]},{english:"dust",transcriptions:{cambridge:"dʌst",opendict:"ˈdəst"},newEnglish:"dast",breakdown:[[{dictionaryKey:"cambridge",sounds:["dʌ"],mapping:"da"},{dictionaryKey:"opendict",sounds:["də"],mapping:"da"}],{sounds:["s"],mapping:"s"},{sounds:["t"],mapping:"t"}]},{english:"purple",transcriptions:{cambridge:"ˈpɝː.pəl",opendict:"ˈpɝpəɫ"},newEnglish:"pëpl",breakdown:[{sounds:["p"],mapping:"p"},{sounds:["ɝ"],mapping:"ë"},{sounds:["p"],mapping:"p"},[{dictionaryKey:"cambridge",sounds:["əl$"],mapping:"l"},{dictionaryKey:"opendict",sounds:["əɫ$"],mapping:"l"}]]},{english:"mauve",transcriptions:{cambridge:"moʊv",opendict:"ˈmɔv"},newEnglish:{cambridge:"mōf",opendict:"maf"},breakdown:[{sounds:["m"],mapping:"m"},[{dictionaryKey:"cambridge",sounds:["oʊ"],mapping:"ō"},{dictionaryKey:"opendict",sounds:["ɔ"],mapping:"a"}],{sounds:["v$"],mapping:"f"}]},{english:"sand",transcriptions:{cambridge:"sænd",opendict:"ˈsænd"},newEnglish:"sānt",breakdown:[{sounds:["s"],mapping:"s"},{sounds:["æ"],mapping:"ā"},{sounds:["n"],mapping:"n"},{sounds:["d$"],mapping:"t"}]},{english:"coffee",transcriptions:{cambridge:"ˈkɑː.fi",opendict:"ˈkɑfi"},newEnglish:"kafi",breakdown:[{sounds:["k"],mapping:"k"},[{dictionaryKey:"opendict",sounds:["ɔ"],mapping:"a"},{dictionaryKey:"any",sounds:["ɑ"],mapping:"a"}],{sounds:["f"],mapping:"f"},{sounds:["i"],mapping:"i"}]},{english:"jade",transcriptions:{cambridge:"dʒeɪd",opendict:"ˈdʒeɪd"},newEnglish:"djeit",breakdown:[{sounds:["d"],mapping:"d"},{sounds:["ʒ"],mapping:"j"},{sounds:["eɪ"],mapping:"eí"},{sounds:["d$"],mapping:"t"}]},{english:"gold",transcriptions:{cambridge:"ɡoʊld",opendict:"ˈɡoʊɫd"},newEnglish:"gōlt",breakdown:[{sounds:["ɡ"],mapping:"g"},{sounds:["oʊ"],mapping:"ō"},[{dictionaryKey:"cambridge",sounds:["l"],mapping:"l"},{dictionaryKey:"opendict",sounds:["ɫ"],mapping:"l"}],{sounds:["d$"],mapping:"t"}]},{english:"turquoise",transcriptions:{cambridge:"ˈtɝː.kɔɪz",opendict:"ˈtɝkwɔɪz"},newEnglish:"tëkoís",breakdown:[{sounds:["t"],mapping:"t"},{sounds:["ɝ"],mapping:"ë"},{sounds:["k"],mapping:"k"},{sounds:["ɔɪ"],mapping:"oí"},{sounds:["z$"],mapping:"s"}]},{english:"lime",transcriptions:{cambridge:"laɪm",opendict:"ˈɫaɪm"},newEnglish:"laím",breakdown:[[{dictionaryKey:"cambridge",sounds:["l"],mapping:"l"},{dictionaryKey:"opendict",sounds:["ɫ"],mapping:"l"}],{sounds:["aɪ"],mapping:"aí"},{sounds:["m"],mapping:"m"}]},{english:"brown",transcriptions:{cambridge:"braʊn",opendict:"ˈbɹaʊn"},newEnglish:"brayn",breakdown:[{sounds:["b"],mapping:"b"},[{dictionaryKey:"cambridge",sounds:["r"],mapping:"r"},{dictionaryKey:"opendict",sounds:["ɹ"],mapping:"r"}],{sounds:["aʊ"],mapping:"ay"},{sounds:["n"],mapping:"n"}]}],G0=(A,E=H)=>{var f=S4();const l=f4(()=>E()[0].mapping),p=f4(()=>E().every(o=>o.mapping===n(l)));var i=v4(f);{var g=o=>{var v=Q0();S(v,"",{},{color:"#000000"});var w=e(v,!0);u(v),d(()=>s(w,n(l))),t(o,v)},D=o=>{var v=S4(),w=v4(v);y(w,17,E,b,(x,G,h4)=>{var Q=U0(),F=v4(Q);{var d4=C=>{var E4=V4(",");t(C,E4)};o4(F,C=>{h4>0&&C(d4)})}var I=a(F,2);let N;var p4=e(I,!0);u(I),d(C=>{N=S(I,"",N,C),s(p4,n(G).mapping)},[()=>({color:`#${wu[n(G).dictionaryKey]}`})]),t(x,Q)}),t(o,v)};o4(i,o=>{n(p)?o(g):o(D,!1)})}t(A,f)};var Q0=r("<span> </span>"),U0=r("<!> <span> </span>",1),J0=r("<!> <span> </span>",1),Y0=r('<span class="text-dark"> </span>'),Z0=r("<!> <span> </span>",1),X0=r('<span class="badge rounded-pill me-1 font-monospace"> </span>'),u3=r('<span class="me-2"></span>'),e3=r("<tr><td></td><td><strong><!></strong></td></tr>"),n3=r('<span class="badge rounded-pill me-2 font-monospace"> </span>'),a3=r("<tr><td></td><td><strong> </strong></td></tr>"),i3=r('<div class="mt-3"><h6> </h6> <table class="table table-sm"><thead><tr><th> </th><th> </th></tr></thead><tbody></tbody></table></div>'),s3=r('<li class="list-group-item"><div class="d-flex justify-content-between align-items-center cursor-pointer"><div><strong> </strong> <span class=" ms-2"></span> <span class="ms-2">→</span> <span class="ms-2 fw-bold"><!></span></div> <button class="btn btn-sm btn-outline-primary"> </button></div> <!></li>'),t3=r('<section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><p class="lead"> </p> <ul class="list-group list-group-flush"></ul></div></div></section>');function r3(A,E){o0(E,!0);const f={en:{title:"Word Examples",description:"See how common English words transform in our phonetic system",hideDetails:"Hide Details",showDetails:"Show Details",soundBreakdown:"Sound Breakdown",phoneticSounds:"Phonetic Sounds",newEnglishLetter:"New English Letter(s)"},ru:{title:"Примеры слов",description:"Посмотрите, как обычные английские слова преобразуются в нашей фонетической системе",hideDetails:"Скрыть детали",showDetails:"Показать детали",soundBreakdown:"Разбор звуков",phoneticSounds:"Фонетические звуки",newEnglishLetter:"Буквы Нового английского"}},l=f4(()=>f[E.locale]);let p=L0(null);function i(Q){n(p)===Q?s0(p,null):s0(p,Q,!0)}var g=t3(),D=e(g),o=e(D,!0);u(D);var v=a(D,2),w=e(v),x=e(w),G=e(x);u(x);var h4=a(x,2);y(h4,21,()=>M0,b,(Q,F,d4)=>{var I=s3(),N=e(I);N.__click=()=>i(d4),S(N,"",{},{cursor:"pointer"});var p4=e(N),C=e(p4),E4=e(C,!0);u(C);var k4=a(C,2);y(k4,21,()=>Object.entries(n(F).transcriptions),b,(_,B,O)=>{var X=f4(()=>t0(n(B),2));let u4=()=>n(X)[0],V=()=>n(X)[1];var U=J0(),$=v4(U);{var y4=k=>{var K=V4(",");t(k,K)};o4($,k=>{O>0&&k(y4)})}var P=a($,2);let L;var J=e(P,!0);u(P),d(k=>{L=S(P,"",L,k),s(J,V())},[()=>({color:`#${wu[u4()]}`})]),t(_,U)}),u(k4);var K4=a(k4,4),H4=e(K4);{var $4=_=>{var B=Y0(),O=e(B,!0);u(B),d(()=>s(O,n(F).newEnglish)),t(_,B)},T4=_=>{var B=S4(),O=v4(B);y(O,17,()=>Object.entries(n(F).newEnglish),b,(X,u4,V)=>{var U=f4(()=>t0(n(u4),2));let $=()=>n(U)[0],y4=()=>n(U)[1];var P=Z0(),L=v4(P);{var J=R=>{var P4=V4(",");t(R,P4)};o4(L,R=>{V>0&&R(J)})}var k=a(L,2);let K;var b4=e(k,!0);u(k),d(R=>{K=S(k,"",K,R),s(b4,y4())},[()=>({color:`#${wu[$()]}`})]),t(X,P)}),t(_,B)};o4(H4,_=>{typeof n(F).newEnglish=="string"?_($4):_(T4,!1)})}u(K4),u(p4);var w4=a(p4,2),W4=e(w4,!0);u(w4),u(N);var I4=a(N,2);{var q4=_=>{var B=i3(),O=e(B),X=e(O);u(O);var u4=a(O,2),V=e(u4),U=e(V),$=e(U),y4=e($,!0);u($);var P=a($),L=e(P,!0);u(P),u(U),u(V);var J=a(V);y(J,21,()=>n(F).breakdown,b,(k,K)=>{var b4=S4(),R=v4(b4);{var P4=e4=>{var n4=e3(),T=e(n4);y(T,21,()=>n(K),b,(A4,c4)=>{var Y=u3();y(Y,21,()=>n(c4).sounds,b,(D4,F4)=>{var s4=X0();let O4;var z4=e(s4,!0);u(s4),d(j4=>{O4=S(s4,"",O4,j4),s(z4,n(F4))},[()=>({"background-color":`#${L4[n(c4).dictionaryKey]}`,color:"#000000",padding:"6px 12px","margin-bottom":"6px","font-size":"1.2rem","font-weight":"normal"})]),t(D4,s4)}),u(Y),t(A4,Y)}),u(T);var a4=a(T),i4=e(a4);S(i4,"",{},{"font-size":"1.5rem",display:"inline-block",padding:"4px 0"});var x4=e(i4);G0(x4,()=>n(K)),u(i4),u(a4),u(n4),t(e4,n4)},N4=e4=>{var n4=a3(),T=e(n4);y(T,21,()=>n(K).sounds,b,(A4,c4)=>{var Y=n3();let D4;var F4=e(Y,!0);u(Y),d(s4=>{D4=S(Y,"",D4,s4),s(F4,n(c4))},[()=>({"background-color":`#${L4.any}`,color:"#000000",border:"1px solid #CCCCCC",padding:"6px 12px","margin-bottom":"6px","font-size":"1.2rem","font-weight":"normal"})]),t(A4,Y)}),u(T);var a4=a(T),i4=e(a4);S(i4,"",{},{"font-size":"1.5rem",display:"inline-block",padding:"4px 0"});var x4=e(i4,!0);u(i4),u(a4),u(n4),d(()=>s(x4,n(K).mapping)),t(e4,n4)};o4(R,e4=>{Array.isArray(n(K))?e4(P4):e4(N4,!1)})}t(k,b4)}),u(J),u(u4),u(B),d(()=>{s(X,`${n(l).soundBreakdown??""}:`),s(y4,n(l).phoneticSounds),s(L,n(l).newEnglishLetter)}),t(_,B)};o4(I4,_=>{n(p)===d4&&_(q4)})}u(I),d(()=>{R4(N,"role",n(p)===d4?n(l).hideDetails:n(l).showDetails),s(E4,n(F).english),s(W4,n(p)===d4?n(l).hideDetails:n(l).showDetails)}),t(Q,I)}),u(h4),u(w),u(v),u(g),d(()=>{s(o,n(l).title),s(G,`${n(l).description??""}:`)}),t(A,g),d0()}W0(["click"]);const o3=[{sources:[{dictionaryKey:"opendict",sounds:["ɔ"]},{dictionaryKey:"any",sounds:["a","ɑ","ʌ","ə$"]}],mapping:"a",examples:["d*u*st","b*u*lk"]},{sources:[{dictionaryKey:"any",sounds:["aɪ"]}],mapping:"aí",examples:["l*i*me","d*i*nosaur"]},{sources:[{dictionaryKey:"any",sounds:["aʊ"]}],mapping:"ay",examples:["m*ou*se","br*ow*n"]},{sources:[{dictionaryKey:"any",sounds:["æ"]}],mapping:"ā",examples:["r*a*t","c*a*t"]},{sources:[{dictionaryKey:"any",sounds:["b"]}],mapping:"b",examples:["*b*ear","ze*b*ra"]},{sources:[{dictionaryKey:"any",sounds:["bə"]}],mapping:"bi",examples:["*be*aver","rab*bi*t"]},{sources:[{dictionaryKey:"any",sounds:["tʃ"]}],mapping:"č",examples:["approa*ch*","*ch*icken"]},{sources:[{dictionaryKey:"any",sounds:["d"]}],mapping:"d",examples:["*d*og","*d*inosaur"]},{sources:[{dictionaryKey:"any",sounds:["də"]}],mapping:"da",examples:["*du*st","aque*du*ct"]},{sources:[{dictionaryKey:"cambridge",sounds:["ɚ$"]},{dictionaryKey:"opendict",sounds:["ɛ","ɝ$"]},{dictionaryKey:"any",sounds:["e","ə"]}],mapping:"e",examples:["r*e*d","b*e*ar"]},{sources:[{dictionaryKey:"any",sounds:["eɪ"]}],mapping:"eí",examples:["sn*a*ke","sh*a*pe"]},{sources:[{dictionaryKey:"any",sounds:["ɝ"]}],mapping:"ë",examples:["t*ur*tle","p*ur*ple"]},{sources:[{dictionaryKey:"any",sounds:["f","θ","v$"]}],mapping:"f",examples:["*f*rog","*f*eather"]},{sources:[{dictionaryKey:"any",sounds:["ɡ"]}],mapping:"g",examples:["*g*oat","*g*reen"]},{sources:[{dictionaryKey:"any",sounds:["h"]}],mapping:"h",examples:["*h*orse","*h*ouse"]},{sources:[{dictionaryKey:"any",sounds:["i","ɪ"]}],mapping:"i",examples:["p*i*g","z*e*bra"]},{sources:[{dictionaryKey:"cambridge",sounds:["ɪˈr"]},{dictionaryKey:"opendict",sounds:["ɝˈ"]}],mapping:"ir",examples:["g*ir*affe","b*ur*rito"]},{sources:[{dictionaryKey:"any",sounds:["əs$"]}],mapping:"is",examples:["vers*us*"]},{sources:[{dictionaryKey:"any",sounds:["j"]}],mapping:"í",examples:["*y*ak","*y*acht"]},{sources:[{dictionaryKey:"any",sounds:["ʒ"]}],mapping:"j",examples:["televi*sio*n","*j*ade"]},{sources:[{dictionaryKey:"any",sounds:["k","ɡ$"]}],mapping:"k",examples:["pin*k*","*c*offee"]},{sources:[{dictionaryKey:"cambridge",sounds:["l","əl$"]},{dictionaryKey:"opendict",sounds:["ɫ","əɫ$"]}],mapping:"l",examples:["*l*ion","go*l*d"]},{sources:[{dictionaryKey:"any",sounds:["lə"]}],mapping:"le",examples:["te*le*vision"]},{sources:[{dictionaryKey:"any",sounds:["m"]}],mapping:"m",examples:["*m*auve","*m*ouse"]},{sources:[{dictionaryKey:"cambridge",sounds:["ɪn$"]},{dictionaryKey:"any",sounds:["n","ŋ","ən"]}],mapping:"n",examples:["brow*n*","sa*n*d"]},{sources:[{dictionaryKey:"any",sounds:["nə"]}],mapping:"na",examples:["di*no*saur"]},{sources:[{dictionaryKey:"cambridge",sounds:["ɔːr"]},{dictionaryKey:"opendict",sounds:["ɔɹ"]},{dictionaryKey:"any",sounds:["ʊ"]}],mapping:"o",examples:["w*o*lf","h*o*rse"]},{sources:[{dictionaryKey:"any",sounds:["ɔɪ"]}],mapping:"oí",examples:["turq*uoi*se"]},{sources:[{dictionaryKey:"any",sounds:["oʊ"]}],mapping:"ō",examples:["g*oa*t","appr*oa*ch"]},{sources:[{dictionaryKey:"any",sounds:["p","b$"]}],mapping:"p",examples:["*p*ig","*p*enguin"]},{sources:[{dictionaryKey:"cambridge",sounds:["r"]},{dictionaryKey:"opendict",sounds:["ɹ"]}],mapping:"r",examples:["*r*ed","f*r*og"]},{sources:[{dictionaryKey:"any",sounds:["rə"]}],mapping:"ra",examples:["zeb*ra*"]},{sources:[{dictionaryKey:"cambridge",sounds:["r$"]},{dictionaryKey:"opendict",sounds:["ɹ$"]}],mapping:"",examples:["bea*r*"]},{sources:[{dictionaryKey:"any",sounds:["s","z$"]}],mapping:"s",examples:["*s*nake","*s*un"]},{sources:[{dictionaryKey:"any",sounds:["ʃ"]}],mapping:"š",examples:["*sh*eep","*sh*epherd"]},{sources:[{dictionaryKey:"cambridge",sounds:["t̬"]},{dictionaryKey:"any",sounds:["t","d$"]}],mapping:"t",examples:["re*d*","goa*t*"]},{sources:[{dictionaryKey:"any",sounds:["u","jə"]}],mapping:"u",examples:["bl*ue*"]},{sources:[{dictionaryKey:"any",sounds:["v","ð"]}],mapping:"v",examples:["fea*th*er","bea*v*er"]},{sources:[{dictionaryKey:"any",sounds:["w"]}],mapping:"w",examples:["*w*olf","*w*ood"]},{sources:[{dictionaryKey:"any",sounds:["wə"]}],mapping:"we",examples:["aq*ue*duct"]},{sources:[{dictionaryKey:"any",sounds:["z"]}],mapping:"z",examples:["*z*ebra","*z*oo"]}],r0=(A,E=H,f=H,l=H,p=H)=>{var i=p3();let g;var D=e(i),o=e(D),v=e(o,!0);u(o),u(D);var w=a(D,2),x=e(w,!0);u(w),u(i),d(G=>{g=S(i,"",g,G),R4(o,"href",p()),s(v,f()),s(x,l())},[()=>({"border-left":`4px solid #${L4[E()]}`})]),t(A,i)},Bu=(A,E=H,f=H,l=H)=>{var p=c3(),i=e(p),g=e(i),D=e(g,!0);u(g),u(i);var o=a(i,2),v=e(o,!0);u(o),u(p),d(()=>{R4(g,"href",l()),s(D,E()),s(v,f())}),t(A,p)},fu=(A,E=H,f=H,l=H)=>{var p=l3(),i=e(p),g=e(i),D=e(g,!0);u(g);var o=a(g,2),v=e(o),w=e(v,!0);u(v);var x=a(v,2),G=e(x,!0);u(x),u(o),u(i),u(p),d(()=>{s(D,l()),s(w,E()),s(G,f())}),t(A,p)},d3=(A,E=H)=>{var f=S4(),l=v4(f);q0(l,()=>(()=>{const[p,i,g]=E().split("*");return[`<span>${p}</span>`,`<strong style="color:#d63384">${i}</strong>`,`<span>${g}</span>`].join("")})()),t(A,f)};var p3=r('<li class="list-group-item"><strong><a target="_blank" rel="noopener noreferrer"> </a></strong> <p class="mb-0 text-muted"> </p></li>'),c3=r('<li class="list-group-item"><strong><a target="_blank" rel="noopener noreferrer"> </a></strong> <p class="mb-0 text-muted"> </p></li>'),l3=r('<li class="list-group-item"><div class="d-flex align-items-center"><span class="badge bg-primary me-2"> </span> <div><strong> </strong> <p class="mb-0 text-muted"> </p></div></div></li>'),g3=r("<li> </li>"),m3=r("<li> </li>"),v3=r('<div class="col"><div class="card h-100"><div class="card-body text-center"><h3 class="card-title"> </h3></div></div></div>'),h3=r('<span class="badge rounded-pill me-2 font-freemono"> </span>'),E3=r('<div class="mb-1"></div>'),y3=r("<span><!> <!></span>"),b3=r("<tr><td></td><td><strong> </strong></td><td></td></tr>"),D3=r('<tr><td><code class="text-dark"> </code></td><td><code class="text-primary fw-bold"> </code></td><td><code class="text-secondary"> </code></td></tr>'),_3=r('<span class="ms-1 text-info" style="cursor: help;"><i class="bi bi-question-circle"></i></span>'),B3=r('<tr><td><code class="text-muted"> </code></td><td><code class="text-muted"> </code></td><td><code class="text-muted"> </code></td><td><code class="text-primary fw-bold"> </code></td><td><code class="text-primary fw-bold"> </code> <!></td></tr>'),f3=r('<div class="container my-5"><div class="responsive-container"><h1 class="mb-4"> </h1> <section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><p class="lead"> </p> <p> </p> <ul></ul></div></div></section> <section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><p class="lead"> </p> <p> </p> <p> </p></div></div></section> <section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><p class="lead"> </p> <p> </p> <ul></ul></div></div></section> <section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><p class="lead"> </p> <ul class="list-group list-group-flush"><!> <!></ul></div></div></section> <section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><p class="lead"> </p> <ul class="list-group list-group-flush"><!> <!> <!></ul></div></div></section> <section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><p class="lead"> </p> <ul class="list-group list-group-flush"><!> <!> <!></ul></div></div></section> <section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><div class="row row-cols-2 row-cols-md-3 row-cols-lg-6 g-2"></div></div></div></section> <section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><p class="lead"> </p> <div class="table-responsive"><table class="table table-striped"><thead><tr><th> </th><th> </th><th> </th></tr></thead><tbody></tbody></table></div></div></div></section> <!> <section class="mb-5"><h2> </h2> <div class="card"><div class="card-body"><p class="lead"> </p> <div class="mb-4"><h4 class="h5"> </h4> <p> </p></div> <div class="mb-4"><h4 class="h5"> </h4> <p> </p> <div class="d-inline-block"><table class="table table-sm table-striped" style="width: auto; min-width: 400px;"><thead><tr><th> </th><th> </th><th> </th></tr></thead><tbody></tbody></table></div> <div class="alert alert-info mt-2"><small><strong>Note:</strong> </small></div></div> <div class="mb-4"><h4 class="h5"> </h4> <p> </p> <div class="table-responsive"><table class="table table-sm table-striped"><thead><tr><th colspan="3" class="text-center bg-light"> </th><th colspan="2" class="text-center bg-primary text-white"> </th></tr><tr><th> </th><th> </th><th> </th><th> </th><th> </th></tr></thead><tbody></tbody></table></div></div></div></div></section></div></div>');function P3(A,E){o0(E,!0);const f=["Aa","Āā","Bb","Čč","Dd","Ee","Ëë","Ff","Gg","Hh","Ii","Íí","Jj","Kk","Ll","Mm","Nn","Oo","Ōō","Pp","Rr","Ss","Šš","Tt","Uu","Vv","Ww","Yy","Zz"],l={en:{_page:{title:"New English — Commune"},title:"New English: A Phonetic Revolution",problem:{title:"The Challenge",description:"English writing and pronunciation have diverged significantly over centuries, creating numerous challenges.",difficulties:{description:"The current English language presents several difficulties",list:["Significant difference between written and spoken forms","Inconsistent pronunciation rules with many exceptions","Difficult accents that vary across regions","Overcomplicated spelling rules that don't match pronunciation","Natural reduction in spoken language not reflected in writing","Redundant elements like multiple ways to represent the same sound"]}},idea:{title:"Early Attempts",description:{1:"New English: Learning from Early Challenges in Phonetic Reform",2:`Our journey began with a simple question: "How might English evolve over time?" Our first attempt at creating New English faced significant challenges, as it incorporated overly aggressive simplifications that ultimately compromised the system's integrity.`,3:"This early version relied on online translator pronunciations rather than established dictionaries. This approach proved problematic, as it lacked the linguistic foundation necessary to establish consistent rules. The resulting translations were unstable, inconsistent across platforms, and resistant to systematic automation."}},implementation:{title:"Our Implementation",description:"A systematic approach to reforming English spelling and pronunciation.",features:{description:"Our New English implementation features",list:["Words written exactly as they are pronounced","Reduction of indefinite articles to their essential forms",'Simplification of definite articles and demonstratives to "da"',"Consistent phonetic representation for all sounds","Elimination of silent letters and redundant spellings","Preservation of word recognition while improving logical consistency"]}},dictionaries:{title:"Reference Dictionaries",description:"Our system is based on established phonetic standards from these authoritative sources",cambridge:{name:"Cambridge English Dictionary",description:"A comprehensive dictionary of English with phonetic transcriptions"},opendict:{name:"Open Dictionary",description:"Open-licensed dictionary data"}},sources:{title:"Research Sources",description:"Our approach is informed by these linguistic resources",englishPhonology:{name:"English Phonology - Wikipedia",description:"Overview of the sound system of the English language"},internationalPhoneticAlphabet:{name:"International Phonetic Alphabet",description:"Standardized representation of sounds in written form"},americanIpaChart:{name:"American IPA Chart",description:"Chart of the American IPA"}},dialects:{title:"Reference Dialects",description:"Our phonetic system prioritizes these dialects in descending order",generalAmerican:{name:"General American",description:"The accent of American English most commonly perceived as neutral"},standardEnglish:{name:"Standard English",description:"The standard accent of Standard English in the United Kingdom"},localDialect:{name:"Local Dialect",description:"The form of English used in the local area"}},alphabet:{title:"New English Alphabet"},rules:{title:"Phonetic Rules",description:"Each sound is consistently represented by specific letter(s)",phoneticSounds:"Phonetic Sounds",newEnglishLetter:"New English Letter(s)",exampleWords:"Example Words"},nuances:{title:"Language Nuances",description:"New English includes several practical modifications to improve consistency and pronunciation flow",pronoun:{title:"Personal Pronouns",description:"New English uses 'mi' instead of 'I'/'me' for consistency with other pronoun forms and to eliminate the arbitrary capitalization rule."},interrogatives:{title:"Question Words",description:"Question words often use the '-sa' suffix to avoid consonant clusters and improve pronunciation flow:",tableHeaders:{original:"Original",preferred:"Preferred",expected:"Expected"},examples:[{before:"what",after:{preferred:"wotsa",expected:"wot"}},{before:"who",after:{preferred:"hysa",expected:"hy"}},{before:"when",after:{preferred:"wensa",expected:"wen"}},{before:"where",after:{preferred:"wersa",expected:"wer"}},{before:"which",after:{preferred:"wičsa",expected:"wič"}},{before:"why",after:{preferred:"waísa",expected:"waí"}},{before:"how",after:{preferred:"haysa",expected:"hay"}},{before:"there",after:{preferred:"tersa",expected:"ter"}},{before:"then",after:{preferred:"tensa",expected:"ten"}}],note:`The '-sa' suffix originated from early language development and helps avoid difficult consonant combinations in speech. Its original meaning was to use ending from "there's a".`},irregularVerbs:{title:"Regular Verb Forms",description:"New English uses regular '-it' endings for past tense instead of irregular forms, creating consistent conjugation patterns.",tableHeaders:{english:"English",newEnglish:"New English",common:"Common",past:"Past",pastParticiple:"Past Participle"},examples:[{english:{common:"cut",past:"cut",pastParticiple:"cut"},newEnglish:{common:"kat",past:"katit"}},{english:{common:"speak",past:"spoke",pastParticiple:"spoken"},newEnglish:{common:"spik",past:"spikit"}},{english:{common:"know",past:"knew",pastParticiple:"known"},newEnglish:{common:"nō",past:"nōit"}},{english:{common:"bring",past:"brought",pastParticiple:"brought"},newEnglish:{common:"brin",past:"brinit"}},{english:{common:"see",past:"saw",pastParticiple:"seen"},newEnglish:{common:"si",past:"sit",note:"double letter cannot be preserved because of language mechanics"}}]}}},ru:{_page:{title:"Новый английский — Коммуна"},title:"Новый английский: фонетическая революция",problem:{title:"Проблема",description:"Правописание и произношение в английском языке значительно разошлись за века, создавая множество трудностей.",difficulties:{description:"Современный английский язык создаёт ряд затруднений",list:["Существенное расхождение между письменной и устной формами","Непоследовательные правила произношения с множеством исключений","Сложные акценты, сильно различающиеся по регионам","Сложные правила орфографии, не соответствующие произношению","Естественные сокращения в устной речи не отражаются на письме","Избыточные элементы, такие как разные способы обозначения одного и того же звука"]}},idea:{title:"Ранние попытки",description:{1:"Новый английский: уроки ранних трудностей фонетической реформы",2:"Наш путь начался с простого вопроса: «Как английский может развиваться со временем?» Первая попытка создать Новый английский столкнулась с серьёзными трудностями из-за чрезмерных упрощений, которые в итоге подорвали целостность системы.",3:"Ранняя версия опиралась на произношения онлайн-переводчиков вместо признанных словарей. Такой подход оказался проблематичным, поскольку не имел лингвистической базы для создания последовательных правил. В результате переводы были нестабильны, непоследовательны между платформами и не поддавались автоматизации."}},implementation:{title:"Наша реализация",description:"Системный подход к реформе английской орфографии и произношения.",features:{description:"Особенности нашей реализации Нового английского",list:["Слова пишутся точно так, как произносятся","Неопределённые артикли сведены к их сути","Определённые артикли и указательные слова упрощены до «da»","Последовательное фонетическое обозначение всех звуков","Исключение немых букв и избыточных написаний","Сохранение узнаваемости слов при улучшении логической согласованности"]}},dictionaries:{title:"Справочные словари",description:"Наша система основывается на признанных фонетических стандартах из этих авторитетных источников",cambridge:{name:"Кембриджский словарь английского языка",description:"Обширный словарь английского языка с фонетическими транскрипциями"},opendict:{name:"OpenDict",description:"Свободно-лицензированные данные словаря"}},sources:{title:"Исследовательские источники",description:"Наш подход основан на следующих лингвистических ресурсах",englishPhonology:{name:"Фонология английского языка — Википедия",description:"Обзор звуковой системы английского языка"},internationalPhoneticAlphabet:{name:"Международный фонетический алфавит",description:"Стандартизированное представление звуков в письменной форме"},americanIpaChart:{name:"IPA-таблица американского английского",description:"Таблица фонем американского английского"}},dialects:{title:"Базовые диалекты",description:"Наша фонетическая система отдаёт приоритет этим диалектам в порядке убывания",generalAmerican:{name:"Общий американский",description:"Акцент американского английского, воспринимаемый как нейтральный"},standardEnglish:{name:"Стандартный английский",description:"Стандартный акцент британского английского"},localDialect:{name:"Местный диалект",description:"Форма английского языка, используемая в конкретной местности"}},alphabet:{title:"Алфавит Нового английского"},rules:{title:"Фонетические правила",description:"Каждый звук последовательно обозначается определённой буквой или буквами",phoneticSounds:"Фонетические звуки",newEnglishLetter:"Буквы Нового английского",exampleWords:"Примеры слов"},nuances:{title:"Особенности языка",description:"Новый английский включает несколько практических изменений для улучшения согласованности и удобства произношения",pronoun:{title:"Личные местоимения",description:"Новый английский использует 'mi' вместо 'I'/'me' для согласованности с другими формами местоимений и устранения произвольного правила заглавных букв."},interrogatives:{title:"Вопросительные слова",description:"Вопросительные слова часто используют суффикс '-sa' для избежания скопления согласных и улучшения произношения:",tableHeaders:{original:"Оригинал",preferred:"Предпочтительно",expected:"Ожидаемо"},examples:[{before:"what",after:{preferred:"wotsa",expected:"wot"}},{before:"who",after:{preferred:"hysa",expected:"hy"}},{before:"when",after:{preferred:"wensa",expected:"wen"}},{before:"where",after:{preferred:"wersa",expected:"wer"}},{before:"which",after:{preferred:"wičsa",expected:"wič"}},{before:"why",after:{preferred:"waísa",expected:"waí"}},{before:"how",after:{preferred:"haysa",expected:"hay"}},{before:"there",after:{preferred:"tersa",expected:"ter"}},{before:"then",after:{preferred:"tensa",expected:"ten"}}],note:`Суффикс '-sa' возник при ранней разработке языка и помогает избежать сложных сочетаний согласных в речи. Его первоначальным значением было использование окончания от "there's a".`},irregularVerbs:{title:"Правильные формы глаголов",description:"Новый английский использует регулярные окончания '-it' для прошедшего времени вместо неправильных форм, создавая последовательные модели спряжения.",tableHeaders:{english:"Английский",newEnglish:"Новый английский",common:"Обычная форма",past:"Прошедшее время",pastParticiple:"Причастие прошедшего времени"},examples:[{english:{common:"cut",past:"cut",pastParticiple:"cut"},newEnglish:{common:"kat",past:"katit"}},{english:{common:"speak",past:"spoke",pastParticiple:"spoken"},newEnglish:{common:"spik",past:"spikit"}},{english:{common:"know",past:"knew",pastParticiple:"known"},newEnglish:{common:"nō",past:"nōit"}},{english:{common:"bring",past:"brought",pastParticiple:"brought"},newEnglish:{common:"brin",past:"brinit"}},{english:{common:"see",past:"saw",pastParticiple:"seen"},newEnglish:{common:"si",past:"sit",note:"сдвоенный звук не может быть сохранён из-за механики языка"}}]}}}},p=f4(()=>E.data.locale),i=f4(()=>l[n(p)]);var g=f3();R0(z=>{d(()=>T0.title=n(i)._page.title)});var D=e(g),o=e(D),v=e(o,!0);u(o);var w=a(o,2),x=e(w),G=e(x,!0);u(x);var h4=a(x,2),Q=e(h4),F=e(Q),d4=e(F,!0);u(F);var I=a(F,2),N=e(I);u(I);var p4=a(I,2);y(p4,21,()=>n(i).problem.difficulties.list,b,(z,m)=>{var c=g3(),h=e(c,!0);u(c),d(()=>s(h,n(m))),t(z,c)}),u(p4),u(Q),u(h4),u(w);var C=a(w,2),E4=e(C),k4=e(E4,!0);u(E4);var K4=a(E4,2),H4=e(K4),$4=e(H4),T4=e($4,!0);u($4);var w4=a($4,2),W4=e(w4,!0);u(w4);var I4=a(w4,2),q4=e(I4,!0);u(I4),u(H4),u(K4),u(C);var _=a(C,2),B=e(_),O=e(B,!0);u(B);var X=a(B,2),u4=e(X),V=e(u4),U=e(V,!0);u(V);var $=a(V,2),y4=e($);u($);var P=a($,2);y(P,21,()=>n(i).implementation.features.list,b,(z,m)=>{var c=m3(),h=e(c,!0);u(c),d(()=>s(h,n(m))),t(z,c)}),u(P),u(u4),u(X),u(_);var L=a(_,2),J=e(L),k=e(J,!0);u(J);var K=a(J,2),b4=e(K),R=e(b4),P4=e(R);u(R);var N4=a(R,2),e4=e(N4);r0(e4,()=>"cambridge",()=>n(i).dictionaries.cambridge.name,()=>n(i).dictionaries.cambridge.description,()=>"https://dictionary.cambridge.org");var n4=a(e4,2);r0(n4,()=>"opendict",()=>n(i).dictionaries.opendict.name,()=>n(i).dictionaries.opendict.description,()=>"https://open-dict-data.github.io"),u(N4),u(b4),u(K),u(L);var T=a(L,2),a4=e(T),i4=e(a4,!0);u(a4);var x4=a(a4,2),A4=e(x4),c4=e(A4),Y=e(c4);u(c4);var D4=a(c4,2),F4=e(D4);Bu(F4,()=>n(i).sources.englishPhonology.name,()=>n(i).sources.englishPhonology.description,()=>"https://en.wikipedia.org/wiki/English_phonology");var s4=a(F4,2);Bu(s4,()=>n(i).sources.internationalPhoneticAlphabet.name,()=>n(i).sources.internationalPhoneticAlphabet.description,()=>"https://www.internationalphoneticalphabet.org/");var O4=a(s4,2);Bu(O4,()=>n(i).sources.americanIpaChart.name,()=>n(i).sources.americanIpaChart.description,()=>"https://americanipachart.com/"),u(D4),u(A4),u(x4),u(T);var z4=a(T,2),j4=e(z4),p0=e(j4,!0);u(j4);var xu=a(j4,2),Au=e(xu),M4=e(Au),c0=e(M4);u(M4);var Fu=a(M4,2),Cu=e(Fu);fu(Cu,()=>n(i).dialects.generalAmerican.name,()=>n(i).dialects.generalAmerican.description,()=>1);var ku=a(Cu,2);fu(ku,()=>n(i).dialects.standardEnglish.name,()=>n(i).dialects.standardEnglish.description,()=>2);var l0=a(ku,2);fu(l0,()=>n(i).dialects.localDialect.name,()=>n(i).dialects.localDialect.description,()=>3),u(Fu),u(Au),u(xu),u(z4);var G4=a(z4,2),Q4=e(G4),g0=e(Q4,!0);u(Q4);var Ku=a(Q4,2),$u=e(Ku),Pu=e($u);y(Pu,21,()=>f,b,(z,m)=>{var c=v3(),h=e(c),j=e(h),Z=e(j),W=e(Z,!0);u(Z),u(j),u(h),u(c),d(()=>s(W,n(m))),t(z,c)}),u(Pu),u($u),u(Ku),u(G4);var U4=a(G4,2),J4=e(U4),m0=e(J4,!0);u(J4);var zu=a(J4,2),ju=e(zu),Y4=e(ju),v0=e(Y4);u(Y4);var Su=a(Y4,2),Hu=e(Su),Z4=e(Hu),Iu=e(Z4),X4=e(Iu),h0=e(X4,!0);u(X4);var uu=a(X4),E0=e(uu,!0);u(uu);var Nu=a(uu),y0=e(Nu,!0);u(Nu),u(Iu),u(Z4);var Ou=a(Z4);y(Ou,21,()=>o3,b,(z,m)=>{var c=b3(),h=e(c);y(h,21,()=>n(m).sources,b,(_4,q)=>{var M=E3();y(M,21,()=>n(q).sounds,b,(t4,g4)=>{var r4=h3();let C4;var m4=e(r4,!0);u(r4),d(B4=>{C4=S(r4,"",C4,B4),s(m4,n(g4))},[()=>({"background-color":`#${L4[n(q).dictionaryKey]}`,color:"#000000",padding:"6px 12px","margin-bottom":"6px","font-size":"1.2rem","font-weight":"normal"})]),t(t4,r4)}),u(M),t(_4,M)}),u(h);var j=a(h),Z=e(j);S(Z,"",{},{"font-size":"1.5rem",display:"inline-block",padding:"4px 0"});var W=e(Z,!0);u(Z),u(j);var l4=a(j);y(l4,21,()=>n(m).examples,b,(_4,q,M)=>{var t4=y3(),g4=e(t4);{var r4=m4=>{var B4=V4(",");t(m4,B4)};o4(g4,m4=>{M>0&&m4(r4)})}var C4=a(g4,2);d3(C4,()=>n(q)),u(t4),t(_4,t4)}),u(l4),u(c),d(()=>s(W,n(m).mapping)),t(z,c)}),u(Ou),u(Hu),u(Su),u(ju),u(zu),u(U4);var Vu=a(U4,2);r3(Vu,{get locale(){return n(p)}});var Lu=a(Vu,2),eu=e(Lu),b0=e(eu,!0);u(eu);var Ru=a(eu,2),Tu=e(Ru),nu=e(Tu),D0=e(nu);u(nu);var au=a(nu,2),iu=e(au),_0=e(iu,!0);u(iu);var Wu=a(iu,2),B0=e(Wu,!0);u(Wu),u(au);var su=a(au,2),tu=e(su),f0=e(tu,!0);u(tu);var ru=a(tu,2),w0=e(ru,!0);u(ru);var ou=a(ru,2),qu=e(ou),du=e(qu),Mu=e(du),pu=e(Mu),x0=e(pu,!0);u(pu);var cu=a(pu),A0=e(cu,!0);u(cu);var Gu=a(cu),F0=e(Gu,!0);u(Gu),u(Mu),u(du);var Qu=a(du);y(Qu,21,()=>n(i).nuances.interrogatives.examples,b,(z,m)=>{var c=D3(),h=e(c),j=e(h),Z=e(j,!0);u(j),u(h);var W=a(h),l4=e(W),_4=e(l4,!0);u(l4),u(W);var q=a(W),M=e(q),t4=e(M,!0);u(M),u(q),u(c),d(()=>{s(Z,n(m).before),s(_4,n(m).after.preferred),s(t4,n(m).after.expected)}),t(z,c)}),u(Qu),u(qu),u(ou);var Uu=a(ou,2),Ju=e(Uu),C0=a(e(Ju));u(Ju),u(Uu),u(su);var Yu=a(su,2),lu=e(Yu),k0=e(lu,!0);u(lu);var gu=a(lu,2),K0=e(gu,!0);u(gu);var Zu=a(gu,2),Xu=e(Zu),mu=e(Xu),vu=e(mu),hu=e(vu),$0=e(hu,!0);u(hu);var u0=a(hu),P0=e(u0,!0);u(u0),u(vu);var e0=a(vu),Eu=e(e0),z0=e(Eu,!0);u(Eu);var yu=a(Eu),j0=e(yu,!0);u(yu);var bu=a(yu),S0=e(bu,!0);u(bu);var Du=a(bu),H0=e(Du,!0);u(Du);var n0=a(Du),I0=e(n0,!0);u(n0),u(e0),u(mu);var a0=a(mu);y(a0,21,()=>n(i).nuances.irregularVerbs.examples,b,(z,m)=>{var c=B3(),h=e(c),j=e(h),Z=e(j,!0);u(j),u(h);var W=a(h),l4=e(W),_4=e(l4,!0);u(l4),u(W);var q=a(W),M=e(q),t4=e(M,!0);u(M),u(q);var g4=a(q),r4=e(g4),C4=e(r4,!0);u(r4),u(g4);var m4=a(g4),B4=e(m4),N0=e(B4,!0);u(B4);var O0=a(B4,2);{var V0=_u=>{var i0=_3();d(()=>R4(i0,"title",n(m).newEnglish.note)),t(_u,i0)};o4(O0,_u=>{n(m).newEnglish.past==="sit"&&n(m).newEnglish.note&&_u(V0)})}u(m4),u(c),d(()=>{s(Z,n(m).english.common),s(_4,n(m).english.past),s(t4,n(m).english.pastParticiple),s(C4,n(m).newEnglish.common),s(N0,n(m).newEnglish.past)}),t(z,c)}),u(a0),u(Xu),u(Zu),u(Yu),u(Tu),u(Ru),u(Lu),u(D),u(g),d(()=>{s(v,n(i).title),s(G,n(i).problem.title),s(d4,n(i).problem.description),s(N,`${n(i).problem.difficulties.description??""}:`),s(k4,n(i).idea.title),s(T4,n(i).idea.description[1]),s(W4,n(i).idea.description[2]),s(q4,n(i).idea.description[3]),s(O,n(i).implementation.title),s(U,n(i).implementation.description),s(y4,`${n(i).implementation.features.description??""}:`),s(k,n(i).dictionaries.title),s(P4,`${n(i).dictionaries.description??""}:`),s(i4,n(i).sources.title),s(Y,`${n(i).sources.description??""}:`),s(p0,n(i).dialects.title),s(c0,`${n(i).dialects.description??""}:`),s(g0,n(i).alphabet.title),s(m0,n(i).rules.title),s(v0,`${n(i).rules.description??""}:`),s(h0,n(i).rules.phoneticSounds),s(E0,n(i).rules.newEnglishLetter),s(y0,n(i).rules.exampleWords),s(b0,n(i).nuances.title),s(D0,`${n(i).nuances.description??""}:`),s(_0,n(i).nuances.pronoun.title),s(B0,n(i).nuances.pronoun.description),s(f0,n(i).nuances.interrogatives.title),s(w0,n(i).nuances.interrogatives.description),s(x0,n(i).nuances.interrogatives.tableHeaders.original),s(A0,n(i).nuances.interrogatives.tableHeaders.preferred),s(F0,n(i).nuances.interrogatives.tableHeaders.expected),s(C0,` ${n(i).nuances.interrogatives.note??""}`),s(k0,n(i).nuances.irregularVerbs.title),s(K0,n(i).nuances.irregularVerbs.description),s($0,n(i).nuances.irregularVerbs.tableHeaders.english),s(P0,n(i).nuances.irregularVerbs.tableHeaders.newEnglish),s(z0,n(i).nuances.irregularVerbs.tableHeaders.common),s(j0,n(i).nuances.irregularVerbs.tableHeaders.past),s(S0,n(i).nuances.irregularVerbs.tableHeaders.pastParticiple),s(H0,n(i).nuances.irregularVerbs.tableHeaders.common),s(I0,n(i).nuances.irregularVerbs.tableHeaders.past)}),t(A,g),d0()}export{P3 as component};
