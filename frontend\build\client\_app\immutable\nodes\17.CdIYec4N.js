import{g as tu}from"../chunks/CGZ87yZq.js";import"../chunks/Bzak7iHL.js";import{p as ru,c as iu,f as w,a as xu,s as o,ax as r,g as u,d as e,b as B,u as b,av as U,r as a,t as A,aw as ku,h as zu,$ as Gu}from"../chunks/RHWQbow4.js";import{e as Nu,s as v,d as yu}from"../chunks/BlWcudmi.js";import{i as J}from"../chunks/CtoItwj4.js";import{s as G}from"../chunks/BdpLTtcP.js";import{s as Ru}from"../chunks/Cxg-bych.js";import{s as X}from"../chunks/CaC9IHEK.js";import{g as Wu}from"../chunks/CKnuo8tw.js";import{c as au,r as qu}from"../chunks/CVTn1FV4.js";import{f as Ju}from"../chunks/CL12WlkV.js";import{M as Fu,e as Hu,a as Xu,f as Zu}from"../chunks/iI8NM7bJ.js";import"../chunks/BiLRrsV0.js";import"../chunks/B0MzmgHo.js";const Yu=async({fetch:h,url:t})=>{const{fetcher:g}=tu();return{me:await g.user.me.get({fetch:h,ctx:{url:t}})}},w4=Object.freeze(Object.defineProperty({__proto__:null,load:Yu},Symbol.toStringTag,{value:"Module"}));var Ku=w('<div class="alert alert-success mb-3"> </div>'),Qu=w('<div class="alert alert-danger mb-3"> </div>'),Vu=w("<!> <!> <form><!> <!></form>",1);function $u(h,t){ru(t,!0);const g={en:{editProfile:"Edit Profile",name:{label:"Name",placeholder:"Enter your name"},description:{label:"Description (optional)",placeholder:"Tell us about yourself"},saveChanges:"Save Changes",cancel:"Cancel",saving:"Saving...",nameRequired:"Name is required",failedToUpdateProfile:"Failed to update profile",profileUpdatedSuccessfully:"Profile updated successfully",errorOccurred:"An error occurred while updating profile"},ru:{editProfile:"Редактировать профиль",name:{label:"Имя",placeholder:"Введите ваше имя"},description:{label:"Описание (необязательно)",placeholder:"Расскажите о себе"},saveChanges:"Сохранить изменения",cancel:"Отменить",saving:"Сохранение...",nameRequired:"Имя обязательно",failedToUpdateProfile:"Не удалось обновить профиль",profileUpdatedSuccessfully:"Профиль обновлен успешно",errorOccurred:"Произошла ошибка при обновлении профиля"}},{fetcher:x}=tu(),i=b(()=>g[t.locale]);let n=U(""),l=U(!1),d=U(!1),m=b(()=>{var s;return((s=t.userData)==null?void 0:s.name)||[]}),y=b(()=>{var s;return((s=t.userData)==null?void 0:s.description)||[]});const N=async()=>{if(!u(m).some(s=>s.value.trim().length)){r(n,u(i).nameRequired,!0);return}r(l,!0),r(n,"");try{await x.user.patch({id:t.userData.id,name:u(m),description:u(y)}),r(d,!0),setTimeout(()=>{O(),t.onProfileUpdated()},1500)}catch(s){r(n,s instanceof Error?s.message:u(i).errorOccurred,!0),console.error(s)}finally{r(l,!1)}},O=()=>{r(n,""),r(d,!1),t.onHide()};{let s=b(()=>u(l)?u(i).saving:u(i).saveChanges),M=b(()=>!u(m).some(C=>C.value.trim().length)||u(l));Fu(h,{get show(){return t.show},get title(){return u(i).editProfile},onClose:O,onSubmit:N,get submitText(){return u(s)},get cancelText(){return u(i).cancel},get submitDisabled(){return u(M)},get cancelDisabled(){return u(l)},get isSubmitting(){return u(l)},children:(C,R)=>{var T=Vu(),j=xu(T);{var W=c=>{var E=Ku(),S=e(E,!0);a(E),A(()=>v(S,u(i).profileUpdatedSuccessfully)),B(c,E)};J(j,c=>{u(d)&&c(W)})}var I=o(j,2);{var k=c=>{var E=Qu(),S=e(E,!0);a(E),A(()=>v(S,u(n))),B(c,E)};J(I,c=>{u(n)&&c(k)})}var L=o(I,2),F=e(L);Hu(F,{id:"profileName",get label(){return u(i).name.label},get placeholder(){return u(i).name.placeholder},required:!0,get locale(){return t.locale},get value(){return u(m)},set value(c){r(m,c)}});var P=o(F,2);Xu(P,{id:"profileDescription",get label(){return u(i).description.label},get placeholder(){return u(i).description.placeholder},rows:4,get locale(){return t.locale},get value(){return u(y)},set value(c){r(y,c)}}),a(L),Nu("submit",L,c=>{c.preventDefault(),N()}),B(C,T)},$$slots:{default:!0}})}iu()}const u4=(h,t,g,x,i)=>{const n=h.target.files;if(r(t,""),!n||n.length===0){r(g,null),r(x,null);return}const l=n[0];if(!au.ALLOWED_IMAGE_FILE_TYPES.includes(l.type)){r(t,u(i).invalidFileTypeError,!0),r(g,null),r(x,null);return}if(l.size>au.MAX_IMAGE_FILE_SIZE){r(t,u(i).fileTooLarge,!0),r(g,null),r(x,null);return}r(g,l,!0);const d=URL.createObjectURL(l);return r(x,d,!0),()=>{URL.revokeObjectURL(d)}};var e4=w('<div class="alert alert-success mb-3"> </div>'),a4=w('<div class="alert alert-danger mb-3"> </div>'),t4=w('<div class="mt-3 text-center"><img alt="Preview" class="img-thumbnail"/></div>'),r4=w('<!> <!> <form><div class="mb-3"><label for="imageInput" class="form-label"> </label> <input id="imageInput" type="file" class="form-control" accept=".jpg,.jpeg,.png,.webp"/> <p class="form-text text-muted"> </p> <!></div></form>',1);function i4(h,t){ru(t,!0);const g=au.MAX_IMAGE_FILE_SIZE/(1024*1024),x={en:{uploadImage:"Upload Profile Image",upload:"Upload",cancel:"Cancel",uploading:"Uploading...",imageUploadedSuccessfully:"Image uploaded successfully!",pleaseSelectImage:"Please select an image to upload",invalidFileTypeError:"Invalid file type. Please upload a JPG, PNG, or WebP image.",fileTooLarge:`File is too large. Maximum size is ${g}MB.`,failedToUploadImage:"Failed to upload image",errorOccurred:"An error occurred while uploading the image",uploadImageMaxSize:`Upload an image (JPG, PNG, WebP), max ${g}MB.`},ru:{uploadImage:"Загрузить изображение профиля",upload:"Загрузить",cancel:"Отменить",uploading:"Загрузка...",imageUploadedSuccessfully:"Изображение загружено успешно!",pleaseSelectImage:"Пожалуйста, выберите изображение для загрузки",invalidFileTypeError:"Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",fileTooLarge:`Файл слишком большой. Максимальный размер - ${g}MB.`,failedToUploadImage:"Не удалось загрузить изображение",errorOccurred:"Произошла ошибка при загрузке изображения",uploadImageMaxSize:`Загрузите изображение (JPG, PNG, WebP), максимальный размер - ${g}MB.`}},i=b(()=>x[t.locale]);let n=U(null),l=U(null),d=U(""),m=U(!1),y=U(!1);const N=async()=>{if(!u(n)){r(d,u(i).pleaseSelectImage,!0);return}r(m,!0),r(d,"");try{const s=new FormData;s.append("image",u(n));const M=await Zu(`/api/user/${t.userId}/image`,{method:"PUT",body:s});if(!M.ok){const R=await M.json();throw new Error(R.message||u(i).failedToUploadImage)}r(y,!0),t.onImageUploaded();const C=document.getElementById("imageInput");C&&(C.files=null),setTimeout(()=>{O()},1500)}catch(s){r(d,s instanceof Error?s.message:u(i).errorOccurred,!0),console.error(s)}finally{r(m,!1)}},O=()=>{r(n,null),r(l,null),r(d,""),r(y,!1),t.onHide()};{let s=b(()=>u(m)?u(i).uploading:u(i).upload),M=b(()=>!u(n)||u(m));Fu(h,{get show(){return t.show},get title(){return u(i).uploadImage},onClose:O,onSubmit:N,get submitText(){return u(s)},get cancelText(){return u(i).cancel},get submitDisabled(){return u(M)},get cancelDisabled(){return u(m)},get isSubmitting(){return u(m)},size:"lg",children:(C,R)=>{var T=r4(),j=xu(T);{var W=D=>{var p=e4(),z=e(p,!0);a(p),A(()=>v(z,u(i).imageUploadedSuccessfully)),B(D,p)};J(j,D=>{u(y)&&D(W)})}var I=o(j,2);{var k=D=>{var p=a4(),z=e(p,!0);a(p),A(()=>v(z,u(d))),B(D,p)};J(I,D=>{u(d)&&D(k)})}var L=o(I,2),F=e(L),P=e(F),c=e(P,!0);a(P);var E=o(P,2);E.__change=[u4,d,n,l,i];var S=o(E,2),K=e(S,!0);a(S);var Z=o(S,2);{var H=D=>{var p=t4(),z=e(p);X(z,"",{},{"max-height":"200px"}),a(p),A(()=>G(z,"src",u(l))),B(D,p)};J(Z,D=>{u(l)&&D(H)})}a(F),a(L),A(()=>{v(c,u(i).pleaseSelectImage),E.disabled=u(m),v(K,u(i).uploadImageMaxSize)}),B(C,T)},$$slots:{default:!0}})}iu()}yu(["change"]);const l4=async(h,t)=>{try{await t.auth.signOut.get()}catch(g){console.error("Error during logout:",g)}finally{qu(),Wu("/")}};var o4=(h,t)=>r(t,!0),s4=w("<span> </span>"),d4=(h,t)=>r(t,!0),n4=w('<p class="mb-0"> </p>'),c4=(h,t)=>r(t,!0),v4=w('<div class="text-center text-muted py-4"><i class="bi bi-file-earmark-text fs-1 mb-2"></i> <p> </p> <button class="btn btn-sm btn-primary"><i class="bi bi-plus-circle me-1"></i> </button></div>'),m4=w('<div class="container-fluid py-4"><div class="row g-4"><div class="col-lg-3"><div class="card border-0 shadow-sm h-100"><div class="text-center p-4"><div class="position-relative mx-auto mb-3"><div class="bg-light rounded-circle overflow-hidden border" role="img"><div class="position-relative w-100 h-100"><img/></div></div> <button class="position-absolute bottom-0 end-0 bg-primary text-white rounded-circle p-1 border-0"><i class="bi bi-plus"></i></button></div> <h4 class="fw-bold mb-1"> </h4> <!> <div class="d-grid gap-2 mt-3"><button class="btn btn-primary"><i class="bi bi-gear me-2"></i> </button> <button class="btn btn-outline-danger"><i class="bi bi-box-arrow-right me-2"></i> </button></div></div> <div class="card-footer bg-light border-top p-3"><div class="d-flex align-items-center mb-2"><i class="bi bi-envelope text-muted me-2"></i> <div class="text-truncate"><small class="text-muted"> </small></div></div> <div class="d-flex align-items-center"><i class="bi bi-calendar3 text-muted me-2"></i> <div><small class="text-muted"> </small></div></div></div></div></div> <div class="col-lg-9"><div class="card border-0 shadow-sm mb-4"><div class="card-header bg-transparent border-bottom-0 pb-0"><h5 class="fw-bold"> </h5></div> <div class="card-body"><div class="row g-4"><div class="col-md-4"><div class="border rounded p-3 text-center h-100"><div class="mb-2"><i class="bi bi-calendar-check fs-3 text-primary"></i></div> <h2 class="mb-0 fw-bold"> </h2> <p class="text-muted mb-0"> </p></div></div></div></div></div> <div class="card border-0 shadow-sm mb-4"><div class="card-header d-flex justify-content-between align-items-center bg-transparent"><h5 class="fw-bold mb-0"> </h5></div> <div class="card-body"><!></div></div></div></div> <!> <!></div>');function I4(h,t){ru(t,!0);const g={en:{_page:{title:"Profile — Commune"},loading:"Loading...",loadingProfile:"Loading your profile...",uploadImage:"Upload profile image",admin:"Administrator",user:"User",editProfile:"Edit Profile",signOut:"Sign Out",joined:"Joined",accountSummary:"Account Summary",accountType:{title:"Account Type",values:{admin:"Administrator",moderator:"Moderator",user:"User"}},daysAsMember:"Days as member",aboutMe:"About Me",noDescription:"No description available yet. Add one to tell others about yourself.",addDescription:"Add Description",dateFormatLocale:"en-US"},ru:{_page:{title:"Профиль — Коммуна"},loading:"Загрузка...",loadingProfile:"Загрузка вашего профиля...",uploadImage:"Загрузить изображение профиля",admin:"Администратор",user:"Пользователь",editProfile:"Редактировать профиль",signOut:"Выйти",joined:"Присоединился",accountSummary:"Информация об аккаунте",accountType:{title:"Тип аккаунта",values:{admin:"Администратор",moderator:"Модератор",user:"Пользователь"}},daysAsMember:"Дней в качестве участника",aboutMe:"Обо мне",noDescription:"Нет описания. Добавьте описание, чтобы рассказать другим о себе.",addDescription:"Добавить описание",dateFormatLocale:"ru-RU"}},{fetcher:x}=tu(),i=b(()=>t.data.locale),n=b(()=>t.data.getAppropriateLocalization),l=b(()=>g[u(i)]);let d=ku(t.data.me),m=U(!1),y=U(!1);const N=b(()=>u(n)(d.name)),O=b(()=>u(n)(d.description)),s=b(()=>new Date(d.createdAt));function M(){window.location.reload()}var C=m4();zu(f=>{A(()=>Gu.title=u(l)._page.title)});var R=e(C),T=e(R),j=e(T),W=e(j),I=e(W);X(I,"",{},{width:"120px",height:"120px"});var k=e(I);X(k,"",{},{width:"100%",height:"100%"});var L=e(k),F=e(L);G(F,"width",120),G(F,"height",120),X(F,"",{},{width:"100%",height:"100%","object-fit":"cover"}),a(L),a(k);var P=o(k,2);P.__click=[o4,y],X(P,"",{},{width:"30px",height:"30px",display:"flex","align-items":"center","justify-content":"center"}),a(I);var c=o(I,2),E=e(c,!0);a(c);var S=o(c,2);{var K=f=>{var _=s4();Ru(_,1,"badge bg-danger mb-3");var q=e(_,!0);a(_),A(()=>v(q,u(l).admin)),B(f,_)};J(S,f=>{d.role==="admin"&&f(K)})}var Z=o(S,2),H=e(Z);H.__click=[d4,m];var D=o(e(H));a(H);var p=o(H,2);p.__click=[l4,x];var z=o(e(p));a(p),a(Z),a(W);var lu=o(W,2),Q=e(lu),ou=o(e(Q),2),su=e(ou),Au=e(su,!0);a(su),a(ou),a(Q);var du=o(Q,2),nu=o(e(du),2),cu=e(nu),Bu=e(cu);a(cu),a(nu),a(du),a(lu),a(j),a(T);var vu=o(T,2),V=e(vu),$=e(V),mu=e($),wu=e(mu,!0);a(mu),a($);var gu=o($,2),pu=e(gu),fu=e(pu),bu=e(fu),uu=o(e(bu),2),Iu=e(uu,!0);a(uu);var _u=o(uu,2),Pu=e(_u,!0);a(_u),a(bu),a(fu),a(pu),a(gu),a(V);var hu=o(V,2),eu=e(hu),Eu=e(eu),Su=e(Eu,!0);a(Eu),a(eu);var Du=o(eu,2),Uu=e(Du);{var Mu=f=>{var _=n4(),q=e(_,!0);a(_),A(()=>v(q,u(O))),B(f,_)},Tu=f=>{var _=v4(),q=o(e(_),2),Ou=e(q,!0);a(q);var Y=o(q,2);Y.__click=[c4,m];var ju=o(e(Y));a(Y),a(_),A(()=>{v(Ou,u(l).noDescription),G(Y,"aria-label",u(l).addDescription),v(ju,` ${u(l).addDescription??""}`)}),B(f,_)};J(Uu,f=>{u(O)?f(Mu):f(Tu,!1)})}a(Du),a(hu),a(vu),a(R);var Cu=o(R,2);{let f=b(()=>d||null);$u(Cu,{get locale(){return u(i)},get show(){return u(m)},onHide:()=>r(m,!1),get userData(){return u(f)},onProfileUpdated:M})}var Lu=o(Cu,2);i4(Lu,{get locale(){return u(i)},get show(){return u(y)},onHide:()=>r(y,!1),get userId(){return d.id},onImageUploaded:M}),a(C),A((f,_)=>{G(F,"src",d.image?`/images/${d.image}`:"/images/default-avatar.png"),G(F,"alt",`${u(N)}'s avatar`),G(P,"title",u(l).uploadImage),G(P,"aria-label",u(l).uploadImage),v(E,u(N)),v(D,` ${u(l).editProfile??""}`),v(z,` ${u(l).signOut??""}`),v(Au,d.email),v(Bu,`${u(l).joined??""} ${f??""}`),v(wu,u(l).accountSummary),v(Iu,_),v(Pu,u(l).daysAsMember),v(Su,u(l).aboutMe)},[()=>Ju(u(s),u(i)),()=>Math.floor((new Date().getTime()-u(s).getTime())/(1e3*60*60*24))]),B(h,C),iu()}yu(["click"]);export{I4 as component,w4 as universal};
