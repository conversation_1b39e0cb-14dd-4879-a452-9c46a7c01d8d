import{e as Ve,c as ze}from"../chunks/CVTn1FV4.js";import{g as He}from"../chunks/CGZ87yZq.js";import"../chunks/Bzak7iHL.js";import{p as We,av as x,aw as Ce,o as Pe,g as e,ax as s,f as g,h as Xe,b,c as Ye,u as W,t as y,s as i,d as t,$ as $e,r as a,a as ge,ay as ea,az as aa}from"../chunks/RHWQbow4.js";import{d as ta,s as d}from"../chunks/BlWcudmi.js";import{i as A}from"../chunks/CtoItwj4.js";import{e as Ie,i as ra}from"../chunks/Dnfvvefi.js";import{s as pe,r as ua}from"../chunks/BdpLTtcP.js";import{s as sa}from"../chunks/Cxg-bych.js";import{s as Me}from"../chunks/CaC9IHEK.js";import{a as ia,c as oa}from"../chunks/iI8NM7bJ.js";import{b as na}from"../chunks/B5DcI8qy.js";import"../chunks/B0MzmgHo.js";import{g as ye}from"../chunks/DGxS2cwR.js";import"../chunks/BiLRrsV0.js";const da=async({fetch:h,params:c,url:E})=>{const{fetcher:F}=He(),[B,[k],S]=await Promise.all([F.user.me.get({fetch:h,ctx:{url:E}}),F.user.list.get({ids:[c.id]},{fetch:h,ctx:{url:E}}),F.rating.feedback.list.get({userId:c.id},{fetch:h,ctx:{url:E}})]);if(!k)throw Ve(404,"User not found");return{me:B,user:k,feedbacks:S,isHasMoreFeedbacks:S.length===ze.PAGE_SIZE}},Ka=Object.freeze(Object.defineProperty({__proto__:null,load:da},Symbol.toStringTag,{value:"Module"})),la=(h,c,E,F,B,k,S)=>{s(c,!0),s(E,null),s(F,!1),s(B,[],!0),s(k,null),s(S,!1)},va=async(h,c,E,F,B,k,S,X,n,Z,K,L)=>{if(e(c)===null){s(E,e(F).ratingRequired,!0);return}if(e(B).length===0||!e(B).some(P=>P.value.trim())){s(E,e(F).feedbackRequired,!0);return}s(k,!0),s(E,null);try{await S.rating.feedback.post({sourceUserId:e(X).id,targetUserId:e(n).id,value:e(c),isAnonymous:e(Z),text:e(B)}),s(K,!0),setTimeout(()=>{L()},1500)}catch(P){s(E,P instanceof Error?P.message:e(F).errorSubmitting,!0),console.error(P)}finally{s(k,!1)}};var ca=g('<div class="alert alert-danger" role="alert"> </div>'),_a=g('<div><button class="btn btn-primary btn-sm"><i class="bi bi-chat-dots me-1"></i> </button></div>'),ma=g('<div class="text-center py-5"><p class="text-muted"> </p></div>'),ba=g('<img class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;"/>'),fa=g('<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white" style="width: 48px; height: 48px;"><i class="bi bi-person-fill"></i></div>'),ga=g('<div class="card mb-3 shadow-sm svelte-s9if29"><div class="card-body"><div class="d-flex align-items-start"><div class="me-3"><!></div> <div class="flex-grow-1"><div class="d-flex justify-content-between align-items-start mb-2"><div><h6 class="mb-1"> </h6></div> <div class="d-flex align-items-center gap-2"><span class="badge fs-6"> </span> <span> </span></div></div> <p class="mb-0 text-muted"> </p></div></div></div></div>'),pa=g('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden"> </span></div> <p class="text-muted mt-2 mb-0"> </p>',1),ya=g('<div class="text-center py-3"><!></div>'),ha=g('<div class="alert alert-danger" role="alert"> </div>'),ka=g('<div class="d-flex justify-content-between align-items-center mb-4"><div><h2 class="mb-1"> </h2> <p class="text-muted mb-0"> </p></div> <!></div> <!> <!> <!>',1),xa=(h,c)=>h.target===h.currentTarget&&c(),Ea=(h,c)=>h.key==="Enter"&&h.target===h.currentTarget&&c(),Fa=g('<button type="button"></button>'),wa=g('<div class="text-muted small"> <span> </span></div>'),Aa=g('<div class="alert alert-danger" role="alert"> </div>'),Da=g('<div class="alert alert-success" role="alert"> </div>'),Ba=g('<span class="spinner-border spinner-border-sm me-2" role="status"></span> ',1),Ca=g('<div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);" role="dialog" aria-modal="true"><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"> </h5> <button type="button" class="btn-close" aria-label="Close"></button></div> <div class="modal-body"><div class="mb-3"><div class="form-label"> </div> <div class="rating-container svelte-s9if29"><div class="rating-stars d-flex gap-1 mb-2 svelte-s9if29"></div></div> <!></div> <div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="anonymousCheck"/> <label class="form-check-label" for="anonymousCheck"> </label></div></div> <!> <!> <!></div> <div class="modal-footer"><button type="button" class="btn btn-secondary"> </button> <button type="button" class="btn btn-primary"><!></button></div></div></div></div>'),Ma=g('<div class="container py-4"><div class="responsive-container"><!></div> <!></div>');function Ja(h,c){We(c,!0);const E={en:{_page:{title:"Feedback — Commune"},userNotFound:"User not found",feedbackHistory:"Feedback History",sendFeedback:"Send Feedback",feedbackModalTitle:"Send Feedback",rating:"Rating",anonymous:"Anonymous",sendAnonymously:"Send anonymously",feedback:"Feedback",feedbackPlaceholder:"Enter your feedback...",cancel:"Cancel",submit:"Submit",submitting:"Submitting...",success:"Feedback sent successfully",errorSubmitting:"Error submitting feedback",noFeedbacks:"No feedback found",loadingMore:"Loading more...",errorOccurred:"An error occurred",errorFetchingFeedbacks:"Error fetching feedbacks",ratingRequired:"Rating is required",feedbackRequired:"Feedback text is required"},ru:{_page:{title:"Отзывы — Коммуна"},userNotFound:"Пользователь не найден",feedbackHistory:"История отзывов",sendFeedback:"Отправить отзыв",feedbackModalTitle:"Отправить отзыв",rating:"Оценка",anonymous:"Анонимно",sendAnonymously:"Отправить анонимно",feedback:"Отзыв",feedbackPlaceholder:"Введите ваш отзыв...",cancel:"Отмена",submit:"Отправить",submitting:"Отправка...",success:"Отзыв успешно отправлен",errorSubmitting:"Ошибка при отправке отзыва",noFeedbacks:"Отзывы не найдены",loadingMore:"Загрузка...",errorOccurred:"Произошла ошибка",errorFetchingFeedbacks:"Ошибка загрузки отзывов",ratingRequired:"Оценка обязательна",feedbackRequired:"Текст отзыва обязателен"}},{fetcher:F}=He(),B=W(()=>c.data.me),k=W(()=>c.data.user),S=W(()=>c.data.locale),X=W(()=>c.data.getAppropriateLocalization),n=W(()=>E[e(S)]);let Z=x(Ce(c.data.feedbacks)),K=x(null),L=x(!1),P=x(1),le=x(Ce(c.data.isHasMoreFeedbacks)),ve=x(null),ce=x(!1),C=x(null),Y=x(!1),T=x(Ce([])),J=x(!1),$=x(null),_e=x(!1);async function Le(){if(!(e(L)||!e(le))){s(L,!0),s(K,null);try{const r=e(P)+1,_=await F.rating.feedback.list.get({pagination:{page:r},userId:e(k).id});s(Z,[...e(Z),..._],!0),s(P,r),s(le,_.length===ze.PAGE_SIZE)}catch(r){s(K,r instanceof Error?r.message:e(n).errorOccurred,!0),console.error(r)}finally{s(L,!1)}}}Pe(()=>{if(!e(ve))return;const r=new IntersectionObserver(_=>{_[0].isIntersecting&&e(le)&&!e(L)&&Le()},{threshold:.1});return r.observe(e(ve)),()=>{r.disconnect()}}),Pe(()=>{if(!e(ce))return;const r=_=>{_.key==="Escape"&&ee()};return document.addEventListener("keydown",r),()=>{document.removeEventListener("keydown",r)}});const Se=W(()=>e(X)(e(k).name)),ee=()=>{s(ce,!1),s(C,null),s(Y,!1),s(T,[],!0),s($,null),s(_e,!1)},Re=r=>r?e(X)(r.name):e(n).anonymous,qe=r=>"★".repeat(Math.floor(r/2))+(r%2===1?"☆":"");function Te(){window.location.reload()}var he=Ma();Xe(r=>{y(()=>$e.title=`${e(Se)??""} ${e(n)._page.title??""}`)});var ke=t(he),je=t(ke);{var Ne=r=>{var _=ca(),I=t(_,!0);a(_),y(()=>d(I,e(n).userNotFound)),b(r,_)},Oe=r=>{var _=ka(),I=ge(_),Q=t(I),j=t(Q),ae=t(j,!0);a(j);var me=i(j,2),xe=t(me,!0);a(me),a(Q);var te=i(Q,2);{var re=l=>{var o=_a(),p=t(o);p.__click=[la,ce,C,Y,T,$,_e];var D=i(t(p));a(p),a(o),y(()=>{pe(p,"aria-label",e(n).sendFeedback),d(D,` ${e(n).sendFeedback??""}`)}),b(l,o)};A(te,l=>{e(B).id!==e(k).id&&l(re)})}a(I);var V=i(I,2);{var Ee=l=>{var o=ma(),p=t(o),D=t(p,!0);a(p),a(o),y(()=>d(D,e(n).noFeedbacks)),b(l,o)},ue=l=>{var o=ea(),p=ge(o);Ie(p,17,()=>e(Z),D=>D.id,(D,m)=>{var R=ga(),z=t(R),N=t(z),O=t(N),U=t(O);{var q=H=>{var M=ba();y(Be=>{pe(M,"src",`/images/${e(m).author.image}`),pe(M,"alt",Be)},[()=>Re(e(m).author)]),b(H,M)},Ae=H=>{var M=fa();b(H,M)};A(U,H=>{var M;(M=e(m).author)!=null&&M.image?H(q):H(Ae,!1)})}a(O);var G=i(O,2),oe=t(G),ne=t(oe),be=t(ne),u=t(be,!0);a(be),a(ne);var v=i(ne,2),f=t(v),w=t(f);a(f);var de=i(f,2),De=t(de,!0);a(de),a(v),a(oe);var fe=i(oe,2),Ze=t(fe,!0);a(fe),a(G),a(N),a(z),a(R),y((H,M,Be,Ke,Je,Qe)=>{d(u,H),Me(f,`color: ${M??""}; border: 1px solid ${Be??""};`),d(w,`${e(m).value??""}/10`),Me(de,`font-size: 1.2rem; color: ${Ke??""};`),d(De,Je),d(Ze,Qe)},[()=>Re(e(m).author),()=>ye(e(m).value),()=>ye(e(m).value),()=>ye(e(m).value),()=>qe(e(m).value),()=>e(X)(e(m).text)]),b(D,R)}),b(l,o)};A(V,l=>{e(Z).length===0?l(Ee):l(ue,!1)})}var se=i(V,2);{var Fe=l=>{var o=ya(),p=t(o);{var D=m=>{var R=pa(),z=ge(R),N=t(z),O=t(N,!0);a(N),a(z);var U=i(z,2),q=t(U,!0);a(U),y(()=>{d(O,e(n).loadingMore),d(q,e(n).loadingMore)}),b(m,R)};A(p,m=>{e(L)&&m(D)})}a(o),na(o,m=>s(ve,m),()=>e(ve)),b(l,o)};A(se,l=>{e(le)&&l(Fe)})}var we=i(se,2);{var ie=l=>{var o=ha(),p=t(o,!0);a(o),y(()=>d(p,e(K))),b(l,o)};A(we,l=>{e(K)&&l(ie)})}y(()=>{d(ae,e(Se)),d(xe,e(n).feedbackHistory)}),b(r,_)};A(je,r=>{e(k)?r(Oe,!1):r(Ne)})}a(ke);var Ue=i(ke,2);{var Ge=r=>{var _=Ca();_.__click=[xa,ee],_.__keydown=[Ea,ee];var I=t(_),Q=t(I),j=t(Q),ae=t(j),me=t(ae,!0);a(ae);var xe=i(ae,2);xe.__click=ee,a(j);var te=i(j,2),re=t(te),V=t(re),Ee=t(V,!0);a(V);var ue=i(V,2),se=t(ue);Ie(se,20,()=>Array(11),ra,(u,v,f)=>{var w=Fa();w.__click=()=>s(C,f,!0),pe(w,"aria-label",`Rate ${f} out of 10`),w.textContent=f,y(()=>{sa(w,1,`btn btn-outline-warning star-btn ${e(C)===f?"active":""}`,"svelte-s9if29"),w.disabled=e(J)}),b(u,w)}),a(se),a(ue);var Fe=i(ue,2);{var we=u=>{var v=wa(),f=t(v),w=i(f),de=t(w,!0);a(w),a(v),y((De,fe)=>{d(f,`Selected rating: ${e(C)??""}/10 `),Me(w,`font-size: 1.1rem; margin-left: 0.5rem; color: ${De??""};`),d(de,fe)},[()=>ye(e(C)),()=>qe(e(C))]),b(u,v)};A(Fe,u=>{e(C)!==null&&u(we)})}a(re);var ie=i(re,2),l=t(ie),o=t(l);ua(o);var p=i(o,2),D=t(p,!0);a(p),a(l),a(ie);var m=i(ie,2);ia(m,{get locale(){return e(S)},id:"feedbackText",get label(){return e(n).feedback},get placeholder(){return e(n).feedbackPlaceholder},rows:4,required:!0,get value(){return e(T)},set value(u){s(T,u,!0)}});var R=i(m,2);{var z=u=>{var v=Aa(),f=t(v,!0);a(v),y(()=>d(f,e($))),b(u,v)};A(R,u=>{e($)&&u(z)})}var N=i(R,2);{var O=u=>{var v=Da(),f=t(v,!0);a(v),y(()=>d(f,e(n).success)),b(u,v)};A(N,u=>{e(_e)&&u(O)})}a(te);var U=i(te,2),q=t(U);q.__click=ee;var Ae=t(q,!0);a(q);var G=i(q,2);G.__click=[va,C,$,n,T,J,F,B,k,Y,_e,Te];var oe=t(G);{var ne=u=>{var v=Ba(),f=i(ge(v));y(()=>d(f,` ${e(n).submitting??""}`)),b(u,v)},be=u=>{var v=aa();y(()=>d(v,e(n).submit)),b(u,v)};A(oe,u=>{e(J)?u(ne):u(be,!1)})}a(G),a(U),a(Q),a(I),a(_),y(u=>{d(me,e(n).feedbackModalTitle),d(Ee,e(n).rating),o.disabled=e(J),d(D,e(n).sendAnonymously),q.disabled=e(J),d(Ae,e(n).cancel),G.disabled=u},[()=>e(J)||e(C)===null||e(T).length===0||!e(T).some(u=>u.value.trim())]),oa(o,()=>e(Y),u=>s(Y,u)),b(r,_)};A(Ue,r=>{e(ce)&&r(Ge)})}a(he),b(h,he),Ye()}ta(["click","keydown"]);export{Ja as component,Ka as universal};
