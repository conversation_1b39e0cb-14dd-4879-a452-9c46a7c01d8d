import"../chunks/Bzak7iHL.js";import{p as ze,av as F,aw as de,o as Ve,g as e,ax as n,f as E,d as t,s,t as x,b as c,c as Ge,u as Z,r as a,ay as ve,a as pe,az as ce}from"../chunks/RHWQbow4.js";import{d as Je,e as Ke,s as d}from"../chunks/BlWcudmi.js";import{i as y}from"../chunks/CtoItwj4.js";import{s as $,a as me,r as ge}from"../chunks/BdpLTtcP.js";import{s as _e}from"../chunks/Cxg-bych.js";import{s as be}from"../chunks/CaC9IHEK.js";import{g as fe}from"../chunks/CKnuo8tw.js";import{p as he}from"../chunks/hBp8sf9T.js";import{L as xe,s as ye}from"../chunks/CVTn1FV4.js";import{g as Qe,H as We}from"../chunks/CGZ87yZq.js";const Xe=async(_,p,m,O)=>{if(_.preventDefault(),!!e(p)){n(m,"pending");try{const{isSent:w}=await O.auth.otp.post({email:e(p)},{skipInterceptor:!0});n(m,w?"sent":"sending-disabled-by-server",!0)}catch(w){console.error("Failed to send OTP:",w),n(m,"error")}}};var Ze=(_,p)=>p("login"),$e=(_,p)=>p("register"),eu=(_,p)=>n(p,_.target.value,!0),uu=E('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>'),ru=E('<span class="badge bg-success-subtle text-success px-2 py-1 rounded-pill"> </span>'),tu=E('<span class="badge bg-warning-subtle text-warning px-2 py-1 rounded-pill"> </span>'),au=(_,p)=>{const m=_.target.value.replace(/\D/g,"");m.length<=6&&n(p,m,!0)},iu=E('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>'),su=E('<span class="text-danger"> <br/> </span>'),nu=E('<span class="text-danger"> </span>'),ou=E('<div class="mt-4 p-3 border rounded bg-light"><h6 class="text-warning mb-3"><i class="bi bi-exclamation-triangle-fill me-2"></i> </h6> <p class="mb-3 text-muted"> </p> <div class="d-flex gap-2"><a target="_blank" rel="noopener noreferrer" class="btn btn-outline-primary btn-sm"><i class="bi bi-telegram me-1"></i> </a></div></div>'),lu=E('<div class="container min-vh-100 d-flex align-items-center justify-content-center"><div class="card shadow-lg border-0"><div class="card-header bg-white border-0 pt-4 pb-0"><div class="position-relative"><ul class="nav nav-tabs border-0 card-header-tabs"><li class="nav-item flex-grow-1 text-center"><button> </button></li> <li class="nav-item flex-grow-1 text-center"><button> </button></li></ul> <div class="position-absolute bottom-0 bg-primary"></div></div></div> <div class="card-body p-4"><form><div class="mb-3"><label for="email" class="form-label"> </label> <input type="email" autoComplete="email" class="form-control" id="email" placeholder="<EMAIL>" required/></div> <div class="mb-3"><button type="button" class="btn btn-outline-primary w-100"><!> </button></div> <div class="mb-3"><div class="d-flex justify-content-between align-items-center mb-2"><label for="otp" class="form-label mb-0"> </label> <!></div> <input id="otp" class="form-control" type="text" aria-describedby="otpHelp"/> <div id="otpHelp" class="form-text"> </div></div> <div class="d-grid gap-2"><button type="submit" class="btn btn-primary"><!> <!></button> <!></div></form> <!></div></div></div>');function yu(_,p){ze(p,!0);const{fetcher:m}=Qe(),O={en:{_page:{title:"Auth — Commune"},authType:{login:"Login",register:"Register"},login:"Log In",register:"Create Account",email:"Email",otp:{short:"OTP",long:"Email Verification Code",send:"Send OTP",enterThe6DigitCodeSentToYourEmail:"Enter the 6-digit code sent to your email.",sent:"OTP Sent",sendingDisabledByServer:"OTP sending is disabled by the server",placeholder:"6-digit code"},failedToSubmitPleaseTryAgain:"Failed to submit. Please try again.",userNotFound:"User not found",userAlreadyExists:"User already exists",invite:{required:"Invitation Required",notInvited:"You are not invited yet, write to one of our chats.",telegram:"Telegram",telegramLink:"https://t.me/ds_commune_en"}},ru:{_page:{title:"Вход — Коммуна"},authType:{login:"Вход",register:"Регистрация"},login:"Войти",register:"Создать аккаунт",email:"Email",otp:{short:"OTP",long:"Код проверки почты",send:"Отправить OTP",enterThe6DigitCodeSentToYourEmail:"Введите 6-значный код, отправленный на ваш email.",sent:"OTP отправлен",sendingDisabledByServer:"Отправка OTP отключена сервером",placeholder:"Шестизначный код"},failedToSubmitPleaseTryAgain:"Не удалось отправить. Пожалуйста, попробуйте снова.",userNotFound:"Пользователь не найден",userAlreadyExists:"Пользователь уже существует",invite:{required:"Требуется приглашение",notInvited:"Вы ещё не приглашены, напишите в один из наших чатов.",telegram:"Telegram",telegramLink:"https://t.me/ds_commune_ru"}}},w=Z(()=>p.data.locale),o=Z(()=>O[e(w)]);var A=(u=>(u.None="none",u.Pending="pending",u.Sent="sent",u.SendingDisabledByServer="sending-disabled-by-server",u.Error="error",u))(A||{}),k=(u=>(u.None="none",u.Pending="pending",u.Error="error",u.CustomError="custom-error",u))(k||{});let b=F("login"),f=F(de("")),D=F(de("")),P=F("none"),g=F("none"),T=F(null),N=F(!1);const ee=Z(()=>e(P)==="pending"||e(g)==="pending");Ve(()=>{e(b),e(f),n(N,!1)});function ue(u){n(b,u,!0),n(g,"none"),n(T,null)}const Ee=async u=>{var r,v;if(u.preventDefault(),!(!e(f)||!e(D))){n(g,"pending");try{if(e(b)==="login"){const i=await m.auth.signIn.post({email:e(f),otp:e(D)},{skipInterceptor:!0}),l=xe.parse(i);ye(l),fe(he.url.searchParams.get("redirectFrom")??"/")}else{const i=await m.auth.signUp.post({referrerId:null,email:e(f),otp:e(D)},{skipInterceptor:!0}),l=xe.parse(i);ye(l),fe(he.url.searchParams.get("redirectFrom")??"/")}}catch(i){if(console.error(i),i instanceof We){if(e(b)==="register"){if(i.status===403&&i.description.includes("must_have_invite")){n(N,!0),n(g,"none"),n(T,null);return}if(i.status===401&&i.description.includes("user_already_exists")){n(g,"custom-error"),n(T,e(o).userAlreadyExists,!0);return}}if(e(b)==="login"&&i.status===401&&i.description.includes("user_not_found")){n(g,"custom-error"),n(T,e(o).userNotFound,!0);return}}const l=((v=(r=i.response)==null?void 0:r.body)==null?void 0:v.error)||i.message;n(g,"error"),n(T,l,!0)}}};var j=lu(),H=t(j);be(H,"",{},{width:"100%","max-width":"400px"});var R=t(H),re=t(R),Y=t(re),M=t(Y),S=t(M);S.__click=[Ze,ue];var De=t(S,!0);a(S),a(M);var te=s(M,2),I=t(te);I.__click=[$e,ue];var Te=t(I,!0);a(I),a(te),a(Y);var Fe=s(Y,2);let ae;a(re),a(R);var ie=s(R,2),L=t(ie),z=t(L),V=t(z),Be=t(V,!0);a(V);var G=s(V,2);ge(G),G.__input=[eu,f],a(z);var J=s(z,2),U=t(J);U.__click=[Xe,f,P,m];var se=t(U);{var Ce=u=>{var r=uu();c(u,r)};y(se,u=>{e(P)===A.Pending&&u(Ce)})}var we=s(se);a(U),a(J);var K=s(J,2),Q=t(K),W=t(Q),Pe=t(W,!0);a(W);var Ae=s(W,2);{var ke=u=>{var r=ru(),v=t(r,!0);a(r),x(()=>d(v,e(o).otp.sent)),c(u,r)},Se=u=>{var r=ve(),v=pe(r);{var i=l=>{var h=tu(),C=t(h,!0);a(h),x(()=>d(C,e(o).otp.sendingDisabledByServer)),c(l,h)};y(v,l=>{e(P)===A.SendingDisabledByServer&&l(i)},!0)}c(u,r)};y(Ae,u=>{e(P)===A.Sent?u(ke):u(Se,!1)})}a(Q);var B=s(Q,2);ge(B),B.__input=[au,D],$(B,"maxlength",6);var ne=s(B,2),Ie=t(ne,!0);a(ne),a(K);var oe=s(K,2),q=t(oe),le=t(q);{var Le=u=>{var r=iu();c(u,r)};y(le,u=>{e(g)===k.Pending&&e(D)&&u(Le)})}var Ue=s(le,2);{var qe=u=>{var r=ce();x(()=>d(r,e(o).login)),c(u,r)},Oe=u=>{var r=ce();x(()=>d(r,e(o).register)),c(u,r)};y(Ue,u=>{e(b)==="login"?u(qe):u(Oe,!1)})}a(q);var Ne=s(q,2);{var je=u=>{var r=su(),v=t(r),i=s(v,2);a(r),x(()=>{d(v,`${e(o).failedToSubmitPleaseTryAgain??""} `),d(i,` ${e(T)??""}`)}),c(u,r)},He=u=>{var r=ve(),v=pe(r);{var i=l=>{var h=nu(),C=t(h,!0);a(h),x(()=>d(C,e(T))),c(l,h)};y(v,l=>{e(g)===k.CustomError&&l(i)},!0)}c(u,r)};y(Ne,u=>{e(g)===k.Error?u(je):u(He,!1)})}a(oe),a(L);var Re=s(L,2);{var Ye=u=>{var r=ou(),v=t(r),i=s(t(v));a(v);var l=s(v,2),h=t(l,!0);a(l);var C=s(l,2),X=t(C),Me=s(t(X));a(X),a(C),a(r),x(()=>{d(i,` ${e(o).invite.required??""}`),d(h,e(o).invite.notInvited),$(X,"href",e(o).invite.telegramLink),d(Me,` ${e(o).invite.telegram??""}`)}),c(u,r)};y(Re,u=>{e(N)&&u(Ye)})}a(ie),a(H),a(j),x(u=>{_e(S,1,`nav-link border-0 w-100 ${e(b)==="login"?"active":""}`,"svelte-il0jyn"),d(De,e(o).authType.login),_e(I,1,`nav-link border-0 w-100 ${e(b)==="register"?"active":""}`,"svelte-il0jyn"),d(Te,e(o).authType.register),ae=be(Fe,"",ae,u),d(Be,e(o).email),me(G,e(f)),U.disabled=!e(f)||e(ee),d(we,` ${e(o).otp.send??""}`),d(Pe,e(o).otp.long),me(B,e(D)),$(B,"placeholder",e(o).otp.placeholder),d(Ie,e(o).otp.enterThe6DigitCodeSentToYourEmail),q.disabled=!e(f)||!e(D)||e(ee)},[()=>({height:"3px",width:"50%",left:e(b)==="login"?"0":"50%",transition:"left 0.3s ease-in-out",borderRadius:"3px 3px 0 0"})]),Ke("submit",L,Ee),c(_,j),Ge()}Je(["click","input"]);export{yu as component};
