import{e as Fu,c as me}from"../chunks/CVTn1FV4.js";import{g as We}from"../chunks/CGZ87yZq.js";import"../chunks/Bzak7iHL.js";import{o as Au}from"../chunks/DeAm3Eed.js";import{p as Lu,aw as se,av as p,f as d,h as Nu,a as V,t as b,b as n,c as Gu,g as e,u as k,$ as ju,s as o,d as r,r as a,ax as t}from"../chunks/RHWQbow4.js";import{d as zu,s as v}from"../chunks/BlWcudmi.js";import{i as h}from"../chunks/CtoItwj4.js";import{e as Ou}from"../chunks/Dnfvvefi.js";import{s as N}from"../chunks/BdpLTtcP.js";import{s as ku}from"../chunks/CaC9IHEK.js";import{b as $u}from"../chunks/B5DcI8qy.js";import{M as Pe,e as qu,a as Wu,f as qe}from"../chunks/iI8NM7bJ.js";import"../chunks/B0MzmgHo.js";import{f as Ru}from"../chunks/CL12WlkV.js";import"../chunks/BiLRrsV0.js";const Ju=async({fetch:G,params:m,url:y})=>{const{fetcher:I}=We(),[M,[z],D]=await Promise.all([I.user.me.get({fetch:G,skipInterceptor:!0}).catch(()=>null),I.reactor.hub.list.get({ids:[m.id]},{fetch:G,ctx:{url:y}}),I.reactor.community.list.get({hubId:m.id},{fetch:G,ctx:{url:y}})]);if(!z)throw Fu(404,"Hub not found");const l=M&&(M.role==="admin"||M.id===z.headUser.id);return{me:M,hub:z,communities:D,canEdit:l,isHasMoreCommunities:D.length===me.PAGE_SIZE}},O4=Object.freeze(Object.defineProperty({__proto__:null,load:Ju},Symbol.toStringTag,{value:"Module"}));function Zu(G,m,y,I,M,z){t(m,!0),t(y,[...I.name],!0),t(M,[...I.description],!0),z()}function Xu(G,m,y){t(m,!0),y()}function Yu(G,m,y,I,M){const D=G.target.files;if(t(m,null),!D||D.length===0){t(y,null),t(I,null);return}const l=D[0];if(!me.ALLOWED_IMAGE_FILE_TYPES.includes(l.type)){t(m,e(M).invalidFileType,!0),t(y,null),t(I,null);return}if(l.size>me.MAX_IMAGE_FILE_SIZE){t(m,e(M).fileTooLarge,!0),t(y,null),t(I,null);return}t(y,l,!0),e(I)&&URL.revokeObjectURL(e(I)),t(I,URL.createObjectURL(l),!0)}function Ku(G,m,y){t(m,!0),y()}var Qu=d('<img class="hub-image svelte-19f9mw5"/>'),Vu=d('<div class="hub-image-placeholder svelte-19f9mw5"><i class="bi bi-collection fs-1 text-muted"></i></div>'),e4=d('<button class="btn btn-outline-danger btn-sm"><i class="bi bi-trash me-1"></i> </button>'),u4=d('<div class="mt-3 d-grid gap-2"><button class="btn btn-outline-primary btn-sm"><i class="bi bi-upload me-1"></i> </button> <!></div>'),t4=d('<button class="btn btn-primary"><i class="bi bi-pencil me-1"></i> </button>'),a4=d('<img class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;"/>'),r4=d('<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;"><i class="bi bi-person-fill text-white"></i></div>'),i4=d('<div class="text-center py-5"><i class="bi bi-collection fs-1 text-muted mb-3"></i> <p class="text-muted"> </p></div>'),l4=d('<img class="community-card-image svelte-19f9mw5"/>'),o4=d('<div class="community-card-image-placeholder svelte-19f9mw5"><i class="bi bi-people fs-2 text-muted"></i></div>'),s4=d('<img class="rounded-circle" style="width: 32px; height: 32px; object-fit: cover;"/>'),n4=d('<div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;"><i class="bi bi-person-fill text-white"></i></div>'),d4=d('<div class="col-md-6 col-lg-4"><div class="card shadow-sm h-100 svelte-19f9mw5"><div class="card-body d-flex flex-column"><div class="community-card-image-container mb-3 svelte-19f9mw5"><!></div> <h5 class="card-title mb-2"><a style="text-decoration: none;"> </a></h5> <p class="card-text text-muted mb-3 flex-grow-1"> </p> <div class="d-flex align-items-center mt-auto"><div class="me-2"><!></div> <div class="small"><div class="fw-medium"> </div></div></div></div></div></div>'),c4=d('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden"> </span></div> <p class="text-muted mt-2 mb-0"> </p>',1),m4=d('<div class="text-center py-3 mt-4"><!></div>'),v4=d('<div class="alert alert-warning mt-3" role="alert"> </div>'),g4=d('<div class="row g-4"></div> <!> <!>',1),f4=d('<div class="alert alert-danger mb-3"> </div>'),_4=d('<div class="alert alert-success mb-3"> </div>'),p4=d("<!> <!> <form><!> <!></form>",1),b4=d('<div class="alert alert-success mb-3"> </div>'),h4=d('<div class="alert alert-danger mb-3"> </div>'),E4=d('<div class="mt-3 text-center"><img alt="Preview" class="img-thumbnail"/></div>'),x4=d('<!> <!> <form><div class="mb-3"><label for="imageInput" class="form-label"> </label> <input id="imageInput" type="file" class="form-control" accept=".jpg,.jpeg,.png,.webp"/> <p class="form-text text-muted"> </p> <!></div></form>',1),D4=d('<div class="alert alert-success mb-3"> </div>'),C4=d('<div class="alert alert-danger mb-3"> </div>'),I4=d("<!> <!> <p> </p>",1),w4=d('<div class="container my-4 mb-5"><div class="row mb-4"><div class="col-md-4 col-lg-3 mb-3"><div class="hub-image-container svelte-19f9mw5"><!></div> <!></div> <div class="col-md-8 col-lg-9"><div class="d-flex justify-content-between align-items-start mb-3"><h1 class="mb-0"> </h1> <!></div> <p class="text-muted mb-3 fs-5"> </p> <div class="row g-3"><div class="col-sm-6"><div class="d-flex align-items-center"><div class="me-3"><!></div> <div><a class="fw-medium" style="text-decoration: none;"> </a></div></div></div> <div class="col-sm-6"><div class="small text-muted"> </div> <div class="fw-medium"> </div></div></div></div></div> <div class="mt-5"><h2 class="mb-4"> </h2> <!></div> <!></div> <!> <!> <!>',1);function k4(G,m){Lu(m,!0);const y={en:{_page:{title:"Hub — Reactor of Commune"},head:"Head",createdOn:"Created on",editHub:"Edit Hub",uploadImage:"Upload Image",deleteImage:"Delete Image",communities:"Communities",noCommunities:"No communities found",loadingMoreCommunities:"Loading more communities...",editHubTitle:"Edit Hub",hubName:"Hub Name",hubDescription:"Hub Description",hubNamePlaceholder:"Enter hub name",hubDescriptionPlaceholder:"Enter hub description",save:"Save",cancel:"Cancel",saving:"Saving...",hubUpdatedSuccess:"Hub updated successfully!",errorUpdatingHub:"Failed to update hub",required:"This field is required",uploadImageTitle:"Upload Hub Image",upload:"Upload",uploading:"Uploading...",imageUploadedSuccess:"Image uploaded successfully!",errorUploadingImage:"Failed to upload image",pleaseSelectImage:"Please select an image to upload",invalidFileType:"Invalid file type. Please upload a JPG, PNG, or WebP image.",fileTooLarge:"File is too large. Maximum size is 5MB.",uploadImageMaxSize:"Upload an image (JPG, PNG, WebP), max 5MB.",confirmDeleteImage:"Are you sure you want to delete this image?",deleteImageTitle:"Delete Image",delete:"Delete",deleting:"Deleting...",imageDeletedSuccess:"Image deleted successfully!",errorDeletingImage:"Failed to delete image"},ru:{_page:{title:"Хаб — Реактор Коммуны"},head:"Глава",createdOn:"Создан",editHub:"Редактировать хаб",uploadImage:"Загрузить изображение",deleteImage:"Удалить изображение",communities:"Сообщества",noCommunities:"Сообщества не найдены",loadingMoreCommunities:"Загружаем больше сообществ...",editHubTitle:"Редактировать хаб",hubName:"Название хаба",hubDescription:"Описание хаба",hubNamePlaceholder:"Введите название хаба",hubDescriptionPlaceholder:"Введите описание хаба",save:"Сохранить",cancel:"Отмена",saving:"Сохранение...",hubUpdatedSuccess:"Хаб успешно обновлен!",errorUpdatingHub:"Не удалось обновить хаб",required:"Это поле обязательно",uploadImageTitle:"Загрузить изображение хаба",upload:"Загрузить",uploading:"Загрузка...",imageUploadedSuccess:"Изображение загружено успешно!",errorUploadingImage:"Не удалось загрузить изображение",pleaseSelectImage:"Пожалуйста, выберите изображение для загрузки",invalidFileType:"Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",fileTooLarge:"Файл слишком большой. Максимальный размер - 5MB.",uploadImageMaxSize:"Загрузите изображение (JPG, PNG, WebP), максимальный размер - 5MB.",confirmDeleteImage:"Вы уверены, что хотите удалить это изображение?",deleteImageTitle:"Удалить изображение",delete:"Удалить",deleting:"Удаление...",imageDeletedSuccess:"Изображение удалено успешно!",errorDeletingImage:"Не удалось удалить изображение"}},{fetcher:I}=We(),M=k(()=>m.data.locale),z=k(()=>m.data.toLocaleHref),D=k(()=>m.data.getAppropriateLocalization),l=k(()=>y[e(M)]);let x=se(m.data.hub),ve=p(!1),Z=p(!1),q=p(null),ee=p(null),X=p(se([])),Y=p(se([])),ge=p(!1),W=p(!1),R=p(null),ue=p(null),K=p(null),te=p(null),fe=p(!1),Q=p(!1),ae=p(null),re=p(null),ne=p(se(m.data.communities)),ie=p(!1),Te=p(1),de=p(se(m.data.isHasMoreCommunities)),le=p(null),ce=p(null);function Re(){t(ve,!1)}function Je(){t(q,null),t(ee,null),t(Z,!1)}function Ze(){return e(X).some(u=>u.value.trim().length>0)?e(Y).some(u=>u.value.trim().length>0)?!0:(t(q,e(l).required,!0),!1):(t(q,e(l).required,!0),!1)}async function Xe(){if(Ze()){t(Z,!0),t(q,null),t(ee,null);try{await I.reactor.hub.patch({id:x.id,name:e(X),description:e(Y)}),t(ee,e(l).hubUpdatedSuccess,!0),setTimeout(()=>{ru()},1500)}catch(u){t(q,u instanceof Error?u.message:e(l).errorUpdatingHub,!0),console.error(u)}finally{t(Z,!1)}}}function Ye(){t(ge,!1),t(K,null),t(te,null)}function Ke(){t(R,null),t(ue,null),t(W,!1),t(K,null),t(te,null)}async function Qe(){if(!e(K)){t(R,e(l).pleaseSelectImage,!0);return}t(W,!0),t(R,null),t(ue,null);try{const u=new FormData;u.append("image",e(K));const i=await qe(`/api/reactor/hub/${x.id}/image`,{method:"PUT",body:u});if(!i.ok)throw new Error(`${e(l).errorUploadingImage}: ${i.statusText}`);t(ue,e(l).imageUploadedSuccess,!0),setTimeout(()=>{window.location.reload()},1500)}catch(u){t(R,u instanceof Error?u.message:e(l).errorUploadingImage,!0),console.error(u)}finally{t(W,!1)}}function Ve(){t(fe,!1)}function eu(){t(ae,null),t(re,null),t(Q,!1)}async function uu(){t(Q,!0),t(ae,null),t(re,null);try{const u=await qe(`/api/reactor/hub/${x.id}/image`,{method:"DELETE"});if(!u.ok)throw new Error(`${e(l).errorDeletingImage}: ${u.statusText}`);t(re,e(l).imageDeletedSuccess,!0),setTimeout(()=>{window.location.reload()},1500)}catch(u){t(ae,u instanceof Error?u.message:e(l).errorDeletingImage,!0),console.error(u)}finally{t(Q,!1)}}async function tu(){if(!(e(ie)||!e(de))){t(ie,!0),t(ce,null);try{const u=e(Te)+1,i=await I.reactor.community.list.get({pagination:{page:u},hubId:x.id});t(ne,[...e(ne),...i],!0),t(Te,u),t(de,i.length===me.PAGE_SIZE)}catch(u){t(ce,u instanceof Error?u.message:"Failed to load more communities",!0),console.error(u)}finally{t(ie,!1)}}}function au(){if(!e(le))return null;const u=new IntersectionObserver(i=>{i[0].isIntersecting&&e(de)&&!e(ie)&&tu()},{rootMargin:"100px",threshold:.1});return u.observe(e(le)),u}Au(()=>{let u=null;const i=()=>{u=au()};return e(le)?i():setTimeout(i,100),()=>{u==null||u.disconnect()}});function ru(){window.location.reload()}var He=w4();Nu(u=>{b(i=>ju.title=`${i??""} — ${e(l)._page.title??""}`,[()=>e(D)(x.name)||"Hub"])});var _e=V(He),pe=r(_e),be=r(pe),he=r(be),iu=r(he);{var lu=u=>{var i=Qu();b(c=>{N(i,"src",`/images/${x.image}`),N(i,"alt",c)},[()=>e(D)(x.name)||"Hub"]),n(u,i)},ou=u=>{var i=Vu();n(u,i)};h(iu,u=>{x.image?u(lu):u(ou,!1)})}a(he);var su=o(he,2);{var nu=u=>{var i=u4(),c=r(i);c.__click=[Xu,ge,Ke];var C=o(r(c));a(c);var j=o(c,2);{var F=P=>{var g=e4();g.__click=[Ku,fe,eu];var s=o(r(g));a(g),b(()=>v(s,` ${e(l).deleteImage??""}`)),n(P,g)};h(j,P=>{x.image&&P(F)})}a(i),b(()=>v(C,` ${e(l).uploadImage??""}`)),n(u,i)};h(su,u=>{m.data.canEdit&&u(nu)})}a(be);var Fe=o(be,2),Ee=r(Fe),xe=r(Ee),du=r(xe,!0);a(xe);var cu=o(xe,2);{var mu=u=>{var i=t4();i.__click=[Zu,ve,X,x,Y,Je];var c=o(r(i));a(i),b(()=>v(c,` ${e(l).editHub??""}`)),n(u,i)};h(cu,u=>{m.data.canEdit&&u(mu)})}a(Ee);var De=o(Ee,2),vu=r(De,!0);a(De);var Ae=o(De,2),Ce=r(Ae),Le=r(Ce),Ie=r(Le),gu=r(Ie);{var fu=u=>{var i=a4();b(c=>{N(i,"src",`/images/${x.headUser.image}`),N(i,"alt",c)},[()=>e(D)(x.headUser.name)]),n(u,i)},_u=u=>{var i=r4();n(u,i)};h(gu,u=>{x.headUser.image?u(fu):u(_u,!1)})}a(Ie);var Ne=o(Ie,2),we=r(Ne),pu=r(we,!0);a(we),a(Ne),a(Le),a(Ce);var Ge=o(Ce,2),ye=r(Ge),bu=r(ye);a(ye);var je=o(ye,2),hu=r(je,!0);a(je),a(Ge),a(Ae),a(Fe),a(pe);var Ue=o(pe,2),Be=r(Ue),Eu=r(Be,!0);a(Be);var xu=o(Be,2);{var Du=u=>{var i=i4(),c=o(r(i),2),C=r(c,!0);a(c),a(i),b(()=>v(C,e(l).noCommunities)),n(u,i)},Cu=u=>{var i=g4(),c=V(i);Ou(c,21,()=>e(ne),g=>g.id,(g,s)=>{var S=d4(),A=r(S),_=r(A),E=r(_),f=r(E);{var w=B=>{var H=l4();b(oe=>{N(H,"src",`/images/${e(s).image}`),N(H,"alt",oe)},[()=>e(D)(e(s).name)||"Community"]),n(B,H)},L=B=>{var H=o4();n(B,H)};h(f,B=>{e(s).image?B(w):B(L,!1)})}a(E);var $=o(E,2),J=r($),Me=r(J,!0);a(J),a($);var U=o($,2),T=r(U,!0);a(U);var O=o(U,2),Se=r(O),Mu=r(Se);{var Su=B=>{var H=s4();b(oe=>{N(H,"src",`/images/${e(s).headUser.image}`),N(H,"alt",oe)},[()=>e(D)(e(s).headUser.name)]),n(B,H)},Pu=B=>{var H=n4();n(B,H)};h(Mu,B=>{e(s).headUser.image?B(Su):B(Pu,!1)})}a(Se);var ke=o(Se,2),$e=r(ke),Tu=r($e,!0);a($e),a(ke),a(O),a(_),a(A),a(S),b((B,H,oe,Hu)=>{N(J,"href",B),v(Me,H),v(T,oe),v(Tu,Hu)},[()=>e(z)(`/reactor/communities/${e(s).id}`),()=>e(D)(e(s).name)||"No name?",()=>e(D)(e(s).description)||"",()=>e(D)(e(s).headUser.name)]),n(g,S)}),a(c);var C=o(c,2);{var j=g=>{var s=m4(),S=r(s);{var A=_=>{var E=c4(),f=V(E),w=r(f),L=r(w,!0);a(w),a(f);var $=o(f,2),J=r($,!0);a($),b(()=>{v(L,e(l).loadingMoreCommunities),v(J,e(l).loadingMoreCommunities)}),n(_,E)};h(S,_=>{e(ie)&&_(A)})}a(s),$u(s,_=>t(le,_),()=>e(le)),n(g,s)};h(C,g=>{e(de)&&g(j)})}var F=o(C,2);{var P=g=>{var s=v4(),S=r(s,!0);a(s),b(()=>v(S,e(ce))),n(g,s)};h(F,g=>{e(ce)&&g(P)})}n(u,i)};h(xu,u=>{e(ne).length===0?u(Du):u(Cu,!1)})}a(Ue);var Iu=o(Ue,2);h(Iu,u=>{}),a(_e);var ze=o(_e,2);{var wu=u=>{{let i=k(()=>e(Z)?e(l).saving:e(l).save),c=k(()=>e(Z)||!e(X).some(C=>C.value.trim().length>0)||!e(Y).some(C=>C.value.trim().length>0));Pe(u,{get show(){return e(ve)},get title(){return e(l).editHubTitle},onClose:Re,onSubmit:Xe,get submitText(){return e(i)},get cancelText(){return e(l).cancel},get submitDisabled(){return e(c)},get isSubmitting(){return e(Z)},children:(C,j)=>{var F=p4(),P=V(F);{var g=f=>{var w=f4(),L=r(w,!0);a(w),b(()=>v(L,e(q))),n(f,w)};h(P,f=>{e(q)&&f(g)})}var s=o(P,2);{var S=f=>{var w=_4(),L=r(w,!0);a(w),b(()=>v(L,e(ee))),n(f,w)};h(s,f=>{e(ee)&&f(S)})}var A=o(s,2),_=r(A);qu(_,{get locale(){return e(M)},id:"hub-name",get label(){return e(l).hubName},get placeholder(){return e(l).hubNamePlaceholder},required:!0,get value(){return e(X)},set value(f){t(X,f,!0)}});var E=o(_,2);Wu(E,{get locale(){return e(M)},id:"hub-description",get label(){return e(l).hubDescription},get placeholder(){return e(l).hubDescriptionPlaceholder},rows:4,required:!0,get value(){return e(Y)},set value(f){t(Y,f,!0)}}),a(A),n(C,F)},$$slots:{default:!0}})}};h(ze,u=>{m.data.canEdit&&u(wu)})}var Oe=o(ze,2);{var yu=u=>{{let i=k(()=>e(W)?e(l).uploading:e(l).upload),c=k(()=>!e(K)||e(W));Pe(u,{get show(){return e(ge)},get title(){return e(l).uploadImageTitle},onClose:Ye,onSubmit:Qe,get submitText(){return e(i)},get cancelText(){return e(l).cancel},get submitDisabled(){return e(c)},get isSubmitting(){return e(W)},size:"lg",children:(C,j)=>{var F=x4(),P=V(F);{var g=U=>{var T=b4(),O=r(T,!0);a(T),b(()=>v(O,e(ue))),n(U,T)};h(P,U=>{e(ue)&&U(g)})}var s=o(P,2);{var S=U=>{var T=h4(),O=r(T,!0);a(T),b(()=>v(O,e(R))),n(U,T)};h(s,U=>{e(R)&&U(S)})}var A=o(s,2),_=r(A),E=r(_),f=r(E,!0);a(E);var w=o(E,2);w.__change=[Yu,R,K,te,l];var L=o(w,2),$=r(L,!0);a(L);var J=o(L,2);{var Me=U=>{var T=E4(),O=r(T);ku(O,"",{},{"max-height":"200px"}),a(T),b(()=>N(O,"src",e(te))),n(U,T)};h(J,U=>{e(te)&&U(Me)})}a(_),a(A),b(()=>{v(f,e(l).pleaseSelectImage),w.disabled=e(W),v($,e(l).uploadImageMaxSize)}),n(C,F)},$$slots:{default:!0}})}};h(Oe,u=>{m.data.canEdit&&u(yu)})}var Uu=o(Oe,2);{var Bu=u=>{{let i=k(()=>e(Q)?e(l).deleting:e(l).delete);Pe(u,{get show(){return e(fe)},get title(){return e(l).deleteImageTitle},onClose:Ve,onSubmit:uu,get submitText(){return e(i)},get cancelText(){return e(l).cancel},get submitDisabled(){return e(Q)},get isSubmitting(){return e(Q)},children:(c,C)=>{var j=I4(),F=V(j);{var P=_=>{var E=D4(),f=r(E,!0);a(E),b(()=>v(f,e(re))),n(_,E)};h(F,_=>{e(re)&&_(P)})}var g=o(F,2);{var s=_=>{var E=C4(),f=r(E,!0);a(E),b(()=>v(f,e(ae))),n(_,E)};h(g,_=>{e(ae)&&_(s)})}var S=o(g,2),A=r(S,!0);a(S),b(()=>v(A,e(l).confirmDeleteImage)),n(c,j)},$$slots:{default:!0}})}};h(Uu,u=>{m.data.canEdit&&x.image&&u(Bu)})}b((u,i,c,C,j)=>{v(du,u),v(vu,i),N(we,"href",c),v(pu,C),v(bu,`${e(l).createdOn??""}:`),v(hu,j),v(Eu,e(l).communities)},[()=>e(D)(x.name)||"Unknown Hub",()=>e(D)(x.description)||"",()=>e(z)(`/users/${x.headUser.id}`),()=>e(D)(x.headUser.name),()=>Ru(x.createdAt,e(M))]),n(G,He),Gu()}zu(["click","change"]);export{k4 as component,O4 as universal};
