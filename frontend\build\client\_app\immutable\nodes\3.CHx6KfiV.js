import{r as P,s as O,a as k}from"../chunks/CVTn1FV4.js";import"../chunks/B0MzmgHo.js";import"../chunks/Bzak7iHL.js";import{ay as b,a as F,b as V}from"../chunks/RHWQbow4.js";import{s as Z}from"../chunks/CkTdM00m.js";function z(a){return Intl.getCanonicalLocales(a)}function A(a,r,n){if(n||arguments.length===2)for(var e=0,i=r.length,t;e<i;e++)(t||!(e in r))&&(t||(t=Array.prototype.slice.call(r,0,e)),t[e]=r[e]);return a.concat(t||Array.prototype.slice.call(r))}var T={supplemental:{languageMatching:{"written-new":[{paradigmLocales:{_locales:"en en_GB es es_419 pt_BR pt_PT"}},{$enUS:{_value:"AS+CA+GU+MH+MP+PH+PR+UM+US+VI"}},{$cnsar:{_value:"HK+MO"}},{$americas:{_value:"019"}},{$maghreb:{_value:"MA+DZ+TN+LY+MR+EH"}},{no:{_desired:"nb",_distance:"1"}},{bs:{_desired:"hr",_distance:"4"}},{bs:{_desired:"sh",_distance:"4"}},{hr:{_desired:"sh",_distance:"4"}},{sr:{_desired:"sh",_distance:"4"}},{aa:{_desired:"ssy",_distance:"4"}},{de:{_desired:"gsw",_distance:"4",_oneway:"true"}},{de:{_desired:"lb",_distance:"4",_oneway:"true"}},{no:{_desired:"da",_distance:"8"}},{nb:{_desired:"da",_distance:"8"}},{ru:{_desired:"ab",_distance:"30",_oneway:"true"}},{en:{_desired:"ach",_distance:"30",_oneway:"true"}},{nl:{_desired:"af",_distance:"20",_oneway:"true"}},{en:{_desired:"ak",_distance:"30",_oneway:"true"}},{en:{_desired:"am",_distance:"30",_oneway:"true"}},{es:{_desired:"ay",_distance:"20",_oneway:"true"}},{ru:{_desired:"az",_distance:"30",_oneway:"true"}},{ur:{_desired:"bal",_distance:"20",_oneway:"true"}},{ru:{_desired:"be",_distance:"20",_oneway:"true"}},{en:{_desired:"bem",_distance:"30",_oneway:"true"}},{hi:{_desired:"bh",_distance:"30",_oneway:"true"}},{en:{_desired:"bn",_distance:"30",_oneway:"true"}},{zh:{_desired:"bo",_distance:"20",_oneway:"true"}},{fr:{_desired:"br",_distance:"20",_oneway:"true"}},{es:{_desired:"ca",_distance:"20",_oneway:"true"}},{fil:{_desired:"ceb",_distance:"30",_oneway:"true"}},{en:{_desired:"chr",_distance:"20",_oneway:"true"}},{ar:{_desired:"ckb",_distance:"30",_oneway:"true"}},{fr:{_desired:"co",_distance:"20",_oneway:"true"}},{fr:{_desired:"crs",_distance:"20",_oneway:"true"}},{sk:{_desired:"cs",_distance:"20"}},{en:{_desired:"cy",_distance:"20",_oneway:"true"}},{en:{_desired:"ee",_distance:"30",_oneway:"true"}},{en:{_desired:"eo",_distance:"30",_oneway:"true"}},{es:{_desired:"eu",_distance:"20",_oneway:"true"}},{da:{_desired:"fo",_distance:"20",_oneway:"true"}},{nl:{_desired:"fy",_distance:"20",_oneway:"true"}},{en:{_desired:"ga",_distance:"20",_oneway:"true"}},{en:{_desired:"gaa",_distance:"30",_oneway:"true"}},{en:{_desired:"gd",_distance:"20",_oneway:"true"}},{es:{_desired:"gl",_distance:"20",_oneway:"true"}},{es:{_desired:"gn",_distance:"20",_oneway:"true"}},{hi:{_desired:"gu",_distance:"30",_oneway:"true"}},{en:{_desired:"ha",_distance:"30",_oneway:"true"}},{en:{_desired:"haw",_distance:"20",_oneway:"true"}},{fr:{_desired:"ht",_distance:"20",_oneway:"true"}},{ru:{_desired:"hy",_distance:"30",_oneway:"true"}},{en:{_desired:"ia",_distance:"30",_oneway:"true"}},{en:{_desired:"ig",_distance:"30",_oneway:"true"}},{en:{_desired:"is",_distance:"20",_oneway:"true"}},{id:{_desired:"jv",_distance:"20",_oneway:"true"}},{en:{_desired:"ka",_distance:"30",_oneway:"true"}},{fr:{_desired:"kg",_distance:"30",_oneway:"true"}},{ru:{_desired:"kk",_distance:"30",_oneway:"true"}},{en:{_desired:"km",_distance:"30",_oneway:"true"}},{en:{_desired:"kn",_distance:"30",_oneway:"true"}},{en:{_desired:"kri",_distance:"30",_oneway:"true"}},{tr:{_desired:"ku",_distance:"30",_oneway:"true"}},{ru:{_desired:"ky",_distance:"30",_oneway:"true"}},{it:{_desired:"la",_distance:"20",_oneway:"true"}},{en:{_desired:"lg",_distance:"30",_oneway:"true"}},{fr:{_desired:"ln",_distance:"30",_oneway:"true"}},{en:{_desired:"lo",_distance:"30",_oneway:"true"}},{en:{_desired:"loz",_distance:"30",_oneway:"true"}},{fr:{_desired:"lua",_distance:"30",_oneway:"true"}},{hi:{_desired:"mai",_distance:"20",_oneway:"true"}},{en:{_desired:"mfe",_distance:"30",_oneway:"true"}},{fr:{_desired:"mg",_distance:"30",_oneway:"true"}},{en:{_desired:"mi",_distance:"20",_oneway:"true"}},{en:{_desired:"ml",_distance:"30",_oneway:"true"}},{ru:{_desired:"mn",_distance:"30",_oneway:"true"}},{hi:{_desired:"mr",_distance:"30",_oneway:"true"}},{id:{_desired:"ms",_distance:"30",_oneway:"true"}},{en:{_desired:"mt",_distance:"30",_oneway:"true"}},{en:{_desired:"my",_distance:"30",_oneway:"true"}},{en:{_desired:"ne",_distance:"30",_oneway:"true"}},{nb:{_desired:"nn",_distance:"20"}},{no:{_desired:"nn",_distance:"20"}},{en:{_desired:"nso",_distance:"30",_oneway:"true"}},{en:{_desired:"ny",_distance:"30",_oneway:"true"}},{en:{_desired:"nyn",_distance:"30",_oneway:"true"}},{fr:{_desired:"oc",_distance:"20",_oneway:"true"}},{en:{_desired:"om",_distance:"30",_oneway:"true"}},{en:{_desired:"or",_distance:"30",_oneway:"true"}},{en:{_desired:"pa",_distance:"30",_oneway:"true"}},{en:{_desired:"pcm",_distance:"20",_oneway:"true"}},{en:{_desired:"ps",_distance:"30",_oneway:"true"}},{es:{_desired:"qu",_distance:"30",_oneway:"true"}},{de:{_desired:"rm",_distance:"20",_oneway:"true"}},{en:{_desired:"rn",_distance:"30",_oneway:"true"}},{fr:{_desired:"rw",_distance:"30",_oneway:"true"}},{hi:{_desired:"sa",_distance:"30",_oneway:"true"}},{en:{_desired:"sd",_distance:"30",_oneway:"true"}},{en:{_desired:"si",_distance:"30",_oneway:"true"}},{en:{_desired:"sn",_distance:"30",_oneway:"true"}},{en:{_desired:"so",_distance:"30",_oneway:"true"}},{en:{_desired:"sq",_distance:"30",_oneway:"true"}},{en:{_desired:"st",_distance:"30",_oneway:"true"}},{id:{_desired:"su",_distance:"20",_oneway:"true"}},{en:{_desired:"sw",_distance:"30",_oneway:"true"}},{en:{_desired:"ta",_distance:"30",_oneway:"true"}},{en:{_desired:"te",_distance:"30",_oneway:"true"}},{ru:{_desired:"tg",_distance:"30",_oneway:"true"}},{en:{_desired:"ti",_distance:"30",_oneway:"true"}},{ru:{_desired:"tk",_distance:"30",_oneway:"true"}},{en:{_desired:"tlh",_distance:"30",_oneway:"true"}},{en:{_desired:"tn",_distance:"30",_oneway:"true"}},{en:{_desired:"to",_distance:"30",_oneway:"true"}},{ru:{_desired:"tt",_distance:"30",_oneway:"true"}},{en:{_desired:"tum",_distance:"30",_oneway:"true"}},{zh:{_desired:"ug",_distance:"20",_oneway:"true"}},{ru:{_desired:"uk",_distance:"20",_oneway:"true"}},{en:{_desired:"ur",_distance:"30",_oneway:"true"}},{ru:{_desired:"uz",_distance:"30",_oneway:"true"}},{fr:{_desired:"wo",_distance:"30",_oneway:"true"}},{en:{_desired:"xh",_distance:"30",_oneway:"true"}},{en:{_desired:"yi",_distance:"30",_oneway:"true"}},{en:{_desired:"yo",_distance:"30",_oneway:"true"}},{zh:{_desired:"za",_distance:"20",_oneway:"true"}},{en:{_desired:"zu",_distance:"30",_oneway:"true"}},{ar:{_desired:"aao",_distance:"10",_oneway:"true"}},{ar:{_desired:"abh",_distance:"10",_oneway:"true"}},{ar:{_desired:"abv",_distance:"10",_oneway:"true"}},{ar:{_desired:"acm",_distance:"10",_oneway:"true"}},{ar:{_desired:"acq",_distance:"10",_oneway:"true"}},{ar:{_desired:"acw",_distance:"10",_oneway:"true"}},{ar:{_desired:"acx",_distance:"10",_oneway:"true"}},{ar:{_desired:"acy",_distance:"10",_oneway:"true"}},{ar:{_desired:"adf",_distance:"10",_oneway:"true"}},{ar:{_desired:"aeb",_distance:"10",_oneway:"true"}},{ar:{_desired:"aec",_distance:"10",_oneway:"true"}},{ar:{_desired:"afb",_distance:"10",_oneway:"true"}},{ar:{_desired:"ajp",_distance:"10",_oneway:"true"}},{ar:{_desired:"apc",_distance:"10",_oneway:"true"}},{ar:{_desired:"apd",_distance:"10",_oneway:"true"}},{ar:{_desired:"arq",_distance:"10",_oneway:"true"}},{ar:{_desired:"ars",_distance:"10",_oneway:"true"}},{ar:{_desired:"ary",_distance:"10",_oneway:"true"}},{ar:{_desired:"arz",_distance:"10",_oneway:"true"}},{ar:{_desired:"auz",_distance:"10",_oneway:"true"}},{ar:{_desired:"avl",_distance:"10",_oneway:"true"}},{ar:{_desired:"ayh",_distance:"10",_oneway:"true"}},{ar:{_desired:"ayl",_distance:"10",_oneway:"true"}},{ar:{_desired:"ayn",_distance:"10",_oneway:"true"}},{ar:{_desired:"ayp",_distance:"10",_oneway:"true"}},{ar:{_desired:"bbz",_distance:"10",_oneway:"true"}},{ar:{_desired:"pga",_distance:"10",_oneway:"true"}},{ar:{_desired:"shu",_distance:"10",_oneway:"true"}},{ar:{_desired:"ssh",_distance:"10",_oneway:"true"}},{az:{_desired:"azb",_distance:"10",_oneway:"true"}},{et:{_desired:"vro",_distance:"10",_oneway:"true"}},{ff:{_desired:"ffm",_distance:"10",_oneway:"true"}},{ff:{_desired:"fub",_distance:"10",_oneway:"true"}},{ff:{_desired:"fue",_distance:"10",_oneway:"true"}},{ff:{_desired:"fuf",_distance:"10",_oneway:"true"}},{ff:{_desired:"fuh",_distance:"10",_oneway:"true"}},{ff:{_desired:"fui",_distance:"10",_oneway:"true"}},{ff:{_desired:"fuq",_distance:"10",_oneway:"true"}},{ff:{_desired:"fuv",_distance:"10",_oneway:"true"}},{gn:{_desired:"gnw",_distance:"10",_oneway:"true"}},{gn:{_desired:"gui",_distance:"10",_oneway:"true"}},{gn:{_desired:"gun",_distance:"10",_oneway:"true"}},{gn:{_desired:"nhd",_distance:"10",_oneway:"true"}},{iu:{_desired:"ikt",_distance:"10",_oneway:"true"}},{kln:{_desired:"enb",_distance:"10",_oneway:"true"}},{kln:{_desired:"eyo",_distance:"10",_oneway:"true"}},{kln:{_desired:"niq",_distance:"10",_oneway:"true"}},{kln:{_desired:"oki",_distance:"10",_oneway:"true"}},{kln:{_desired:"pko",_distance:"10",_oneway:"true"}},{kln:{_desired:"sgc",_distance:"10",_oneway:"true"}},{kln:{_desired:"tec",_distance:"10",_oneway:"true"}},{kln:{_desired:"tuy",_distance:"10",_oneway:"true"}},{kok:{_desired:"gom",_distance:"10",_oneway:"true"}},{kpe:{_desired:"gkp",_distance:"10",_oneway:"true"}},{luy:{_desired:"ida",_distance:"10",_oneway:"true"}},{luy:{_desired:"lkb",_distance:"10",_oneway:"true"}},{luy:{_desired:"lko",_distance:"10",_oneway:"true"}},{luy:{_desired:"lks",_distance:"10",_oneway:"true"}},{luy:{_desired:"lri",_distance:"10",_oneway:"true"}},{luy:{_desired:"lrm",_distance:"10",_oneway:"true"}},{luy:{_desired:"lsm",_distance:"10",_oneway:"true"}},{luy:{_desired:"lto",_distance:"10",_oneway:"true"}},{luy:{_desired:"lts",_distance:"10",_oneway:"true"}},{luy:{_desired:"lwg",_distance:"10",_oneway:"true"}},{luy:{_desired:"nle",_distance:"10",_oneway:"true"}},{luy:{_desired:"nyd",_distance:"10",_oneway:"true"}},{luy:{_desired:"rag",_distance:"10",_oneway:"true"}},{lv:{_desired:"ltg",_distance:"10",_oneway:"true"}},{mg:{_desired:"bhr",_distance:"10",_oneway:"true"}},{mg:{_desired:"bjq",_distance:"10",_oneway:"true"}},{mg:{_desired:"bmm",_distance:"10",_oneway:"true"}},{mg:{_desired:"bzc",_distance:"10",_oneway:"true"}},{mg:{_desired:"msh",_distance:"10",_oneway:"true"}},{mg:{_desired:"skg",_distance:"10",_oneway:"true"}},{mg:{_desired:"tdx",_distance:"10",_oneway:"true"}},{mg:{_desired:"tkg",_distance:"10",_oneway:"true"}},{mg:{_desired:"txy",_distance:"10",_oneway:"true"}},{mg:{_desired:"xmv",_distance:"10",_oneway:"true"}},{mg:{_desired:"xmw",_distance:"10",_oneway:"true"}},{mn:{_desired:"mvf",_distance:"10",_oneway:"true"}},{ms:{_desired:"bjn",_distance:"10",_oneway:"true"}},{ms:{_desired:"btj",_distance:"10",_oneway:"true"}},{ms:{_desired:"bve",_distance:"10",_oneway:"true"}},{ms:{_desired:"bvu",_distance:"10",_oneway:"true"}},{ms:{_desired:"coa",_distance:"10",_oneway:"true"}},{ms:{_desired:"dup",_distance:"10",_oneway:"true"}},{ms:{_desired:"hji",_distance:"10",_oneway:"true"}},{ms:{_desired:"id",_distance:"10",_oneway:"true"}},{ms:{_desired:"jak",_distance:"10",_oneway:"true"}},{ms:{_desired:"jax",_distance:"10",_oneway:"true"}},{ms:{_desired:"kvb",_distance:"10",_oneway:"true"}},{ms:{_desired:"kvr",_distance:"10",_oneway:"true"}},{ms:{_desired:"kxd",_distance:"10",_oneway:"true"}},{ms:{_desired:"lce",_distance:"10",_oneway:"true"}},{ms:{_desired:"lcf",_distance:"10",_oneway:"true"}},{ms:{_desired:"liw",_distance:"10",_oneway:"true"}},{ms:{_desired:"max",_distance:"10",_oneway:"true"}},{ms:{_desired:"meo",_distance:"10",_oneway:"true"}},{ms:{_desired:"mfa",_distance:"10",_oneway:"true"}},{ms:{_desired:"mfb",_distance:"10",_oneway:"true"}},{ms:{_desired:"min",_distance:"10",_oneway:"true"}},{ms:{_desired:"mqg",_distance:"10",_oneway:"true"}},{ms:{_desired:"msi",_distance:"10",_oneway:"true"}},{ms:{_desired:"mui",_distance:"10",_oneway:"true"}},{ms:{_desired:"orn",_distance:"10",_oneway:"true"}},{ms:{_desired:"ors",_distance:"10",_oneway:"true"}},{ms:{_desired:"pel",_distance:"10",_oneway:"true"}},{ms:{_desired:"pse",_distance:"10",_oneway:"true"}},{ms:{_desired:"tmw",_distance:"10",_oneway:"true"}},{ms:{_desired:"urk",_distance:"10",_oneway:"true"}},{ms:{_desired:"vkk",_distance:"10",_oneway:"true"}},{ms:{_desired:"vkt",_distance:"10",_oneway:"true"}},{ms:{_desired:"xmm",_distance:"10",_oneway:"true"}},{ms:{_desired:"zlm",_distance:"10",_oneway:"true"}},{ms:{_desired:"zmi",_distance:"10",_oneway:"true"}},{ne:{_desired:"dty",_distance:"10",_oneway:"true"}},{om:{_desired:"gax",_distance:"10",_oneway:"true"}},{om:{_desired:"hae",_distance:"10",_oneway:"true"}},{om:{_desired:"orc",_distance:"10",_oneway:"true"}},{or:{_desired:"spv",_distance:"10",_oneway:"true"}},{ps:{_desired:"pbt",_distance:"10",_oneway:"true"}},{ps:{_desired:"pst",_distance:"10",_oneway:"true"}},{qu:{_desired:"qub",_distance:"10",_oneway:"true"}},{qu:{_desired:"qud",_distance:"10",_oneway:"true"}},{qu:{_desired:"quf",_distance:"10",_oneway:"true"}},{qu:{_desired:"qug",_distance:"10",_oneway:"true"}},{qu:{_desired:"quh",_distance:"10",_oneway:"true"}},{qu:{_desired:"quk",_distance:"10",_oneway:"true"}},{qu:{_desired:"qul",_distance:"10",_oneway:"true"}},{qu:{_desired:"qup",_distance:"10",_oneway:"true"}},{qu:{_desired:"qur",_distance:"10",_oneway:"true"}},{qu:{_desired:"qus",_distance:"10",_oneway:"true"}},{qu:{_desired:"quw",_distance:"10",_oneway:"true"}},{qu:{_desired:"qux",_distance:"10",_oneway:"true"}},{qu:{_desired:"quy",_distance:"10",_oneway:"true"}},{qu:{_desired:"qva",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvc",_distance:"10",_oneway:"true"}},{qu:{_desired:"qve",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvh",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvi",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvj",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvl",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvm",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvn",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvo",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvp",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvs",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvw",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvz",_distance:"10",_oneway:"true"}},{qu:{_desired:"qwa",_distance:"10",_oneway:"true"}},{qu:{_desired:"qwc",_distance:"10",_oneway:"true"}},{qu:{_desired:"qwh",_distance:"10",_oneway:"true"}},{qu:{_desired:"qws",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxa",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxc",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxh",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxl",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxn",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxo",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxp",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxr",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxt",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxu",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxw",_distance:"10",_oneway:"true"}},{sc:{_desired:"sdc",_distance:"10",_oneway:"true"}},{sc:{_desired:"sdn",_distance:"10",_oneway:"true"}},{sc:{_desired:"sro",_distance:"10",_oneway:"true"}},{sq:{_desired:"aae",_distance:"10",_oneway:"true"}},{sq:{_desired:"aat",_distance:"10",_oneway:"true"}},{sq:{_desired:"aln",_distance:"10",_oneway:"true"}},{syr:{_desired:"aii",_distance:"10",_oneway:"true"}},{uz:{_desired:"uzs",_distance:"10",_oneway:"true"}},{yi:{_desired:"yih",_distance:"10",_oneway:"true"}},{zh:{_desired:"cdo",_distance:"10",_oneway:"true"}},{zh:{_desired:"cjy",_distance:"10",_oneway:"true"}},{zh:{_desired:"cpx",_distance:"10",_oneway:"true"}},{zh:{_desired:"czh",_distance:"10",_oneway:"true"}},{zh:{_desired:"czo",_distance:"10",_oneway:"true"}},{zh:{_desired:"gan",_distance:"10",_oneway:"true"}},{zh:{_desired:"hak",_distance:"10",_oneway:"true"}},{zh:{_desired:"hsn",_distance:"10",_oneway:"true"}},{zh:{_desired:"lzh",_distance:"10",_oneway:"true"}},{zh:{_desired:"mnp",_distance:"10",_oneway:"true"}},{zh:{_desired:"nan",_distance:"10",_oneway:"true"}},{zh:{_desired:"wuu",_distance:"10",_oneway:"true"}},{zh:{_desired:"yue",_distance:"10",_oneway:"true"}},{"*":{_desired:"*",_distance:"80"}},{"en-Latn":{_desired:"am-Ethi",_distance:"10",_oneway:"true"}},{"ru-Cyrl":{_desired:"az-Latn",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"bn-Beng",_distance:"10",_oneway:"true"}},{"zh-Hans":{_desired:"bo-Tibt",_distance:"10",_oneway:"true"}},{"ru-Cyrl":{_desired:"hy-Armn",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ka-Geor",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"km-Khmr",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"kn-Knda",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"lo-Laoo",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ml-Mlym",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"my-Mymr",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ne-Deva",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"or-Orya",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"pa-Guru",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ps-Arab",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"sd-Arab",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"si-Sinh",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ta-Taml",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"te-Telu",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ti-Ethi",_distance:"10",_oneway:"true"}},{"ru-Cyrl":{_desired:"tk-Latn",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ur-Arab",_distance:"10",_oneway:"true"}},{"ru-Cyrl":{_desired:"uz-Latn",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"yi-Hebr",_distance:"10",_oneway:"true"}},{"sr-Cyrl":{_desired:"sr-Latn",_distance:"5"}},{"zh-Hans":{_desired:"za-Latn",_distance:"10",_oneway:"true"}},{"zh-Hans":{_desired:"zh-Hani",_distance:"20",_oneway:"true"}},{"zh-Hant":{_desired:"zh-Hani",_distance:"20",_oneway:"true"}},{"ar-Arab":{_desired:"ar-Latn",_distance:"20",_oneway:"true"}},{"bn-Beng":{_desired:"bn-Latn",_distance:"20",_oneway:"true"}},{"gu-Gujr":{_desired:"gu-Latn",_distance:"20",_oneway:"true"}},{"hi-Deva":{_desired:"hi-Latn",_distance:"20",_oneway:"true"}},{"kn-Knda":{_desired:"kn-Latn",_distance:"20",_oneway:"true"}},{"ml-Mlym":{_desired:"ml-Latn",_distance:"20",_oneway:"true"}},{"mr-Deva":{_desired:"mr-Latn",_distance:"20",_oneway:"true"}},{"ta-Taml":{_desired:"ta-Latn",_distance:"20",_oneway:"true"}},{"te-Telu":{_desired:"te-Latn",_distance:"20",_oneway:"true"}},{"zh-Hans":{_desired:"zh-Latn",_distance:"20",_oneway:"true"}},{"ja-Jpan":{_desired:"ja-Latn",_distance:"5",_oneway:"true"}},{"ja-Jpan":{_desired:"ja-Hani",_distance:"5",_oneway:"true"}},{"ja-Jpan":{_desired:"ja-Hira",_distance:"5",_oneway:"true"}},{"ja-Jpan":{_desired:"ja-Kana",_distance:"5",_oneway:"true"}},{"ja-Jpan":{_desired:"ja-Hrkt",_distance:"5",_oneway:"true"}},{"ja-Hrkt":{_desired:"ja-Hira",_distance:"5",_oneway:"true"}},{"ja-Hrkt":{_desired:"ja-Kana",_distance:"5",_oneway:"true"}},{"ko-Kore":{_desired:"ko-Hani",_distance:"5",_oneway:"true"}},{"ko-Kore":{_desired:"ko-Hang",_distance:"5",_oneway:"true"}},{"ko-Kore":{_desired:"ko-Jamo",_distance:"5",_oneway:"true"}},{"ko-Hang":{_desired:"ko-Jamo",_distance:"5",_oneway:"true"}},{"*-*":{_desired:"*-*",_distance:"50"}},{"ar-*-$maghreb":{_desired:"ar-*-$maghreb",_distance:"4"}},{"ar-*-$!maghreb":{_desired:"ar-*-$!maghreb",_distance:"4"}},{"ar-*-*":{_desired:"ar-*-*",_distance:"5"}},{"en-*-$enUS":{_desired:"en-*-$enUS",_distance:"4"}},{"en-*-GB":{_desired:"en-*-$!enUS",_distance:"3"}},{"en-*-$!enUS":{_desired:"en-*-$!enUS",_distance:"4"}},{"en-*-*":{_desired:"en-*-*",_distance:"5"}},{"es-*-$americas":{_desired:"es-*-$americas",_distance:"4"}},{"es-*-$!americas":{_desired:"es-*-$!americas",_distance:"4"}},{"es-*-*":{_desired:"es-*-*",_distance:"5"}},{"pt-*-$americas":{_desired:"pt-*-$americas",_distance:"4"}},{"pt-*-$!americas":{_desired:"pt-*-$!americas",_distance:"4"}},{"pt-*-*":{_desired:"pt-*-*",_distance:"5"}},{"zh-Hant-$cnsar":{_desired:"zh-Hant-$cnsar",_distance:"4"}},{"zh-Hant-$!cnsar":{_desired:"zh-Hant-$!cnsar",_distance:"4"}},{"zh-Hant-*":{_desired:"zh-Hant-*",_distance:"5"}},{"*-*-*":{_desired:"*-*-*",_distance:"4"}}]}}},x={"001":["001","001-status-grouping","002","005","009","011","013","014","015","017","018","019","021","029","030","034","035","039","053","054","057","061","142","143","145","150","151","154","155","AC","AD","AE","AF","AG","AI","AL","AM","AO","AQ","AR","AS","AT","AU","AW","AX","AZ","BA","BB","BD","BE","BF","BG","BH","BI","BJ","BL","BM","BN","BO","BQ","BR","BS","BT","BV","BW","BY","BZ","CA","CC","CD","CF","CG","CH","CI","CK","CL","CM","CN","CO","CP","CQ","CR","CU","CV","CW","CX","CY","CZ","DE","DG","DJ","DK","DM","DO","DZ","EA","EC","EE","EG","EH","ER","ES","ET","EU","EZ","FI","FJ","FK","FM","FO","FR","GA","GB","GD","GE","GF","GG","GH","GI","GL","GM","GN","GP","GQ","GR","GS","GT","GU","GW","GY","HK","HM","HN","HR","HT","HU","IC","ID","IE","IL","IM","IN","IO","IQ","IR","IS","IT","JE","JM","JO","JP","KE","KG","KH","KI","KM","KN","KP","KR","KW","KY","KZ","LA","LB","LC","LI","LK","LR","LS","LT","LU","LV","LY","MA","MC","MD","ME","MF","MG","MH","MK","ML","MM","MN","MO","MP","MQ","MR","MS","MT","MU","MV","MW","MX","MY","MZ","NA","NC","NE","NF","NG","NI","NL","NO","NP","NR","NU","NZ","OM","PA","PE","PF","PG","PH","PK","PL","PM","PN","PR","PS","PT","PW","PY","QA","QO","RE","RO","RS","RU","RW","SA","SB","SC","SD","SE","SG","SH","SI","SJ","SK","SL","SM","SN","SO","SR","SS","ST","SV","SX","SY","SZ","TA","TC","TD","TF","TG","TH","TJ","TK","TL","TM","TN","TO","TR","TT","TV","TW","TZ","UA","UG","UM","UN","US","UY","UZ","VA","VC","VE","VG","VI","VN","VU","WF","WS","XK","YE","YT","ZA","ZM","ZW"],"002":["002","002-status-grouping","011","014","015","017","018","202","AO","BF","BI","BJ","BW","CD","CF","CG","CI","CM","CV","DJ","DZ","EA","EG","EH","ER","ET","GA","GH","GM","GN","GQ","GW","IC","IO","KE","KM","LR","LS","LY","MA","MG","ML","MR","MU","MW","MZ","NA","NE","NG","RE","RW","SC","SD","SH","SL","SN","SO","SS","ST","SZ","TD","TF","TG","TN","TZ","UG","YT","ZA","ZM","ZW"],"003":["003","013","021","029","AG","AI","AW","BB","BL","BM","BQ","BS","BZ","CA","CR","CU","CW","DM","DO","GD","GL","GP","GT","HN","HT","JM","KN","KY","LC","MF","MQ","MS","MX","NI","PA","PM","PR","SV","SX","TC","TT","US","VC","VG","VI"],"005":["005","AR","BO","BR","BV","CL","CO","EC","FK","GF","GS","GY","PE","PY","SR","UY","VE"],"009":["009","053","054","057","061","AC","AQ","AS","AU","CC","CK","CP","CX","DG","FJ","FM","GU","HM","KI","MH","MP","NC","NF","NR","NU","NZ","PF","PG","PN","PW","QO","SB","TA","TK","TO","TV","UM","VU","WF","WS"],"011":["011","BF","BJ","CI","CV","GH","GM","GN","GW","LR","ML","MR","NE","NG","SH","SL","SN","TG"],"013":["013","BZ","CR","GT","HN","MX","NI","PA","SV"],"014":["014","BI","DJ","ER","ET","IO","KE","KM","MG","MU","MW","MZ","RE","RW","SC","SO","SS","TF","TZ","UG","YT","ZM","ZW"],"015":["015","DZ","EA","EG","EH","IC","LY","MA","SD","TN"],"017":["017","AO","CD","CF","CG","CM","GA","GQ","ST","TD"],"018":["018","BW","LS","NA","SZ","ZA"],"019":["003","005","013","019","019-status-grouping","021","029","419","AG","AI","AR","AW","BB","BL","BM","BO","BQ","BR","BS","BV","BZ","CA","CL","CO","CR","CU","CW","DM","DO","EC","FK","GD","GF","GL","GP","GS","GT","GY","HN","HT","JM","KN","KY","LC","MF","MQ","MS","MX","NI","PA","PE","PM","PR","PY","SR","SV","SX","TC","TT","US","UY","VC","VE","VG","VI"],"021":["021","BM","CA","GL","PM","US"],"029":["029","AG","AI","AW","BB","BL","BQ","BS","CU","CW","DM","DO","GD","GP","HT","JM","KN","KY","LC","MF","MQ","MS","PR","SX","TC","TT","VC","VG","VI"],"030":["030","CN","HK","JP","KP","KR","MN","MO","TW"],"034":["034","AF","BD","BT","IN","IR","LK","MV","NP","PK"],"035":["035","BN","ID","KH","LA","MM","MY","PH","SG","TH","TL","VN"],"039":["039","AD","AL","BA","ES","GI","GR","HR","IT","ME","MK","MT","PT","RS","SI","SM","VA","XK"],"053":["053","AU","CC","CX","HM","NF","NZ"],"054":["054","FJ","NC","PG","SB","VU"],"057":["057","FM","GU","KI","MH","MP","NR","PW","UM"],"061":["061","AS","CK","NU","PF","PN","TK","TO","TV","WF","WS"],142:["030","034","035","142","143","145","AE","AF","AM","AZ","BD","BH","BN","BT","CN","CY","GE","HK","ID","IL","IN","IQ","IR","JO","JP","KG","KH","KP","KR","KW","KZ","LA","LB","LK","MM","MN","MO","MV","MY","NP","OM","PH","PK","PS","QA","SA","SG","SY","TH","TJ","TL","TM","TR","TW","UZ","VN","YE"],143:["143","KG","KZ","TJ","TM","UZ"],145:["145","AE","AM","AZ","BH","CY","GE","IL","IQ","JO","KW","LB","OM","PS","QA","SA","SY","TR","YE"],150:["039","150","151","154","155","AD","AL","AT","AX","BA","BE","BG","BY","CH","CQ","CZ","DE","DK","EE","ES","FI","FO","FR","GB","GG","GI","GR","HR","HU","IE","IM","IS","IT","JE","LI","LT","LU","LV","MC","MD","ME","MK","MT","NL","NO","PL","PT","RO","RS","RU","SE","SI","SJ","SK","SM","UA","VA","XK"],151:["151","BG","BY","CZ","HU","MD","PL","RO","RU","SK","UA"],154:["154","AX","CQ","DK","EE","FI","FO","GB","GG","IE","IM","IS","JE","LT","LV","NO","SE","SJ"],155:["155","AT","BE","CH","DE","FR","LI","LU","MC","NL"],202:["011","014","017","018","202","AO","BF","BI","BJ","BW","CD","CF","CG","CI","CM","CV","DJ","ER","ET","GA","GH","GM","GN","GQ","GW","IO","KE","KM","LR","LS","MG","ML","MR","MU","MW","MZ","NA","NE","NG","RE","RW","SC","SH","SL","SN","SO","SS","ST","SZ","TD","TF","TG","TZ","UG","YT","ZA","ZM","ZW"],419:["005","013","029","419","AG","AI","AR","AW","BB","BL","BO","BQ","BR","BS","BV","BZ","CL","CO","CR","CU","CW","DM","DO","EC","FK","GD","GF","GP","GS","GT","GY","HN","HT","JM","KN","KY","LC","MF","MQ","MS","MX","NI","PA","PE","PR","PY","SR","SV","SX","TC","TT","UY","VC","VE","VG","VI"],EU:["AT","BE","BG","CY","CZ","DE","DK","EE","ES","EU","FI","FR","GR","HR","HU","IE","IT","LT","LU","LV","MT","NL","PL","PT","RO","SE","SI","SK"],EZ:["AT","BE","CY","DE","EE","ES","EZ","FI","FR","GR","IE","IT","LT","LU","LV","MT","NL","PT","SI","SK"],QO:["AC","AQ","CP","DG","QO","TA"],UN:["AD","AE","AF","AG","AL","AM","AO","AR","AT","AU","AZ","BA","BB","BD","BE","BF","BG","BH","BI","BJ","BN","BO","BR","BS","BT","BW","BY","BZ","CA","CD","CF","CG","CH","CI","CL","CM","CN","CO","CR","CU","CV","CY","CZ","DE","DJ","DK","DM","DO","DZ","EC","EE","EG","ER","ES","ET","FI","FJ","FM","FR","GA","GB","GD","GE","GH","GM","GN","GQ","GR","GT","GW","GY","HN","HR","HT","HU","ID","IE","IL","IN","IQ","IR","IS","IT","JM","JO","JP","KE","KG","KH","KI","KM","KN","KP","KR","KW","KZ","LA","LB","LC","LI","LK","LR","LS","LT","LU","LV","LY","MA","MC","MD","ME","MG","MH","MK","ML","MM","MN","MR","MT","MU","MV","MW","MX","MY","MZ","NA","NE","NG","NI","NL","NO","NP","NR","NZ","OM","PA","PE","PG","PH","PK","PL","PT","PW","PY","QA","RO","RS","RU","RW","SA","SB","SC","SD","SE","SG","SI","SK","SL","SM","SN","SO","SR","SS","ST","SV","SY","SZ","TD","TG","TH","TJ","TL","TM","TN","TO","TR","TT","TV","TZ","UA","UG","UN","US","UY","UZ","VC","VE","VN","VU","WS","YE","ZA","ZM","ZW"]},H=/-u(?:-[0-9a-z]{2,8})+/gi;function l(a,r,n){if(n===void 0&&(n=Error),!a)throw new n(r)}var W=838,E;function Y(){var a,r;if(!E){var n=(r=(a=T.supplemental.languageMatching["written-new"][0])===null||a===void 0?void 0:a.paradigmLocales)===null||r===void 0?void 0:r._locales.split(" "),e=T.supplemental.languageMatching["written-new"].slice(1,5),i=T.supplemental.languageMatching["written-new"].slice(5),t=i.map(function(_){var d=Object.keys(_)[0],s=_[d];return{supported:d,desired:s._desired,distance:+s._distance,oneway:s.oneway==="true"}},{});E={matches:t,matchVariables:e.reduce(function(_,d){var s=Object.keys(d)[0],u=d[s];return _[s.slice(1)]=u._value.split("+"),_},{}),paradigmLocales:A(A([],n,!0),n.map(function(_){return new Intl.Locale(_.replace(/_/g,"-")).maximize().toString()}),!0)}}return E}function g(a,r,n){var e=r.split("-"),i=e[0],t=e[1],_=e[2],d=!0;if(_&&_[0]==="$"){var s=_[1]!=="!",u=s?n[_.slice(1)]:n[_.slice(2)],c=u.map(function(o){return x[o]||[o]}).reduce(function(o,M){return A(A([],o,!0),M,!0)},[]);d&&(d=c.indexOf(a.region||"")>-1==s)}else d&&(d=a.region?_==="*"||_===a.region:!0);return d&&(d=a.script?t==="*"||t===a.script:!0),d&&(d=a.language?i==="*"||i===a.language:!0),d}function R(a){return[a.language,a.script,a.region].filter(Boolean).join("-")}function h(a,r,n){for(var e=0,i=n.matches;e<i.length;e++){var t=i[e],_=g(a,t.desired,n.matchVariables)&&g(r,t.supported,n.matchVariables);if(!t.oneway&&!_&&(_=g(a,t.supported,n.matchVariables)&&g(r,t.desired,n.matchVariables)),_){var d=t.distance*10;return n.paradigmLocales.indexOf(R(a))>-1!=n.paradigmLocales.indexOf(R(r))>-1?d-1:d}}throw new Error("No matching distance found")}function J(a,r){var n=new Intl.Locale(a).maximize(),e=new Intl.Locale(r).maximize(),i={language:n.language,script:n.script||"",region:n.region||""},t={language:e.language,script:e.script||"",region:e.region||""},_=0,d=Y();return i.language!==t.language&&(_+=h({language:n.language,script:"",region:""},{language:e.language,script:"",region:""},d)),i.script!==t.script&&(_+=h({language:n.language,script:i.script,region:""},{language:e.language,script:t.script,region:""},d)),i.region!==t.region&&(_+=h(i,t,d)),_}function Q(a,r,n){n===void 0&&(n=W);var e=1/0,i={matchedDesiredLocale:"",distances:{}};return a.forEach(function(t,_){i.distances[t]||(i.distances[t]={}),r.forEach(function(d){var s=J(t,d)+0+_*40;i.distances[t][d]=s,s<e&&(e=s,i.matchedDesiredLocale=t,i.matchedSupportedLocale=d)})}),e>=n&&(i.matchedDesiredLocale=void 0,i.matchedSupportedLocale=void 0),i}function j(a,r,n){var e,i,t=[],_=r.reduce(function(s,u){var c=u.replace(H,"");return t.push(c),s[c]=u,s},{}),d=Q(t,a);return d.matchedSupportedLocale&&d.matchedDesiredLocale&&(e=d.matchedSupportedLocale,i=_[d.matchedDesiredLocale].slice(d.matchedDesiredLocale.length)||void 0),e?{locale:e,extension:i}:{locale:n()}}function $(a,r){var n=r.toLowerCase();l(a!==void 0,"ukey must be defined");var e=n;return e}function K(a){return Intl.getCanonicalLocales(a)[0]}function X(a,r,n){l(a.indexOf("-u-")===-1,"Expected locale to not have a Unicode locale extension");for(var e="-u",i=0,t=r;i<t.length;i++){var _=t[i];e+="-".concat(_)}for(var d=0,s=n;d<s.length;d++){var u=s[d],c=u.key,o=u.value;e+="-".concat(c),o!==""&&(e+="-".concat(o))}if(e==="-u")return K(a);var M=a.indexOf("-x-"),L;if(M===-1)L=a+e;else{var m=a.slice(0,M),G=a.slice(M);L=m+e+G}return K(L)}function ee(a,r){for(var n=r;;){if(a.indexOf(n)>-1)return n;var e=n.lastIndexOf("-");if(!~e)return;e>=2&&n[e-2]==="-"&&(e-=2),n=n.slice(0,e)}}function ae(a,r,n){for(var e={locale:""},i=0,t=r;i<t.length;i++){var _=t[i],d=_.replace(H,""),s=ee(a,d);if(s)return e.locale=s,_!==d&&(e.extension=_.slice(d.length,_.length)),e}return e.locale=n(),e}function ne(a){l(a===a.toLowerCase(),"Expected extension to be lowercase"),l(a.slice(0,3)==="-u-","Expected extension to be a Unicode locale extension");for(var r=[],n=[],e,i=a.length,t=3;t<i;){var _=a.indexOf("-",t),d=void 0;_===-1?d=i-t:d=_-t;var s=a.slice(t,t+d);l(d>=2,"Expected a subtag to have at least 2 characters"),e===void 0&&d!=2?r.indexOf(s)===-1&&r.push(s):d===2?(e={key:s,value:""},n.find(function(u){return u.key===(e==null?void 0:e.key)})===void 0&&n.push(e)):(e==null?void 0:e.value)===""?e.value=s:(l(e!==void 0,"Expected keyword to be defined"),e.value+="-"+s),t+=d+1}return{attributes:r,keywords:n}}function de(a,r,n,e,i,t){var _,d=n.localeMatcher,s;d==="lookup"?s=ae(Array.from(a),r,t):s=j(Array.from(a),r,t),s==null&&(s={locale:t(),extension:""});var u=s.locale,c=i[u],o={locale:"en",dataLocale:u},M,L;s.extension?(M=ne(s.extension),L=M.keywords):L=[];for(var m=[],G=function(f){var S=(_=c==null?void 0:c[f])!==null&&_!==void 0?_:[];l(Array.isArray(S),"keyLocaleData for ".concat(f," must be an array"));var w=S[0];l(w===void 0||typeof w=="string","value must be a string or undefined");var C=void 0,I=L.find(function(q){return q.key===f});if(I){var p=I.value;p!==""?S.indexOf(p)>-1&&(w=p,C={key:f,value:w}):S.indexOf("true")>-1&&(w="true",C={key:f,value:w})}var y=n[f];if(l(y==null||typeof y=="string","optionsValue must be a string or undefined"),typeof y=="string"){var U=f.toLowerCase();y=$(U,y),y===""&&(y="true")}y!==w&&S.indexOf(y)>-1&&(w=y,C=void 0),C&&m.push(C),o[f]=w},v=0,B=e;v<B.length;v++){var D=B[v];G(D)}var N=[];return m.length>0&&(N=[],u=X(u,N,m)),o.locale=u,o}function re(a,r,n,e){return de(r,z(a),{localeMatcher:"best fit"},[],{},function(){return n}).locale}const te="en";function _e(a,r){const n=a?[a,...r]:r;function e(i){var _;const t=i.map(d=>d.locale);try{const d=re(n,t,te),s=i.find(u=>u.locale===d);return(s==null?void 0:s.value)??((_=i[0])==null?void 0:_.value)??null}catch(d){return console.error("Error during locale negotiation:",d),null}}return e}const ie=a=>{const r=a.data.me,n=r?{id:r.id,email:r.email,role:r.role}:null;r||P(),r&&O(n);const e=se(a),i=e?`/${e}`:"",t=e??a.data.preferredLocale??"en";return{routeLocale:e,preferredLocale:a.data.preferredLocale,locale:t,user:n,toLocaleHref(_){return`${i}${_}`},getAppropriateLocalization:_e(e,a.data.userLocales)}};function se(a){const r=k.WebsiteLocaleSchema.safeParse(a.params.locale);return r.success?r.data:null}const le=Object.freeze(Object.defineProperty({__proto__:null,load:ie},Symbol.toStringTag,{value:"Module"}));function Me(a,r){var n=b(),e=F(n);Z(e,()=>r.children),V(a,n)}export{Me as component,le as universal};
