import"../chunks/Bzak7iHL.js";import{p as _,f as j,t as C,b as I,c as U,s as a,d as s,r as i}from"../chunks/RHWQbow4.js";import{s as c}from"../chunks/BlWcudmi.js";var M=j('<div class="admin-dashboard svelte-1ch1yjx"><div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"><h1 class="h2">Admin Dashboard</h1></div> <div class="row"><div class="col-md-6 col-lg-4 mb-4"><div class="card h-100 svelte-1ch1yjx"><div class="card-body d-flex flex-column"><div class="d-flex align-items-center mb-3"><div class="icon-circle bg-primary text-white me-3 svelte-1ch1yjx"><i class="bi bi-envelope"></i></div> <div><h5 class="card-title mb-0">User Invites</h5> <small class="text-muted">Manage user invitations</small></div></div> <p class="card-text flex-grow-1">Create and manage user invitations. View all sent invites and their status.</p> <a href="/admin/invites" class="btn btn-primary"><i class="bi bi-arrow-right me-1"></i> Manage Invites</a></div></div></div> <div class="col-md-6 col-lg-4 mb-4"><div class="card h-100 svelte-1ch1yjx"><div class="card-body d-flex flex-column"><div class="d-flex align-items-center mb-3"><div class="icon-circle bg-success text-white me-3 svelte-1ch1yjx"><i class="bi bi-people"></i></div> <div><h5 class="card-title mb-0">Users</h5> <small class="text-muted">User management</small></div></div> <p class="card-text flex-grow-1">View and manage user accounts, roles, and permissions.</p> <button class="btn btn-outline-secondary" disabled><i class="bi bi-arrow-right me-1"></i> Coming Soon</button></div></div></div> <div class="col-md-6 col-lg-4 mb-4"><div class="card h-100 svelte-1ch1yjx"><div class="card-body d-flex flex-column"><div class="d-flex align-items-center mb-3"><div class="icon-circle bg-info text-white me-3 svelte-1ch1yjx"><i class="bi bi-house"></i></div> <div><h5 class="card-title mb-0">Communes</h5> <small class="text-muted">Commune management</small></div></div> <p class="card-text flex-grow-1">Manage communes, memberships, and community settings.</p> <button class="btn btn-outline-secondary" disabled><i class="bi bi-arrow-right me-1"></i> Coming Soon</button></div></div></div></div> <div class="row mt-4"><div class="col-12"><div class="card svelte-1ch1yjx"><div class="card-header"><h5 class="mb-0"><i class="bi bi-info-circle me-2"></i> System Information</h5></div> <div class="card-body"><div class="row"><div class="col-md-6"><p><strong>Logged in as:</strong> </p> <p><strong>Role:</strong> <span class="badge bg-primary"> </span></p></div> <div class="col-md-6"><p><strong>User ID:</strong> <code> </code></p></div></div></div></div></div></div></div>');function A(u,e){_(e,!0);var d=M(),r=a(s(d),4),v=s(r),n=s(v),o=a(s(n),2),m=s(o),t=s(m),l=s(t),f=a(s(l));i(l);var b=a(l,2),g=a(s(b),2),y=s(g,!0);i(g),i(b),i(t);var h=a(t,2),p=s(h),x=a(s(p),2),w=s(x,!0);i(x),i(p),i(h),i(m),i(o),i(n),i(v),i(r),i(d),C(()=>{c(f,` ${e.data.me.email??""}`),c(y,e.data.me.role),c(w,e.data.me.id)}),I(u,d),U()}export{A as component};
