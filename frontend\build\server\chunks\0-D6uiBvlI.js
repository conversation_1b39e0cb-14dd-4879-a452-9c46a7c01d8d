const index = 0;
let component_cache;
const component = async () => component_cache ??= (await import('./_layout.svelte-pZNrwI1a.js')).default;
const imports = ["_app/immutable/nodes/0.B6PDugDT.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/BdpLTtcP.js"];
const stylesheets = ["_app/immutable/assets/0.DOCStFsm.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=0-D6uiBvlI.js.map
