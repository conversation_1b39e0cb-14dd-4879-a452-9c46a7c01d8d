const index = 1;
let component_cache;
const component = async () => component_cache ??= (await import('./error.svelte-Dw7V3w_a.js')).default;
const imports = ["_app/immutable/nodes/1.OxXjcIzf.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/BiLRrsV0.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/hBp8sf9T.js","_app/immutable/chunks/B0MzmgHo.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CKnuo8tw.js","_app/immutable/chunks/CYgJF_JY.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=1-C_9kxUPw.js.map
