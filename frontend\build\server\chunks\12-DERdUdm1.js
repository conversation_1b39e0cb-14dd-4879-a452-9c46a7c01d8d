import { e as error } from './index-CT944rr3.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import './schema-CmMg_B_X.js';
import './current-user-BM0W6LNm.js';

const load = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();
  const [
    me,
    [commune],
    members
  ] = await Promise.all([
    api.user.me.get({ fetch, skipInterceptor: true }).catch(() => null),
    api.commune.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
    api.commune.member.list.get({ communeId: params.id }, { fetch, ctx: { url } })
  ]);
  if (!commune) {
    throw error(404, "Commune not found");
  }
  const isLoggedIn = !!me;
  const isAdmin = me?.role === "admin";
  const isHeadMember = isLoggedIn && commune.headMember.actorType === "user" && commune.headMember.actorId === me.id;
  const isMember = isLoggedIn && members.some(
    (member) => member.actorType === "user" && member.actorId === me.id && !member.deletedAt
  );
  let hasPendingJoinRequest = false;
  if (isLoggedIn && !isMember) {
    const joinRequests = await api.commune.joinRequest.list.get(
      {},
      { fetch, ctx: { url } }
    );
    hasPendingJoinRequest = joinRequests.some(
      ({ communeId, status }) => communeId === params.id && status === "pending"
    );
  }
  return {
    commune,
    members,
    userPermissions: {
      isLoggedIn,
      isAdmin,
      isHeadMember,
      isMember,
      canInvite: isAdmin || isHeadMember,
      canRequestJoin: isLoggedIn && !isMember && !hasPendingJoinRequest,
      hasPendingJoinRequest
    }
  };
};

var _page_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 12;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-Ca1oPZ3L.js')).default;
const universal_id = "src/routes/[[locale]]/(index)/communes/[id]/+page.ts";
const imports = ["_app/immutable/nodes/12.QFdKNsZH.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CGZ87yZq.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/CaC9IHEK.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/hBp8sf9T.js","_app/immutable/chunks/B0MzmgHo.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CKnuo8tw.js","_app/immutable/chunks/iI8NM7bJ.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/CR3e0W7L.js","_app/immutable/chunks/B5DcI8qy.js","_app/immutable/chunks/BiLRrsV0.js","_app/immutable/chunks/CL12WlkV.js","_app/immutable/chunks/C_wziyCN.js"];
const stylesheets = ["_app/immutable/assets/create-post-modal.BRelZfpq.css","_app/immutable/assets/12.DBKDFuDx.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, _page_ts as universal, universal_id };
//# sourceMappingURL=12-DERdUdm1.js.map
