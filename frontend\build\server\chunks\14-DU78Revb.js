import { e as error } from './index-CT944rr3.js';
import { a as consts_exports } from './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import './schema-CmMg_B_X.js';

const load = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();
  const [
    user,
    [commune]
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.commune.list.get({ ids: [params.id] }, { fetch, ctx: { url } })
  ]);
  if (!commune) {
    throw error(404, "Commune not found");
  }
  const isAdmin = user?.role === "admin";
  const isHeadMember = user && commune.headMember.actorType === "user" && commune.headMember.actorId === user.id;
  if (!isAdmin && !isHeadMember) {
    throw new Error("Access denied: You must be an admin or commune head to view join requests");
  }
  const joinRequests = await api.commune.joinRequest.list.get(
    { communeId: params.id },
    { fetch, ctx: { url } }
  );
  const users = joinRequests.length ? await api.user.list.get(
    { ids: joinRequests.map(({ userId }) => userId) },
    { fetch, ctx: { url } }
  ) : [];
  const userMap = new Map(users.map((user2) => [user2.id, user2]));
  const joinRequestsWithUserDetails = joinRequests.map((joinRequest) => ({
    ...joinRequest,
    user: userMap.get(joinRequest.userId)
  }));
  return {
    commune,
    joinRequests: joinRequestsWithUserDetails,
    isHasMoreJoinRequests: joinRequests.length === consts_exports.PAGE_SIZE,
    userPermissions: {
      isAdmin,
      isHeadMember,
      canManageJoinRequests: isAdmin || isHeadMember
    }
  };
};

var _page_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 14;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-CEFEvs3p.js')).default;
const universal_id = "src/routes/[[locale]]/(index)/communes/[id]/join-requests/+page.ts";
const imports = ["_app/immutable/nodes/14.DoKJFkAg.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CGZ87yZq.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/B5DcI8qy.js","_app/immutable/chunks/B0MzmgHo.js","_app/immutable/chunks/CL12WlkV.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets, _page_ts as universal, universal_id };
//# sourceMappingURL=14-DU78Revb.js.map
