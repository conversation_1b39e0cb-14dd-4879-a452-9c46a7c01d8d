{"version": 3, "file": "14-DU78Revb.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/communes/_id_/join-requests/_page.ts.js", "../../../.svelte-kit/adapter-node/nodes/14.js"], "sourcesContent": ["import { error } from \"@sveltejs/kit\";\nimport { a as consts_exports } from \"../../../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../../../chunks/acrpc.js\";\nconst load = async ({ fetch, params, url }) => {\n  const { fetcher: api } = getClient();\n  const [\n    user,\n    [commune]\n  ] = await Promise.all([\n    api.user.me.get({ fetch, ctx: { url } }),\n    api.commune.list.get({ ids: [params.id] }, { fetch, ctx: { url } })\n  ]);\n  if (!commune) {\n    throw error(404, \"Commune not found\");\n  }\n  const isAdmin = user?.role === \"admin\";\n  const isHeadMember = user && commune.headMember.actorType === \"user\" && commune.headMember.actorId === user.id;\n  if (!isAdmin && !isHeadMember) {\n    throw new Error(\"Access denied: You must be an admin or commune head to view join requests\");\n  }\n  const joinRequests = await api.commune.joinRequest.list.get(\n    { communeId: params.id },\n    { fetch, ctx: { url } }\n  );\n  const users = joinRequests.length ? await api.user.list.get(\n    { ids: joinRequests.map(({ userId }) => userId) },\n    { fetch, ctx: { url } }\n  ) : [];\n  const userMap = new Map(users.map((user2) => [user2.id, user2]));\n  const joinRequestsWithUserDetails = joinRequests.map((joinRequest) => ({\n    ...joinRequest,\n    user: userMap.get(joinRequest.userId)\n  }));\n  return {\n    commune,\n    joinRequests: joinRequestsWithUserDetails,\n    isHasMoreJoinRequests: joinRequests.length === consts_exports.PAGE_SIZE,\n    userPermissions: {\n      isAdmin,\n      isHeadMember,\n      canManageJoinRequests: isAdmin || isHeadMember\n    }\n  };\n};\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/__locale__/(index)/communes/_id_/join-requests/_page.ts.js';\n\nexport const index = 14;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/communes/_id_/join-requests/_page.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/[[locale]]/(index)/communes/[id]/join-requests/+page.ts\";\nexport const imports = [\"_app/immutable/nodes/14.DoKJFkAg.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/CGZ87yZq.js\",\"_app/immutable/chunks/Bzak7iHL.js\",\"_app/immutable/chunks/DeAm3Eed.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CkTdM00m.js\",\"_app/immutable/chunks/CtoItwj4.js\",\"_app/immutable/chunks/Dnfvvefi.js\",\"_app/immutable/chunks/BdpLTtcP.js\",\"_app/immutable/chunks/Cxg-bych.js\",\"_app/immutable/chunks/q36Eg1F8.js\",\"_app/immutable/chunks/B5DcI8qy.js\",\"_app/immutable/chunks/B0MzmgHo.js\",\"_app/immutable/chunks/CL12WlkV.js\"];\nexport const stylesheets = [];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK;AAC/C,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM;AACR,IAAI,IAAI;AACR,IAAI,CAAC,OAAO;AACZ,GAAG,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACxB,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5C,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;AACtE,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,mBAAmB,CAAC;AACzC,EAAE;AACF,EAAE,MAAM,OAAO,GAAG,IAAI,EAAE,IAAI,KAAK,OAAO;AACxC,EAAE,MAAM,YAAY,GAAG,IAAI,IAAI,OAAO,CAAC,UAAU,CAAC,SAAS,KAAK,MAAM,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,KAAK,IAAI,CAAC,EAAE;AAChH,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,YAAY,EAAE;AACjC,IAAI,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC;AAChG,EAAE;AACF,EAAE,MAAM,YAAY,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG;AAC7D,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,EAAE;AAC5B,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE;AACzB,GAAG;AACH,EAAE,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AAC7D,IAAI,EAAE,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,MAAM,CAAC,EAAE;AACrD,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE;AACzB,GAAG,GAAG,EAAE;AACR,EAAE,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;AAClE,EAAE,MAAM,2BAA2B,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,WAAW,MAAM;AACzE,IAAI,GAAG,WAAW;AAClB,IAAI,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM;AACxC,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,YAAY,EAAE,2BAA2B;AAC7C,IAAI,qBAAqB,EAAE,YAAY,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS;AAC3E,IAAI,eAAe,EAAE;AACrB,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,qBAAqB,EAAE,OAAO,IAAI;AACxC;AACA,GAAG;AACH,CAAC;;;;;;;ACzCW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAiF,CAAC,EAAE;AAE/I,MAAC,YAAY,GAAG;AAChB,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACjnB,MAAC,WAAW,GAAG;AACf,MAAC,KAAK,GAAG;;;;"}