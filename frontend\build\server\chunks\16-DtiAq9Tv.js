const index = 16;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-DWL71IfN.js')).default;
const imports = ["_app/immutable/nodes/16.CiA_LQTK.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/C_sRNQCS.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/CaC9IHEK.js","_app/immutable/chunks/q36Eg1F8.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=16-DtiAq9Tv.js.map
