const index = 18;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-dZF-JQpp.js')).default;
const imports = ["_app/immutable/nodes/18.DLR-QXod.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/CaC9IHEK.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=18-BATQg0v8.js.map
