import { e as error } from './index-CT944rr3.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import './schema-CmMg_B_X.js';
import './current-user-BM0W6LNm.js';

const load = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();
  const [
    [user],
    note,
    summary
  ] = await Promise.all([
    api.user.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
    api.user.note.get({ userId: params.id }, { fetch, ctx: { url } }),
    api.rating.summary.get({ userId: params.id }, { fetch, ctx: { url } })
  ]);
  if (!user) {
    throw error(404, "User not found");
  }
  return {
    user,
    userNote: note.text,
    ratingSummary: summary
  };
};

var _page_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 21;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-CntOVWTa.js')).default;
const universal_id = "src/routes/[[locale]]/(index)/users/[id]/+page.ts";
const imports = ["_app/immutable/nodes/21.zXJt1skS.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CGZ87yZq.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/CaC9IHEK.js","_app/immutable/chunks/B0MzmgHo.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/DGxS2cwR.js"];
const stylesheets = ["_app/immutable/assets/21.BU79Yo5H.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, _page_ts as universal, universal_id };
//# sourceMappingURL=21-BFiKGcbC.js.map
