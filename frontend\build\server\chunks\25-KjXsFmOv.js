import { a as consts_exports } from './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import './index-CT944rr3.js';
import './schema-CmMg_B_X.js';

const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  await api.user.me.get({ fetch, skipInterceptor: true }).catch(() => null);
  const [posts, lenses] = await Promise.all([
    api.reactor.post.list.get({ lensId: null }, { fetch, ctx: { url } }),
    api.reactor.lens.list.get({ fetch, ctx: { url } })
  ]);
  return {
    posts,
    lenses,
    isHasMorePosts: posts.length === consts_exports.PAGE_SIZE
  };
};

var _page_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 25;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-CP0UqosS.js')).default;
const universal_id = "src/routes/[[locale]]/reactor/+page.ts";
const imports = ["_app/immutable/nodes/25.CHbMxRQ3.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CGZ87yZq.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/B5DcI8qy.js","_app/immutable/chunks/iI8NM7bJ.js","_app/immutable/chunks/CKnuo8tw.js","_app/immutable/chunks/B0MzmgHo.js","_app/immutable/chunks/CR3e0W7L.js","_app/immutable/chunks/BiLRrsV0.js","_app/immutable/chunks/B-tQx-ev.js","_app/immutable/chunks/C_sRNQCS.js","_app/immutable/chunks/CaC9IHEK.js","_app/immutable/chunks/Np2weedy.js"];
const stylesheets = ["_app/immutable/assets/create-post-modal.BRelZfpq.css","_app/immutable/assets/right-menu.BCyxSBRm.css","_app/immutable/assets/25.D23-cVSR.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, _page_ts as universal, universal_id };
//# sourceMappingURL=25-KjXsFmOv.js.map
