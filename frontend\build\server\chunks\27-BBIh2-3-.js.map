{"version": 3, "file": "27-BBIh2-3-.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/reactor/communities/_id_/_page.ts.js", "../../../.svelte-kit/adapter-node/nodes/27.js"], "sourcesContent": ["import { error } from \"@sveltejs/kit\";\nimport { g as getClient } from \"../../../../../../chunks/acrpc.js\";\nconst load = async ({ fetch, params, url }) => {\n  const { fetcher: api } = getClient();\n  const [\n    me,\n    [community]\n  ] = await Promise.all([\n    api.user.me.get({ fetch, skipInterceptor: true }).catch(() => null),\n    api.reactor.community.list.get({ ids: [params.id] }, { fetch, ctx: { url } })\n  ]);\n  if (!community) {\n    throw error(404, \"Community not found\");\n  }\n  const canEdit = me && (me.role === \"admin\" || me.id === community.headUser.id);\n  return {\n    me,\n    community,\n    canEdit\n  };\n};\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/__locale__/reactor/communities/_id_/_page.ts.js';\n\nexport const index = 27;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/__locale__/reactor/communities/_id_/_page.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/[[locale]]/reactor/communities/[id]/+page.ts\";\nexport const imports = [\"_app/immutable/nodes/27.jsdv_DM_.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/CGZ87yZq.js\",\"_app/immutable/chunks/Bzak7iHL.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CtoItwj4.js\",\"_app/immutable/chunks/BdpLTtcP.js\",\"_app/immutable/chunks/CaC9IHEK.js\",\"_app/immutable/chunks/q36Eg1F8.js\",\"_app/immutable/chunks/iI8NM7bJ.js\",\"_app/immutable/chunks/Dnfvvefi.js\",\"_app/immutable/chunks/Cxg-bych.js\",\"_app/immutable/chunks/CKnuo8tw.js\",\"_app/immutable/chunks/DeAm3Eed.js\",\"_app/immutable/chunks/CkTdM00m.js\",\"_app/immutable/chunks/B0MzmgHo.js\",\"_app/immutable/chunks/CR3e0W7L.js\",\"_app/immutable/chunks/B5DcI8qy.js\",\"_app/immutable/chunks/BiLRrsV0.js\",\"_app/immutable/chunks/CL12WlkV.js\"];\nexport const stylesheets = [\"_app/immutable/assets/create-post-modal.BRelZfpq.css\",\"_app/immutable/assets/27.De4c4Imr.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK;AAC/C,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM;AACR,IAAI,EAAE;AACN,IAAI,CAAC,SAAS;AACd,GAAG,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACxB,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC;AACvE,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;AAChF,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,qBAAqB,CAAC;AAC3C,EAAE;AACF,EAAE,MAAM,OAAO,GAAG,EAAE,KAAK,EAAE,CAAC,IAAI,KAAK,OAAO,IAAI,EAAE,CAAC,EAAE,KAAK,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;AAChF,EAAE,OAAO;AACT,IAAI,EAAE;AACN,IAAI,SAAS;AACb,IAAI;AACJ,GAAG;AACH,CAAC;;;;;;;AClBW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAsE,CAAC,EAAE;AAEpI,MAAC,YAAY,GAAG;AAChB,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACryB,MAAC,WAAW,GAAG,CAAC,sDAAsD,CAAC,uCAAuC;AAC9G,MAAC,KAAK,GAAG;;;;"}