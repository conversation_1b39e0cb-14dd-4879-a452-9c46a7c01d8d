{"version": 3, "file": "29-C6sXBzhD.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/reactor/hubs/_id_/_page.ts.js", "../../../.svelte-kit/adapter-node/nodes/29.js"], "sourcesContent": ["import { error } from \"@sveltejs/kit\";\nimport { a as consts_exports } from \"../../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../../chunks/acrpc.js\";\nconst load = async ({ fetch, params, url }) => {\n  const { fetcher: api } = getClient();\n  const [\n    me,\n    [hub],\n    communities\n  ] = await Promise.all([\n    api.user.me.get({ fetch, skipInterceptor: true }).catch(() => null),\n    api.reactor.hub.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),\n    api.reactor.community.list.get({ hubId: params.id }, { fetch, ctx: { url } })\n  ]);\n  if (!hub) {\n    throw error(404, \"Hub not found\");\n  }\n  const canEdit = me && (me.role === \"admin\" || me.id === hub.headUser.id);\n  return {\n    me,\n    hub,\n    communities,\n    canEdit,\n    isHasMoreCommunities: communities.length === consts_exports.PAGE_SIZE\n  };\n};\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/__locale__/reactor/hubs/_id_/_page.ts.js';\n\nexport const index = 29;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/__locale__/reactor/hubs/_id_/_page.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/[[locale]]/reactor/hubs/[id]/+page.ts\";\nexport const imports = [\"_app/immutable/nodes/29.B4u2RwGC.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/CGZ87yZq.js\",\"_app/immutable/chunks/Bzak7iHL.js\",\"_app/immutable/chunks/DeAm3Eed.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CkTdM00m.js\",\"_app/immutable/chunks/CtoItwj4.js\",\"_app/immutable/chunks/Dnfvvefi.js\",\"_app/immutable/chunks/BdpLTtcP.js\",\"_app/immutable/chunks/CaC9IHEK.js\",\"_app/immutable/chunks/q36Eg1F8.js\",\"_app/immutable/chunks/B5DcI8qy.js\",\"_app/immutable/chunks/iI8NM7bJ.js\",\"_app/immutable/chunks/Cxg-bych.js\",\"_app/immutable/chunks/CKnuo8tw.js\",\"_app/immutable/chunks/B0MzmgHo.js\",\"_app/immutable/chunks/CR3e0W7L.js\",\"_app/immutable/chunks/BiLRrsV0.js\",\"_app/immutable/chunks/CL12WlkV.js\"];\nexport const stylesheets = [\"_app/immutable/assets/create-post-modal.BRelZfpq.css\",\"_app/immutable/assets/29.Bx5DuwCs.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK;AAC/C,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM;AACR,IAAI,EAAE;AACN,IAAI,CAAC,GAAG,CAAC;AACT,IAAI;AACJ,GAAG,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACxB,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC;AACvE,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC3E,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;AAChF,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,GAAG,EAAE;AACZ,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;AACrC,EAAE;AACF,EAAE,MAAM,OAAO,GAAG,EAAE,KAAK,EAAE,CAAC,IAAI,KAAK,OAAO,IAAI,EAAE,CAAC,EAAE,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;AAC1E,EAAE,OAAO;AACT,IAAI,EAAE;AACN,IAAI,GAAG;AACP,IAAI,WAAW;AACf,IAAI,OAAO;AACX,IAAI,oBAAoB,EAAE,WAAW,CAAC,MAAM,KAAK,cAAc,CAAC;AAChE,GAAG;AACH,CAAC;;;;;;;ACvBW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA+D,CAAC,EAAE;AAE7H,MAAC,YAAY,GAAG;AAChB,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACryB,MAAC,WAAW,GAAG,CAAC,sDAAsD,CAAC,uCAAuC;AAC9G,MAAC,KAAK,GAAG;;;;"}