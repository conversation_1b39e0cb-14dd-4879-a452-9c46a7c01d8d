{"version": 3, "file": "3-CisMCa_-.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/_layout.ts.js", "../../../.svelte-kit/adapter-node/entries/pages/__locale__/_layout.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/3.js"], "sourcesContent": ["import { c as common_exports } from \"../../../chunks/current-user.js\";\nimport \"clsx\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../chunks/exports.js\";\nimport \"../../../chunks/state.svelte.js\";\nimport { match } from \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nconst DEFAULT_LOCALE = \"en\";\nfunction getAppropriateLocalizationFactory(routeLocale, userLocales) {\n  const requestedLocales = routeLocale ? [routeLocale, ...userLocales] : userLocales;\n  function getAppropriateLocalization(localizations) {\n    const availableLocales = localizations.map((localization) => localization.locale);\n    try {\n      const matchedLocale = match(requestedLocales, availableLocales, DEFAULT_LOCALE);\n      const matchedLocalization = localizations.find(\n        (localization) => localization.locale === matchedLocale\n      );\n      return matchedLocalization?.value ?? localizations[0]?.value ?? null;\n    } catch (error) {\n      console.error(\"Error during locale negotiation:\", error);\n      return null;\n    }\n  }\n  return getAppropriateLocalization;\n}\nconst load = (event) => {\n  const me = event.data.me;\n  const user = me ? {\n    id: me.id,\n    email: me.email,\n    role: me.role\n  } : null;\n  const routeLocale = getRouteLocale(event);\n  const hrefLocale = routeLocale ? `/${routeLocale}` : \"\";\n  const locale = routeLocale ?? event.data.preferredLocale ?? \"en\";\n  return {\n    routeLocale,\n    preferredLocale: event.data.preferredLocale,\n    locale,\n    user,\n    toLocaleHref(href) {\n      return `${hrefLocale}${href}`;\n    },\n    getAppropriateLocalization: getAppropriateLocalizationFactory(\n      routeLocale,\n      event.data.userLocales\n    )\n  };\n};\nfunction getRouteLocale(event) {\n  const parsedLocale = common_exports.WebsiteLocaleSchema.safeParse(event.params.locale);\n  if (parsedLocale.success) {\n    return parsedLocale.data;\n  }\n  return null;\n}\nexport {\n  load\n};\n", "import Negotiator from \"negotiator\";\nimport { match } from \"@formatjs/intl-localematcher\";\nimport { c as common_exports } from \"../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../chunks/acrpc.js\";\nconst load = async (event) => {\n  const { fetcher: api } = getClient();\n  const me = await api.user.me.get({ fetch: event.fetch, skipInterceptor: true }).catch(() => null);\n  const userLocales = getUserLocales(event);\n  const preferredLocale = getPreferredLocale(event);\n  return {\n    me,\n    preferredLocale,\n    userLocales\n  };\n};\nfunction getUserLocales(event) {\n  const acceptLanguage = event.request.headers.get(\"accept-language\");\n  if (acceptLanguage) {\n    const negotiatorRequest = {\n      headers: {\n        \"accept-language\": acceptLanguage\n      }\n    };\n    const preferredLanguages = new Negotiator(negotiatorRequest).languages();\n    return preferredLanguages;\n  }\n  return [];\n}\nfunction getPreferredLocale(event) {\n  const userLocales = getUserLocales(event);\n  return match(\n    userLocales,\n    common_exports.WebsiteLocaleSchema._def.values,\n    \"en\"\n  );\n}\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/__locale__/_layout.ts.js';\nimport * as server from '../entries/pages/__locale__/_layout.server.ts.js';\n\nexport const index = 3;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/fallbacks/layout.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/[[locale]]/+layout.ts\";\nexport { server };\nexport const server_id = \"src/routes/[[locale]]/+layout.server.ts\";\nexport const imports = [\"_app/immutable/nodes/3.CHx6KfiV.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/B0MzmgHo.js\",\"_app/immutable/chunks/DeAm3Eed.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CkTdM00m.js\",\"_app/immutable/chunks/Bzak7iHL.js\"];\nexport const stylesheets = [];\nexport const fonts = [];\n"], "names": ["load"], "mappings": ";;;;;;;;;AAOA,MAAM,cAAc,GAAG,IAAI;AAC3B,SAAS,iCAAiC,CAAC,WAAW,EAAE,WAAW,EAAE;AACrE,EAAE,MAAM,gBAAgB,GAAG,WAAW,GAAG,CAAC,WAAW,EAAE,GAAG,WAAW,CAAC,GAAG,WAAW;AACpF,EAAE,SAAS,0BAA0B,CAAC,aAAa,EAAE;AACrD,IAAI,MAAM,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,KAAK,YAAY,CAAC,MAAM,CAAC;AACrF,IAAI,IAAI;AACR,MAAM,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,CAAC;AACrF,MAAM,MAAM,mBAAmB,GAAG,aAAa,CAAC,IAAI;AACpD,QAAQ,CAAC,YAAY,KAAK,YAAY,CAAC,MAAM,KAAK;AAClD,OAAO;AACP,MAAM,OAAO,mBAAmB,EAAE,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,IAAI;AAC1E,IAAI,CAAC,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC9D,MAAM,OAAO,IAAI;AACjB,IAAI;AACJ,EAAE;AACF,EAAE,OAAO,0BAA0B;AACnC;AACA,MAAMA,MAAI,GAAG,CAAC,KAAK,KAAK;AACxB,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AAC1B,EAAE,MAAM,IAAI,GAAG,EAAE,GAAG;AACpB,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;AACb,IAAI,KAAK,EAAE,EAAE,CAAC,KAAK;AACnB,IAAI,IAAI,EAAE,EAAE,CAAC;AACb,GAAG,GAAG,IAAI;AACV,EAAE,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC;AAC3C,EAAE,MAAM,UAAU,GAAG,WAAW,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,EAAE;AACzD,EAAE,MAAM,MAAM,GAAG,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI;AAClE,EAAE,OAAO;AACT,IAAI,WAAW;AACf,IAAI,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,eAAe;AAC/C,IAAI,MAAM;AACV,IAAI,IAAI;AACR,IAAI,YAAY,CAAC,IAAI,EAAE;AACvB,MAAM,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;AACnC,IAAI,CAAC;AACL,IAAI,0BAA0B,EAAE,iCAAiC;AACjE,MAAM,WAAW;AACjB,MAAM,KAAK,CAAC,IAAI,CAAC;AACjB;AACA,GAAG;AACH,CAAC;AACD,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,MAAM,YAAY,GAAG,cAAc,CAAC,mBAAmB,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;AACxF,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,YAAY,CAAC,IAAI;AAC5B,EAAE;AACF,EAAE,OAAO,IAAI;AACb;;;;;;;ACnDA,MAAM,IAAI,GAAG,OAAO,KAAK,KAAK;AAC9B,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC;AACnG,EAAE,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC;AAC3C,EAAE,MAAM,eAAe,GAAG,kBAAkB,CAAC,KAAK,CAAC;AACnD,EAAE,OAAO;AACT,IAAI,EAAE;AACN,IAAI,eAAe;AACnB,IAAI;AACJ,GAAG;AACH,CAAC;AACD,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;AACrE,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,MAAM,iBAAiB,GAAG;AAC9B,MAAM,OAAO,EAAE;AACf,QAAQ,iBAAiB,EAAE;AAC3B;AACA,KAAK;AACL,IAAI,MAAM,kBAAkB,GAAG,IAAI,UAAU,CAAC,iBAAiB,CAAC,CAAC,SAAS,EAAE;AAC5E,IAAI,OAAO,kBAAkB;AAC7B,EAAE;AACF,EAAE,OAAO,EAAE;AACX;AACA,SAAS,kBAAkB,CAAC,KAAK,EAAE;AACnC,EAAE,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC;AAC3C,EAAE,OAAO,KAAK;AACd,IAAI,WAAW;AACf,IAAI,cAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM;AAClD,IAAI;AACJ,GAAG;AACH;;;;;;;AChCY,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,6BAAuC,CAAC,EAAE;AAErG,MAAC,YAAY,GAAG;AAEhB,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,oCAAoC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAChV,MAAC,WAAW,GAAG;AACf,MAAC,KAAK,GAAG;;;;"}