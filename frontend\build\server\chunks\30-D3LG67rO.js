import { e as error } from './index-CT944rr3.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import './schema-CmMg_B_X.js';
import './current-user-BM0W6LNm.js';

const load = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();
  const [
    [post],
    comments
  ] = await Promise.all([
    api.reactor.post.list.get({ id: params.id, lensId: null }, { fetch, ctx: { url } }),
    api.reactor.comment.list.get({ entityType: "post", entityId: params.id }, { fetch, ctx: { url } })
  ]);
  if (!post) {
    throw error(404, "Post not found");
  }
  return {
    post,
    comments
  };
};

var _page_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 30;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-BxWAc7tK.js')).default;
const universal_id = "src/routes/[[locale]]/reactor/[id]/+page.ts";
const imports = ["_app/immutable/nodes/30.woOvaC-K.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CGZ87yZq.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/B-tQx-ev.js","_app/immutable/chunks/C_sRNQCS.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/CaC9IHEK.js","_app/immutable/chunks/CR3e0W7L.js","_app/immutable/chunks/iI8NM7bJ.js","_app/immutable/chunks/CKnuo8tw.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/B0MzmgHo.js","_app/immutable/chunks/B5DcI8qy.js","_app/immutable/chunks/BiLRrsV0.js"];
const stylesheets = ["_app/immutable/assets/right-menu.BCyxSBRm.css","_app/immutable/assets/create-post-modal.BRelZfpq.css","_app/immutable/assets/30.Ch-6q2wN.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, _page_ts as universal, universal_id };
//# sourceMappingURL=30-D3LG67rO.js.map
