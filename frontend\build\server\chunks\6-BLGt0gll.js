const index = 6;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-BefHkUCx.js')).default;
const imports = ["_app/immutable/nodes/6.CGV_dNZn.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js"];
const stylesheets = ["_app/immutable/assets/6.FN57z1Yt.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=6-BLGt0gll.js.map
