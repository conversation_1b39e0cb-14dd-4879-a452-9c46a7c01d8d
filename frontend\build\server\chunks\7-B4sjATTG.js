import { a as consts_exports } from './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import './index-CT944rr3.js';
import './schema-CmMg_B_X.js';

const load = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();
  const invites = await api.user.invite.list.get(
    {
      pagination: {
        page: 1,
        size: consts_exports.PAGE_SIZE
      }
    },
    { fetch, ctx: { url } }
  );
  return {
    invites,
    isHasMoreInvites: invites.length === consts_exports.PAGE_SIZE
  };
};

var _page_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 7;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-Bi0PMZfO.js')).default;
const universal_id = "src/routes/admin/invites/+page.ts";
const imports = ["_app/immutable/nodes/7.ZZQvPf5l.js","_app/immutable/chunks/CVTn1FV4.js","_app/immutable/chunks/CYgJF_JY.js","_app/immutable/chunks/CGZ87yZq.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/CtoItwj4.js","_app/immutable/chunks/Dnfvvefi.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/iI8NM7bJ.js","_app/immutable/chunks/CKnuo8tw.js","_app/immutable/chunks/DeAm3Eed.js","_app/immutable/chunks/CkTdM00m.js","_app/immutable/chunks/B0MzmgHo.js","_app/immutable/chunks/CR3e0W7L.js","_app/immutable/chunks/B5DcI8qy.js","_app/immutable/chunks/BiLRrsV0.js","_app/immutable/chunks/Np2weedy.js"];
const stylesheets = ["_app/immutable/assets/create-post-modal.BRelZfpq.css","_app/immutable/assets/7.B1F65g0r.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, _page_ts as universal, universal_id };
//# sourceMappingURL=7-B4sjATTG.js.map
