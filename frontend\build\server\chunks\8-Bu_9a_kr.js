const index = 8;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-W-KnA6HA.js')).default;
const imports = ["_app/immutable/nodes/8.DdG-z9_l.js","_app/immutable/chunks/Bzak7iHL.js","_app/immutable/chunks/RHWQbow4.js","_app/immutable/chunks/BlWcudmi.js","_app/immutable/chunks/BdpLTtcP.js","_app/immutable/chunks/Cxg-bych.js","_app/immutable/chunks/q36Eg1F8.js","_app/immutable/chunks/CaC9IHEK.js"];
const stylesheets = ["_app/immutable/assets/8.DrNs20W2.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=8-Bu_9a_kr.js.map
