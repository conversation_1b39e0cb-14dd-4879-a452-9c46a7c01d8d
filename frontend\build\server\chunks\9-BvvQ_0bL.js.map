{"version": 3, "file": "9-BvvQ_0bL.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/communes/_page.ts.js", "../../../.svelte-kit/adapter-node/nodes/9.js"], "sourcesContent": ["import { a as consts_exports } from \"../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../chunks/acrpc.js\";\nconst load = async ({ fetch, url }) => {\n  const { fetcher: api } = getClient();\n  const [\n    me,\n    communes\n  ] = await Promise.all([\n    api.user.me.get({ fetch, ctx: { url } }),\n    api.commune.list.get({}, { fetch, ctx: { url } })\n  ]);\n  let pendingInvitationsCount = 0;\n  if (me) {\n    const invitations = await api.commune.invitation.list.get({}, { fetch, ctx: { url } });\n    pendingInvitationsCount = invitations.filter(({ status }) => status === \"pending\").length;\n  }\n  return {\n    communes,\n    isHasMoreCommunes: communes.length === consts_exports.PAGE_SIZE,\n    pendingInvitationsCount,\n    isLoggedIn: !!me\n  };\n};\nexport {\n  load\n};\n", "import * as universal from '../entries/pages/__locale__/(index)/communes/_page.ts.js';\n\nexport const index = 9;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/__locale__/(index)/communes/_page.svelte.js')).default;\nexport { universal };\nexport const universal_id = \"src/routes/[[locale]]/(index)/communes/+page.ts\";\nexport const imports = [\"_app/immutable/nodes/9.CUwqN0Kk.js\",\"_app/immutable/chunks/CVTn1FV4.js\",\"_app/immutable/chunks/CYgJF_JY.js\",\"_app/immutable/chunks/CGZ87yZq.js\",\"_app/immutable/chunks/Bzak7iHL.js\",\"_app/immutable/chunks/DeAm3Eed.js\",\"_app/immutable/chunks/RHWQbow4.js\",\"_app/immutable/chunks/BlWcudmi.js\",\"_app/immutable/chunks/CkTdM00m.js\",\"_app/immutable/chunks/CtoItwj4.js\",\"_app/immutable/chunks/Dnfvvefi.js\",\"_app/immutable/chunks/BdpLTtcP.js\",\"_app/immutable/chunks/B5DcI8qy.js\",\"_app/immutable/chunks/CKnuo8tw.js\",\"_app/immutable/chunks/B0MzmgHo.js\",\"_app/immutable/chunks/C_wziyCN.js\",\"_app/immutable/chunks/iI8NM7bJ.js\",\"_app/immutable/chunks/Cxg-bych.js\",\"_app/immutable/chunks/q36Eg1F8.js\",\"_app/immutable/chunks/CR3e0W7L.js\",\"_app/immutable/chunks/BiLRrsV0.js\"];\nexport const stylesheets = [\"_app/immutable/assets/create-post-modal.BRelZfpq.css\",\"_app/immutable/assets/9.BafTnGzW.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK;AACvC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM;AACR,IAAI,EAAE;AACN,IAAI;AACJ,GAAG,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACxB,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5C,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;AACpD,GAAG,CAAC;AACJ,EAAE,IAAI,uBAAuB,GAAG,CAAC;AACjC,EAAE,IAAI,EAAE,EAAE;AACV,IAAI,MAAM,WAAW,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC1F,IAAI,uBAAuB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM;AAC7F,EAAE;AACF,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,iBAAiB,EAAE,QAAQ,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS;AACnE,IAAI,uBAAuB;AAC3B,IAAI,UAAU,EAAE,CAAC,CAAC;AAClB,GAAG;AACH,CAAC;;;;;;;ACpBW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA8D,CAAC,EAAE;AAE5H,MAAC,YAAY,GAAG;AAChB,MAAC,OAAO,GAAG,CAAC,oCAAoC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAChwB,MAAC,WAAW,GAAG,CAAC,sDAAsD,CAAC,sCAAsC;AAC7G,MAAC,KAAK,GAAG;;;;"}