{"version": 3, "file": "_layout.svelte-B1pT4bmt.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/_layout.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { y as attr, F as attr_style, G as attr_class, z as escape_html, w as pop, u as push } from \"../../../../chunks/index.js\";\n/* empty css                                                                        */\nimport { L as Locale_switcher } from \"../../../../chunks/locale-switcher.js\";\nimport \"../../../../chunks/current-user.js\";\nimport \"@sveltejs/kit\";\nimport \"../../../../chunks/schema.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../chunks/exports.js\";\nimport \"../../../../chunks/state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nfunction Header($$payload, $$props) {\n  push();\n  const i18n = {\n    ru: {\n      navGap: \"mx-auto\",\n      theLaw: \"Право\",\n      rules: \"Правила\",\n      newEnglish: \"Новый английский\",\n      newCalendar: \"Новый календарь\",\n      reactor: \"Реактор\",\n      users: \"Люди\",\n      communes: \"Коммуны\",\n      profile: \"Профиль\"\n    },\n    en: {\n      navGap: \"mx-auto\",\n      theLaw: \"The Law\",\n      rules: \"Rules\",\n      newEnglish: \"New English\",\n      newCalendar: \"New Calendar\",\n      reactor: \"Reactor\",\n      users: \"People\",\n      communes: \"Communes\",\n      profile: \"Profile\"\n    }\n  };\n  const { user, locale, routeLocale, toLocaleHref } = $$props;\n  const t = i18n[locale];\n  $$payload.out.push(`<nav class=\"navbar navbar-expand-lg sticky-top compact-navbar svelte-19phhed\"><div class=\"container\"><a${attr(\"href\", toLocaleHref(\"/\"))} class=\"navbar-brand py-0 ps-5\"><img src=\"/images/full-v3-transparent.svg\" alt=\"Site Logo\"${attr(\"height\", 60)}${attr(\"width\", 60)}${attr_style(\"\", { width: \"auto\" })}/></a> <button class=\"navbar-toggler\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#navbarNav\" aria-controls=\"navbarNav\" aria-expanded=\"false\" aria-label=\"Toggle navigation\"><span class=\"navbar-toggler-icon\"></span></button> <div class=\"collapse navbar-collapse\" id=\"navbarNav\"><ul class=\"navbar-nav mx-auto\"><li${attr_class(`nav-item ${t.navGap} text-nowrap`, \"svelte-19phhed\")}><a${attr(\"href\", toLocaleHref(\"/the-law\"))} class=\"nav-link\">${escape_html(t.theLaw)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, \"svelte-19phhed\")}><a${attr(\"href\", toLocaleHref(\"/rules\"))} class=\"nav-link\">${escape_html(t.rules)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, \"svelte-19phhed\")}><a${attr(\"href\", toLocaleHref(\"/new-english\"))} class=\"nav-link\">${escape_html(t.newEnglish)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, \"svelte-19phhed\")}><a${attr(\"href\", toLocaleHref(\"/new-calendar\"))} class=\"nav-link\">${escape_html(t.newCalendar)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, \"svelte-19phhed\")}><a${attr(\"href\", toLocaleHref(\"/users\"))} class=\"nav-link\">${escape_html(t.users)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, \"svelte-19phhed\")}><a${attr(\"href\", toLocaleHref(\"/communes\"))} class=\"nav-link\">${escape_html(t.communes)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, \"svelte-19phhed\")}><a${attr(\"href\", toLocaleHref(\"/reactor\"))} class=\"nav-link\">${escape_html(t.reactor)}</a></li></ul> <ul class=\"navbar-nav\"${attr_style(\"\", { position: \"relative\" })}><li class=\"nav-item\"><a${attr(\"href\", toLocaleHref(\"/profile\"))} class=\"btn btn-primary btn-sm\">${escape_html(t.profile)}</a></li> <li class=\"nav-item\">`);\n  Locale_switcher($$payload, { currentLocale: routeLocale });\n  $$payload.out.push(`<!----></li> `);\n  if (user?.role === \"admin\") {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<li class=\"nav-item\"${attr_style(\"\", { position: \"absolute\", right: \"-105px\" })}><a${attr(\"href\", toLocaleHref(\"/admin\"))} class=\"btn btn-primary btn-sm\">Админка</a></li>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></ul></div></div></nav>`);\n  pop();\n}\nconst boostyIcon = \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%3e%3cpath%20fill='%23495057'%20d='M2.661%2014.337L6.801%200h6.362L11.88%204.444l-.038.077l-3.378%2011.733h3.15q-1.982%204.934-3.086%207.733c-5.816-.063-7.442-4.228-6.02-9.155M8.554%2024l7.67-11.035h-3.25l2.83-7.073c4.852.508%207.137%204.33%205.791%208.952C20.16%2019.81%2014.344%2024%208.68%2024h-.127z'%20/%3e%3c/svg%3e\";\nfunction Footer($$payload, $$props) {\n  push();\n  const i18n = {\n    ru: {\n      logoAlt: \"Логотип Цифрового Сообщества «Коммуна»\",\n      title: \"О нас\",\n      about: \"Цифровое Сообщество «Коммуна» - где общие ценности создают будущее. Мы строим жизнь в сообществе, которое поддерживает сотрудничество, взаимопомощь и развитие.\",\n      social: { telegram: \"https://t.me/ds_commune_ru\" },\n      links: {\n        title: \"Cсылки\",\n        reactor: \"Реактор\",\n        communes: \"Коммуны\",\n        users: \"Люди\",\n        newCalendar: \"Новый календарь\",\n        newEnglish: \"Новый английский\",\n        theLaw: \"Право\",\n        rules: \"Правила\"\n      },\n      contactUs: {\n        title: \"Связаться с нами\"\n      },\n      legal: {\n        disclaimer: \"Цифровое Сообщество «Коммуна». Все права защищены.\"\n      }\n    },\n    en: {\n      logoAlt: \"Digital Society «Commune» Logo\",\n      title: \"About Us\",\n      about: \"Digital Society «Commune» - where common values create the future. We're building a vibrant community dedicated to fostering collaboration, mutual aid, and development.\",\n      social: { telegram: \"https://t.me/ds_commune_en\" },\n      links: {\n        title: \"Quick Links\",\n        reactor: \"Reactor\",\n        communes: \"Communes\",\n        users: \"People\",\n        newCalendar: \"New Calendar\",\n        newEnglish: \"New English\",\n        theLaw: \"The Law\",\n        rules: \"Rules\"\n      },\n      contactUs: { title: \"Contact Us\" },\n      legal: {\n        disclaimer: \"Digital Society «Commune». All rights reserved.\"\n      }\n    }\n  };\n  const { locale, toLocaleHref } = $$props;\n  const t = i18n[locale];\n  const contactUsEmail = \"<EMAIL>\";\n  $$payload.out.push(`<footer class=\"footer svelte-og7cjd\"><div class=\"container\"><div class=\"row gy-4\"><div class=\"col-lg-4 col-md-6\"><div class=\"about-section svelte-og7cjd\"><a${attr(\"href\", toLocaleHref(\"/\"))} class=\"d-inline-block mb-3\"><img src=\"/images/full-v3-transparent.svg\"${attr(\"alt\", t.logoAlt)}${attr(\"width\", 80)}${attr(\"height\", 80)} class=\"footer-logo svelte-og7cjd\"/></a> <h5 class=\"fw-bold mb-3\">${escape_html(t.title)}</h5> <p class=\"text-muted\">${escape_html(t.about)}</p> <div class=\"social-icons svelte-og7cjd\"><a${attr(\"href\", t.social.telegram)} class=\"social-icon svelte-og7cjd\" aria-label=\"Telegram\" target=\"_blank\" rel=\"noopener noreferrer\"><i class=\"bi bi-telegram\"></i></a> <a href=\"https://github.com/ds-commune\" class=\"social-icon svelte-og7cjd\" aria-label=\"GitHub\" target=\"_blank\" rel=\"noopener noreferrer\"><i class=\"bi bi-github\"></i></a> <a href=\"https://boosty.to/ds.commune\" class=\"social-icon svelte-og7cjd\" aria-label=\"Boosty\" target=\"_blank\" rel=\"noopener noreferrer\"><img${attr(\"src\", boostyIcon)} alt=\"Boosty\"${attr(\"width\", 24)}${attr(\"height\", 24)}/></a></div></div></div> <div class=\"col-lg-4 col-md-6\"><h5 class=\"fw-bold mb-3\">${escape_html(t.links.title)}</h5> <ul class=\"list-unstyled quick-links svelte-og7cjd\"><li class=\"svelte-og7cjd\"><a${attr(\"href\", toLocaleHref(\"/the-law\"))} class=\"svelte-og7cjd\">${escape_html(t.links.theLaw)}</a></li> <li class=\"svelte-og7cjd\"><a${attr(\"href\", toLocaleHref(\"/rules\"))} class=\"svelte-og7cjd\">${escape_html(t.links.rules)}</a></li> <li class=\"svelte-og7cjd\"><a${attr(\"href\", toLocaleHref(\"/new-english\"))} class=\"svelte-og7cjd\">${escape_html(t.links.newEnglish)}</a></li> <li class=\"svelte-og7cjd\"><a${attr(\"href\", toLocaleHref(\"/new-calendar\"))} class=\"svelte-og7cjd\">${escape_html(t.links.newCalendar)}</a></li> <li class=\"svelte-og7cjd\"><a${attr(\"href\", toLocaleHref(\"/users\"))} class=\"svelte-og7cjd\">${escape_html(t.links.users)}</a></li> <li class=\"svelte-og7cjd\"><a${attr(\"href\", toLocaleHref(\"/communes\"))} class=\"svelte-og7cjd\">${escape_html(t.links.communes)}</a></li> <li class=\"svelte-og7cjd\"><a${attr(\"href\", toLocaleHref(\"/reactor\"))} class=\"svelte-og7cjd\">${escape_html(t.links.reactor)}</a></li></ul></div> <div class=\"col-lg-4 col-md-12\"><h5 class=\"fw-bold mb-3\">${escape_html(t.contactUs.title)}</h5> <ul class=\"list-unstyled contact-info svelte-og7cjd\"><li class=\"svelte-og7cjd\"><i class=\"bi bi-envelope svelte-og7cjd\"></i> <a${attr(\"href\", `mailto:${contactUsEmail}`)} target=\"_blank\" rel=\"noopener noreferrer\">${escape_html(contactUsEmail)}</a></li></ul></div></div> <hr class=\"divider svelte-og7cjd\"/> <div class=\"copyright svelte-og7cjd\"><p class=\"svelte-og7cjd\">${escape_html((/* @__PURE__ */ new Date()).getFullYear())} ${escape_html(t.legal.disclaimer)}</p></div></div></footer>`);\n  pop();\n}\nfunction _layout($$payload, $$props) {\n  const { children, data } = $$props;\n  const { user, locale, routeLocale, toLocaleHref } = data;\n  $$payload.out.push(`<div class=\"page-wrapper svelte-100cqw2\">`);\n  Header($$payload, { user, locale, toLocaleHref, routeLocale });\n  $$payload.out.push(`<!----> <main class=\"container flex-grow-1 mb-5\">`);\n  children($$payload);\n  $$payload.out.push(`<!----></main> `);\n  Footer($$payload, { locale, toLocaleHref });\n  $$payload.out.push(`<!----></div>`);\n}\nexport {\n  _layout as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAWA,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,UAAU,EAAE,kBAAkB;AACpC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,OAAO,EAAE;AACf,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,OAAO,EAAE;AACf;AACA,GAAG;AACH,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,OAAO;AAC7D,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uGAAuG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,0FAA0F,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,mUAAmU,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,qCAAqC,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC,wBAAwB,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,+BAA+B,CAAC,CAAC;AACjgE,EAAE,eAAe,CAAC,SAAS,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC;AAC5D,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AACrC,EAAE,IAAI,IAAI,EAAE,IAAI,KAAK,OAAO,EAAE;AAC9B,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,gDAAgD,CAAC,CAAC;AACpM,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+BAA+B,CAAC,CAAC;AACvD,EAAE,GAAG,EAAE;AACP;AACA,MAAM,UAAU,GAAG,wbAAwb;AAC3c,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,OAAO,EAAE,wCAAwC;AACvD,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,KAAK,EAAE,iKAAiK;AAC9K,MAAM,MAAM,EAAE,EAAE,QAAQ,EAAE,4BAA4B,EAAE;AACxD,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,QAAQ;AACvB,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,QAAQ,EAAE,SAAS;AAC3B,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,WAAW,EAAE,iBAAiB;AACtC,QAAQ,UAAU,EAAE,kBAAkB;AACtC,QAAQ,MAAM,EAAE,OAAO;AACvB,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,KAAK,EAAE;AACb,QAAQ,UAAU,EAAE;AACpB;AACA,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,KAAK,EAAE,0KAA0K;AACvL,MAAM,MAAM,EAAE,EAAE,QAAQ,EAAE,4BAA4B,EAAE;AACxD,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,aAAa;AAC5B,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,QAAQ,EAAE,UAAU;AAC5B,QAAQ,KAAK,EAAE,QAAQ;AACvB,QAAQ,WAAW,EAAE,cAAc;AACnC,QAAQ,UAAU,EAAE,aAAa;AACjC,QAAQ,MAAM,EAAE,SAAS;AACzB,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,SAAS,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;AACxC,MAAM,KAAK,EAAE;AACb,QAAQ,UAAU,EAAE;AACpB;AACA;AACA,GAAG;AACH,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO;AAC1C,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,MAAM,cAAc,GAAG,wBAAwB;AACjD,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4JAA4J,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,uEAAuE,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,kEAAkE,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,+CAA+C,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,0bAA0b,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,iFAAiF,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,sFAAsF,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,8EAA8E,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,oIAAoI,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,2CAA2C,EAAE,WAAW,CAAC,cAAc,CAAC,CAAC,6HAA6H,EAAE,WAAW,CAAC,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,yBAAyB,CAAC,CAAC;AAC1uF,EAAE,GAAG,EAAE;AACP;AACA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,OAAO;AACpC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,IAAI;AAC1D,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yCAAyC,CAAC,CAAC;AACjE,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC;AAChE,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iDAAiD,CAAC,CAAC;AACzE,EAAE,QAAQ,CAAC,SAAS,CAAC;AACrB,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AACvC,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;AAC7C,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AACrC;;;;"}