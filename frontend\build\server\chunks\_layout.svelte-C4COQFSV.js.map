{"version": 3, "file": "_layout.svelte-C4COQFSV.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/admin/_layout.svelte.js"], "sourcesContent": ["import { z as escape_html, w as pop, u as push } from \"../../../chunks/index.js\";\nimport \"clsx\";\nfunction _layout($$payload, $$props) {\n  push();\n  const { data, children } = $$props;\n  $$payload.out.push(`<div class=\"admin-layout svelte-e2baey\"><div class=\"container-fluid\"><div class=\"row\"><nav class=\"col-md-3 col-lg-2 d-md-block bg-light sidebar svelte-e2baey\"><div class=\"position-sticky pt-3\"><div class=\"sidebar-header mb-3 svelte-e2baey\"><h5 class=\"text-primary\"><i class=\"bi bi-gear-fill me-2\"></i> Admin Panel</h5> <small class=\"text-muted\">Welcome, ${escape_html(data.me.email)}</small></div> <ul class=\"nav flex-column\"><li class=\"nav-item\"><a class=\"nav-link svelte-e2baey\" href=\"/admin\" data-sveltekit-preload-data=\"hover\"><i class=\"bi bi-house-door me-2\"></i> Dashboard</a></li> <li class=\"nav-item\"><a class=\"nav-link svelte-e2baey\" href=\"/admin/invites\" data-sveltekit-preload-data=\"hover\"><i class=\"bi bi-envelope me-2\"></i> User Invites</a></li></ul></div></nav> <main class=\"col-md-9 ms-sm-auto col-lg-10 px-md-4 svelte-e2baey\"><div class=\"pt-3\">`);\n  children($$payload);\n  $$payload.out.push(`<!----></div></main></div></div></div>`);\n  pop();\n}\nexport {\n  _layout as default\n};\n"], "names": [], "mappings": ";;AAEA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;AACpC,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kWAAkW,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,6dAA6d,CAAC,CAAC;AACp3B,EAAE,QAAQ,CAAC,SAAS,CAAC;AACrB,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,CAAC,CAAC;AAC9D,EAAE,GAAG,EAAE;AACP;;;;"}