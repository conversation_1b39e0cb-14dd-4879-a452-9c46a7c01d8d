{"version": 3, "file": "_layout.svelte-DPyRE-ju.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/reactor/_layout.svelte.js"], "sourcesContent": ["import { y as attr, F as attr_style, G as attr_class, z as escape_html, J as stringify } from \"../../../../chunks/index.js\";\n/* empty css                                                                        */\nimport { L as Locale_switcher } from \"../../../../chunks/locale-switcher.js\";\nimport \"../../../../chunks/current-user.js\";\nimport \"@sveltejs/kit\";\nimport \"../../../../chunks/schema.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../chunks/exports.js\";\nimport \"../../../../chunks/state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nfunction _layout($$payload, $$props) {\n  const i18n = {\n    ru: {\n      navGap: \"mx-1\",\n      commune: \"Коммуна\",\n      theLaw: \"Право\",\n      rules: \"Правила\",\n      newEnglish: \"Новый английский\",\n      newCalendar: \"Новый календарь\",\n      news: \"Новости\",\n      users: \"Пользователи\",\n      communes: \"Коммуны\",\n      hubs: \"Хабы\",\n      communities: \"Сообщества\",\n      profile: \"Профиль\",\n      feed: \"Лента\",\n      hole: \"Яма\"\n    },\n    en: {\n      navGap: \"mx-2\",\n      commune: \"Commune\",\n      theLaw: \"The Law\",\n      rules: \"Rules\",\n      newEnglish: \"New English\",\n      newCalendar: \"New Calendar\",\n      news: \"News\",\n      users: \"Users\",\n      communes: \"Communes\",\n      hubs: \"Hubs\",\n      communities: \"Communities\",\n      profile: \"Profile\",\n      feed: \"Feed\",\n      hole: \"Pit\"\n    }\n  };\n  const { children, data } = $$props;\n  const { locale, routeLocale, toLocaleHref } = data;\n  const t = i18n[locale];\n  $$payload.out.push(`<div class=\"page-wrapper svelte-5oi5pp\"><nav class=\"navbar navbar-expand-lg sticky-top reactor-navbar svelte-5oi5pp\"><div class=\"container\"><a${attr(\"href\", toLocaleHref(\"/\"))} class=\"navbar-brand py-0 ps-5 svelte-5oi5pp\"><img src=\"/images/full-v3-transparent-white.svg\" alt=\"Site Logo\"${attr(\"height\", 60)}${attr(\"width\", 60)}${attr_style(\"\", { width: \"auto\" })}/></a> <button class=\"navbar-toggler\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#navbarNav\" aria-controls=\"navbarNav\" aria-expanded=\"false\" aria-label=\"Toggle navigation\"><span class=\"navbar-toggler-icon\"></span></button> <div class=\"collapse navbar-collapse svelte-5oi5pp\" id=\"navbarNav\"><ul class=\"navbar-nav mx-auto svelte-5oi5pp\"><li${attr_class(`nav-item dropdown ${stringify(t.navGap)} text-nowrap`, \"svelte-5oi5pp\")}><a${attr(\"href\", toLocaleHref(\"/\"))} class=\"nav-link svelte-5oi5pp\">${escape_html(t.commune)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, \"svelte-5oi5pp\")}><a${attr(\"href\", toLocaleHref(\"/reactor\"))} class=\"nav-link svelte-5oi5pp\">${escape_html(t.feed)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, \"svelte-5oi5pp\")}><a${attr(\"href\", toLocaleHref(\"/reactor/hubs\"))} class=\"nav-link svelte-5oi5pp\">${escape_html(t.hubs)}</a></li> <li${attr_class(`nav-item ${t.navGap} text-nowrap`, \"svelte-5oi5pp\")}><a${attr(\"href\", toLocaleHref(\"/reactor/communities\"))} class=\"nav-link svelte-5oi5pp\">${escape_html(t.communities)}</a></li></ul> <ul class=\"navbar-nav svelte-5oi5pp\"><li class=\"nav-item svelte-5oi5pp\">`);\n  Locale_switcher($$payload, { currentLocale: routeLocale });\n  $$payload.out.push(`<!----></li></ul></div></div></nav> <main class=\"container-fluid flex-grow-1 mb-5 reactor-container svelte-5oi5pp\">`);\n  children($$payload);\n  $$payload.out.push(`<!----></main></div>`);\n}\nexport {\n  _layout as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAUA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,UAAU,EAAE,kBAAkB;AACpC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,WAAW,EAAE,YAAY;AAC/B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE;AACZ;AACA,GAAG;AACH,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,OAAO;AACpC,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,IAAI;AACpD,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8IAA8I,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,8GAA8G,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,+VAA+V,EAAE,UAAU,CAAC,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,uFAAuF,CAAC,CAAC;AACxhD,EAAE,eAAe,CAAC,SAAS,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC;AAC5D,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mHAAmH,CAAC,CAAC;AAC3I,EAAE,QAAQ,CAAC,SAAS,CAAC;AACrB,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC5C;;;;"}