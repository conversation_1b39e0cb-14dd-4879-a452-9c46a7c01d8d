import { x as head, y as attr } from './index-0Ke2LYl0.js';

const scriptSrc = "/_app/immutable/assets/bootstrap.bundle.Cc3wpyM8.js";
function _layout($$payload, $$props) {
  const { children } = $$props;
  head($$payload, ($$payload2) => {
    $$payload2.out.push(`<script${attr("src", scriptSrc)}><\/script> <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css"/>`);
  });
  children($$payload);
  $$payload.out.push(`<!---->`);
}

export { _layout as default };
//# sourceMappingURL=_layout.svelte-pZNrwI1a.js.map
