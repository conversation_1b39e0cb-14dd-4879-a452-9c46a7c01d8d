{"version": 3, "file": "_layout.svelte-pZNrwI1a.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/_layout.svelte.js"], "sourcesContent": ["import { x as head, y as attr } from \"../../chunks/index.js\";\nconst scriptSrc = \"/_app/immutable/assets/bootstrap.bundle.Cc3wpyM8.js\";\nfunction _layout($$payload, $$props) {\n  const { children } = $$props;\n  head($$payload, ($$payload2) => {\n    $$payload2.out.push(`<script${attr(\"src\", scriptSrc)}><\\/script> <link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\"/>`);\n  });\n  children($$payload);\n  $$payload.out.push(`<!---->`);\n}\nexport {\n  _layout as default\n};\n"], "names": [], "mappings": ";;AACA,MAAM,SAAS,GAAG,qDAAqD;AACvE,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO;AAC9B,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,wHAAwH,CAAC,CAAC;AACnL,EAAE,CAAC,CAAC;AACJ,EAAE,QAAQ,CAAC,SAAS,CAAC;AACrB,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;AAC/B;;;;"}