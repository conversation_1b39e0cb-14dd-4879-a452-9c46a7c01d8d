import { u as push, x as head, z as escape_html, y as attr, N as ensure_array_like, J as attr_class, w as pop } from './index-0Ke2LYl0.js';
import './current-user-BM0W6LNm.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import { f as formatDate } from './format-date-DgRnEWcB.js';
import './schema-CmMg_B_X.js';

function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Commune Invitations — Commune" },
      invitations: "Commune Invitations",
      loading: "Loading...",
      noInvitations: "No invitations found",
      member: "member",
      members: "members",
      headMember: "Head",
      errorFetchingInvitations: "Failed to fetch invitations",
      errorOccurred: "An error occurred while fetching invitations",
      loadingMore: "Loading more invitations...",
      accept: "Accept",
      reject: "Reject",
      pending: "Pending",
      accepted: "Accepted",
      rejected: "Rejected",
      expired: "Expired",
      invitedOn: "Invited on",
      acceptingInvitation: "Accepting...",
      rejectingInvitation: "Rejecting...",
      errorAcceptingInvitation: "Failed to accept invitation",
      errorRejectingInvitation: "Failed to reject invitation",
      invitationAccepted: "Invitation accepted! Redirecting to commune...",
      backToCommunes: "Back to Communes",
      viewCommune: "View Commune",
      noImage: "No image",
      communeImageAlt: "Commune image"
    },
    ru: {
      _page: {
        title: "Приглашения в коммуны — Коммуна"
      },
      invitations: "Приглашения в коммуны",
      loading: "Загрузка...",
      noInvitations: "Приглашения не найдены",
      member: "участник",
      members: "участников",
      headMember: "Глава",
      errorFetchingInvitations: "Не удалось загрузить приглашения",
      errorOccurred: "Произошла ошибка при загрузке приглашений",
      loadingMore: "Загружаем больше приглашений...",
      accept: "Принять",
      reject: "Отклонить",
      pending: "Ожидает",
      accepted: "Принято",
      rejected: "Отклонено",
      expired: "Истекло",
      invitedOn: "Приглашен",
      acceptingInvitation: "Принимаем...",
      rejectingInvitation: "Отклоняем...",
      errorAcceptingInvitation: "Не удалось принять приглашение",
      errorRejectingInvitation: "Не удалось отклонить приглашение",
      invitationAccepted: "Приглашение принято! Перенаправляем в коммуну...",
      backToCommunes: "Назад к коммунам",
      viewCommune: "Посмотреть коммуну",
      noImage: "Нет изображения",
      communeImageAlt: "Изображение коммуны"
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const {
    locale,
    routeLocale,
    toLocaleHref,
    getAppropriateLocalization
  } = data;
  const t = i18n[locale];
  let invitations = data.invitations;
  let isHasMoreInvitations = data.isHasMoreInvitations;
  let loadingStates = {};
  function getStatusBadgeClass(status) {
    switch (status) {
      case "pending":
        return "bg-warning text-dark";
      case "accepted":
        return "bg-success";
      case "rejected":
        return "bg-danger";
      case "expired":
        return "bg-secondary";
      default:
        return "bg-secondary";
    }
  }
  function getStatusText(status) {
    switch (status) {
      case "pending":
        return t.pending;
      case "accepted":
        return t.accepted;
      case "rejected":
        return t.rejected;
      case "expired":
        return t.expired;
      default:
        return status;
    }
  }
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;
  });
  $$payload.out.push(`<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4"><h1>${escape_html(t.invitations)}</h1> <a${attr("href", toLocaleHref("/communes"))} class="btn btn-outline-secondary">${escape_html(t.backToCommunes)}</a></div> `);
  if (invitations.length === 0) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-5"><p class="text-muted">${escape_html(t.noInvitations)}</p></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    const each_array = ensure_array_like(invitations);
    $$payload.out.push(`<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4"><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let invitation = each_array[$$index];
      $$payload.out.push(`<div class="col"><div class="card h-100 shadow-sm">`);
      if (invitation.commune.image) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="image-container svelte-lafg1c"><img${attr("src", `/images/${invitation.commune.image}`)}${attr("alt", `${t.communeImageAlt}`)} class="svelte-lafg1c"/></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="bg-light text-center d-flex align-items-center justify-content-center" style="height: 140px;"><span class="text-muted">${escape_html(t.noImage)}</span></div>`);
      }
      $$payload.out.push(`<!--]--> <div class="card-body d-flex flex-column"><div class="d-flex justify-content-between align-items-start mb-2"><span${attr_class(`badge ${getStatusBadgeClass(invitation.status)}`, "svelte-lafg1c")}>${escape_html(getStatusText(invitation.status))}</span> <small class="text-muted">${escape_html(t.invitedOn)}
                  ${escape_html(formatDate(invitation.createdAt, locale))}</small></div> <h5 class="card-title fs-5 text-truncate mb-2">${escape_html(getAppropriateLocalization(invitation.commune?.name) || "Unknown Commune")}</h5> <p class="card-text text-muted small mb-3" style="height: 3rem; overflow: hidden">${escape_html(getAppropriateLocalization(invitation.commune.description) || "")}</p> <div class="mb-3"><span class="badge bg-primary mb-2">${escape_html(invitation.commune?.memberCount || 0)}
                  ${escape_html((invitation.commune?.memberCount || 0) === 1 ? t.member : t.members)}</span> <div class="small text-muted"><div>${escape_html(t.headMember)}:</div> <div class="d-flex flex-column">${escape_html(getAppropriateLocalization(invitation.commune.headMember.name) || "Unknown")}</div></div></div> `);
      if (invitation.status === "pending") {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="mt-auto"><div class="d-grid gap-2"><button class="btn btn-success"${attr("disabled", loadingStates[invitation.id] === "accepting", true)}>`);
        if (loadingStates[invitation.id] === "accepting") {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<span class="spinner-border spinner-border-sm me-2" role="status"></span> ${escape_html(t.acceptingInvitation)}`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`${escape_html(t.accept)}`);
        }
        $$payload.out.push(`<!--]--></button> <button class="btn btn-outline-danger"${attr("disabled", loadingStates[invitation.id] === "rejecting", true)}>`);
        if (loadingStates[invitation.id] === "rejecting") {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<span class="spinner-border spinner-border-sm me-2" role="status"></span> ${escape_html(t.rejectingInvitation)}`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`${escape_html(t.reject)}`);
        }
        $$payload.out.push(`<!--]--></button></div></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="mt-auto"><a${attr("href", toLocaleHref(`/communes/${invitation.commune?.id || invitation.communeId}`))} class="btn btn-outline-primary w-100">${escape_html(t.viewCommune)}</a></div>`);
      }
      $$payload.out.push(`<!--]--></div></div></div>`);
    }
    $$payload.out.push(`<!--]--></div>`);
  }
  $$payload.out.push(`<!--]--> `);
  if (isHasMoreInvitations) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-3">`);
    {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-B0VHlQs7.js.map
