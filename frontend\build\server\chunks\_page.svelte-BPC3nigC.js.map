{"version": 3, "file": "_page.svelte-BPC3nigC.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/reactor/communities/_page.svelte.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, w as pop, u as push, x as head, z as escape_html, y as attr, K as ensure_array_like } from \"../../../../../chunks/index.js\";\nimport \"../../../../../chunks/current-user.js\";\nimport { g as goto } from \"../../../../../chunks/client.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { f as formatDate } from \"../../../../../chunks/format-date.js\";\nimport { g as getClient } from \"../../../../../chunks/acrpc.js\";\nimport { M as Modal } from \"../../../../../chunks/modal.js\";\nimport { L as Localized_input } from \"../../../../../chunks/localized-input.js\";\nimport { L as Localized_textarea } from \"../../../../../chunks/localized-textarea.js\";\n/* empty css                                                                           */\nimport { U as User_picker } from \"../../../../../chunks/user-picker.js\";\nimport { R as Reactor_hub_picker } from \"../../../../../chunks/reactor-hub-picker.js\";\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Communities — Reactor of Commune\" },\n      communities: \"Communities\",\n      createCommunity: \"Create Community\",\n      noCommunities: \"No communities found\",\n      head: \"Head\",\n      errorFetchingCommunities: \"Failed to fetch communities\",\n      errorOccurred: \"An error occurred while fetching communities\",\n      loadingMore: \"Loading more communities...\",\n      createdOn: \"Created on\",\n      createCommunityTitle: \"Create New Community\",\n      communityName: \"Community Name\",\n      communityDescription: \"Community Description\",\n      headUser: \"Head User\",\n      headUserPlaceholder: \"Leave empty to use current user\",\n      hub: \"Hub\",\n      hubPlaceholder: \"Leave empty for no hub\",\n      communityNamePlaceholder: \"Enter community name\",\n      communityDescriptionPlaceholder: \"Enter community description\",\n      create: \"Create\",\n      cancel: \"Cancel\",\n      creating: \"Creating...\",\n      communityCreatedSuccess: \"Community created successfully!\",\n      errorCreatingCommunity: \"Failed to create community\",\n      required: \"This field is required\",\n      searchPlaceholder: \"Search communities...\",\n      noHub: \"No hub\"\n    },\n    ru: {\n      _page: {\n        title: \"Сообщества — Реактор Коммуны\"\n      },\n      communities: \"Сообщества\",\n      createCommunity: \"Создать сообщество\",\n      noCommunities: \"Сообщества не найдены\",\n      head: \"Глава\",\n      errorFetchingCommunities: \"Не удалось загрузить сообщества\",\n      errorOccurred: \"Произошла ошибка при загрузке сообществ\",\n      loadingMore: \"Загружаем больше сообществ...\",\n      createdOn: \"Создано\",\n      createCommunityTitle: \"Создать новое сообщество\",\n      communityName: \"Название сообщества\",\n      communityDescription: \"Описание сообщества\",\n      headUser: \"Главный пользователь\",\n      headUserPlaceholder: \"Оставьте пустым для использования текущего пользователя\",\n      hub: \"Хаб\",\n      hubPlaceholder: \"Оставьте пустым для отсутствия хаба\",\n      communityNamePlaceholder: \"Введите название сообщества\",\n      communityDescriptionPlaceholder: \"Введите описание сообщества\",\n      create: \"Создать\",\n      cancel: \"Отмена\",\n      creating: \"Создаем...\",\n      communityCreatedSuccess: \"Сообщество успешно создано!\",\n      errorCreatingCommunity: \"Не удалось создать сообщество\",\n      required: \"Это поле обязательно\",\n      searchPlaceholder: \"Поиск сообществ...\",\n      noHub: \"Нет хаба\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const { locale, toLocaleHref, getAppropriateLocalization } = data;\n  const t = i18n[locale];\n  let communities = data.communities;\n  let showCreateModal = false;\n  let searchInputValue = data.searchQuery || \"\";\n  let isHasMoreCommunities = data.isHasMoreCommunities;\n  let isCreating = false;\n  let createError = null;\n  let createSuccess = null;\n  let communityName = [];\n  let communityDescription = [];\n  let headUserId = null;\n  let hubId = null;\n  function closeCreateModal() {\n    showCreateModal = false;\n  }\n  function validateCreateForm() {\n    if (!communityName.some((item) => item.value.trim().length > 0)) {\n      createError = t.required;\n      return false;\n    }\n    if (!communityDescription.some((item) => item.value.trim().length > 0)) {\n      createError = t.required;\n      return false;\n    }\n    return true;\n  }\n  async function handleCreateCommunity() {\n    if (!validateCreateForm()) return;\n    isCreating = true;\n    createError = null;\n    createSuccess = null;\n    try {\n      const { id } = await api.reactor.community.post({\n        hubId: hubId || null,\n        headUserId,\n        name: communityName,\n        description: communityDescription\n      });\n      createSuccess = t.communityCreatedSuccess;\n      setTimeout(\n        () => {\n          goto(toLocaleHref(`/reactor/communities/${id}`));\n        },\n        1500\n      );\n    } catch (err) {\n      createError = err instanceof Error ? err.message : t.errorCreatingCommunity;\n      console.error(err);\n    } finally {\n      isCreating = false;\n    }\n  }\n  function truncateDescription(text, maxLength = 200) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    head($$payload2, ($$payload3) => {\n      $$payload3.title = `<title>${escape_html(t._page.title)}</title>`;\n    });\n    $$payload2.out.push(`<div class=\"container my-4 mb-5\"><div class=\"d-flex justify-content-between align-items-center my-4 gap-3\"><h1 class=\"mb-0\">${escape_html(t.communities)}</h1> <div class=\"d-flex align-items-center gap-3\"><div class=\"search-container svelte-jyzss\"><input type=\"text\" class=\"form-control svelte-jyzss\"${attr(\"placeholder\", t.searchPlaceholder)}${attr(\"value\", searchInputValue)} style=\"min-width: 250px;\"/></div> `);\n    if (data.user?.role === \"admin\") {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<button class=\"btn btn-primary\">${escape_html(t.createCommunity)}</button>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div></div> `);\n    if (communities.length === 0) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"text-center py-5\"><p class=\"text-muted\">${escape_html(t.noCommunities)}</p></div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      const each_array = ensure_array_like(communities);\n      $$payload2.out.push(`<div class=\"row g-4\"><!--[-->`);\n      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n        let community = each_array[$$index];\n        $$payload2.out.push(`<div class=\"col-12\"><div class=\"card shadow-sm h-100 svelte-jyzss\"><div class=\"row g-0 h-100\"><div class=\"col-md-3 col-lg-2\"><div class=\"community-image-container svelte-jyzss\">`);\n        if (community.image) {\n          $$payload2.out.push(\"<!--[-->\");\n          $$payload2.out.push(`<img${attr(\"src\", `/images/${community.image}`)}${attr(\"alt\", getAppropriateLocalization(community.name) || \"Community\")} class=\"community-image svelte-jyzss\"/>`);\n        } else {\n          $$payload2.out.push(\"<!--[!-->\");\n          $$payload2.out.push(`<div class=\"community-image-placeholder svelte-jyzss\"><i class=\"bi bi-people fs-1 text-muted\"></i></div>`);\n        }\n        $$payload2.out.push(`<!--]--></div></div> <div class=\"col-md-9 col-lg-10\"><div class=\"card-body d-flex flex-column h-100 p-4\"><div class=\"d-flex justify-content-between align-items-start mb-3\"><h4 class=\"card-title mb-0 flex-grow-1\"><a${attr(\"href\", toLocaleHref(`/reactor/communities/${community.id}`))} style=\"text-decoration: none;\">${escape_html(getAppropriateLocalization(community.name) || \"No name?\")}</a></h4> <small class=\"text-muted ms-3\">${escape_html(t.createdOn)}\n                      ${escape_html(formatDate(community.createdAt, locale))}</small></div> <p class=\"card-text text-muted mb-3 flex-grow-1\">${escape_html(truncateDescription(getAppropriateLocalization(community.description) || \"\"))}</p> `);\n        if (community.hub) {\n          $$payload2.out.push(\"<!--[-->\");\n          $$payload2.out.push(`<div class=\"mb-3\"><div class=\"d-flex align-items-center\"><div class=\"me-3\">`);\n          if (community.hub.image) {\n            $$payload2.out.push(\"<!--[-->\");\n            $$payload2.out.push(`<img${attr(\"src\", `/images/${community.hub.image}`)}${attr(\"alt\", getAppropriateLocalization(community.hub.name))} class=\"rounded\" style=\"width: 32px; height: 32px; object-fit: cover;\"/>`);\n          } else {\n            $$payload2.out.push(\"<!--[!-->\");\n            $$payload2.out.push(`<div class=\"rounded bg-secondary d-flex align-items-center justify-content-center\" style=\"width: 32px; height: 32px;\"><i class=\"bi bi-collection text-white\"></i></div>`);\n          }\n          $$payload2.out.push(`<!--]--></div> <div><a${attr(\"href\", toLocaleHref(`/reactor/hubs/${community.hub.id}`))} class=\"fw-medium\" style=\"text-decoration: none;\">${escape_html(getAppropriateLocalization(community.hub.name))}</a></div></div></div>`);\n        } else {\n          $$payload2.out.push(\"<!--[!-->\");\n        }\n        $$payload2.out.push(`<!--]--> <div class=\"d-flex align-items-center\"><div class=\"me-3\">`);\n        if (community.headUser.image) {\n          $$payload2.out.push(\"<!--[-->\");\n          $$payload2.out.push(`<img${attr(\"src\", `/images/${community.headUser.image}`)}${attr(\"alt\", getAppropriateLocalization(community.headUser.name))} class=\"rounded-circle\" style=\"width: 48px; height: 48px; object-fit: cover;\"/>`);\n        } else {\n          $$payload2.out.push(\"<!--[!-->\");\n          $$payload2.out.push(`<div class=\"rounded-circle bg-secondary d-flex align-items-center justify-content-center\" style=\"width: 48px; height: 48px;\"><i class=\"bi bi-person-fill text-white\"></i></div>`);\n        }\n        $$payload2.out.push(`<!--]--></div> <div><a${attr(\"href\", toLocaleHref(`/users/${community.headUser.id}`))} class=\"fw-medium\" style=\"text-decoration: none;\">${escape_html(getAppropriateLocalization(community.headUser.name))}</a></div></div></div></div></div></div></div>`);\n      }\n      $$payload2.out.push(`<!--]--></div>`);\n    }\n    $$payload2.out.push(`<!--]--> `);\n    if (isHasMoreCommunities) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"text-center py-3\">`);\n      {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--></div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--> `);\n    {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div> `);\n    if (data.user?.role === \"admin\") {\n      $$payload2.out.push(\"<!--[-->\");\n      Modal($$payload2, {\n        show: showCreateModal,\n        title: t.createCommunityTitle,\n        onClose: closeCreateModal,\n        onSubmit: handleCreateCommunity,\n        submitText: isCreating ? t.creating : t.create,\n        cancelText: t.cancel,\n        submitDisabled: isCreating || !communityName.some((item) => item.value.trim().length > 0) || !communityDescription.some((item) => item.value.trim().length > 0),\n        isSubmitting: isCreating,\n        children: ($$payload3) => {\n          if (createError) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"alert alert-danger mb-3\">${escape_html(createError)}</div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n          }\n          $$payload3.out.push(`<!--]--> `);\n          if (createSuccess) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"alert alert-success mb-3\">${escape_html(createSuccess)}</div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n          }\n          $$payload3.out.push(`<!--]--> <form>`);\n          User_picker($$payload3, {\n            locale,\n            label: t.headUser,\n            placeholder: t.headUserPlaceholder,\n            get selectedUserId() {\n              return headUserId;\n            },\n            set selectedUserId($$value) {\n              headUserId = $$value;\n              $$settled = false;\n            }\n          });\n          $$payload3.out.push(`<!----> <div class=\"form-text mb-3\">${escape_html(t.headUserPlaceholder)}</div> `);\n          Reactor_hub_picker($$payload3, {\n            locale,\n            label: t.hub,\n            placeholder: t.hubPlaceholder,\n            get selectedHubId() {\n              return hubId;\n            },\n            set selectedHubId($$value) {\n              hubId = $$value;\n              $$settled = false;\n            }\n          });\n          $$payload3.out.push(`<!----> <div class=\"form-text mb-3\">${escape_html(t.hubPlaceholder)}</div> `);\n          Localized_input($$payload3, {\n            locale,\n            id: \"community-name\",\n            label: t.communityName,\n            placeholder: t.communityNamePlaceholder,\n            required: true,\n            get value() {\n              return communityName;\n            },\n            set value($$value) {\n              communityName = $$value;\n              $$settled = false;\n            }\n          });\n          $$payload3.out.push(`<!----> `);\n          Localized_textarea($$payload3, {\n            locale,\n            id: \"community-description\",\n            label: t.communityDescription,\n            placeholder: t.communityDescriptionPlaceholder,\n            rows: 4,\n            required: true,\n            get value() {\n              return communityDescription;\n            },\n            set value($$value) {\n              communityDescription = $$value;\n              $$settled = false;\n            }\n          });\n          $$payload3.out.push(`<!----></form>`);\n        }\n      });\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]-->`);\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAaA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE;AAC1D,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,aAAa,EAAE,sBAAsB;AAC3C,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,wBAAwB,EAAE,6BAA6B;AAC7D,MAAM,aAAa,EAAE,8CAA8C;AACnE,MAAM,WAAW,EAAE,6BAA6B;AAChD,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,oBAAoB,EAAE,sBAAsB;AAClD,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,oBAAoB,EAAE,uBAAuB;AACnD,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,mBAAmB,EAAE,iCAAiC;AAC5D,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,cAAc,EAAE,wBAAwB;AAC9C,MAAM,wBAAwB,EAAE,sBAAsB;AACtD,MAAM,+BAA+B,EAAE,6BAA6B;AACpE,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,uBAAuB,EAAE,iCAAiC;AAChE,MAAM,sBAAsB,EAAE,4BAA4B;AAC1D,MAAM,QAAQ,EAAE,wBAAwB;AACxC,MAAM,iBAAiB,EAAE,uBAAuB;AAChD,MAAM,KAAK,EAAE;AACb,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,WAAW,EAAE,YAAY;AAC/B,MAAM,eAAe,EAAE,oBAAoB;AAC3C,MAAM,aAAa,EAAE,uBAAuB;AAC5C,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,wBAAwB,EAAE,iCAAiC;AACjE,MAAM,aAAa,EAAE,yCAAyC;AAC9D,MAAM,WAAW,EAAE,+BAA+B;AAClD,MAAM,SAAS,EAAE,SAAS;AAC1B,MAAM,oBAAoB,EAAE,0BAA0B;AACtD,MAAM,aAAa,EAAE,qBAAqB;AAC1C,MAAM,oBAAoB,EAAE,qBAAqB;AACjD,MAAM,QAAQ,EAAE,sBAAsB;AACtC,MAAM,mBAAmB,EAAE,yDAAyD;AACpF,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,cAAc,EAAE,qCAAqC;AAC3D,MAAM,wBAAwB,EAAE,6BAA6B;AAC7D,MAAM,+BAA+B,EAAE,6BAA6B;AACpE,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,uBAAuB,EAAE,6BAA6B;AAC5D,MAAM,sBAAsB,EAAE,+BAA+B;AAC7D,MAAM,QAAQ,EAAE,sBAAsB;AACtC,MAAM,iBAAiB,EAAE,oBAAoB;AAC7C,MAAM,KAAK,EAAE;AACb;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,0BAA0B,EAAE,GAAG,IAAI;AACnE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW;AACpC,EAAE,IAAI,eAAe,GAAG,KAAK;AAC7B,EAAE,IAAI,gBAAgB,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE;AAC/C,EAAE,IAAI,oBAAoB,GAAG,IAAI,CAAC,oBAAoB;AACtD,EAAE,IAAI,UAAU,GAAG,KAAK;AACxB,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,IAAI,aAAa,GAAG,IAAI;AAC1B,EAAE,IAAI,aAAa,GAAG,EAAE;AACxB,EAAE,IAAI,oBAAoB,GAAG,EAAE;AAC/B,EAAE,IAAI,UAAU,GAAG,IAAI;AACvB,EAAE,IAAI,KAAK,GAAG,IAAI;AAClB,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,eAAe,GAAG,KAAK;AAC3B,EAAE;AACF,EAAE,SAAS,kBAAkB,GAAG;AAChC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;AACrE,MAAM,WAAW,GAAG,CAAC,CAAC,QAAQ;AAC9B,MAAM,OAAO,KAAK;AAClB,IAAI;AACJ,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;AAC5E,MAAM,WAAW,GAAG,CAAC,CAAC,QAAQ;AAC9B,MAAM,OAAO,KAAK;AAClB,IAAI;AACJ,IAAI,OAAO,IAAI;AACf,EAAE;AACF,EAAE,eAAe,qBAAqB,GAAG;AACzC,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;AAC/B,IAAI,UAAU,GAAG,IAAI;AACrB,IAAI,WAAW,GAAG,IAAI;AACtB,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,IAAI;AACR,MAAM,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;AACtD,QAAQ,KAAK,EAAE,KAAK,IAAI,IAAI;AAC5B,QAAQ,UAAU;AAClB,QAAQ,IAAI,EAAE,aAAa;AAC3B,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR,MAAM,aAAa,GAAG,CAAC,CAAC,uBAAuB;AAC/C,MAAM,UAAU;AAChB,QAAQ,MAAM;AACd,UAAU,IAAI,CAAC,YAAY,CAAC,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1D,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO;AACP,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,WAAW,GAAG,GAAG,YAAY,KAAK,GAAG,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,sBAAsB;AACjF,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AACxB,IAAI,CAAC,SAAS;AACd,MAAM,UAAU,GAAG,KAAK;AACxB,IAAI;AACJ,EAAE;AACF,EAAE,SAAS,mBAAmB,CAAC,IAAI,EAAE,SAAS,GAAG,GAAG,EAAE;AACtD,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,OAAO,IAAI;AAC7C,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,KAAK;AAC3C,EAAE;AACF,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,KAAK;AACrC,MAAM,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACvE,IAAI,CAAC,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4HAA4H,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,kJAAkJ,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC,mCAAmC,CAAC,CAAC;AACtb,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,OAAO,EAAE;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,CAAC;AACvG,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,CAAC;AAChD,IAAI,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AAClC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC;AAC1H,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,MAAM,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC;AACvD,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6BAA6B,CAAC,CAAC;AAC1D,MAAM,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACzF,QAAQ,IAAI,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC;AAC3C,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iLAAiL,CAAC,CAAC;AAChN,QAAQ,IAAI,SAAS,CAAC,KAAK,EAAE;AAC7B,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC,uCAAuC,CAAC,CAAC;AACjM,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wGAAwG,CAAC,CAAC;AACzI,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sNAAsN,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,qBAAqB,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;AACne,sBAAsB,EAAE,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,gEAAgE,EAAE,WAAW,CAAC,mBAAmB,CAAC,0BAA0B,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAChP,QAAQ,IAAI,SAAS,CAAC,GAAG,EAAE;AAC3B,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2EAA2E,CAAC,CAAC;AAC5G,UAAU,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE;AACnC,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,wEAAwE,CAAC,CAAC;AAC7N,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uKAAuK,CAAC,CAAC;AAC1M,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,kDAAkD,EAAE,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC;AAC/P,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kEAAkE,CAAC,CAAC;AACjG,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE;AACtC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,+EAA+E,CAAC,CAAC;AAC5O,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+KAA+K,CAAC,CAAC;AAChN,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,kDAAkD,EAAE,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,8CAA8C,CAAC,CAAC;AACxR,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI,IAAI,oBAAoB,EAAE;AAC9B,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AAC3D,MAAM;AACN,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI;AACJ,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC1C,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,OAAO,EAAE;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,KAAK,CAAC,UAAU,EAAE;AACxB,QAAQ,IAAI,EAAE,eAAe;AAC7B,QAAQ,KAAK,EAAE,CAAC,CAAC,oBAAoB;AACrC,QAAQ,OAAO,EAAE,gBAAgB;AACjC,QAAQ,QAAQ,EAAE,qBAAqB;AACvC,QAAQ,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM;AACtD,QAAQ,UAAU,EAAE,CAAC,CAAC,MAAM;AAC5B,QAAQ,cAAc,EAAE,UAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AACvK,QAAQ,YAAY,EAAE,UAAU;AAChC,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,IAAI,WAAW,EAAE;AAC3B,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;AACzG,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AAC1C,UAAU,IAAI,aAAa,EAAE;AAC7B,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC;AAC5G,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAChD,UAAU,WAAW,CAAC,UAAU,EAAE;AAClC,YAAY,MAAM;AAClB,YAAY,KAAK,EAAE,CAAC,CAAC,QAAQ;AAC7B,YAAY,WAAW,EAAE,CAAC,CAAC,mBAAmB;AAC9C,YAAY,IAAI,cAAc,GAAG;AACjC,cAAc,OAAO,UAAU;AAC/B,YAAY,CAAC;AACb,YAAY,IAAI,cAAc,CAAC,OAAO,EAAE;AACxC,cAAc,UAAU,GAAG,OAAO;AAClC,cAAc,SAAS,GAAG,KAAK;AAC/B,YAAY;AACZ,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oCAAoC,EAAE,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,CAAC;AACjH,UAAU,kBAAkB,CAAC,UAAU,EAAE;AACzC,YAAY,MAAM;AAClB,YAAY,KAAK,EAAE,CAAC,CAAC,GAAG;AACxB,YAAY,WAAW,EAAE,CAAC,CAAC,cAAc;AACzC,YAAY,IAAI,aAAa,GAAG;AAChC,cAAc,OAAO,KAAK;AAC1B,YAAY,CAAC;AACb,YAAY,IAAI,aAAa,CAAC,OAAO,EAAE;AACvC,cAAc,KAAK,GAAG,OAAO;AAC7B,cAAc,SAAS,GAAG,KAAK;AAC/B,YAAY;AACZ,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oCAAoC,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC;AAC5G,UAAU,eAAe,CAAC,UAAU,EAAE;AACtC,YAAY,MAAM;AAClB,YAAY,EAAE,EAAE,gBAAgB;AAChC,YAAY,KAAK,EAAE,CAAC,CAAC,aAAa;AAClC,YAAY,WAAW,EAAE,CAAC,CAAC,wBAAwB;AACnD,YAAY,QAAQ,EAAE,IAAI;AAC1B,YAAY,IAAI,KAAK,GAAG;AACxB,cAAc,OAAO,aAAa;AAClC,YAAY,CAAC;AACb,YAAY,IAAI,KAAK,CAAC,OAAO,EAAE;AAC/B,cAAc,aAAa,GAAG,OAAO;AACrC,cAAc,SAAS,GAAG,KAAK;AAC/B,YAAY;AACZ,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACzC,UAAU,kBAAkB,CAAC,UAAU,EAAE;AACzC,YAAY,MAAM;AAClB,YAAY,EAAE,EAAE,uBAAuB;AACvC,YAAY,KAAK,EAAE,CAAC,CAAC,oBAAoB;AACzC,YAAY,WAAW,EAAE,CAAC,CAAC,+BAA+B;AAC1D,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,QAAQ,EAAE,IAAI;AAC1B,YAAY,IAAI,KAAK,GAAG;AACxB,cAAc,OAAO,oBAAoB;AACzC,YAAY,CAAC;AACb,YAAY,IAAI,KAAK,CAAC,OAAO,EAAE;AAC/B,cAAc,oBAAoB,GAAG,OAAO;AAC5C,cAAc,SAAS,GAAG,KAAK;AAC/B,YAAY;AACZ,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC/C,QAAQ;AACR,OAAO,CAAC;AACR,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACnC,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;;;;"}