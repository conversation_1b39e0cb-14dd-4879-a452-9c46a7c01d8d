{"version": 3, "file": "_page.svelte-BefHkUCx.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/admin/_page.svelte.js"], "sourcesContent": ["import { z as escape_html, w as pop, u as push } from \"../../../chunks/index.js\";\nimport \"clsx\";\nfunction _page($$payload, $$props) {\n  push();\n  const { data } = $$props;\n  $$payload.out.push(`<div class=\"admin-dashboard svelte-1ch1yjx\"><div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom\"><h1 class=\"h2\">Admin Dashboard</h1></div> <div class=\"row\"><div class=\"col-md-6 col-lg-4 mb-4\"><div class=\"card h-100 svelte-1ch1yjx\"><div class=\"card-body d-flex flex-column\"><div class=\"d-flex align-items-center mb-3\"><div class=\"icon-circle bg-primary text-white me-3 svelte-1ch1yjx\"><i class=\"bi bi-envelope\"></i></div> <div><h5 class=\"card-title mb-0\">User Invites</h5> <small class=\"text-muted\">Manage user invitations</small></div></div> <p class=\"card-text flex-grow-1\">Create and manage user invitations. View all sent invites and their status.</p> <a href=\"/admin/invites\" class=\"btn btn-primary\"><i class=\"bi bi-arrow-right me-1\"></i> Manage Invites</a></div></div></div> <div class=\"col-md-6 col-lg-4 mb-4\"><div class=\"card h-100 svelte-1ch1yjx\"><div class=\"card-body d-flex flex-column\"><div class=\"d-flex align-items-center mb-3\"><div class=\"icon-circle bg-success text-white me-3 svelte-1ch1yjx\"><i class=\"bi bi-people\"></i></div> <div><h5 class=\"card-title mb-0\">Users</h5> <small class=\"text-muted\">User management</small></div></div> <p class=\"card-text flex-grow-1\">View and manage user accounts, roles, and permissions.</p> <button class=\"btn btn-outline-secondary\" disabled><i class=\"bi bi-arrow-right me-1\"></i> Coming Soon</button></div></div></div> <div class=\"col-md-6 col-lg-4 mb-4\"><div class=\"card h-100 svelte-1ch1yjx\"><div class=\"card-body d-flex flex-column\"><div class=\"d-flex align-items-center mb-3\"><div class=\"icon-circle bg-info text-white me-3 svelte-1ch1yjx\"><i class=\"bi bi-house\"></i></div> <div><h5 class=\"card-title mb-0\">Communes</h5> <small class=\"text-muted\">Commune management</small></div></div> <p class=\"card-text flex-grow-1\">Manage communes, memberships, and community settings.</p> <button class=\"btn btn-outline-secondary\" disabled><i class=\"bi bi-arrow-right me-1\"></i> Coming Soon</button></div></div></div></div> <div class=\"row mt-4\"><div class=\"col-12\"><div class=\"card svelte-1ch1yjx\"><div class=\"card-header\"><h5 class=\"mb-0\"><i class=\"bi bi-info-circle me-2\"></i> System Information</h5></div> <div class=\"card-body\"><div class=\"row\"><div class=\"col-md-6\"><p><strong>Logged in as:</strong> ${escape_html(data.me.email)}</p> <p><strong>Role:</strong> <span class=\"badge bg-primary\">${escape_html(data.me.role)}</span></p></div> <div class=\"col-md-6\"><p><strong>User ID:</strong> <code>${escape_html(data.me.id)}</code></p></div></div></div></div></div></div></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;AAEA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,ywEAAywE,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,8DAA8D,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,2EAA2E,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,qDAAqD,CAAC,CAAC;AACljF,EAAE,GAAG,EAAE;AACP;;;;"}