import { u as push, N as ensure_array_like, z as escape_html, J as attr_class, y as attr, w as pop, O as maybe_selected } from './index-0Ke2LYl0.js';
import './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import { M as Modal } from './modal-BDhz9azZ.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import './schema-CmMg_B_X.js';

/* empty css                                                                        */
function _page($$payload, $$props) {
  push();
  const { data } = $$props;
  const { fetcher: api } = getClient();
  let invites = data.invites;
  let isHasMoreInvites = data.isHasMoreInvites;
  let isLoadingMore = false;
  let showInviteModal = false;
  let newInviteEmail = "";
  let newInviteName = "";
  let newInviteLocale = "ru";
  let isInviting = false;
  let inviteError = null;
  let inviteSuccess = null;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  async function handleInviteUser() {
    if (!newInviteEmail.trim()) {
      inviteError = "Email is required";
      return;
    }
    if (!emailRegex.test(newInviteEmail.trim())) {
      inviteError = "Please enter a valid email address";
      return;
    }
    isInviting = true;
    inviteError = null;
    inviteSuccess = null;
    try {
      const response = await api.user.invite.put({
        email: newInviteEmail.trim(),
        name: newInviteName.trim() || null,
        locale: newInviteLocale
      });
      const existingIndex = invites.findIndex((invite) => invite.email === newInviteEmail.trim());
      const newInviteItem = {
        id: response.id,
        email: newInviteEmail.trim(),
        name: newInviteName.trim() || null,
        locale: newInviteLocale,
        isUsed: false
      };
      if (existingIndex >= 0) {
        invites[existingIndex] = newInviteItem;
      } else {
        invites = [newInviteItem, ...invites];
      }
      inviteSuccess = "Invitation sent successfully!";
      newInviteEmail = "";
      newInviteName = "";
      newInviteLocale = "ru";
      setTimeout(
        () => {
          showInviteModal = false;
          inviteSuccess = null;
        },
        1500
      );
    } catch (error) {
      inviteError = error instanceof Error ? error.message : "Failed to send invitation";
      console.error("Error sending invitation:", error);
    } finally {
      isInviting = false;
    }
  }
  function closeInviteModal() {
    showInviteModal = false;
    newInviteEmail = "";
    newInviteName = "";
    newInviteLocale = "ru";
    inviteError = null;
    inviteSuccess = null;
  }
  $$payload.out.push(`<div class="admin-invites svelte-17chgb9"><div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"><h1 class="h2">User Invites</h1> <button type="button" class="btn btn-primary"><i class="bi bi-plus-circle me-1"></i> Send Invite</button></div> `);
  if (invites.length === 0) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-5"><div class="mb-3"><i class="bi bi-envelope display-1 text-muted"></i></div> <h4 class="text-muted">No invitations yet</h4> <p class="text-muted">Start by sending your first user invitation.</p> <button type="button" class="btn btn-primary"><i class="bi bi-plus-circle me-1"></i> Send First Invite</button></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    const each_array = ensure_array_like(invites);
    $$payload.out.push(`<div class="card"><div class="card-header"><h5 class="mb-0"><i class="bi bi-list-ul me-2"></i> All Invitations (${escape_html(invites.length)})</h5></div> <div class="card-body p-0"><div class="table-responsive"><table class="table table-hover mb-0"><thead class="table-light"><tr><th scope="col">Email</th><th scope="col">Name</th><th scope="col">Language</th><th scope="col">Status</th><th scope="col">Actions</th></tr></thead><tbody><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let invite = each_array[$$index];
      $$payload.out.push(`<tr${attr_class("svelte-17chgb9", void 0, { "table-secondary": invite.isUsed })}><td><div class="d-flex align-items-center"><i class="bi bi-envelope me-2 text-muted svelte-17chgb9"></i> <span${attr_class("svelte-17chgb9", void 0, { "text-muted": invite.isUsed })}>${escape_html(invite.email)}</span></div></td><td><div class="d-flex align-items-center"><i class="bi bi-person me-2 text-muted svelte-17chgb9"></i> <span${attr_class("svelte-17chgb9", void 0, { "text-muted": invite.isUsed })}>${escape_html(invite.name || "—")}</span></div></td><td><div class="d-flex align-items-center"><i class="bi bi-globe me-2 text-muted svelte-17chgb9"></i> <span class="badge bg-light text-dark border">${escape_html(invite.locale.toUpperCase())}</span></div></td><td>`);
      if (invite.isUsed) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i> Used</span>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<span class="badge bg-warning text-dark"><i class="bi bi-clock me-1"></i> Pending</span>`);
      }
      $$payload.out.push(`<!--]--></td><td><button type="button" class="btn btn-sm btn-outline-danger" aria-label="Delete invitation"><i class="bi bi-trash"></i></button></td></tr>`);
    }
    $$payload.out.push(`<!--]--></tbody></table></div></div></div> `);
    if (isHasMoreInvites) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="text-center mt-4"><button type="button" class="btn btn-outline-primary"${attr("disabled", isLoadingMore, true)}>`);
      {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<i class="bi bi-arrow-down-circle me-1"></i> Load More`);
      }
      $$payload.out.push(`<!--]--></button></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]--></div> `);
  Modal($$payload, {
    show: showInviteModal,
    title: "Send User Invitation",
    onClose: closeInviteModal,
    onSubmit: handleInviteUser,
    submitText: "Send Invite",
    cancelText: "Cancel",
    submitDisabled: isInviting || !newInviteEmail.trim() || !emailRegex.test(newInviteEmail.trim()),
    isSubmitting: isInviting,
    children: ($$payload2) => {
      if (inviteSuccess) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div class="alert alert-success" role="alert"><i class="bi bi-check-circle me-2"></i> ${escape_html(inviteSuccess)}</div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
        $$payload2.out.push(`<form><div class="mb-3"><label for="inviteEmail" class="form-label">Email Address *</label> <input type="email" class="form-control" id="inviteEmail"${attr("value", newInviteEmail)} placeholder="Enter email address" required${attr("disabled", isInviting, true)}/> <div class="form-text">The user will receive an invitation to join the platform.</div></div> <div class="mb-3"><label for="inviteName" class="form-label">Name (Optional)</label> <input type="text" class="form-control" id="inviteName"${attr("value", newInviteName)} placeholder="Enter user's name"${attr("disabled", isInviting, true)}/> <div class="form-text">This name will be used in the invitation email.</div></div> <div class="mb-3"><label for="inviteLocale" class="form-label">Language *</label> <select class="form-select" id="inviteLocale"${attr("disabled", isInviting, true)}>`);
        $$payload2.select_value = newInviteLocale;
        $$payload2.out.push(`<option value="en"${maybe_selected($$payload2, "en")}>English</option><option value="ru"${maybe_selected($$payload2, "ru")}>Русский</option>`);
        $$payload2.select_value = void 0;
        $$payload2.out.push(`</select> <div class="form-text">The language for the invitation email.</div></div> `);
        if (inviteError) {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`<div class="alert alert-danger" role="alert"><i class="bi bi-exclamation-triangle me-2"></i> ${escape_html(inviteError)}</div>`);
        } else {
          $$payload2.out.push("<!--[!-->");
        }
        $$payload2.out.push(`<!--]--></form>`);
      }
      $$payload2.out.push(`<!--]-->`);
    }
  });
  $$payload.out.push(`<!---->`);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-Bi0PMZfO.js.map
