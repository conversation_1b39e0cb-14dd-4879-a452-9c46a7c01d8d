import { u as push, x as head, z as escape_html, y as attr, N as ensure_array_like, J as attr_class, w as pop } from './index-0Ke2LYl0.js';
import './current-user-BM0W6LNm.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import { f as formatDate } from './format-date-DgRnEWcB.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import './schema-CmMg_B_X.js';

function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Join Requests — Commune" },
      joinRequests: "Join Requests",
      loading: "Loading...",
      noJoinRequests: "No join requests found",
      member: "member",
      members: "members",
      headMember: "Head",
      errorFetchingJoinRequests: "Failed to fetch join requests",
      errorOccurred: "An error occurred while fetching join requests",
      loadingMore: "Loading more join requests...",
      cancel: "Cancel Request",
      pending: "Pending",
      accepted: "Accepted",
      rejected: "Rejected",
      requestedOn: "Requested on",
      cancelingRequest: "Canceling...",
      errorCancelingRequest: "Failed to cancel join request",
      requestCanceled: "Join request canceled",
      backToCommunes: "Back to Communes",
      viewCommune: "View Commune",
      awaitingApproval: "Awaiting approval from commune head",
      noImage: "No image",
      communeImageAlt: "Commune image"
    },
    ru: {
      _page: {
        title: "Заявки на вступление — Коммуна"
      },
      joinRequests: "Заявки на вступление",
      loading: "Загрузка...",
      noJoinRequests: "Заявки не найдены",
      member: "участник",
      members: "участников",
      headMember: "Глава",
      errorFetchingJoinRequests: "Не удалось загрузить заявки",
      errorOccurred: "Произошла ошибка при загрузке заявок",
      loadingMore: "Загружаем больше заявок...",
      cancel: "Отменить заявку",
      pending: "Ожидает",
      accepted: "Принято",
      rejected: "Отклонено",
      requestedOn: "Подана",
      cancelingRequest: "Отменяем...",
      errorCancelingRequest: "Не удалось отменить заявку",
      requestCanceled: "Заявка отменена",
      backToCommunes: "Назад к коммунам",
      viewCommune: "Посмотреть коммуну",
      awaitingApproval: "Ожидает одобрения главы коммуны",
      noImage: "Нет изображения",
      communeImageAlt: "Изображение коммуны"
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const { locale, toLocaleHref, getAppropriateLocalization } = data;
  const t = i18n[locale];
  let joinRequests = data.joinRequests;
  let isHasMoreJoinRequests = data.isHasMoreJoinRequests;
  let loadingStates = {};
  function getStatusBadgeClass(status) {
    switch (status) {
      case "pending":
        return "bg-warning text-dark";
      case "accepted":
        return "bg-success";
      case "rejected":
        return "bg-danger";
      default:
        return "bg-secondary";
    }
  }
  function getStatusText(status) {
    switch (status) {
      case "pending":
        return t.pending;
      case "accepted":
        return t.accepted;
      case "rejected":
        return t.rejected;
      default:
        return status;
    }
  }
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;
  });
  $$payload.out.push(`<div class="container my-4 mb-5"><div class="d-flex justify-content-between align-items-center my-4"><h1>${escape_html(t.joinRequests)}</h1> <a${attr("href", toLocaleHref("/communes"))} class="btn btn-outline-secondary">${escape_html(t.backToCommunes)}</a></div> `);
  if (joinRequests.length === 0) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-5"><p class="text-muted">${escape_html(t.noJoinRequests)}</p></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    const each_array = ensure_array_like(joinRequests);
    $$payload.out.push(`<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4"><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let joinRequest = each_array[$$index];
      $$payload.out.push(`<div class="col"><div class="card h-100 shadow-sm">`);
      if (joinRequest.commune.image) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="image-container svelte-lafg1c"><img${attr("src", `/images/${joinRequest.commune.image}`)}${attr("alt", `${t.communeImageAlt}`)} class="svelte-lafg1c"/></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="bg-light text-center d-flex align-items-center justify-content-center" style="height: 140px;"><span class="text-muted">${escape_html(t.noImage)}</span></div>`);
      }
      $$payload.out.push(`<!--]--> <div class="card-body d-flex flex-column"><div class="d-flex justify-content-between align-items-start mb-2"><span${attr_class(`badge ${getStatusBadgeClass(joinRequest.status)}`, "svelte-lafg1c")}>${escape_html(getStatusText(joinRequest.status))}</span> <small class="text-muted">${escape_html(t.requestedOn)}
                  ${escape_html(formatDate(joinRequest.createdAt, locale))}</small></div> <h5 class="card-title fs-5 text-truncate mb-2">${escape_html(getAppropriateLocalization(joinRequest.commune?.name) || "Unknown Commune")}</h5> <p class="card-text text-muted small mb-3" style="height: 3rem; overflow: hidden">${escape_html(getAppropriateLocalization(joinRequest.commune.description) || "")}</p> <div class="mb-3"><span class="badge bg-primary mb-2">${escape_html(joinRequest.commune?.memberCount || 0)}
                  ${escape_html((joinRequest.commune?.memberCount || 0) === 1 ? t.member : t.members)}</span> <div class="small text-muted"><div>${escape_html(t.headMember)}:</div> <div class="d-flex flex-column">${escape_html(getAppropriateLocalization(joinRequest.commune.headMember.name) || "Unknown")}</div></div></div> `);
      if (joinRequest.status === "pending") {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="mt-auto"><div class="alert alert-info small mb-2">${escape_html(t.awaitingApproval)}</div> <button class="btn btn-outline-danger w-100"${attr("disabled", loadingStates[joinRequest.id] === "canceling", true)}>`);
        if (loadingStates[joinRequest.id] === "canceling") {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<span class="spinner-border spinner-border-sm me-2" role="status"></span> ${escape_html(t.cancelingRequest)}`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`${escape_html(t.cancel)}`);
        }
        $$payload.out.push(`<!--]--></button></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="mt-auto"><a${attr("href", toLocaleHref(`/communes/${joinRequest.commune?.id || joinRequest.communeId}`))} class="btn btn-outline-primary w-100">${escape_html(t.viewCommune)}</a></div>`);
      }
      $$payload.out.push(`<!--]--></div></div></div>`);
    }
    $$payload.out.push(`<!--]--></div>`);
  }
  $$payload.out.push(`<!--]--> `);
  if (isHasMoreJoinRequests) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-3">`);
    {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BpxNT0Q7.js.map
