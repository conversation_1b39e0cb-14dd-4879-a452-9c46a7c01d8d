import { u as push, Q as copy_payload, T as assign_payload, w as pop, x as head, z as escape_html, N as ensure_array_like, G as attr_style, y as attr, K as stringify, J as attr_class } from './index-0Ke2LYl0.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import { P as Post_card, R as Right_menu, C as Create_post_modal } from './right-menu-LUsku24I.js';
import './current-user-BM0W6LNm.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import { L as Localized_textarea } from './localized-textarea-SdDnJXwN.js';
import './schema-CmMg_B_X.js';
import './client-BUddp2Wf.js';
import './index2-DkUtb91y.js';
import './modal-BDhz9azZ.js';
import './localized-input-BFX4O5ct.js';
import './editor-DhPp1GEa.js';
import './reactor-hub-picker-B9joYKuZ.js';
import './html-FW6Ia4bL.js';

function Comment($$payload, $$props) {
  push();
  const i18n = {
    en: {
      expand: "expand",
      collapse: "collapse",
      respond: "Respond",
      cancel: "Cancel",
      report: "Report",
      save: "Save",
      share: "Share",
      like: "Like",
      dislike: "Dislike",
      copied: "Copied!",
      send: "Send",
      responsePlaceholder: "Write your response...",
      getPlural(n) {
        if (n === 1) return 0;
        return 1;
      },
      ratingTooltipText(rating2) {
        const likesWord = ["like", "likes"][this.getPlural(rating2.likes % 10)];
        const dislikesWord = ["dislike", "dislikes"][this.getPlural(rating2.dislikes % 10)];
        return `${rating2.likes} ${likesWord}, ${rating2.dislikes} ${dislikesWord}`;
      },
      time: {
        days(n) {
          return `${n} ${n === 1 ? "day" : "days"} ago`;
        },
        hours(n) {
          return `${n} ${n === 1 ? "hour" : "hours"} ago`;
        },
        minutes(n) {
          return `${n} ${n === 1 ? "minute" : "minutes"} ago`;
        },
        seconds(n) {
          return `${n} ${n === 1 ? "second" : "seconds"} ago`;
        },
        rightNow: "right now"
      }
    },
    ru: {
      expand: "развернуть",
      collapse: "свернуть",
      respond: "Ответить",
      cancel: "Отменить",
      report: "Пожаловаться",
      save: "Сохранить",
      share: "Поделиться",
      like: "Нравится",
      dislike: "Не нравится",
      copied: "Скопировано!",
      send: "Отправить",
      responsePlaceholder: "Напишите ваш ответ...",
      getPlural(n) {
        if (n === 1) return 0;
        if (n >= 2 && n <= 4) return 1;
        return 2;
      },
      ratingTooltipText(rating2) {
        const likesWord = [
          "лайк",
          "лайка",
          "лайков"
        ][this.getPlural(rating2.likes % 10)];
        const dislikesWord = [
          "дизлайк",
          "дизлайка",
          "дизлайков"
        ][this.getPlural(rating2.dislikes % 10)];
        return `${rating2.likes} ${likesWord}, ${rating2.dislikes} ${dislikesWord}`;
      },
      time: {
        getPlural(n) {
          if (n === 1) return 0;
          if (n >= 2 && n <= 4) return 1;
          return 2;
        },
        days(n) {
          const word = [
            "день",
            "дня",
            "дней"
          ][this.getPlural(n)];
          return `${n} ${word} назад`;
        },
        hours(n) {
          const word = [
            "час",
            "часа",
            "часов"
          ][this.getPlural(n)];
          return `${n} ${word} назад`;
        },
        minutes(n) {
          const word = [
            "минуту",
            "минуты",
            "минут"
          ][this.getPlural(n)];
          return `${n} ${word} назад`;
        },
        seconds(n) {
          const word = [
            "секунду",
            "секунды",
            "секунд"
          ][this.getPlural(n)];
          return `${n} ${word} назад`;
        },
        rightNow: "только что"
      }
    }
  };
  const { fetcher: api } = getClient();
  const {
    comment,
    locale,
    routeLocale,
    level = 0,
    commentTree,
    addComment,
    getAppropriateLocalization,
    $$slots,
    $$events,
    ...props
  } = $$props;
  const t = i18n[locale];
  let expanded = props.expanded;
  const authorName = getAppropriateLocalization(comment.author?.name ?? []);
  const body = getAppropriateLocalization(comment.body ?? []);
  let rating = { ...comment.rating };
  const ratingValue = rating.likes - rating.dislikes;
  const ratingTooltipText = t.ratingTooltipText(rating);
  const grayLevelColor = "hsl(0,0%,70%)";
  const levelColors = [
    "hsl(0,70%,50%)",
    "hsl(30,70%,50%)",
    "hsl(60,70%,50%)",
    "hsl(120,70%,50%)",
    "hsl(180,70%,50%)",
    "hsl(240,70%,50%)",
    "hsl(270,70%,50%)",
    "hsl(300,70%,50%)",
    "hsl(330,70%,50%)"
  ];
  const levelColor = levelColors[level % levelColors.length];
  const levelBackgroundColor = comment.childrenCount === 0 ? grayLevelColor : levelColor;
  function formatDate(date) {
    const now = /* @__PURE__ */ new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1e3);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    if (diffDay > 0) {
      return t.time.days(diffDay);
    } else if (diffHour > 0) {
      return t.time.hours(diffHour);
    } else if (diffMin > 0) {
      return t.time.minutes(diffMin);
    } else if (diffSec > 3) {
      return t.time.seconds(diffSec);
    } else {
      return t.time.rightNow;
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<div class="comment mb-3 svelte-1aus6h6"${attr_style("", { "margin-left": level > 0 ? "1.5rem" : "0" })}>`);
    if (comment.childrenCount > 0) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="level-indicator svelte-1aus6h6"${attr_style(`background-color: ${stringify(levelBackgroundColor)};`)} role="button" tabindex="0"${attr("title", expanded ? t.collapse : t.expand)}></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<div class="level-indicator no-children svelte-1aus6h6"${attr_style(`background-color: ${stringify(levelBackgroundColor)};`)}></div>`);
    }
    $$payload2.out.push(`<!--]--> <div class="card svelte-1aus6h6"><div class="card-body p-3 svelte-1aus6h6"><div class="comment-header d-flex justify-content-between align-items-center mb-2 svelte-1aus6h6"><div class="d-flex align-items-center svelte-1aus6h6"><div class="rating-block d-flex align-items-center me-3 svelte-1aus6h6">`);
    if (ratingValue > 0) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<span class="rating-value me-2 text-success svelte-1aus6h6" data-bs-toggle="tooltip" data-bs-placement="top"${attr("title", ratingTooltipText)}>${escape_html(ratingValue)}</span>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      if (ratingValue < 0) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<span class="rating-value me-2 text-danger svelte-1aus6h6" data-bs-toggle="tooltip" data-bs-placement="top"${attr("title", ratingTooltipText)}>${escape_html(ratingValue)}</span>`);
      } else {
        $$payload2.out.push("<!--[!-->");
        $$payload2.out.push(`<span class="rating-value me-2 svelte-1aus6h6" data-bs-toggle="tooltip" data-bs-placement="top"${attr("title", ratingTooltipText)}>0</span>`);
      }
      $$payload2.out.push(`<!--]-->`);
    }
    $$payload2.out.push(`<!--]--> <div class="rating-buttons svelte-1aus6h6"><button${attr_class(`btn btn-sm me-1 ${rating.status === "like" ? "btn-success" : "btn-outline-success"}`, "svelte-1aus6h6")}${attr("aria-label", t.like)}><i class="bi bi-hand-thumbs-up"></i></button> <button${attr_class(`btn btn-sm ${rating.status === "dislike" ? "btn-danger" : "btn-outline-danger"}`, "svelte-1aus6h6")}${attr("aria-label", t.dislike)}><i class="bi bi-hand-thumbs-down"></i></button></div></div> <div class="author-info d-flex align-items-center">`);
    if (comment.author?.image) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<img${attr("src", `/images/${comment.author.image}`)} alt="avatar" class="avatar rounded-circle me-2 svelte-1aus6h6" width="32" height="32"${attr_style("", { "object-fit": "cover" })}/>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`<div class="avatar rounded-circle me-2 svelte-1aus6h6"${attr_style(`background-color: ${stringify(levelColor)};`)}></div>`);
    }
    $$payload2.out.push(`<!--]--> <div><div class="author-name fw-bold">`);
    if (comment.isAnonymous) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`${escape_html(comment.anonimityReason ? `Anonymous (${comment.anonimityReason})` : "Anonymous")}`);
    } else {
      $$payload2.out.push("<!--[!-->");
      $$payload2.out.push(`${escape_html(authorName ?? (Math.random() > 0.5 ? "John Doe" : "Jane Doe"))}`);
    }
    $$payload2.out.push(`<!--]--></div> <div class="comment-time small text-muted svelte-1aus6h6"${attr("title", comment.createdAt.toISOString())}>${escape_html(formatDate(comment.createdAt))}</div></div></div></div> <div class="action-buttons svelte-1aus6h6"></div></div> <div class="comment-body mb-2"><p class="mb-0">`);
    if (comment.deleteReason) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<span class="text-muted">deleted: ${escape_html(comment.deleteReason)}</span>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      if (comment.deletedAt) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<span class="text-muted">deleted with no reason</span>`);
      } else {
        $$payload2.out.push("<!--[!-->");
        if (body) {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`${escape_html(body)}`);
        } else {
          $$payload2.out.push("<!--[!-->");
          $$payload2.out.push(`<span class="text-muted">No body?</span>`);
        }
        $$payload2.out.push(`<!--]-->`);
      }
      $$payload2.out.push(`<!--]-->`);
    }
    $$payload2.out.push(`<!--]--></p></div> <div class="comment-footer d-flex mt-3">`);
    {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<button class="btn btn-sm btn-outline-secondary me-2 respond-btn svelte-1aus6h6"${attr("aria-label", t.respond)}><i class="bi bi-reply me-1"></i> `);
      if (comment.childrenCount > 0) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`(${escape_html(comment.childrenCount)})`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--></button>`);
    }
    $$payload2.out.push(`<!--]--></div></div> `);
    {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div> `);
    if (comment.childrenCount > 0) {
      $$payload2.out.push("<!--[-->");
      if (expanded) {
        $$payload2.out.push("<!--[-->");
        const each_array = ensure_array_like(commentTree.getChildren(comment.path));
        $$payload2.out.push(`<div class="children-comments svelte-1aus6h6"${attr_style("", { "margin-top": "1rem" })}><!--[-->`);
        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
          let childComment = each_array[$$index];
          Comment($$payload2, {
            comment: childComment,
            locale,
            routeLocale,
            level: level + 1,
            expanded,
            commentTree,
            addComment,
            getAppropriateLocalization
          });
        }
        $$payload2.out.push(`<!--]--></div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]-->`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
class CommentTree {
  pathMap;
  parentChildrenMap;
  constructor(comments) {
    this.pathMap = /* @__PURE__ */ new Map();
    this.parentChildrenMap = /* @__PURE__ */ new Map();
    for (const comment of comments) {
      this.pathMap.set(comment.path, comment);
      const parentPath = this.getParentPath(comment.path);
      if (!this.parentChildrenMap.has(parentPath)) {
        this.parentChildrenMap.set(parentPath, []);
      }
      this.parentChildrenMap.get(parentPath).push(comment);
    }
  }
  getRootComments() {
    return this.getChildren(null);
  }
  getChildren(path) {
    return this.parentChildrenMap.get(path)?.toSorted((a, b) => {
      if (a.isMustBeTop !== b.isMustBeTop) {
        return a.isMustBeTop ? -1 : 1;
      }
      return b.rating.likes - b.rating.dislikes - (a.rating.likes - a.rating.dislikes);
    }) ?? [];
  }
  getParentPath(path) {
    return path.split(".").slice(0, -1).join(".") || null;
  }
  incrementChildrenCount(path) {
    const segments = path.split(".");
    for (let i = 0; i < segments.length; i++) {
      const currentPath = segments.slice(0, i + 1).join(".");
      const comment = this.pathMap.get(currentPath);
      if (comment) {
        comment.childrenCount++;
      }
    }
  }
}
function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      reactor: "Reactor",
      comments: "Comments",
      commentPlaceholder: "Write your comment...",
      submit: "Submit"
    },
    ru: {
      reactor: "Реактор",
      comments: "Комментарии",
      commentPlaceholder: "Напишите ваш комментарий...",
      submit: "Отправить"
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const {
    locale,
    routeLocale,
    toLocaleHref,
    getAppropriateLocalization
  } = data;
  const t = i18n[locale];
  let post = data.post;
  const title = getAppropriateLocalization(post.title);
  let comments = data.comments;
  const commentTree = new CommentTree(comments);
  let commentText = [];
  let showEditModal = false;
  let postToEdit = void 0;
  function handleEditPost(postData) {
    postToEdit = postData;
    showEditModal = true;
  }
  function handleCloseEditModal() {
    showEditModal = false;
    postToEdit = void 0;
  }
  function handlePostUpdated() {
    window.location.reload();
  }
  async function addComment(id) {
    const [comment] = await api.reactor.comment.list.get({ id });
    if (!comment) {
      throw new Error("Comment not found");
    }
    comments = [...comments, { ...comment, isMustBeTop: true }];
    const parentPath = commentTree.getParentPath(comment.path);
    if (parentPath) {
      commentTree.incrementChildrenCount(parentPath);
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>${escape_html(title)} — ${escape_html(t.reactor)}</title>`;
    });
    $$payload2.out.push(`<div class="row g-4 mt-3"><div class="col-3"></div> <div class="col-6"><div class="post-detail">`);
    Post_card($$payload2, {
      locale,
      post,
      toLocaleHref,
      getAppropriateLocalization,
      currentUser: data.user,
      onEditPost: handleEditPost
    });
    $$payload2.out.push(`<!----> <div class="comments-section mt-4 svelte-15cyxq6"><h4 class="mb-3">${escape_html(t.comments)} (${escape_html(comments.length)})</h4> <div class="comments-list">`);
    if (commentTree) {
      $$payload2.out.push("<!--[-->");
      const each_array = ensure_array_like(commentTree.getRootComments());
      $$payload2.out.push(`<!--[-->`);
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let comment = each_array[$$index];
        Comment($$payload2, {
          comment,
          locale,
          routeLocale,
          expanded: false,
          commentTree,
          addComment,
          getAppropriateLocalization
        });
      }
      $$payload2.out.push(`<!--]-->`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div></div> <div class="post-comment-form mt-4">`);
    Localized_textarea($$payload2, {
      locale,
      id: "post-comment",
      label: "",
      placeholder: t.commentPlaceholder,
      rows: 3,
      languageSelectPosition: "bottom",
      get value() {
        return commentText;
      },
      set value($$value) {
        commentText = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out.push(`<button class="btn btn-success btn-sm"><i class="bi bi-send me-1"></i> ${escape_html(t.submit)}</button>`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!----></div></div></div> <div class="col-2">`);
    Right_menu($$payload2, { locale, toLocaleHref });
    $$payload2.out.push(`<!----></div></div> `);
    Create_post_modal($$payload2, {
      show: showEditModal,
      locale,
      toLocaleHref,
      onClose: handleCloseEditModal,
      onPostCreated: handlePostUpdated,
      post: postToEdit
    });
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BxWAc7tK.js.map
