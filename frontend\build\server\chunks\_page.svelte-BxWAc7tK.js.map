{"version": 3, "file": "_page.svelte-BxWAc7tK.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/reactor/_id_/_page.svelte.js"], "sourcesContent": ["import { u as push, O as copy_payload, P as assign_payload, w as pop, F as attr_style, y as attr, J as stringify, z as escape_html, G as attr_class, K as ensure_array_like, x as head } from \"../../../../../chunks/index.js\";\nimport { g as getClient } from \"../../../../../chunks/acrpc.js\";\nimport { P as Post_card, R as Right_menu, C as Create_post_modal } from \"../../../../../chunks/right-menu.js\";\n/* empty css                                                                           */\nimport \"../../../../../chunks/current-user.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../../chunks/exports.js\";\nimport \"../../../../../chunks/state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport \"clsx\";\nimport { L as Localized_textarea } from \"../../../../../chunks/localized-textarea.js\";\nfunction Comment($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      expand: \"expand\",\n      collapse: \"collapse\",\n      respond: \"Respond\",\n      cancel: \"Cancel\",\n      report: \"Report\",\n      save: \"Save\",\n      share: \"Share\",\n      like: \"Like\",\n      dislike: \"Dislike\",\n      copied: \"Copied!\",\n      send: \"Send\",\n      responsePlaceholder: \"Write your response...\",\n      getPlural(n) {\n        if (n === 1) return 0;\n        return 1;\n      },\n      ratingTooltipText(rating2) {\n        const likesWord = [\"like\", \"likes\"][this.getPlural(rating2.likes % 10)];\n        const dislikesWord = [\"dislike\", \"dislikes\"][this.getPlural(rating2.dislikes % 10)];\n        return `${rating2.likes} ${likesWord}, ${rating2.dislikes} ${dislikesWord}`;\n      },\n      time: {\n        days(n) {\n          return `${n} ${n === 1 ? \"day\" : \"days\"} ago`;\n        },\n        hours(n) {\n          return `${n} ${n === 1 ? \"hour\" : \"hours\"} ago`;\n        },\n        minutes(n) {\n          return `${n} ${n === 1 ? \"minute\" : \"minutes\"} ago`;\n        },\n        seconds(n) {\n          return `${n} ${n === 1 ? \"second\" : \"seconds\"} ago`;\n        },\n        rightNow: \"right now\"\n      }\n    },\n    ru: {\n      expand: \"развернуть\",\n      collapse: \"свернуть\",\n      respond: \"Ответить\",\n      cancel: \"Отменить\",\n      report: \"Пожаловаться\",\n      save: \"Сохранить\",\n      share: \"Поделиться\",\n      like: \"Нравится\",\n      dislike: \"Не нравится\",\n      copied: \"Скопировано!\",\n      send: \"Отправить\",\n      responsePlaceholder: \"Напишите ваш ответ...\",\n      getPlural(n) {\n        if (n === 1) return 0;\n        if (n >= 2 && n <= 4) return 1;\n        return 2;\n      },\n      ratingTooltipText(rating2) {\n        const likesWord = [\n          \"лайк\",\n          \"лайка\",\n          \"лайков\"\n        ][this.getPlural(rating2.likes % 10)];\n        const dislikesWord = [\n          \"дизлайк\",\n          \"дизлайка\",\n          \"дизлайков\"\n        ][this.getPlural(rating2.dislikes % 10)];\n        return `${rating2.likes} ${likesWord}, ${rating2.dislikes} ${dislikesWord}`;\n      },\n      time: {\n        getPlural(n) {\n          if (n === 1) return 0;\n          if (n >= 2 && n <= 4) return 1;\n          return 2;\n        },\n        days(n) {\n          const word = [\n            \"день\",\n            \"дня\",\n            \"дней\"\n          ][this.getPlural(n)];\n          return `${n} ${word} назад`;\n        },\n        hours(n) {\n          const word = [\n            \"час\",\n            \"часа\",\n            \"часов\"\n          ][this.getPlural(n)];\n          return `${n} ${word} назад`;\n        },\n        minutes(n) {\n          const word = [\n            \"минуту\",\n            \"минуты\",\n            \"минут\"\n          ][this.getPlural(n)];\n          return `${n} ${word} назад`;\n        },\n        seconds(n) {\n          const word = [\n            \"секунду\",\n            \"секунды\",\n            \"секунд\"\n          ][this.getPlural(n)];\n          return `${n} ${word} назад`;\n        },\n        rightNow: \"только что\"\n      }\n    }\n  };\n  const { fetcher: api } = getClient();\n  const {\n    comment,\n    locale,\n    routeLocale,\n    level = 0,\n    commentTree,\n    addComment,\n    getAppropriateLocalization,\n    $$slots,\n    $$events,\n    ...props\n  } = $$props;\n  const t = i18n[locale];\n  let expanded = props.expanded;\n  const authorName = getAppropriateLocalization(comment.author?.name ?? []);\n  const body = getAppropriateLocalization(comment.body ?? []);\n  let rating = { ...comment.rating };\n  const ratingValue = rating.likes - rating.dislikes;\n  const ratingTooltipText = t.ratingTooltipText(rating);\n  const grayLevelColor = \"hsl(0,0%,70%)\";\n  const levelColors = [\n    \"hsl(0,70%,50%)\",\n    \"hsl(30,70%,50%)\",\n    \"hsl(60,70%,50%)\",\n    \"hsl(120,70%,50%)\",\n    \"hsl(180,70%,50%)\",\n    \"hsl(240,70%,50%)\",\n    \"hsl(270,70%,50%)\",\n    \"hsl(300,70%,50%)\",\n    \"hsl(330,70%,50%)\"\n  ];\n  const levelColor = levelColors[level % levelColors.length];\n  const levelBackgroundColor = comment.childrenCount === 0 ? grayLevelColor : levelColor;\n  function formatDate(date) {\n    const now = /* @__PURE__ */ new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffSec = Math.floor(diffMs / 1e3);\n    const diffMin = Math.floor(diffSec / 60);\n    const diffHour = Math.floor(diffMin / 60);\n    const diffDay = Math.floor(diffHour / 24);\n    if (diffDay > 0) {\n      return t.time.days(diffDay);\n    } else if (diffHour > 0) {\n      return t.time.hours(diffHour);\n    } else if (diffMin > 0) {\n      return t.time.minutes(diffMin);\n    } else if (diffSec > 3) {\n      return t.time.seconds(diffSec);\n    } else {\n      return t.time.rightNow;\n    }\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out.push(`<div class=\"comment mb-3 svelte-1aus6h6\"${attr_style(\"\", { \"margin-left\": level > 0 ? \"1.5rem\" : \"0\" })}>`);\n    if (comment.childrenCount > 0) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"level-indicator svelte-1aus6h6\"${attr_style(`background-color: ${stringify(levelBackgroundColor)};`)} role=\"button\" tabindex=\"0\"${attr(\"title\", expanded ? t.collapse : t.expand)}></div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      $$payload2.out.push(`<div class=\"level-indicator no-children svelte-1aus6h6\"${attr_style(`background-color: ${stringify(levelBackgroundColor)};`)}></div>`);\n    }\n    $$payload2.out.push(`<!--]--> <div class=\"card svelte-1aus6h6\"><div class=\"card-body p-3 svelte-1aus6h6\"><div class=\"comment-header d-flex justify-content-between align-items-center mb-2 svelte-1aus6h6\"><div class=\"d-flex align-items-center svelte-1aus6h6\"><div class=\"rating-block d-flex align-items-center me-3 svelte-1aus6h6\">`);\n    if (ratingValue > 0) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<span class=\"rating-value me-2 text-success svelte-1aus6h6\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\"${attr(\"title\", ratingTooltipText)}>${escape_html(ratingValue)}</span>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      if (ratingValue < 0) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<span class=\"rating-value me-2 text-danger svelte-1aus6h6\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\"${attr(\"title\", ratingTooltipText)}>${escape_html(ratingValue)}</span>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n        $$payload2.out.push(`<span class=\"rating-value me-2 svelte-1aus6h6\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\"${attr(\"title\", ratingTooltipText)}>0</span>`);\n      }\n      $$payload2.out.push(`<!--]-->`);\n    }\n    $$payload2.out.push(`<!--]--> <div class=\"rating-buttons svelte-1aus6h6\"><button${attr_class(`btn btn-sm me-1 ${rating.status === \"like\" ? \"btn-success\" : \"btn-outline-success\"}`, \"svelte-1aus6h6\")}${attr(\"aria-label\", t.like)}><i class=\"bi bi-hand-thumbs-up\"></i></button> <button${attr_class(`btn btn-sm ${rating.status === \"dislike\" ? \"btn-danger\" : \"btn-outline-danger\"}`, \"svelte-1aus6h6\")}${attr(\"aria-label\", t.dislike)}><i class=\"bi bi-hand-thumbs-down\"></i></button></div></div> <div class=\"author-info d-flex align-items-center\">`);\n    if (comment.author?.image) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<img${attr(\"src\", `/images/${comment.author.image}`)} alt=\"avatar\" class=\"avatar rounded-circle me-2 svelte-1aus6h6\" width=\"32\" height=\"32\"${attr_style(\"\", { \"object-fit\": \"cover\" })}/>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      $$payload2.out.push(`<div class=\"avatar rounded-circle me-2 svelte-1aus6h6\"${attr_style(`background-color: ${stringify(levelColor)};`)}></div>`);\n    }\n    $$payload2.out.push(`<!--]--> <div><div class=\"author-name fw-bold\">`);\n    if (comment.isAnonymous) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`${escape_html(comment.anonimityReason ? `Anonymous (${comment.anonimityReason})` : \"Anonymous\")}`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      $$payload2.out.push(`${escape_html(authorName ?? (Math.random() > 0.5 ? \"John Doe\" : \"Jane Doe\"))}`);\n    }\n    $$payload2.out.push(`<!--]--></div> <div class=\"comment-time small text-muted svelte-1aus6h6\"${attr(\"title\", comment.createdAt.toISOString())}>${escape_html(formatDate(comment.createdAt))}</div></div></div></div> <div class=\"action-buttons svelte-1aus6h6\"></div></div> <div class=\"comment-body mb-2\"><p class=\"mb-0\">`);\n    if (comment.deleteReason) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<span class=\"text-muted\">deleted: ${escape_html(comment.deleteReason)}</span>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      if (comment.deletedAt) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<span class=\"text-muted\">deleted with no reason</span>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n        if (body) {\n          $$payload2.out.push(\"<!--[-->\");\n          $$payload2.out.push(`${escape_html(body)}`);\n        } else {\n          $$payload2.out.push(\"<!--[!-->\");\n          $$payload2.out.push(`<span class=\"text-muted\">No body?</span>`);\n        }\n        $$payload2.out.push(`<!--]-->`);\n      }\n      $$payload2.out.push(`<!--]-->`);\n    }\n    $$payload2.out.push(`<!--]--></p></div> <div class=\"comment-footer d-flex mt-3\">`);\n    {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<button class=\"btn btn-sm btn-outline-secondary me-2 respond-btn svelte-1aus6h6\"${attr(\"aria-label\", t.respond)}><i class=\"bi bi-reply me-1\"></i> `);\n      if (comment.childrenCount > 0) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`(${escape_html(comment.childrenCount)})`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--></button>`);\n    }\n    $$payload2.out.push(`<!--]--></div></div> `);\n    {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div> `);\n    if (comment.childrenCount > 0) {\n      $$payload2.out.push(\"<!--[-->\");\n      if (expanded) {\n        $$payload2.out.push(\"<!--[-->\");\n        const each_array = ensure_array_like(commentTree.getChildren(comment.path));\n        $$payload2.out.push(`<div class=\"children-comments svelte-1aus6h6\"${attr_style(\"\", { \"margin-top\": \"1rem\" })}><!--[-->`);\n        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n          let childComment = each_array[$$index];\n          Comment($$payload2, {\n            comment: childComment,\n            locale,\n            routeLocale,\n            level: level + 1,\n            expanded,\n            commentTree,\n            addComment,\n            getAppropriateLocalization\n          });\n        }\n        $$payload2.out.push(`<!--]--></div>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]-->`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div>`);\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nclass CommentTree {\n  pathMap;\n  parentChildrenMap;\n  constructor(comments) {\n    this.pathMap = /* @__PURE__ */ new Map();\n    this.parentChildrenMap = /* @__PURE__ */ new Map();\n    for (const comment of comments) {\n      this.pathMap.set(comment.path, comment);\n      const parentPath = this.getParentPath(comment.path);\n      if (!this.parentChildrenMap.has(parentPath)) {\n        this.parentChildrenMap.set(parentPath, []);\n      }\n      this.parentChildrenMap.get(parentPath).push(comment);\n    }\n  }\n  getRootComments() {\n    return this.getChildren(null);\n  }\n  getChildren(path) {\n    return this.parentChildrenMap.get(path)?.toSorted((a, b) => {\n      if (a.isMustBeTop !== b.isMustBeTop) {\n        return a.isMustBeTop ? -1 : 1;\n      }\n      return b.rating.likes - b.rating.dislikes - (a.rating.likes - a.rating.dislikes);\n    }) ?? [];\n  }\n  getParentPath(path) {\n    return path.split(\".\").slice(0, -1).join(\".\") || null;\n  }\n  incrementChildrenCount(path) {\n    const segments = path.split(\".\");\n    for (let i = 0; i < segments.length; i++) {\n      const currentPath = segments.slice(0, i + 1).join(\".\");\n      const comment = this.pathMap.get(currentPath);\n      if (comment) {\n        comment.childrenCount++;\n      }\n    }\n  }\n}\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      reactor: \"Reactor\",\n      comments: \"Comments\",\n      commentPlaceholder: \"Write your comment...\",\n      submit: \"Submit\"\n    },\n    ru: {\n      reactor: \"Реактор\",\n      comments: \"Комментарии\",\n      commentPlaceholder: \"Напишите ваш комментарий...\",\n      submit: \"Отправить\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const {\n    locale,\n    routeLocale,\n    toLocaleHref,\n    getAppropriateLocalization\n  } = data;\n  const t = i18n[locale];\n  let post = data.post;\n  const title = getAppropriateLocalization(post.title);\n  let comments = data.comments;\n  const commentTree = new CommentTree(comments);\n  let commentText = [];\n  let showEditModal = false;\n  let postToEdit = void 0;\n  function handleEditPost(postData) {\n    postToEdit = postData;\n    showEditModal = true;\n  }\n  function handleCloseEditModal() {\n    showEditModal = false;\n    postToEdit = void 0;\n  }\n  function handlePostUpdated() {\n    window.location.reload();\n  }\n  async function addComment(id) {\n    const [comment] = await api.reactor.comment.list.get({ id });\n    if (!comment) {\n      throw new Error(\"Comment not found\");\n    }\n    comments = [...comments, { ...comment, isMustBeTop: true }];\n    const parentPath = commentTree.getParentPath(comment.path);\n    if (parentPath) {\n      commentTree.incrementChildrenCount(parentPath);\n    }\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    head($$payload2, ($$payload3) => {\n      $$payload3.title = `<title>${escape_html(title)} — ${escape_html(t.reactor)}</title>`;\n    });\n    $$payload2.out.push(`<div class=\"row g-4 mt-3\"><div class=\"col-3\"></div> <div class=\"col-6\"><div class=\"post-detail\">`);\n    Post_card($$payload2, {\n      locale,\n      post,\n      toLocaleHref,\n      getAppropriateLocalization,\n      currentUser: data.user,\n      onEditPost: handleEditPost\n    });\n    $$payload2.out.push(`<!----> <div class=\"comments-section mt-4 svelte-15cyxq6\"><h4 class=\"mb-3\">${escape_html(t.comments)} (${escape_html(comments.length)})</h4> <div class=\"comments-list\">`);\n    if (commentTree) {\n      $$payload2.out.push(\"<!--[-->\");\n      const each_array = ensure_array_like(commentTree.getRootComments());\n      $$payload2.out.push(`<!--[-->`);\n      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n        let comment = each_array[$$index];\n        Comment($$payload2, {\n          comment,\n          locale,\n          routeLocale,\n          expanded: false,\n          commentTree,\n          addComment,\n          getAppropriateLocalization\n        });\n      }\n      $$payload2.out.push(`<!--]-->`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div></div> <div class=\"post-comment-form mt-4\">`);\n    Localized_textarea($$payload2, {\n      locale,\n      id: \"post-comment\",\n      label: \"\",\n      placeholder: t.commentPlaceholder,\n      rows: 3,\n      languageSelectPosition: \"bottom\",\n      get value() {\n        return commentText;\n      },\n      set value($$value) {\n        commentText = $$value;\n        $$settled = false;\n      },\n      children: ($$payload3) => {\n        $$payload3.out.push(`<button class=\"btn btn-success btn-sm\"><i class=\"bi bi-send me-1\"></i> ${escape_html(t.submit)}</button>`);\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out.push(`<!----></div></div></div> <div class=\"col-2\">`);\n    Right_menu($$payload2, { locale, toLocaleHref });\n    $$payload2.out.push(`<!----></div></div> `);\n    Create_post_modal($$payload2, {\n      show: showEditModal,\n      locale,\n      toLocaleHref,\n      onClose: handleCloseEditModal,\n      onPostCreated: handlePostUpdated,\n      post: postToEdit\n    });\n    $$payload2.out.push(`<!---->`);\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAYA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,mBAAmB,EAAE,wBAAwB;AACnD,MAAM,SAAS,CAAC,CAAC,EAAE;AACnB,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC;AAC7B,QAAQ,OAAO,CAAC;AAChB,MAAM,CAAC;AACP,MAAM,iBAAiB,CAAC,OAAO,EAAE;AACjC,QAAQ,MAAM,SAAS,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;AAC/E,QAAQ,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;AAC3F,QAAQ,OAAO,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;AACnF,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,CAAC,CAAC,EAAE;AAChB,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;AACvD,QAAQ,CAAC;AACT,QAAQ,KAAK,CAAC,CAAC,EAAE;AACjB,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AACzD,QAAQ,CAAC;AACT,QAAQ,OAAO,CAAC,CAAC,EAAE;AACnB,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC;AAC7D,QAAQ,CAAC;AACT,QAAQ,OAAO,CAAC,CAAC,EAAE;AACnB,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC;AAC7D,QAAQ,CAAC;AACT,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,mBAAmB,EAAE,uBAAuB;AAClD,MAAM,SAAS,CAAC,CAAC,EAAE;AACnB,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC;AACtC,QAAQ,OAAO,CAAC;AAChB,MAAM,CAAC;AACP,MAAM,iBAAiB,CAAC,OAAO,EAAE;AACjC,QAAQ,MAAM,SAAS,GAAG;AAC1B,UAAU,MAAM;AAChB,UAAU,OAAO;AACjB,UAAU;AACV,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;AAC7C,QAAQ,MAAM,YAAY,GAAG;AAC7B,UAAU,SAAS;AACnB,UAAU,UAAU;AACpB,UAAU;AACV,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;AAChD,QAAQ,OAAO,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;AACnF,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,SAAS,CAAC,CAAC,EAAE;AACrB,UAAU,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC;AAC/B,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC;AACxC,UAAU,OAAO,CAAC;AAClB,QAAQ,CAAC;AACT,QAAQ,IAAI,CAAC,CAAC,EAAE;AAChB,UAAU,MAAM,IAAI,GAAG;AACvB,YAAY,MAAM;AAClB,YAAY,KAAK;AACjB,YAAY;AACZ,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC9B,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;AACrC,QAAQ,CAAC;AACT,QAAQ,KAAK,CAAC,CAAC,EAAE;AACjB,UAAU,MAAM,IAAI,GAAG;AACvB,YAAY,KAAK;AACjB,YAAY,MAAM;AAClB,YAAY;AACZ,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC9B,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;AACrC,QAAQ,CAAC;AACT,QAAQ,OAAO,CAAC,CAAC,EAAE;AACnB,UAAU,MAAM,IAAI,GAAG;AACvB,YAAY,QAAQ;AACpB,YAAY,QAAQ;AACpB,YAAY;AACZ,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC9B,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;AACrC,QAAQ,CAAC;AACT,QAAQ,OAAO,CAAC,CAAC,EAAE;AACnB,UAAU,MAAM,IAAI,GAAG;AACvB,YAAY,SAAS;AACrB,YAAY,SAAS;AACrB,YAAY;AACZ,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC9B,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;AACrC,QAAQ,CAAC;AACT,QAAQ,QAAQ,EAAE;AAClB;AACA;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM;AACR,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,WAAW;AACf,IAAI,KAAK,GAAG,CAAC;AACb,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,0BAA0B;AAC9B,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC,QAAQ;AAC/B,EAAE,MAAM,UAAU,GAAG,0BAA0B,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC;AAC3E,EAAE,MAAM,IAAI,GAAG,0BAA0B,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;AAC7D,EAAE,IAAI,MAAM,GAAG,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE;AACpC,EAAE,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ;AACpD,EAAE,MAAM,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC;AACvD,EAAE,MAAM,cAAc,GAAG,eAAe;AACxC,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,gBAAgB;AACpB,IAAI,iBAAiB;AACrB,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI;AACJ,GAAG;AACH,EAAE,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC;AAC5D,EAAE,MAAM,oBAAoB,GAAG,OAAO,CAAC,aAAa,KAAK,CAAC,GAAG,cAAc,GAAG,UAAU;AACxF,EAAE,SAAS,UAAU,CAAC,IAAI,EAAE;AAC5B,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE;AACjD,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;AAC5C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AAC5C,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AAC7C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;AAC7C,IAAI,IAAI,OAAO,GAAG,CAAC,EAAE;AACrB,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACjC,IAAI,CAAC,MAAM,IAAI,QAAQ,GAAG,CAAC,EAAE;AAC7B,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;AACnC,IAAI,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,EAAE;AAC5B,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;AACpC,IAAI,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,EAAE;AAC5B,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;AACpC,IAAI,CAAC,MAAM;AACX,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ;AAC5B,IAAI;AACJ,EAAE;AACF,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wCAAwC,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,aAAa,EAAE,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACpI,IAAI,IAAI,OAAO,CAAC,aAAa,GAAG,CAAC,EAAE;AACnC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2CAA2C,EAAE,UAAU,CAAC,CAAC,kBAAkB,EAAE,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;AAClO,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uDAAuD,EAAE,UAAU,CAAC,CAAC,kBAAkB,EAAE,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACjK,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oTAAoT,CAAC,CAAC;AAC/U,IAAI,IAAI,WAAW,GAAG,CAAC,EAAE;AACzB,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4GAA4G,EAAE,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC;AAC/M,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,IAAI,WAAW,GAAG,CAAC,EAAE;AAC3B,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2GAA2G,EAAE,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC;AAChN,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+FAA+F,EAAE,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,SAAS,CAAC,CAAC;AAC1K,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2DAA2D,EAAE,UAAU,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,MAAM,KAAK,MAAM,GAAG,aAAa,GAAG,qBAAqB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,sDAAsD,EAAE,UAAU,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,KAAK,SAAS,GAAG,YAAY,GAAG,oBAAoB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,gHAAgH,CAAC,CAAC;AACjiB,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE;AAC/B,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,sFAAsF,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACtN,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sDAAsD,EAAE,UAAU,CAAC,CAAC,kBAAkB,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACtJ,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+CAA+C,CAAC,CAAC;AAC1E,IAAI,IAAI,OAAO,CAAC,WAAW,EAAE;AAC7B,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AAC7H,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,KAAK,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1G,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wEAAwE,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,gIAAgI,CAAC,CAAC;AAClU,IAAI,IAAI,OAAO,CAAC,YAAY,EAAE;AAC9B,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC;AAC1G,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,IAAI,OAAO,CAAC,SAAS,EAAE;AAC7B,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sDAAsD,CAAC,CAAC;AACrF,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,QAAQ,IAAI,IAAI,EAAE;AAClB,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACrD,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wCAAwC,CAAC,CAAC;AACzE,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2DAA2D,CAAC,CAAC;AACtF,IAAI;AACJ,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gFAAgF,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,kCAAkC,CAAC,CAAC;AAC/K,MAAM,IAAI,OAAO,CAAC,aAAa,GAAG,CAAC,EAAE;AACrC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,CAAC;AAC9C,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,CAAC;AAChD,IAAI;AACJ,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC1C,IAAI,IAAI,OAAO,CAAC,aAAa,GAAG,CAAC,EAAE;AACnC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,MAAM,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACnF,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6CAA6C,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC;AAChI,QAAQ,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AAC3F,UAAU,IAAI,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC;AAChD,UAAU,OAAO,CAAC,UAAU,EAAE;AAC9B,YAAY,OAAO,EAAE,YAAY;AACjC,YAAY,MAAM;AAClB,YAAY,WAAW;AACvB,YAAY,KAAK,EAAE,KAAK,GAAG,CAAC;AAC5B,YAAY,QAAQ;AACpB,YAAY,WAAW;AACvB,YAAY,UAAU;AACtB,YAAY;AACZ,WAAW,CAAC;AACZ,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC7C,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACzC,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;AACA,MAAM,WAAW,CAAC;AAClB,EAAE,OAAO;AACT,EAAE,iBAAiB;AACnB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,OAAO,mBAAmB,IAAI,GAAG,EAAE;AAC5C,IAAI,IAAI,CAAC,iBAAiB,mBAAmB,IAAI,GAAG,EAAE;AACtD,IAAI,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACpC,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;AAC7C,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC;AACzD,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AACnD,QAAQ,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC;AAClD,MAAM;AACN,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AAC1D,IAAI;AACJ,EAAE;AACF,EAAE,eAAe,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AACjC,EAAE;AACF,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAChE,MAAM,IAAI,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,WAAW,EAAE;AAC3C,QAAQ,OAAO,CAAC,CAAC,WAAW,GAAG,EAAE,GAAG,CAAC;AACrC,MAAM;AACN,MAAM,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;AACtF,IAAI,CAAC,CAAC,IAAI,EAAE;AACZ,EAAE;AACF,EAAE,aAAa,CAAC,IAAI,EAAE;AACtB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI;AACzD,EAAE;AACF,EAAE,sBAAsB,CAAC,IAAI,EAAE;AAC/B,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;AACpC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC9C,MAAM,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AAC5D,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;AACnD,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,OAAO,CAAC,aAAa,EAAE;AAC/B,MAAM;AACN,IAAI;AACJ,EAAE;AACF;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,kBAAkB,EAAE,uBAAuB;AACjD,MAAM,MAAM,EAAE;AACd,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,kBAAkB,EAAE,6BAA6B;AACvD,MAAM,MAAM,EAAE;AACd;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM;AACR,IAAI,MAAM;AACV,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI;AACJ,GAAG,GAAG,IAAI;AACV,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;AACtB,EAAE,MAAM,KAAK,GAAG,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC;AACtD,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC9B,EAAE,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC;AAC/C,EAAE,IAAI,WAAW,GAAG,EAAE;AACtB,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,IAAI,UAAU,GAAG,MAAM;AACzB,EAAE,SAAS,cAAc,CAAC,QAAQ,EAAE;AACpC,IAAI,UAAU,GAAG,QAAQ;AACzB,IAAI,aAAa,GAAG,IAAI;AACxB,EAAE;AACF,EAAE,SAAS,oBAAoB,GAAG;AAClC,IAAI,aAAa,GAAG,KAAK;AACzB,IAAI,UAAU,GAAG,MAAM;AACvB,EAAE;AACF,EAAE,SAAS,iBAAiB,GAAG;AAC/B,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE;AAC5B,EAAE;AACF,EAAE,eAAe,UAAU,CAAC,EAAE,EAAE;AAChC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AAChE,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC;AAC1C,IAAI;AACJ,IAAI,QAAQ,GAAG,CAAC,GAAG,QAAQ,EAAE,EAAE,GAAG,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;AAC/D,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC;AAC9D,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,WAAW,CAAC,sBAAsB,CAAC,UAAU,CAAC;AACpD,IAAI;AACJ,EAAE;AACF,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,KAAK;AACrC,MAAM,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC;AAC3F,IAAI,CAAC,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gGAAgG,CAAC,CAAC;AAC3H,IAAI,SAAS,CAAC,UAAU,EAAE;AAC1B,MAAM,MAAM;AACZ,MAAM,IAAI;AACV,MAAM,YAAY;AAClB,MAAM,0BAA0B;AAChC,MAAM,WAAW,EAAE,IAAI,CAAC,IAAI;AAC5B,MAAM,UAAU,EAAE;AAClB,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2EAA2E,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,kCAAkC,CAAC,CAAC;AACnM,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,MAAM,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;AACzE,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,MAAM,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACzF,QAAQ,IAAI,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACzC,QAAQ,OAAO,CAAC,UAAU,EAAE;AAC5B,UAAU,OAAO;AACjB,UAAU,MAAM;AAChB,UAAU,WAAW;AACrB,UAAU,QAAQ,EAAE,KAAK;AACzB,UAAU,WAAW;AACrB,UAAU,UAAU;AACpB,UAAU;AACV,SAAS,CAAC;AACV,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yDAAyD,CAAC,CAAC;AACpF,IAAI,kBAAkB,CAAC,UAAU,EAAE;AACnC,MAAM,MAAM;AACZ,MAAM,EAAE,EAAE,cAAc;AACxB,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,WAAW,EAAE,CAAC,CAAC,kBAAkB;AACvC,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,sBAAsB,EAAE,QAAQ;AACtC,MAAM,IAAI,KAAK,GAAG;AAClB,QAAQ,OAAO,WAAW;AAC1B,MAAM,CAAC;AACP,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,WAAW,GAAG,OAAO;AAC7B,QAAQ,SAAS,GAAG,KAAK;AACzB,MAAM,CAAC;AACP,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uEAAuE,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC;AACvI,MAAM,CAAC;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6CAA6C,CAAC,CAAC;AACxE,IAAI,UAAU,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;AACpD,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC/C,IAAI,iBAAiB,CAAC,UAAU,EAAE;AAClC,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,MAAM;AACZ,MAAM,YAAY;AAClB,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,aAAa,EAAE,iBAAiB;AACtC,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;AAClC,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;;;;"}