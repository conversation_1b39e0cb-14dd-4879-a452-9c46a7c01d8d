{"version": 3, "file": "_page.svelte-CEFEvs3p.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/communes/_id_/join-requests/_page.svelte.js"], "sourcesContent": ["import { x as head, z as escape_html, y as attr, K as ensure_array_like, G as attr_class, w as pop, u as push } from \"../../../../../../../chunks/index.js\";\nimport \"../../../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../../../chunks/acrpc.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../../../../chunks/exports.js\";\nimport \"../../../../../../../chunks/state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { f as formatDate } from \"../../../../../../../chunks/format-date.js\";\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Join Requests — Commune\" },\n      communeJoinRequests: \"Join Requests\",\n      loading: \"Loading...\",\n      noJoinRequests: \"No join requests found\",\n      errorFetchingJoinRequests: \"Failed to fetch join requests\",\n      errorOccurred: \"An error occurred while fetching join requests\",\n      loadingMore: \"Loading more join requests...\",\n      accept: \"Accept\",\n      reject: \"Reject\",\n      pending: \"Pending\",\n      accepted: \"Accepted\",\n      rejected: \"Rejected\",\n      requestedOn: \"Requested on\",\n      acceptingRequest: \"Accepting...\",\n      rejectingRequest: \"Rejecting...\",\n      errorAcceptingRequest: \"Failed to accept join request\",\n      errorRejectingRequest: \"Failed to reject join request\",\n      requestAccepted: \"Join request accepted\",\n      requestRejected: \"Join request rejected\",\n      backToCommune: \"Back to Commune\",\n      requestingUser: \"Requesting User\",\n      status: \"Status\",\n      actions: \"Actions\",\n      confirmAccept: \"Are you sure you want to accept this join request?\",\n      confirmReject: \"Are you sure you want to reject this join request?\"\n    },\n    ru: {\n      _page: {\n        title: \"Заявки на вступление — Коммуна\"\n      },\n      communeJoinRequests: \"Заявки на вступление\",\n      loading: \"Загрузка...\",\n      noJoinRequests: \"Заявки не найдены\",\n      errorFetchingJoinRequests: \"Не удалось загрузить заявки\",\n      errorOccurred: \"Произошла ошибка при загрузке заявок\",\n      loadingMore: \"Загружаем больше заявок...\",\n      accept: \"Принять\",\n      reject: \"Отклонить\",\n      pending: \"Ожидает\",\n      accepted: \"Принято\",\n      rejected: \"Отклонено\",\n      requestedOn: \"Подана\",\n      acceptingRequest: \"Принимаем...\",\n      rejectingRequest: \"Отклоняем...\",\n      errorAcceptingRequest: \"Не удалось принять заявку\",\n      errorRejectingRequest: \"Не удалось отклонить заявку\",\n      requestAccepted: \"Заявка принята\",\n      requestRejected: \"Заявка отклонена\",\n      backToCommune: \"Назад к коммуне\",\n      requestingUser: \"Пользователь\",\n      status: \"Статус\",\n      actions: \"Действия\",\n      confirmAccept: \"Вы уверены, что хотите принять эту заявку?\",\n      confirmReject: \"Вы уверены, что хотите отклонить эту заявку?\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const {\n    locale,\n    routeLocale,\n    toLocaleHref,\n    getAppropriateLocalization\n  } = data;\n  const t = i18n[locale];\n  let joinRequests = data.joinRequests;\n  let isHasMoreJoinRequests = data.isHasMoreJoinRequests;\n  let loadingStates = {};\n  function getStatusBadgeClass(status) {\n    switch (status) {\n      case \"pending\":\n        return \"bg-warning text-dark\";\n      case \"accepted\":\n        return \"bg-success\";\n      case \"rejected\":\n        return \"bg-danger\";\n      default:\n        return \"bg-secondary\";\n    }\n  }\n  function getStatusText(status) {\n    switch (status) {\n      case \"pending\":\n        return t.pending;\n      case \"accepted\":\n        return t.accepted;\n      case \"rejected\":\n        return t.rejected;\n      default:\n        return status;\n    }\n  }\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;\n  });\n  $$payload.out.push(`<div class=\"container my-4 mb-5\"><div class=\"d-flex justify-content-between align-items-center my-4\"><div><h1>${escape_html(t.communeJoinRequests)}</h1> <p class=\"text-muted mb-0\">${escape_html(getAppropriateLocalization(data.commune.name))}</p></div> <a${attr(\"href\", toLocaleHref(`/communes/${data.commune.id}`))} class=\"btn btn-outline-secondary\">${escape_html(t.backToCommune)}</a></div> `);\n  if (joinRequests.length === 0) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-5\"><p class=\"text-muted\">${escape_html(t.noJoinRequests)}</p></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    const each_array = ensure_array_like(joinRequests);\n    const each_array_1 = ensure_array_like(joinRequests);\n    $$payload.out.push(`<div class=\"d-none d-md-block\"><div class=\"table-responsive\"><table class=\"table table-hover\"><thead><tr><th>${escape_html(t.requestingUser)}</th><th>${escape_html(t.status)}</th><th>${escape_html(t.requestedOn)}</th><th>${escape_html(t.actions)}</th></tr></thead><tbody><!--[-->`);\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let joinRequest = each_array[$$index];\n      $$payload.out.push(`<tr><td><div class=\"d-flex align-items-center\">`);\n      if (joinRequest.user.image) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<img${attr(\"src\", `/images/${joinRequest.user.image}`)} alt=\"User avatar\" class=\"rounded-circle me-2\" style=\"width: 32px; height: 32px; object-fit: cover;\"/>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<div class=\"rounded-circle bg-secondary d-flex align-items-center justify-content-center me-2\" style=\"width: 32px; height: 32px;\"><i class=\"bi bi-person text-white\"></i></div>`);\n      }\n      $$payload.out.push(`<!--]--> <div><div class=\"fw-medium\">${escape_html(getAppropriateLocalization(joinRequest.user.name))}</div></div></div></td><td><span${attr_class(`badge ${getStatusBadgeClass(joinRequest.status)}`)}>${escape_html(getStatusText(joinRequest.status))}</span></td><td class=\"text-muted\">${escape_html(formatDate(joinRequest.createdAt, locale))}</td><td>`);\n      if (joinRequest.status === \"pending\") {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<div class=\"d-flex gap-1\"><button class=\"btn btn-sm btn-success\"${attr(\"disabled\", loadingStates[joinRequest.id] === \"accepting\", true)}>`);\n        if (loadingStates[joinRequest.id] === \"accepting\") {\n          $$payload.out.push(\"<!--[-->\");\n          $$payload.out.push(`<span class=\"spinner-border spinner-border-sm me-1\" role=\"status\"></span> ${escape_html(t.acceptingRequest)}`);\n        } else {\n          $$payload.out.push(\"<!--[!-->\");\n          $$payload.out.push(`${escape_html(t.accept)}`);\n        }\n        $$payload.out.push(`<!--]--></button> <button class=\"btn btn-sm btn-outline-danger\"${attr(\"disabled\", loadingStates[joinRequest.id] === \"rejecting\", true)}>`);\n        if (loadingStates[joinRequest.id] === \"rejecting\") {\n          $$payload.out.push(\"<!--[-->\");\n          $$payload.out.push(`<span class=\"spinner-border spinner-border-sm me-1\" role=\"status\"></span> ${escape_html(t.rejectingRequest)}`);\n        } else {\n          $$payload.out.push(\"<!--[!-->\");\n          $$payload.out.push(`${escape_html(t.reject)}`);\n        }\n        $$payload.out.push(`<!--]--></button></div>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<span class=\"text-muted\">—</span>`);\n      }\n      $$payload.out.push(`<!--]--></td></tr>`);\n    }\n    $$payload.out.push(`<!--]--></tbody></table></div></div> <div class=\"d-md-none\"><div class=\"row g-3\"><!--[-->`);\n    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n      let joinRequest = each_array_1[$$index_1];\n      $$payload.out.push(`<div class=\"col-12\"><div class=\"card\"><div class=\"card-body\"><div class=\"d-flex align-items-start justify-content-between mb-3\"><div class=\"d-flex align-items-center\">`);\n      if (joinRequest.user.image) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<img${attr(\"src\", `/images/${joinRequest.user.image}`)} alt=\"User avatar\" class=\"rounded-circle me-3\" style=\"width: 48px; height: 48px; object-fit: cover;\"/>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<div class=\"rounded-circle bg-secondary d-flex align-items-center justify-content-center me-3\" style=\"width: 48px; height: 48px;\"><i class=\"bi bi-person text-white\"></i></div>`);\n      }\n      $$payload.out.push(`<!--]--> <div><div class=\"fw-medium\">${escape_html(getAppropriateLocalization(joinRequest.user.name))}</div></div></div> <span${attr_class(`badge ${getStatusBadgeClass(joinRequest.status)}`)}>${escape_html(getStatusText(joinRequest.status))}</span></div> <div class=\"d-flex justify-content-between align-items-center\"><small class=\"text-muted\">${escape_html(t.requestedOn)}\n                    ${escape_html(formatDate(joinRequest.createdAt, locale))}</small> `);\n      if (joinRequest.status === \"pending\") {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<div class=\"d-flex gap-1\"><button class=\"btn btn-sm btn-success\"${attr(\"disabled\", loadingStates[joinRequest.id] === \"accepting\", true)}>`);\n        if (loadingStates[joinRequest.id] === \"accepting\") {\n          $$payload.out.push(\"<!--[-->\");\n          $$payload.out.push(`<span class=\"spinner-border spinner-border-sm me-1\" role=\"status\"></span> ${escape_html(t.acceptingRequest)}`);\n        } else {\n          $$payload.out.push(\"<!--[!-->\");\n          $$payload.out.push(`${escape_html(t.accept)}`);\n        }\n        $$payload.out.push(`<!--]--></button> <button class=\"btn btn-sm btn-outline-danger\"${attr(\"disabled\", loadingStates[joinRequest.id] === \"rejecting\", true)}>`);\n        if (loadingStates[joinRequest.id] === \"rejecting\") {\n          $$payload.out.push(\"<!--[-->\");\n          $$payload.out.push(`<span class=\"spinner-border spinner-border-sm me-1\" role=\"status\"></span> ${escape_html(t.rejectingRequest)}`);\n        } else {\n          $$payload.out.push(\"<!--[!-->\");\n          $$payload.out.push(`${escape_html(t.reject)}`);\n        }\n        $$payload.out.push(`<!--]--></button></div>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n      }\n      $$payload.out.push(`<!--]--></div></div></div></div>`);\n    }\n    $$payload.out.push(`<!--]--></div></div>`);\n  }\n  $$payload.out.push(`<!--]--> `);\n  if (isHasMoreJoinRequests) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-3\">`);\n    {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;AASA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE;AACjD,MAAM,mBAAmB,EAAE,eAAe;AAC1C,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,cAAc,EAAE,wBAAwB;AAC9C,MAAM,yBAAyB,EAAE,+BAA+B;AAChE,MAAM,aAAa,EAAE,gDAAgD;AACrE,MAAM,WAAW,EAAE,+BAA+B;AAClD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,gBAAgB,EAAE,cAAc;AACtC,MAAM,gBAAgB,EAAE,cAAc;AACtC,MAAM,qBAAqB,EAAE,+BAA+B;AAC5D,MAAM,qBAAqB,EAAE,+BAA+B;AAC5D,MAAM,eAAe,EAAE,uBAAuB;AAC9C,MAAM,eAAe,EAAE,uBAAuB;AAC9C,MAAM,aAAa,EAAE,iBAAiB;AACtC,MAAM,cAAc,EAAE,iBAAiB;AACvC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,aAAa,EAAE,oDAAoD;AACzE,MAAM,aAAa,EAAE;AACrB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,mBAAmB,EAAE,sBAAsB;AACjD,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,cAAc,EAAE,mBAAmB;AACzC,MAAM,yBAAyB,EAAE,6BAA6B;AAC9D,MAAM,aAAa,EAAE,sCAAsC;AAC3D,MAAM,WAAW,EAAE,4BAA4B;AAC/C,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,gBAAgB,EAAE,cAAc;AACtC,MAAM,gBAAgB,EAAE,cAAc;AACtC,MAAM,qBAAqB,EAAE,2BAA2B;AACxD,MAAM,qBAAqB,EAAE,6BAA6B;AAC1D,MAAM,eAAe,EAAE,gBAAgB;AACvC,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,aAAa,EAAE,iBAAiB;AACtC,MAAM,cAAc,EAAE,cAAc;AACpC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,aAAa,EAAE,4CAA4C;AACjE,MAAM,aAAa,EAAE;AACrB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM;AACR,IAAI,MAAM;AACV,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI;AACJ,GAAG,GAAG,IAAI;AACV,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY;AACtC,EAAE,IAAI,qBAAqB,GAAG,IAAI,CAAC,qBAAqB;AACxD,EAAE,IAAI,aAAa,GAAG,EAAE;AACxB,EAAE,SAAS,mBAAmB,CAAC,MAAM,EAAE;AACvC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,sBAAsB;AACrC,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,YAAY;AAC3B,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,WAAW;AAC1B,MAAM;AACN,QAAQ,OAAO,cAAc;AAC7B;AACA,EAAE;AACF,EAAE,SAAS,aAAa,CAAC,MAAM,EAAE;AACjC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,CAAC,CAAC,OAAO;AACxB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,CAAC,CAAC,QAAQ;AACzB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,CAAC,CAAC,QAAQ;AACzB,MAAM;AACN,QAAQ,OAAO,MAAM;AACrB;AACA,EAAE;AACF,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACrE,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8GAA8G,EAAE,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,iCAAiC,EAAE,WAAW,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,mCAAmC,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;AACha,EAAE,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;AACjC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,CAAC;AACxH,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAC;AACtD,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,YAAY,CAAC;AACxD,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6GAA6G,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,iCAAiC,CAAC,CAAC;AACjT,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC;AAC3C,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+CAA+C,CAAC,CAAC;AAC3E,MAAM,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE;AAClC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,sGAAsG,CAAC,CAAC;AAC3L,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+KAA+K,CAAC,CAAC;AAC7M,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,0BAA0B,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gCAAgC,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,mBAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,mCAAmC,EAAE,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAC1X,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,SAAS,EAAE;AAC5C,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gEAAgE,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACvK,QAAQ,IAAI,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE;AAC3D,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC5I,QAAQ,CAAC,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxD,QAAQ;AACR,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+DAA+D,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACtK,QAAQ,IAAI,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE;AAC3D,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC5I,QAAQ,CAAC,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxD,QAAQ;AACR,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACrD,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iCAAiC,CAAC,CAAC;AAC/D,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC9C,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yFAAyF,CAAC,CAAC;AACnH,IAAI,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC/F,MAAM,IAAI,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC;AAC/C,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uKAAuK,CAAC,CAAC;AACnM,MAAM,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE;AAClC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,sGAAsG,CAAC,CAAC;AAC3L,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+KAA+K,CAAC,CAAC;AAC7M,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,0BAA0B,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,mBAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,uGAAuG,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC;AAC9Y,oBAAoB,EAAE,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AACxF,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,SAAS,EAAE;AAC5C,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gEAAgE,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACvK,QAAQ,IAAI,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE;AAC3D,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC5I,QAAQ,CAAC,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxD,QAAQ;AACR,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+DAA+D,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACtK,QAAQ,IAAI,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE;AAC3D,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC5I,QAAQ,CAAC,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxD,QAAQ;AACR,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACrD,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gCAAgC,CAAC,CAAC;AAC5D,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC9C,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE,IAAI,qBAAqB,EAAE;AAC7B,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AACxD,IAAI;AACJ,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,GAAG,EAAE;AACP;;;;"}