import { u as push, N as ensure_array_like, x as head, z as escape_html, w as pop, y as attr, G as attr_style } from './index-0Ke2LYl0.js';
import './current-user-BM0W6LNm.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';

function pad2zeros(value) {
  return value.toString().padStart(2, "0");
}
class NewCalendarDate extends Date {
  static MS_PER_DAY = 1e3 * 60 * 60 * 24;
  static DAYS_PER_MONTH = 28;
  static PEACE_DAY = 365;
  static LEAP_DAY = 366;
  getIsLeapYear(year = this.getFullYear()) {
    return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;
  }
  getDayOfYear(year = this.getFullYear()) {
    const msFromStartOfYear = this.getTime() - new Date(year, 0, 0).getTime();
    return Math.floor(msFromStartOfYear / NewCalendarDate.MS_PER_DAY);
  }
  getDayPosition(dayOfYear) {
    if (dayOfYear === NewCalendarDate.LEAP_DAY) {
      return {
        month: 14,
        day: 2
      };
    }
    if (dayOfYear === NewCalendarDate.PEACE_DAY) {
      return {
        month: 14,
        day: 1
      };
    }
    return {
      month: Math.ceil(dayOfYear / NewCalendarDate.DAYS_PER_MONTH),
      day: dayOfYear % NewCalendarDate.DAYS_PER_MONTH || 28
    };
  }
  getParsed() {
    const year = this.getFullYear();
    const dayOfYear = this.getDayOfYear(year);
    const { month, day } = this.getDayPosition(dayOfYear);
    return {
      year,
      month,
      day
    };
  }
  toDateString() {
    const { year, month, day } = this.getParsed();
    return `${pad2zeros(day)}.${pad2zeros(month)}.${year}`;
  }
  toString() {
    const originalIsoString = super.toISOString();
    const timeFragment = originalIsoString.split("T")[1].slice(0, 8);
    const { year, month, day } = this.getParsed();
    return `${pad2zeros(day)}.${pad2zeros(month)}.${year} ${timeFragment}`;
  }
  toISODateString() {
    const { year, month, day } = this.getParsed();
    return `${year}-${pad2zeros(month)}-${pad2zeros(day)}`;
  }
  toISOString() {
    const originalIsoString = super.toISOString();
    const timeFragment = originalIsoString.split("T")[1];
    return `${this.toISODateString()}T${timeFragment}`;
  }
}
function Accordion_item($$payload, $$props) {
  push();
  const { title, children, isOpen, onToggle } = $$props;
  let isExpanded = isOpen ?? false;
  $$payload.out.push(`<div class="card mb-2"><div class="card-header d-flex justify-content-between align-items-center" role="switch" tabindex="0"${attr("aria-checked", isExpanded)}${attr_style("", { cursor: "pointer" })}><div>${escape_html(title)}</div> <button class="btn btn-link" type="button"${attr("aria-expanded", isExpanded)}>${escape_html(isExpanded ? "−" : "+")}</button></div> `);
  if (isExpanded) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="card-body">`);
    children($$payload);
    $$payload.out.push(`<!----></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}
function Accordion($$payload, $$props) {
  const { children } = $$props;
  $$payload.out.push(`<div class="accordion">`);
  children($$payload);
  $$payload.out.push(`<!----></div>`);
}
function Date_calculator($$payload, $$props) {
  push();
  const i18n = {
    en: {
      description: "Convert any date to our new calendar system",
      selectDate: "Select a date",
      isLeapYear: { title: "Is Leap Year", yes: "yes", no: "no" },
      dayOfYear: "Day of Year",
      day: "Day",
      month: "Month",
      year: "Year",
      commonString: "Common Format",
      isoString: "ISO Format",
      peaceDay: {
        title: "Peace Day",
        description: "The 365th day of the year that falls outside the regular month structure."
      },
      leapDay: {
        title: "Leap Day",
        description: "The 366th day of the year that only occurs in leap years."
      }
    },
    ru: {
      description: "Конвертировать любую дату в наш новый календарь",
      selectDate: "Выберите дату",
      isLeapYear: {
        title: "Високосный год",
        yes: "да",
        no: "нет"
      },
      dayOfYear: "День года",
      day: "День",
      month: "Месяц",
      year: "Год",
      commonString: "Бытовой формат",
      isoString: "Формат ISO",
      peaceDay: {
        title: "День мира",
        description: "365-й день года, который выпадает за пределы регулярной структуры месяцев."
      },
      leapDay: {
        title: "Високосный день",
        description: "366-й день года, который встречается только в високосных годах."
      }
    }
  };
  const { locale } = $$props;
  const t = i18n[locale];
  function calculateNewCalendarDate(date) {
    const ncDate = new NewCalendarDate(date);
    const isLeapYear = ncDate.getIsLeapYear();
    const dayOfYear = ncDate.getDayOfYear();
    const parsed = ncDate.getParsed();
    const commonString = ncDate.toString().slice(0, 10);
    return {
      newCalendarDate: ncDate,
      isLeapYear,
      dayOfYear,
      parsed,
      commonString,
      isoString: ncDate.toISOString().slice(0, 10)
    };
  }
  let selectedDate = /* @__PURE__ */ new Date();
  const calculatedDate = calculateNewCalendarDate(selectedDate);
  $$payload.out.push(`<div class="card"><div class="card-body"><p class="lead">${escape_html(t.description)}</p> <div class="row mb-4"><div class="col-md-6"><label for="date-picker" class="form-label">${escape_html(t.selectDate)}:</label> <input id="date-picker" class="form-control" type="date"${attr("value", selectedDate.toISOString().split("T")[0])}/></div></div> `);
  if (calculatedDate) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="row"><div class="col-md-12"><div class="alert alert-success"><h4 class="alert-heading">${escape_html(selectedDate.toLocaleDateString())} → ${escape_html(calculatedDate.commonString)}</h4> <hr/> <div class="row"><div class="col-md-6"><ul class="list-unstyled"><li><strong>${escape_html(t.isLeapYear.title)}:</strong>  
                    ${escape_html(calculatedDate.isLeapYear ? t.isLeapYear.yes : t.isLeapYear.no)}</li> <li><strong>${escape_html(t.dayOfYear)}:</strong>  
                    ${escape_html(calculatedDate.dayOfYear)}</li> <li><strong>${escape_html(t.day)}:</strong>  
                    ${escape_html(calculatedDate.parsed.day)}</li> <li><strong>${escape_html(t.month)}:</strong>  
                    ${escape_html(calculatedDate.parsed.month)}</li> <li><strong>${escape_html(t.year)}:</strong>  
                    ${escape_html(calculatedDate.parsed.year)}</li> <li><strong>${escape_html(t.commonString)}:</strong>  
                    ${escape_html(calculatedDate.commonString)}</li> <li><strong>${escape_html(t.isoString)}:</strong>  
                    ${escape_html(calculatedDate.isoString)}</li></ul></div> <div class="col-md-6">`);
    if (calculatedDate.parsed.month === 14) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="special-day-info"><h5>${escape_html(calculatedDate.parsed.day === 1 ? t.peaceDay.title : t.leapDay.title)}</h5> <p>${escape_html(calculatedDate.parsed.day === 1 ? t.peaceDay.description : t.leapDay.description)}</p></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div></div></div></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></div>`);
  pop();
}
function Realtime_watches($$payload, $$props) {
  push();
  const i18n = {
    en: {
      gregorianCalendar: "Gregorian Calendar",
      newCalendar: "New Calendar"
    },
    ru: {
      gregorianCalendar: "Григорианский календарь",
      newCalendar: "Новый календарь"
    }
  };
  const { locale } = $$props;
  const t = i18n[locale];
  function formatTime(date) {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit", second: "2-digit" });
  }
  let currentTime = /* @__PURE__ */ new Date(0);
  const formattedTime = formatTime(currentTime);
  $$payload.out.push(`<div class="my-4"><div class="row"><div class="col-md-6"><div class="card mb-3"><div class="card-body"><h5 class="card-title text-center">${escape_html(t.gregorianCalendar)}</h5> <div class="display-4 text-center">${escape_html(currentTime.toLocaleDateString([], { year: "numeric", month: "numeric", day: "numeric" }))}</div> <div class="display-6 text-center font-monospace">${escape_html(formattedTime)}</div></div></div></div> <div class="col-md-6"><div class="card mb-3"><div class="card-body"><h5 class="card-title text-center">${escape_html(t.newCalendar)}</h5> <div class="display-4 text-center">${escape_html(new NewCalendarDate(currentTime).toDateString())}</div> <div class="display-6 text-center font-monospace">${escape_html(formattedTime)}</div></div></div></div></div></div>`);
  pop();
}
function _page($$payload, $$props) {
  push();
  const calendarExamples = [
    new Date(2025, 0, 1),
    new Date(2024, 1, 29),
    new Date(2025, 7, 23),
    new Date(2024, 7, 23),
    new Date(2025, 11, 30),
    new Date(2025, 11, 31),
    new Date(2024, 11, 30),
    new Date(2024, 11, 31)
  ].map((date) => {
    const ncDate = new NewCalendarDate(date);
    const isLeapYear = ncDate.getIsLeapYear();
    const dayOfYear = ncDate.getDayOfYear();
    const parsed = ncDate.getParsed();
    const commonString = ncDate.toString().slice(0, 10);
    const isoString = ncDate.toISOString().slice(0, 10);
    return {
      currentCalendarDate: date,
      newCalendarDate: ncDate,
      isLeapYear,
      dayOfYear,
      parsed,
      commonString,
      isoString
    };
  });
  const i18n = {
    en: {
      _page: { title: "New Calendar — Commune" },
      title: "Future of Time: New Calendar",
      problem: {
        title: "The Challenge",
        description: "Our current calendar is fundamentally flawed with inconsistent month lengths and unpredictable date patterns.",
        inefficiencies: {
          description: "The Gregorian calendar creates significant inefficiencies in modern society",
          list: [
            "Irregular month lengths (28/29, 30, or 31 days) make planning inconsistent",
            "Dates shift to different weekdays each year, disrupting scheduling patterns",
            "Business quarters contain unequal numbers of days and weeks",
            "Leap year calculations add unnecessary complexity to date management"
          ]
        }
      },
      solution: {
        title: "Our Solution",
        description: "Introducing a revolutionary fixed calendar designed for the modern digital age.",
        benefits: {
          description: "Our calendar system delivers these essential benefits",
          list: [
            "Consistent, predictable structure that eliminates date confusion",
            "Intuitive design that allows for instant mental calculations",
            "Perfect alignment with business quarters for streamlined planning",
            "Flexible framework that adapts to contemporary scheduling requirements"
          ]
        }
      },
      historicalPrecedents: {
        title: "Historical Precedents",
        description: "Description",
        inspirations: "What inspired us from the project",
        whyNotUseAsIs: "Why not use as is",
        list: [
          {
            title: "International Fixed Calendar",
            description: "The International Fixed Calendar revolutionized timekeeping with its groundbreaking 13-month structure, each containing exactly 28 days. Pioneered by visionary Moses B. Cotsworth and championed by business leader George Eastman, this system directly addressed the inefficiencies of traditional calendars. Despite gaining significant momentum in early 20th century business circles, entrenched cultural resistance prevented its full adoption—a barrier our modern approach now overcomes.",
            inspirations: [
              "Perfect 28-day month symmetry for optimal scheduling",
              "Strategic 13-month yearly framework that aligns with lunar cycles",
              "Innovative concept of special days outside the standard week structure"
            ],
            whyNotUseAsIs: [
              "Outdated month naming convention that preserves Gregorian inconsistencies",
              "Problematic leap day placement disrupting mid-year planning cycles",
              "Suboptimal week structure with Sunday start that misaligns with modern work patterns"
            ]
          },
          {
            title: "World Calendar",
            description: "Elisabeth Achelis's World Calendar (1930) delivered a transformative approach to time organization with its quarterly structure. Each 91-day quarter featured a balanced three-month pattern, while introducing the revolutionary concept of 'World Day' and 'Leap Day' as special days outside the standard week cycle—creating perfect year-to-year alignment while honoring the astronomical reality of Earth's orbit.",
            inspirations: [
              "Breakthrough concept of special days outside the weekly cycle for perfect alignment",
              "Precisely balanced 13-week quarters for optimal business planning"
            ],
            whyNotUseAsIs: [
              "Inconsistent month lengths that perpetuate calculation difficulties",
              "Suboptimal placement of special days that disrupts natural year transitions"
            ]
          },
          {
            title: "Positivist Calendar",
            description: "Auguste Comte's visionary Positivist Calendar (1849) established a perfectly symmetrical time structure with 13 identical 28-day months plus a special year-end day. This system transcended mere timekeeping by connecting each month to humanity's greatest achievements and institutions—creating not just a calendar but a celebration of human progress and intellectual advancement.",
            inspirations: [
              "Mathematically perfect 28-day month structure for complete predictability",
              "Optimal 13-month yearly framework that aligns with natural cycles",
              "Innovative special day concept that honors the astronomical year"
            ],
            whyNotUseAsIs: [
              "Culturally specific month naming system that limits global adoption",
              "Philosophical and religious associations that restrict universal acceptance",
              "Problematic leap day placement disrupting yearly planning",
              "Suboptimal week structure with Sunday start that conflicts with modern work patterns"
            ]
          },
          {
            title: "Symmetry454 Calendar",
            description: "Dr. Irv Bromberg's Symmetry454 Calendar represents a mathematical breakthrough in temporal organization. This ingenious system creates perfect quarterly symmetry through a precise pattern: two 28-day months followed by one 35-day month in each quarter. This pattern delivers consistent month starts while maintaining perfect alignment with the solar year through a leap week mechanism in December.",
            inspirations: [
              "Mathematically elegant approach to quarter and month design that balances regularity with astronomical reality"
            ],
            whyNotUseAsIs: [
              "Variable month lengths that complicate scheduling and planning",
              "Full-week leap year adjustments that create excessive disruption to yearly patterns"
            ]
          },
          {
            title: "Hanke-Henry Permanent Calendar",
            description: "The Hanke-Henry Permanent Calendar, developed by economists Steve Hanke and Richard Henry, delivers remarkable consistency through its ingenious design. By implementing a fixed 30-30-31 day quarterly pattern and adding a leap week every 5-6 years, this system ensures dates always fall on the same weekday year after year—dramatically simplifying business planning and personal scheduling across decades.",
            inspirations: [
              "Perfect date-to-weekday consistency that eliminates year-to-year variations"
            ],
            whyNotUseAsIs: [
              "Inconsistent month lengths that perpetuate mental calculation challenges"
            ]
          }
        ]
      },
      ourDesign: {
        title: "Our Design",
        description: "A perfect balance: 13 uniform months of 28 days with special celebration days",
        structure: {
          description: "Our calendar features an elegant, mathematically sound structure",
          list: [
            "13 identical months of exactly 28 days (4 complete weeks) for perfect symmetry",
            "Every month begins on the same weekday, creating consistent patterns",
            "Peace Day: A special celebration at year's end (day 365)",
            "Leap Day: A bonus celebration in leap years (day 366)"
          ]
        },
        celebrations: "The final celebrations (Peace Day and optional Leap Day) create a special period outside the regular month structure—a dedicated time for global celebration and renewal as we transition to a new year.",
        advantages: {
          title: "Transformative Advantages",
          list: [
            "Perfect monthly symmetry with exactly 4 weeks in every month",
            "Precise quarterly alignment with exactly 91 days (13 weeks) per quarter",
            "Permanent weekday-date alignment that never changes year to year",
            "Effortless date calculations that can be done mentally in seconds",
            "Revolutionary improvement for business planning and financial cycles"
          ]
        }
      },
      examples: {
        title: "See It In Action",
        isLeapYear: { title: "Is Leap Year", yes: "yes", no: "no" },
        dayOfYear: "Day of Year",
        day: "Day",
        month: "Month",
        year: "Year",
        commonString: "Common Format",
        isoString: "ISO Format"
      },
      realtimeWatches: { title: "Live Calendar Comparison" },
      dateCalculator: { title: "Date Calculator" }
    },
    ru: {
      _page: {
        title: "Новый календарь — Коммуна"
      },
      title: "Будущее времени: Новый календарь",
      problem: {
        title: "Проблема",
        description: "Наш текущий календарь имеет фундаментальные недостатки: нерегулярная длина месяцев и непредсказуемость дат.",
        inefficiencies: {
          description: "Григорианский календарь создает значительные неэффективности в современном обществе",
          list: [
            "Нерегулярная длина месяцев (28/29, 30 или 31 день) делает планирование нестабильным",
            "Даты сдвигаются на разные дни недели каждый год, нарушая графики",
            "Бизнес-кварталы содержат неравное количество дней и недель",
            "Високосные годы добавляют ненужную сложность в управление датами"
          ]
        }
      },
      solution: {
        title: "Наше решение",
        description: "Представляем революционный фиксированный календарь, созданный для цифровой эпохи.",
        benefits: {
          description: "Наша система календаря обеспечивает следующие ключевые преимущества",
          list: [
            "Последовательная и предсказуемая структура, исключающая путаницу в датах",
            "Интуитивный дизайн, позволяющий мгновенно производить расчёты в уме",
            "Идеальное совпадение с бизнес-кварталами для упрощенного планирования",
            "Гибкая система, адаптированная к современным требованиям расписания"
          ]
        }
      },
      historicalPrecedents: {
        title: "Исторические прецеденты",
        description: "Описание",
        inspirations: "Что вдохновило нас в этом проекте",
        whyNotUseAsIs: "Почему не использовать как есть",
        list: [
          {
            title: "Международный фиксированный календарь",
            description: "Международный фиксированный календарь произвел революцию во временном учете, введя инновационную структуру из 13 месяцев по 28 дней. Разработанный Моисеем Б. Котсвортом и поддержанный бизнес-лидером Джорджем Истменом, этот календарь решал основные проблемы традиционных систем. Несмотря на активное распространение в деловых кругах XX века, культурное сопротивление помешало его полному внедрению — проблему, которую наше современное решение успешно преодолевает.",
            inspirations: [
              "Идеальная симметрия месяцев по 28 дней для оптимального планирования",
              "Стратегическая структура из 13 месяцев, согласованная с лунными циклами",
              "Инновационная концепция специальных дней вне стандартной недели"
            ],
            whyNotUseAsIs: [
              "Устаревшие названия месяцев, сохраняющие григорианские несоответствия",
              "Неподходящее размещение високосного дня, нарушающее середину года",
              "Нерациональная недельная структура с началом в воскресенье, не соответствующая современным трудовым стандартам"
            ]
          },
          {
            title: "Мировой календарь",
            description: "Мировой календарь Элизабет Ахелис (1930) предложил радикальный подход к организации времени с квартальной структурой. Каждый квартал состоял из сбалансированных трёх месяцев (по 91 дню), включая уникальные «Мировой день» и «Високосный день» вне недели, обеспечивая идеальное совпадение года с годом и астрономической реальностью орбиты Земли.",
            inspirations: [
              "Прорывная идея специальных дней вне недельного цикла для точного выравнивания",
              "Точно сбалансированные кварталы по 13 недель для оптимального бизнес-планирования"
            ],
            whyNotUseAsIs: [
              "Нерегулярная длина месяцев сохраняет сложности расчётов",
              "Неудачное расположение специальных дней нарушает естественный переход года"
            ]
          },
          {
            title: "Позитивистский календарь",
            description: "Позитивистский календарь Огюста Конта (1849) установил симметричную структуру времени: 13 идентичных месяцев по 28 дней и один специальный день в конце года. Календарь выходил за рамки простой хронологии, связывая каждый месяц с достижениями человечества — превращая календарь в праздник человеческого прогресса и разума.",
            inspirations: [
              "Математически идеальная структура месяцев по 28 дней для полной предсказуемости",
              "Оптимальный год из 13 месяцев, согласованный с природными циклами",
              "Идея специального дня, отражающего астрономический год"
            ],
            whyNotUseAsIs: [
              "Культурно-специфичная система названий месяцев затрудняет глобальное внедрение",
              "Философские и религиозные ассоциации ограничивают универсальное принятие",
              "Проблемное размещение високосного дня нарушает годовое планирование",
              "Нерациональное начало недели (воскресенье) противоречит современным трудовым нормам"
            ]
          },
          {
            title: "Календарь Symmetry454",
            description: "Календарь Symmetry454, разработанный доктором Ирвом Бромбергом, представляет собой математический прорыв в организации времени. Он создает симметрию кварталов с чёткой последовательностью: два месяца по 28 дней и один — 35 дней. Такая структура сохраняет точное начало месяцев и выравнивание с солнечным годом благодаря добавлению 'високосной недели' в декабре.",
            inspirations: [
              "Элегантный математический подход к квартальной и месячной структуре, сочетающий регулярность с астрономической точностью"
            ],
            whyNotUseAsIs: [
              "Переменная длина месяцев усложняет планирование",
              "Добавление целой недели в високосные годы нарушает годовой ритм"
            ]
          },
          {
            title: "Постоянный календарь Ханке-Генри",
            description: "Постоянный календарь Ханке-Генри, разработанный экономистами Стивом Ханке и Ричардом Генри, предлагает поразительную регулярность благодаря своей структуре: месяцы по 30, 30 и 31 дню в каждом квартале и добавление 'високосной недели' каждые 5–6 лет. Это обеспечивает постоянное совпадение даты и дня недели из года в год — упрощая планирование на десятилетия вперёд.",
            inspirations: [
              "Постоянное совпадение даты и дня недели, устраняющее межгодовую путаницу"
            ],
            whyNotUseAsIs: [
              "Нерегулярная длина месяцев сохраняет сложности при вычислениях"
            ]
          }
        ]
      },
      ourDesign: {
        title: "Наш дизайн",
        description: "Идеальный баланс: 13 равных месяцев по 28 дней и особые праздничные дни",
        structure: {
          description: "Наш календарь имеет элегантную и математически точную структуру",
          list: [
            "13 идентичных месяцев по ровно 28 дней (4 полные недели) для идеальной симметрии",
            "Каждый месяц начинается в один и тот же день недели, создавая стабильные шаблоны",
            "День Мира — особый праздник в конце года (365-й день)",
            "Високосный день — дополнительный праздник в високосные годы (366-й день)"
          ]
        },
        celebrations: "Финальные праздники (День Мира и дополнительный Високосный день) образуют особый период вне стандартной структуры месяцев — время для глобального празднования и обновления перед новым годом.",
        advantages: {
          title: "Преимущества преобразования",
          list: [
            "Идеальная симметрия месяцев: ровно 4 недели в каждом месяце",
            "Точное совпадение кварталов: ровно 91 день (13 недель) в каждом",
            "Постоянное совпадение дня недели и даты, не меняющееся с годами",
            "Моментальные расчеты дат в уме без усилий",
            "Революционное улучшение бизнес-планирования и финансовых циклов"
          ]
        }
      },
      examples: {
        title: "Примеры",
        isLeapYear: {
          title: "Високосный год",
          yes: "да",
          no: "нет"
        },
        dayOfYear: "День года",
        day: "День",
        month: "Месяц",
        year: "Год",
        commonString: "Бытовой формат",
        isoString: "Формат ISO"
      },
      realtimeWatches: {
        title: "Сравнение календарей в реальном времени"
      },
      dateCalculator: {
        title: "Калькулятор дат"
      }
    }
  };
  const { data } = $$props;
  const { locale } = data;
  const t = i18n[locale];
  const each_array = ensure_array_like(t.problem.inefficiencies.list);
  const each_array_1 = ensure_array_like(t.solution.benefits.list);
  const each_array_5 = ensure_array_like(t.ourDesign.structure.list);
  const each_array_6 = ensure_array_like(t.ourDesign.advantages.list);
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;
  });
  $$payload.out.push(`<div class="container my-5"><div class="responsive-container"><h1 class="mb-4">${escape_html(t.title)}</h1> <section class="mb-5"><h2>${escape_html(t.problem.title)}</h2> <div class="card"><div class="card-body"><p class="lead">${escape_html(t.problem.description)}</p> <p>${escape_html(t.problem.inefficiencies.description)}:</p> <ul><!--[-->`);
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let item = each_array[$$index];
    $$payload.out.push(`<li>${escape_html(item)}</li>`);
  }
  $$payload.out.push(`<!--]--></ul></div></div></section> <section class="mb-5"><h2>${escape_html(t.solution.title)}</h2> <div class="card"><div class="card-body"><p class="lead">${escape_html(t.solution.description)}</p> <p>${escape_html(t.solution.benefits.description)}:</p> <ul><!--[-->`);
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let item = each_array_1[$$index_1];
    $$payload.out.push(`<li>${escape_html(item)}</li>`);
  }
  $$payload.out.push(`<!--]--></ul></div></div></section> <section class="mb-5"><h2>${escape_html(t.historicalPrecedents.title)}</h2> `);
  Accordion($$payload, {
    children: ($$payload2) => {
      const each_array_2 = ensure_array_like(t.historicalPrecedents.list);
      $$payload2.out.push(`<!--[-->`);
      for (let $$index_4 = 0, $$length = each_array_2.length; $$index_4 < $$length; $$index_4++) {
        let item = each_array_2[$$index_4];
        Accordion_item($$payload2, {
          title: item.title,
          children: ($$payload3) => {
            const each_array_3 = ensure_array_like(item.inspirations);
            const each_array_4 = ensure_array_like(item.whyNotUseAsIs);
            $$payload3.out.push(`<p><strong>${escape_html(t.historicalPrecedents.description)}</strong>  
              ${escape_html(item.description)}</p> <p><strong>${escape_html(t.historicalPrecedents.inspirations)}</strong></p> <ul><!--[-->`);
            for (let $$index_2 = 0, $$length2 = each_array_3.length; $$index_2 < $$length2; $$index_2++) {
              let point = each_array_3[$$index_2];
              $$payload3.out.push(`<li>${escape_html(point)}</li>`);
            }
            $$payload3.out.push(`<!--]--></ul> <p><strong>${escape_html(t.historicalPrecedents.whyNotUseAsIs)}</strong></p> <ul><!--[-->`);
            for (let $$index_3 = 0, $$length2 = each_array_4.length; $$index_3 < $$length2; $$index_3++) {
              let point = each_array_4[$$index_3];
              $$payload3.out.push(`<li>${escape_html(point)}</li>`);
            }
            $$payload3.out.push(`<!--]--></ul>`);
          }
        });
      }
      $$payload2.out.push(`<!--]-->`);
    }
  });
  $$payload.out.push(`<!----></section> <section class="mb-5"><h2>${escape_html(t.ourDesign.title)}</h2> <div class="card"><div class="card-body"><p class="lead">${escape_html(t.ourDesign.description)}</p> <p>${escape_html(t.ourDesign.structure.description)}</p> <ul><!--[-->`);
  for (let $$index_5 = 0, $$length = each_array_5.length; $$index_5 < $$length; $$index_5++) {
    let item = each_array_5[$$index_5];
    $$payload.out.push(`<li>${escape_html(item)}</li>`);
  }
  $$payload.out.push(`<!--]--></ul> <p>${escape_html(t.ourDesign.celebrations)}</p> <div class="mt-4"><h4>${escape_html(t.ourDesign.advantages.title)}</h4> <ul><!--[-->`);
  for (let $$index_6 = 0, $$length = each_array_6.length; $$index_6 < $$length; $$index_6++) {
    let item = each_array_6[$$index_6];
    $$payload.out.push(`<li>${escape_html(item)}</li>`);
  }
  $$payload.out.push(`<!--]--></ul></div></div></div></section> <section class="mb-5"><h2>${escape_html(t.examples.title)}</h2> `);
  Accordion($$payload, {
    children: ($$payload2) => {
      const each_array_7 = ensure_array_like(calendarExamples);
      $$payload2.out.push(`<!--[-->`);
      for (let $$index_7 = 0, $$length = each_array_7.length; $$index_7 < $$length; $$index_7++) {
        let example = each_array_7[$$index_7];
        Accordion_item($$payload2, {
          title: `${example.currentCalendarDate.toLocaleDateString()} → ${example.commonString}`,
          children: ($$payload3) => {
            $$payload3.out.push(`<div class="row"><div class="col-md-6"><ul class="list-unstyled"><li><strong>${escape_html(t.examples.isLeapYear.title)}:</strong>  
                    ${escape_html(example.isLeapYear ? t.examples.isLeapYear.yes : t.examples.isLeapYear.no)}</li> <li><strong>${escape_html(t.examples.dayOfYear)}:</strong>  
                    ${escape_html(example.dayOfYear)}</li> <li><strong>${escape_html(t.examples.day)}:</strong>  
                    ${escape_html(example.parsed.day)}</li> <li><strong>${escape_html(t.examples.month)}:</strong>  
                    ${escape_html(example.parsed.month)}</li> <li><strong>${escape_html(t.examples.year)}:</strong>  
                    ${escape_html(example.parsed.year)}</li> <li><strong>${escape_html(t.examples.commonString)}:</strong>  
                    ${escape_html(example.commonString)}</li> <li><strong>${escape_html(t.examples.isoString)}:</strong>  
                    ${escape_html(example.isoString)}</li></ul></div></div>`);
          }
        });
      }
      $$payload2.out.push(`<!--]-->`);
    }
  });
  $$payload.out.push(`<!----></section> <section class="mb-5"><h2>${escape_html(t.realtimeWatches.title)}</h2> `);
  Realtime_watches($$payload, { locale });
  $$payload.out.push(`<!----></section> <section class="mb-5"><h2>${escape_html(t.dateCalculator.title)}</h2> `);
  Date_calculator($$payload, { locale });
  $$payload.out.push(`<!----></section></div></div>`);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CEW1-8cb.js.map
