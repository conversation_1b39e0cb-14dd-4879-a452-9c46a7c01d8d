{"version": 3, "file": "_page.svelte-CEW1-8cb.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/new-calendar/_page.svelte.js"], "sourcesContent": ["import { y as attr, F as attr_style, z as escape_html, w as pop, u as push, K as ensure_array_like, x as head } from \"../../../../../chunks/index.js\";\nimport \"../../../../../chunks/current-user.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../../chunks/exports.js\";\nimport \"clsx\";\nimport \"../../../../../chunks/state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nfunction pad2zeros(value) {\n  return value.toString().padStart(2, \"0\");\n}\nclass NewCalendarDate extends Date {\n  static MS_PER_DAY = 1e3 * 60 * 60 * 24;\n  static DAYS_PER_MONTH = 28;\n  static PEACE_DAY = 365;\n  static LEAP_DAY = 366;\n  getIsLeapYear(year = this.getFullYear()) {\n    return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;\n  }\n  getDayOfYear(year = this.getFullYear()) {\n    const msFromStartOfYear = this.getTime() - new Date(year, 0, 0).getTime();\n    return Math.floor(msFromStartOfYear / NewCalendarDate.MS_PER_DAY);\n  }\n  getDayPosition(dayOfYear) {\n    if (dayOfYear === NewCalendarDate.LEAP_DAY) {\n      return {\n        month: 14,\n        day: 2\n      };\n    }\n    if (dayOfYear === NewCalendarDate.PEACE_DAY) {\n      return {\n        month: 14,\n        day: 1\n      };\n    }\n    return {\n      month: Math.ceil(dayOfYear / NewCalendarDate.DAYS_PER_MONTH),\n      day: dayOfYear % NewCalendarDate.DAYS_PER_MONTH || 28\n    };\n  }\n  getParsed() {\n    const year = this.getFullYear();\n    const dayOfYear = this.getDayOfYear(year);\n    const { month, day } = this.getDayPosition(dayOfYear);\n    return {\n      year,\n      month,\n      day\n    };\n  }\n  toDateString() {\n    const { year, month, day } = this.getParsed();\n    return `${pad2zeros(day)}.${pad2zeros(month)}.${year}`;\n  }\n  toString() {\n    const originalIsoString = super.toISOString();\n    const timeFragment = originalIsoString.split(\"T\")[1].slice(0, 8);\n    const { year, month, day } = this.getParsed();\n    return `${pad2zeros(day)}.${pad2zeros(month)}.${year} ${timeFragment}`;\n  }\n  toISODateString() {\n    const { year, month, day } = this.getParsed();\n    return `${year}-${pad2zeros(month)}-${pad2zeros(day)}`;\n  }\n  toISOString() {\n    const originalIsoString = super.toISOString();\n    const timeFragment = originalIsoString.split(\"T\")[1];\n    return `${this.toISODateString()}T${timeFragment}`;\n  }\n}\nfunction Accordion_item($$payload, $$props) {\n  push();\n  const { title, children, isOpen, onToggle } = $$props;\n  let isExpanded = isOpen ?? false;\n  $$payload.out.push(`<div class=\"card mb-2\"><div class=\"card-header d-flex justify-content-between align-items-center\" role=\"switch\" tabindex=\"0\"${attr(\"aria-checked\", isExpanded)}${attr_style(\"\", { cursor: \"pointer\" })}><div>${escape_html(title)}</div> <button class=\"btn btn-link\" type=\"button\"${attr(\"aria-expanded\", isExpanded)}>${escape_html(isExpanded ? \"−\" : \"+\")}</button></div> `);\n  if (isExpanded) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"card-body\">`);\n    children($$payload);\n    $$payload.out.push(`<!----></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  pop();\n}\nfunction Accordion($$payload, $$props) {\n  const { children } = $$props;\n  $$payload.out.push(`<div class=\"accordion\">`);\n  children($$payload);\n  $$payload.out.push(`<!----></div>`);\n}\nfunction Date_calculator($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      description: \"Convert any date to our new calendar system\",\n      selectDate: \"Select a date\",\n      isLeapYear: { title: \"Is Leap Year\", yes: \"yes\", no: \"no\" },\n      dayOfYear: \"Day of Year\",\n      day: \"Day\",\n      month: \"Month\",\n      year: \"Year\",\n      commonString: \"Common Format\",\n      isoString: \"ISO Format\",\n      peaceDay: {\n        title: \"Peace Day\",\n        description: \"The 365th day of the year that falls outside the regular month structure.\"\n      },\n      leapDay: {\n        title: \"Leap Day\",\n        description: \"The 366th day of the year that only occurs in leap years.\"\n      }\n    },\n    ru: {\n      description: \"Конвертировать любую дату в наш новый календарь\",\n      selectDate: \"Выберите дату\",\n      isLeapYear: {\n        title: \"Високосный год\",\n        yes: \"да\",\n        no: \"нет\"\n      },\n      dayOfYear: \"День года\",\n      day: \"День\",\n      month: \"Месяц\",\n      year: \"Год\",\n      commonString: \"Бытовой формат\",\n      isoString: \"Формат ISO\",\n      peaceDay: {\n        title: \"День мира\",\n        description: \"365-й день года, который выпадает за пределы регулярной структуры месяцев.\"\n      },\n      leapDay: {\n        title: \"Високосный день\",\n        description: \"366-й день года, который встречается только в високосных годах.\"\n      }\n    }\n  };\n  const { locale } = $$props;\n  const t = i18n[locale];\n  function calculateNewCalendarDate(date) {\n    const ncDate = new NewCalendarDate(date);\n    const isLeapYear = ncDate.getIsLeapYear();\n    const dayOfYear = ncDate.getDayOfYear();\n    const parsed = ncDate.getParsed();\n    const commonString = ncDate.toString().slice(0, 10);\n    return {\n      newCalendarDate: ncDate,\n      isLeapYear,\n      dayOfYear,\n      parsed,\n      commonString,\n      isoString: ncDate.toISOString().slice(0, 10)\n    };\n  }\n  let selectedDate = /* @__PURE__ */ new Date();\n  const calculatedDate = calculateNewCalendarDate(selectedDate);\n  $$payload.out.push(`<div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.description)}</p> <div class=\"row mb-4\"><div class=\"col-md-6\"><label for=\"date-picker\" class=\"form-label\">${escape_html(t.selectDate)}:</label> <input id=\"date-picker\" class=\"form-control\" type=\"date\"${attr(\"value\", selectedDate.toISOString().split(\"T\")[0])}/></div></div> `);\n  if (calculatedDate) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"row\"><div class=\"col-md-12\"><div class=\"alert alert-success\"><h4 class=\"alert-heading\">${escape_html(selectedDate.toLocaleDateString())} → ${escape_html(calculatedDate.commonString)}</h4> <hr/> <div class=\"row\"><div class=\"col-md-6\"><ul class=\"list-unstyled\"><li><strong>${escape_html(t.isLeapYear.title)}:</strong>  \n                    ${escape_html(calculatedDate.isLeapYear ? t.isLeapYear.yes : t.isLeapYear.no)}</li> <li><strong>${escape_html(t.dayOfYear)}:</strong>  \n                    ${escape_html(calculatedDate.dayOfYear)}</li> <li><strong>${escape_html(t.day)}:</strong>  \n                    ${escape_html(calculatedDate.parsed.day)}</li> <li><strong>${escape_html(t.month)}:</strong>  \n                    ${escape_html(calculatedDate.parsed.month)}</li> <li><strong>${escape_html(t.year)}:</strong>  \n                    ${escape_html(calculatedDate.parsed.year)}</li> <li><strong>${escape_html(t.commonString)}:</strong>  \n                    ${escape_html(calculatedDate.commonString)}</li> <li><strong>${escape_html(t.isoString)}:</strong>  \n                    ${escape_html(calculatedDate.isoString)}</li></ul></div> <div class=\"col-md-6\">`);\n    if (calculatedDate.parsed.month === 14) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"special-day-info\"><h5>${escape_html(calculatedDate.parsed.day === 1 ? t.peaceDay.title : t.leapDay.title)}</h5> <p>${escape_html(calculatedDate.parsed.day === 1 ? t.peaceDay.description : t.leapDay.description)}</p></div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div></div></div></div></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div></div>`);\n  pop();\n}\nfunction Realtime_watches($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      gregorianCalendar: \"Gregorian Calendar\",\n      newCalendar: \"New Calendar\"\n    },\n    ru: {\n      gregorianCalendar: \"Григорианский календарь\",\n      newCalendar: \"Новый календарь\"\n    }\n  };\n  const { locale } = $$props;\n  const t = i18n[locale];\n  function formatTime(date) {\n    return date.toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\", second: \"2-digit\" });\n  }\n  let currentTime = /* @__PURE__ */ new Date(0);\n  const formattedTime = formatTime(currentTime);\n  $$payload.out.push(`<div class=\"my-4\"><div class=\"row\"><div class=\"col-md-6\"><div class=\"card mb-3\"><div class=\"card-body\"><h5 class=\"card-title text-center\">${escape_html(t.gregorianCalendar)}</h5> <div class=\"display-4 text-center\">${escape_html(currentTime.toLocaleDateString([], { year: \"numeric\", month: \"numeric\", day: \"numeric\" }))}</div> <div class=\"display-6 text-center font-monospace\">${escape_html(formattedTime)}</div></div></div></div> <div class=\"col-md-6\"><div class=\"card mb-3\"><div class=\"card-body\"><h5 class=\"card-title text-center\">${escape_html(t.newCalendar)}</h5> <div class=\"display-4 text-center\">${escape_html(new NewCalendarDate(currentTime).toDateString())}</div> <div class=\"display-6 text-center font-monospace\">${escape_html(formattedTime)}</div></div></div></div></div></div>`);\n  pop();\n}\nfunction _page($$payload, $$props) {\n  push();\n  const calendarExamples = [\n    new Date(2025, 0, 1),\n    new Date(2024, 1, 29),\n    new Date(2025, 7, 23),\n    new Date(2024, 7, 23),\n    new Date(2025, 11, 30),\n    new Date(2025, 11, 31),\n    new Date(2024, 11, 30),\n    new Date(2024, 11, 31)\n  ].map((date) => {\n    const ncDate = new NewCalendarDate(date);\n    const isLeapYear = ncDate.getIsLeapYear();\n    const dayOfYear = ncDate.getDayOfYear();\n    const parsed = ncDate.getParsed();\n    const commonString = ncDate.toString().slice(0, 10);\n    const isoString = ncDate.toISOString().slice(0, 10);\n    return {\n      currentCalendarDate: date,\n      newCalendarDate: ncDate,\n      isLeapYear,\n      dayOfYear,\n      parsed,\n      commonString,\n      isoString\n    };\n  });\n  const i18n = {\n    en: {\n      _page: { title: \"New Calendar — Commune\" },\n      title: \"Future of Time: New Calendar\",\n      problem: {\n        title: \"The Challenge\",\n        description: \"Our current calendar is fundamentally flawed with inconsistent month lengths and unpredictable date patterns.\",\n        inefficiencies: {\n          description: \"The Gregorian calendar creates significant inefficiencies in modern society\",\n          list: [\n            \"Irregular month lengths (28/29, 30, or 31 days) make planning inconsistent\",\n            \"Dates shift to different weekdays each year, disrupting scheduling patterns\",\n            \"Business quarters contain unequal numbers of days and weeks\",\n            \"Leap year calculations add unnecessary complexity to date management\"\n          ]\n        }\n      },\n      solution: {\n        title: \"Our Solution\",\n        description: \"Introducing a revolutionary fixed calendar designed for the modern digital age.\",\n        benefits: {\n          description: \"Our calendar system delivers these essential benefits\",\n          list: [\n            \"Consistent, predictable structure that eliminates date confusion\",\n            \"Intuitive design that allows for instant mental calculations\",\n            \"Perfect alignment with business quarters for streamlined planning\",\n            \"Flexible framework that adapts to contemporary scheduling requirements\"\n          ]\n        }\n      },\n      historicalPrecedents: {\n        title: \"Historical Precedents\",\n        description: \"Description\",\n        inspirations: \"What inspired us from the project\",\n        whyNotUseAsIs: \"Why not use as is\",\n        list: [\n          {\n            title: \"International Fixed Calendar\",\n            description: \"The International Fixed Calendar revolutionized timekeeping with its groundbreaking 13-month structure, each containing exactly 28 days. Pioneered by visionary Moses B. Cotsworth and championed by business leader George Eastman, this system directly addressed the inefficiencies of traditional calendars. Despite gaining significant momentum in early 20th century business circles, entrenched cultural resistance prevented its full adoption—a barrier our modern approach now overcomes.\",\n            inspirations: [\n              \"Perfect 28-day month symmetry for optimal scheduling\",\n              \"Strategic 13-month yearly framework that aligns with lunar cycles\",\n              \"Innovative concept of special days outside the standard week structure\"\n            ],\n            whyNotUseAsIs: [\n              \"Outdated month naming convention that preserves Gregorian inconsistencies\",\n              \"Problematic leap day placement disrupting mid-year planning cycles\",\n              \"Suboptimal week structure with Sunday start that misaligns with modern work patterns\"\n            ]\n          },\n          {\n            title: \"World Calendar\",\n            description: \"Elisabeth Achelis's World Calendar (1930) delivered a transformative approach to time organization with its quarterly structure. Each 91-day quarter featured a balanced three-month pattern, while introducing the revolutionary concept of 'World Day' and 'Leap Day' as special days outside the standard week cycle—creating perfect year-to-year alignment while honoring the astronomical reality of Earth's orbit.\",\n            inspirations: [\n              \"Breakthrough concept of special days outside the weekly cycle for perfect alignment\",\n              \"Precisely balanced 13-week quarters for optimal business planning\"\n            ],\n            whyNotUseAsIs: [\n              \"Inconsistent month lengths that perpetuate calculation difficulties\",\n              \"Suboptimal placement of special days that disrupts natural year transitions\"\n            ]\n          },\n          {\n            title: \"Positivist Calendar\",\n            description: \"Auguste Comte's visionary Positivist Calendar (1849) established a perfectly symmetrical time structure with 13 identical 28-day months plus a special year-end day. This system transcended mere timekeeping by connecting each month to humanity's greatest achievements and institutions—creating not just a calendar but a celebration of human progress and intellectual advancement.\",\n            inspirations: [\n              \"Mathematically perfect 28-day month structure for complete predictability\",\n              \"Optimal 13-month yearly framework that aligns with natural cycles\",\n              \"Innovative special day concept that honors the astronomical year\"\n            ],\n            whyNotUseAsIs: [\n              \"Culturally specific month naming system that limits global adoption\",\n              \"Philosophical and religious associations that restrict universal acceptance\",\n              \"Problematic leap day placement disrupting yearly planning\",\n              \"Suboptimal week structure with Sunday start that conflicts with modern work patterns\"\n            ]\n          },\n          {\n            title: \"Symmetry454 Calendar\",\n            description: \"Dr. Irv Bromberg's Symmetry454 Calendar represents a mathematical breakthrough in temporal organization. This ingenious system creates perfect quarterly symmetry through a precise pattern: two 28-day months followed by one 35-day month in each quarter. This pattern delivers consistent month starts while maintaining perfect alignment with the solar year through a leap week mechanism in December.\",\n            inspirations: [\n              \"Mathematically elegant approach to quarter and month design that balances regularity with astronomical reality\"\n            ],\n            whyNotUseAsIs: [\n              \"Variable month lengths that complicate scheduling and planning\",\n              \"Full-week leap year adjustments that create excessive disruption to yearly patterns\"\n            ]\n          },\n          {\n            title: \"Hanke-Henry Permanent Calendar\",\n            description: \"The Hanke-Henry Permanent Calendar, developed by economists Steve Hanke and Richard Henry, delivers remarkable consistency through its ingenious design. By implementing a fixed 30-30-31 day quarterly pattern and adding a leap week every 5-6 years, this system ensures dates always fall on the same weekday year after year—dramatically simplifying business planning and personal scheduling across decades.\",\n            inspirations: [\n              \"Perfect date-to-weekday consistency that eliminates year-to-year variations\"\n            ],\n            whyNotUseAsIs: [\n              \"Inconsistent month lengths that perpetuate mental calculation challenges\"\n            ]\n          }\n        ]\n      },\n      ourDesign: {\n        title: \"Our Design\",\n        description: \"A perfect balance: 13 uniform months of 28 days with special celebration days\",\n        structure: {\n          description: \"Our calendar features an elegant, mathematically sound structure\",\n          list: [\n            \"13 identical months of exactly 28 days (4 complete weeks) for perfect symmetry\",\n            \"Every month begins on the same weekday, creating consistent patterns\",\n            \"Peace Day: A special celebration at year's end (day 365)\",\n            \"Leap Day: A bonus celebration in leap years (day 366)\"\n          ]\n        },\n        celebrations: \"The final celebrations (Peace Day and optional Leap Day) create a special period outside the regular month structure—a dedicated time for global celebration and renewal as we transition to a new year.\",\n        advantages: {\n          title: \"Transformative Advantages\",\n          list: [\n            \"Perfect monthly symmetry with exactly 4 weeks in every month\",\n            \"Precise quarterly alignment with exactly 91 days (13 weeks) per quarter\",\n            \"Permanent weekday-date alignment that never changes year to year\",\n            \"Effortless date calculations that can be done mentally in seconds\",\n            \"Revolutionary improvement for business planning and financial cycles\"\n          ]\n        }\n      },\n      examples: {\n        title: \"See It In Action\",\n        isLeapYear: { title: \"Is Leap Year\", yes: \"yes\", no: \"no\" },\n        dayOfYear: \"Day of Year\",\n        day: \"Day\",\n        month: \"Month\",\n        year: \"Year\",\n        commonString: \"Common Format\",\n        isoString: \"ISO Format\"\n      },\n      realtimeWatches: { title: \"Live Calendar Comparison\" },\n      dateCalculator: { title: \"Date Calculator\" }\n    },\n    ru: {\n      _page: {\n        title: \"Новый календарь — Коммуна\"\n      },\n      title: \"Будущее времени: Новый календарь\",\n      problem: {\n        title: \"Проблема\",\n        description: \"Наш текущий календарь имеет фундаментальные недостатки: нерегулярная длина месяцев и непредсказуемость дат.\",\n        inefficiencies: {\n          description: \"Григорианский календарь создает значительные неэффективности в современном обществе\",\n          list: [\n            \"Нерегулярная длина месяцев (28/29, 30 или 31 день) делает планирование нестабильным\",\n            \"Даты сдвигаются на разные дни недели каждый год, нарушая графики\",\n            \"Бизнес-кварталы содержат неравное количество дней и недель\",\n            \"Високосные годы добавляют ненужную сложность в управление датами\"\n          ]\n        }\n      },\n      solution: {\n        title: \"Наше решение\",\n        description: \"Представляем революционный фиксированный календарь, созданный для цифровой эпохи.\",\n        benefits: {\n          description: \"Наша система календаря обеспечивает следующие ключевые преимущества\",\n          list: [\n            \"Последовательная и предсказуемая структура, исключающая путаницу в датах\",\n            \"Интуитивный дизайн, позволяющий мгновенно производить расчёты в уме\",\n            \"Идеальное совпадение с бизнес-кварталами для упрощенного планирования\",\n            \"Гибкая система, адаптированная к современным требованиям расписания\"\n          ]\n        }\n      },\n      historicalPrecedents: {\n        title: \"Исторические прецеденты\",\n        description: \"Описание\",\n        inspirations: \"Что вдохновило нас в этом проекте\",\n        whyNotUseAsIs: \"Почему не использовать как есть\",\n        list: [\n          {\n            title: \"Международный фиксированный календарь\",\n            description: \"Международный фиксированный календарь произвел революцию во временном учете, введя инновационную структуру из 13 месяцев по 28 дней. Разработанный Моисеем Б. Котсвортом и поддержанный бизнес-лидером Джорджем Истменом, этот календарь решал основные проблемы традиционных систем. Несмотря на активное распространение в деловых кругах XX века, культурное сопротивление помешало его полному внедрению — проблему, которую наше современное решение успешно преодолевает.\",\n            inspirations: [\n              \"Идеальная симметрия месяцев по 28 дней для оптимального планирования\",\n              \"Стратегическая структура из 13 месяцев, согласованная с лунными циклами\",\n              \"Инновационная концепция специальных дней вне стандартной недели\"\n            ],\n            whyNotUseAsIs: [\n              \"Устаревшие названия месяцев, сохраняющие григорианские несоответствия\",\n              \"Неподходящее размещение високосного дня, нарушающее середину года\",\n              \"Нерациональная недельная структура с началом в воскресенье, не соответствующая современным трудовым стандартам\"\n            ]\n          },\n          {\n            title: \"Мировой календарь\",\n            description: \"Мировой календарь Элизабет Ахелис (1930) предложил радикальный подход к организации времени с квартальной структурой. Каждый квартал состоял из сбалансированных трёх месяцев (по 91 дню), включая уникальные «Мировой день» и «Високосный день» вне недели, обеспечивая идеальное совпадение года с годом и астрономической реальностью орбиты Земли.\",\n            inspirations: [\n              \"Прорывная идея специальных дней вне недельного цикла для точного выравнивания\",\n              \"Точно сбалансированные кварталы по 13 недель для оптимального бизнес-планирования\"\n            ],\n            whyNotUseAsIs: [\n              \"Нерегулярная длина месяцев сохраняет сложности расчётов\",\n              \"Неудачное расположение специальных дней нарушает естественный переход года\"\n            ]\n          },\n          {\n            title: \"Позитивистский календарь\",\n            description: \"Позитивистский календарь Огюста Конта (1849) установил симметричную структуру времени: 13 идентичных месяцев по 28 дней и один специальный день в конце года. Календарь выходил за рамки простой хронологии, связывая каждый месяц с достижениями человечества — превращая календарь в праздник человеческого прогресса и разума.\",\n            inspirations: [\n              \"Математически идеальная структура месяцев по 28 дней для полной предсказуемости\",\n              \"Оптимальный год из 13 месяцев, согласованный с природными циклами\",\n              \"Идея специального дня, отражающего астрономический год\"\n            ],\n            whyNotUseAsIs: [\n              \"Культурно-специфичная система названий месяцев затрудняет глобальное внедрение\",\n              \"Философские и религиозные ассоциации ограничивают универсальное принятие\",\n              \"Проблемное размещение високосного дня нарушает годовое планирование\",\n              \"Нерациональное начало недели (воскресенье) противоречит современным трудовым нормам\"\n            ]\n          },\n          {\n            title: \"Календарь Symmetry454\",\n            description: \"Календарь Symmetry454, разработанный доктором Ирвом Бромбергом, представляет собой математический прорыв в организации времени. Он создает симметрию кварталов с чёткой последовательностью: два месяца по 28 дней и один — 35 дней. Такая структура сохраняет точное начало месяцев и выравнивание с солнечным годом благодаря добавлению 'високосной недели' в декабре.\",\n            inspirations: [\n              \"Элегантный математический подход к квартальной и месячной структуре, сочетающий регулярность с астрономической точностью\"\n            ],\n            whyNotUseAsIs: [\n              \"Переменная длина месяцев усложняет планирование\",\n              \"Добавление целой недели в високосные годы нарушает годовой ритм\"\n            ]\n          },\n          {\n            title: \"Постоянный календарь Ханке-Генри\",\n            description: \"Постоянный календарь Ханке-Генри, разработанный экономистами Стивом Ханке и Ричардом Генри, предлагает поразительную регулярность благодаря своей структуре: месяцы по 30, 30 и 31 дню в каждом квартале и добавление 'високосной недели' каждые 5–6 лет. Это обеспечивает постоянное совпадение даты и дня недели из года в год — упрощая планирование на десятилетия вперёд.\",\n            inspirations: [\n              \"Постоянное совпадение даты и дня недели, устраняющее межгодовую путаницу\"\n            ],\n            whyNotUseAsIs: [\n              \"Нерегулярная длина месяцев сохраняет сложности при вычислениях\"\n            ]\n          }\n        ]\n      },\n      ourDesign: {\n        title: \"Наш дизайн\",\n        description: \"Идеальный баланс: 13 равных месяцев по 28 дней и особые праздничные дни\",\n        structure: {\n          description: \"Наш календарь имеет элегантную и математически точную структуру\",\n          list: [\n            \"13 идентичных месяцев по ровно 28 дней (4 полные недели) для идеальной симметрии\",\n            \"Каждый месяц начинается в один и тот же день недели, создавая стабильные шаблоны\",\n            \"День Мира — особый праздник в конце года (365-й день)\",\n            \"Високосный день — дополнительный праздник в високосные годы (366-й день)\"\n          ]\n        },\n        celebrations: \"Финальные праздники (День Мира и дополнительный Високосный день) образуют особый период вне стандартной структуры месяцев — время для глобального празднования и обновления перед новым годом.\",\n        advantages: {\n          title: \"Преимущества преобразования\",\n          list: [\n            \"Идеальная симметрия месяцев: ровно 4 недели в каждом месяце\",\n            \"Точное совпадение кварталов: ровно 91 день (13 недель) в каждом\",\n            \"Постоянное совпадение дня недели и даты, не меняющееся с годами\",\n            \"Моментальные расчеты дат в уме без усилий\",\n            \"Революционное улучшение бизнес-планирования и финансовых циклов\"\n          ]\n        }\n      },\n      examples: {\n        title: \"Примеры\",\n        isLeapYear: {\n          title: \"Високосный год\",\n          yes: \"да\",\n          no: \"нет\"\n        },\n        dayOfYear: \"День года\",\n        day: \"День\",\n        month: \"Месяц\",\n        year: \"Год\",\n        commonString: \"Бытовой формат\",\n        isoString: \"Формат ISO\"\n      },\n      realtimeWatches: {\n        title: \"Сравнение календарей в реальном времени\"\n      },\n      dateCalculator: {\n        title: \"Калькулятор дат\"\n      }\n    }\n  };\n  const { data } = $$props;\n  const { locale } = data;\n  const t = i18n[locale];\n  const each_array = ensure_array_like(t.problem.inefficiencies.list);\n  const each_array_1 = ensure_array_like(t.solution.benefits.list);\n  const each_array_5 = ensure_array_like(t.ourDesign.structure.list);\n  const each_array_6 = ensure_array_like(t.ourDesign.advantages.list);\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;\n  });\n  $$payload.out.push(`<div class=\"container my-5\"><div class=\"responsive-container\"><h1 class=\"mb-4\">${escape_html(t.title)}</h1> <section class=\"mb-5\"><h2>${escape_html(t.problem.title)}</h2> <div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.problem.description)}</p> <p>${escape_html(t.problem.inefficiencies.description)}:</p> <ul><!--[-->`);\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let item = each_array[$$index];\n    $$payload.out.push(`<li>${escape_html(item)}</li>`);\n  }\n  $$payload.out.push(`<!--]--></ul></div></div></section> <section class=\"mb-5\"><h2>${escape_html(t.solution.title)}</h2> <div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.solution.description)}</p> <p>${escape_html(t.solution.benefits.description)}:</p> <ul><!--[-->`);\n  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n    let item = each_array_1[$$index_1];\n    $$payload.out.push(`<li>${escape_html(item)}</li>`);\n  }\n  $$payload.out.push(`<!--]--></ul></div></div></section> <section class=\"mb-5\"><h2>${escape_html(t.historicalPrecedents.title)}</h2> `);\n  Accordion($$payload, {\n    children: ($$payload2) => {\n      const each_array_2 = ensure_array_like(t.historicalPrecedents.list);\n      $$payload2.out.push(`<!--[-->`);\n      for (let $$index_4 = 0, $$length = each_array_2.length; $$index_4 < $$length; $$index_4++) {\n        let item = each_array_2[$$index_4];\n        Accordion_item($$payload2, {\n          title: item.title,\n          children: ($$payload3) => {\n            const each_array_3 = ensure_array_like(item.inspirations);\n            const each_array_4 = ensure_array_like(item.whyNotUseAsIs);\n            $$payload3.out.push(`<p><strong>${escape_html(t.historicalPrecedents.description)}</strong>  \n              ${escape_html(item.description)}</p> <p><strong>${escape_html(t.historicalPrecedents.inspirations)}</strong></p> <ul><!--[-->`);\n            for (let $$index_2 = 0, $$length2 = each_array_3.length; $$index_2 < $$length2; $$index_2++) {\n              let point = each_array_3[$$index_2];\n              $$payload3.out.push(`<li>${escape_html(point)}</li>`);\n            }\n            $$payload3.out.push(`<!--]--></ul> <p><strong>${escape_html(t.historicalPrecedents.whyNotUseAsIs)}</strong></p> <ul><!--[-->`);\n            for (let $$index_3 = 0, $$length2 = each_array_4.length; $$index_3 < $$length2; $$index_3++) {\n              let point = each_array_4[$$index_3];\n              $$payload3.out.push(`<li>${escape_html(point)}</li>`);\n            }\n            $$payload3.out.push(`<!--]--></ul>`);\n          }\n        });\n      }\n      $$payload2.out.push(`<!--]-->`);\n    }\n  });\n  $$payload.out.push(`<!----></section> <section class=\"mb-5\"><h2>${escape_html(t.ourDesign.title)}</h2> <div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.ourDesign.description)}</p> <p>${escape_html(t.ourDesign.structure.description)}</p> <ul><!--[-->`);\n  for (let $$index_5 = 0, $$length = each_array_5.length; $$index_5 < $$length; $$index_5++) {\n    let item = each_array_5[$$index_5];\n    $$payload.out.push(`<li>${escape_html(item)}</li>`);\n  }\n  $$payload.out.push(`<!--]--></ul> <p>${escape_html(t.ourDesign.celebrations)}</p> <div class=\"mt-4\"><h4>${escape_html(t.ourDesign.advantages.title)}</h4> <ul><!--[-->`);\n  for (let $$index_6 = 0, $$length = each_array_6.length; $$index_6 < $$length; $$index_6++) {\n    let item = each_array_6[$$index_6];\n    $$payload.out.push(`<li>${escape_html(item)}</li>`);\n  }\n  $$payload.out.push(`<!--]--></ul></div></div></div></section> <section class=\"mb-5\"><h2>${escape_html(t.examples.title)}</h2> `);\n  Accordion($$payload, {\n    children: ($$payload2) => {\n      const each_array_7 = ensure_array_like(calendarExamples);\n      $$payload2.out.push(`<!--[-->`);\n      for (let $$index_7 = 0, $$length = each_array_7.length; $$index_7 < $$length; $$index_7++) {\n        let example = each_array_7[$$index_7];\n        Accordion_item($$payload2, {\n          title: `${example.currentCalendarDate.toLocaleDateString()} → ${example.commonString}`,\n          children: ($$payload3) => {\n            $$payload3.out.push(`<div class=\"row\"><div class=\"col-md-6\"><ul class=\"list-unstyled\"><li><strong>${escape_html(t.examples.isLeapYear.title)}:</strong>  \n                    ${escape_html(example.isLeapYear ? t.examples.isLeapYear.yes : t.examples.isLeapYear.no)}</li> <li><strong>${escape_html(t.examples.dayOfYear)}:</strong>  \n                    ${escape_html(example.dayOfYear)}</li> <li><strong>${escape_html(t.examples.day)}:</strong>  \n                    ${escape_html(example.parsed.day)}</li> <li><strong>${escape_html(t.examples.month)}:</strong>  \n                    ${escape_html(example.parsed.month)}</li> <li><strong>${escape_html(t.examples.year)}:</strong>  \n                    ${escape_html(example.parsed.year)}</li> <li><strong>${escape_html(t.examples.commonString)}:</strong>  \n                    ${escape_html(example.commonString)}</li> <li><strong>${escape_html(t.examples.isoString)}:</strong>  \n                    ${escape_html(example.isoString)}</li></ul></div></div>`);\n          }\n        });\n      }\n      $$payload2.out.push(`<!--]-->`);\n    }\n  });\n  $$payload.out.push(`<!----></section> <section class=\"mb-5\"><h2>${escape_html(t.realtimeWatches.title)}</h2> `);\n  Realtime_watches($$payload, { locale });\n  $$payload.out.push(`<!----></section> <section class=\"mb-5\"><h2>${escape_html(t.dateCalculator.title)}</h2> `);\n  Date_calculator($$payload, { locale });\n  $$payload.out.push(`<!----></section></div></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;AAQA,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,EAAE,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;AAC1C;AACA,MAAM,eAAe,SAAS,IAAI,CAAC;AACnC,EAAE,OAAO,UAAU,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC,EAAE,OAAO,cAAc,GAAG,EAAE;AAC5B,EAAE,OAAO,SAAS,GAAG,GAAG;AACxB,EAAE,OAAO,QAAQ,GAAG,GAAG;AACvB,EAAE,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,EAAE;AAC3C,IAAI,OAAO,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC;AACjE,EAAE;AACF,EAAE,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,EAAE;AAC1C,IAAI,MAAM,iBAAiB,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE;AAC7E,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,eAAe,CAAC,UAAU,CAAC;AACrE,EAAE;AACF,EAAE,cAAc,CAAC,SAAS,EAAE;AAC5B,IAAI,IAAI,SAAS,KAAK,eAAe,CAAC,QAAQ,EAAE;AAChD,MAAM,OAAO;AACb,QAAQ,KAAK,EAAE,EAAE;AACjB,QAAQ,GAAG,EAAE;AACb,OAAO;AACP,IAAI;AACJ,IAAI,IAAI,SAAS,KAAK,eAAe,CAAC,SAAS,EAAE;AACjD,MAAM,OAAO;AACb,QAAQ,KAAK,EAAE,EAAE;AACjB,QAAQ,GAAG,EAAE;AACb,OAAO;AACP,IAAI;AACJ,IAAI,OAAO;AACX,MAAM,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,cAAc,CAAC;AAClE,MAAM,GAAG,EAAE,SAAS,GAAG,eAAe,CAAC,cAAc,IAAI;AACzD,KAAK;AACL,EAAE;AACF,EAAE,SAAS,GAAG;AACd,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE;AACnC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AAC7C,IAAI,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;AACzD,IAAI,OAAO;AACX,MAAM,IAAI;AACV,MAAM,KAAK;AACX,MAAM;AACN,KAAK;AACL,EAAE;AACF,EAAE,YAAY,GAAG;AACjB,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE;AACjD,IAAI,OAAO,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC1D,EAAE;AACF,EAAE,QAAQ,GAAG;AACb,IAAI,MAAM,iBAAiB,GAAG,KAAK,CAAC,WAAW,EAAE;AACjD,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACpE,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE;AACjD,IAAI,OAAO,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;AAC1E,EAAE;AACF,EAAE,eAAe,GAAG;AACpB,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE;AACjD,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1D,EAAE;AACF,EAAE,WAAW,GAAG;AAChB,IAAI,MAAM,iBAAiB,GAAG,KAAK,CAAC,WAAW,EAAE;AACjD,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;AACtD,EAAE;AACF;AACA,SAAS,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;AAC5C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO;AACvD,EAAE,IAAI,UAAU,GAAG,MAAM,IAAI,KAAK;AAClC,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4HAA4H,EAAE,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,iDAAiD,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACtY,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACjD,IAAI,QAAQ,CAAC,SAAS,CAAC;AACvB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AACvC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACvC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO;AAC9B,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC;AAC/C,EAAE,QAAQ,CAAC,SAAS,CAAC;AACrB,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AACrC;AACA,SAAS,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE;AAC7C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,WAAW,EAAE,6CAA6C;AAChE,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE;AACjE,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,YAAY,EAAE,eAAe;AACnC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,QAAQ,EAAE;AAChB,QAAQ,KAAK,EAAE,WAAW;AAC1B,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE,UAAU;AACzB,QAAQ,WAAW,EAAE;AACrB;AACA,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,WAAW,EAAE,iDAAiD;AACpE,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,UAAU,EAAE;AAClB,QAAQ,KAAK,EAAE,gBAAgB;AAC/B,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,EAAE,EAAE;AACZ,OAAO;AACP,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,GAAG,EAAE,MAAM;AACjB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,QAAQ,EAAE;AAChB,QAAQ,KAAK,EAAE,WAAW;AAC1B,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE,iBAAiB;AAChC,QAAQ,WAAW,EAAE;AACrB;AACA;AACA,GAAG;AACH,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO;AAC5B,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,SAAS,wBAAwB,CAAC,IAAI,EAAE;AAC1C,IAAI,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC;AAC5C,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE;AAC7C,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;AAC3C,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE;AACrC,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;AACvD,IAAI,OAAO;AACX,MAAM,eAAe,EAAE,MAAM;AAC7B,MAAM,UAAU;AAChB,MAAM,SAAS;AACf,MAAM,MAAM;AACZ,MAAM,YAAY;AAClB,MAAM,SAAS,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE;AACjD,KAAK;AACL,EAAE;AACF,EAAE,IAAI,YAAY,mBAAmB,IAAI,IAAI,EAAE;AAC/C,EAAE,MAAM,cAAc,GAAG,wBAAwB,CAAC,YAAY,CAAC;AAC/D,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yDAAyD,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,6FAA6F,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,kEAAkE,EAAE,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;AAClX,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mGAAmG,EAAE,WAAW,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,GAAG,EAAE,WAAW,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,yFAAyF,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACrV,oBAAoB,EAAE,WAAW,CAAC,cAAc,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAC/I,oBAAoB,EAAE,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACnG,oBAAoB,EAAE,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AACtG,oBAAoB,EAAE,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACvG,oBAAoB,EAAE,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;AAC9G,oBAAoB,EAAE,WAAW,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAC5G,oBAAoB,EAAE,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,uCAAuC,CAAC,CAAC;AACrG,IAAI,IAAI,cAAc,CAAC,MAAM,CAAC,KAAK,KAAK,EAAE,EAAE;AAC5C,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,WAAW,GAAG,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC;AACrQ,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,CAAC,CAAC;AAChE,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;AACA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,iBAAiB,EAAE,oBAAoB;AAC7C,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,iBAAiB,EAAE,yBAAyB;AAClD,MAAM,WAAW,EAAE;AACnB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO;AAC5B,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,SAAS,UAAU,CAAC,IAAI,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;AACjG,EAAE;AACF,EAAE,IAAI,WAAW,mBAAmB,IAAI,IAAI,CAAC,CAAC,CAAC;AAC/C,EAAE,MAAM,aAAa,GAAG,UAAU,CAAC,WAAW,CAAC;AAC/C,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0IAA0I,EAAE,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,WAAW,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,yDAAyD,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,gIAAgI,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,IAAI,eAAe,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,yDAAyD,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,oCAAoC,CAAC,CAAC;AAC5yB,EAAE,GAAG,EAAE;AACP;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,gBAAgB,GAAG;AAC3B,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AACxB,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;AACzB,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;AACzB,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;AACzB,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC;AAC1B,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC;AAC1B,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC;AAC1B,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE;AACzB,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AAClB,IAAI,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC;AAC5C,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE;AAC7C,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;AAC3C,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE;AACrC,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;AACvD,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;AACvD,IAAI,OAAO;AACX,MAAM,mBAAmB,EAAE,IAAI;AAC/B,MAAM,eAAe,EAAE,MAAM;AAC7B,MAAM,UAAU;AAChB,MAAM,SAAS;AACf,MAAM,MAAM;AACZ,MAAM,YAAY;AAClB,MAAM;AACN,KAAK;AACL,EAAE,CAAC,CAAC;AACJ,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,wBAAwB,EAAE;AAChD,MAAM,KAAK,EAAE,8BAA8B;AAC3C,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE,eAAe;AAC9B,QAAQ,WAAW,EAAE,+GAA+G;AACpI,QAAQ,cAAc,EAAE;AACxB,UAAU,WAAW,EAAE,6EAA6E;AACpG,UAAU,IAAI,EAAE;AAChB,YAAY,4EAA4E;AACxF,YAAY,6EAA6E;AACzF,YAAY,6DAA6D;AACzE,YAAY;AACZ;AACA;AACA,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,KAAK,EAAE,cAAc;AAC7B,QAAQ,WAAW,EAAE,iFAAiF;AACtG,QAAQ,QAAQ,EAAE;AAClB,UAAU,WAAW,EAAE,uDAAuD;AAC9E,UAAU,IAAI,EAAE;AAChB,YAAY,kEAAkE;AAC9E,YAAY,8DAA8D;AAC1E,YAAY,mEAAmE;AAC/E,YAAY;AACZ;AACA;AACA,OAAO;AACP,MAAM,oBAAoB,EAAE;AAC5B,QAAQ,KAAK,EAAE,uBAAuB;AACtC,QAAQ,WAAW,EAAE,aAAa;AAClC,QAAQ,YAAY,EAAE,mCAAmC;AACzD,QAAQ,aAAa,EAAE,mBAAmB;AAC1C,QAAQ,IAAI,EAAE;AACd,UAAU;AACV,YAAY,KAAK,EAAE,8BAA8B;AACjD,YAAY,WAAW,EAAE,ueAAue;AAChgB,YAAY,YAAY,EAAE;AAC1B,cAAc,sDAAsD;AACpE,cAAc,mEAAmE;AACjF,cAAc;AACd,aAAa;AACb,YAAY,aAAa,EAAE;AAC3B,cAAc,2EAA2E;AACzF,cAAc,oEAAoE;AAClF,cAAc;AACd;AACA,WAAW;AACX,UAAU;AACV,YAAY,KAAK,EAAE,gBAAgB;AACnC,YAAY,WAAW,EAAE,2ZAA2Z;AACpb,YAAY,YAAY,EAAE;AAC1B,cAAc,qFAAqF;AACnG,cAAc;AACd,aAAa;AACb,YAAY,aAAa,EAAE;AAC3B,cAAc,qEAAqE;AACnF,cAAc;AACd;AACA,WAAW;AACX,UAAU;AACV,YAAY,KAAK,EAAE,qBAAqB;AACxC,YAAY,WAAW,EAAE,4XAA4X;AACrZ,YAAY,YAAY,EAAE;AAC1B,cAAc,2EAA2E;AACzF,cAAc,mEAAmE;AACjF,cAAc;AACd,aAAa;AACb,YAAY,aAAa,EAAE;AAC3B,cAAc,qEAAqE;AACnF,cAAc,6EAA6E;AAC3F,cAAc,2DAA2D;AACzE,cAAc;AACd;AACA,WAAW;AACX,UAAU;AACV,YAAY,KAAK,EAAE,sBAAsB;AACzC,YAAY,WAAW,EAAE,+YAA+Y;AACxa,YAAY,YAAY,EAAE;AAC1B,cAAc;AACd,aAAa;AACb,YAAY,aAAa,EAAE;AAC3B,cAAc,gEAAgE;AAC9E,cAAc;AACd;AACA,WAAW;AACX,UAAU;AACV,YAAY,KAAK,EAAE,gCAAgC;AACnD,YAAY,WAAW,EAAE,sZAAsZ;AAC/a,YAAY,YAAY,EAAE;AAC1B,cAAc;AACd,aAAa;AACb,YAAY,aAAa,EAAE;AAC3B,cAAc;AACd;AACA;AACA;AACA,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,KAAK,EAAE,YAAY;AAC3B,QAAQ,WAAW,EAAE,+EAA+E;AACpG,QAAQ,SAAS,EAAE;AACnB,UAAU,WAAW,EAAE,kEAAkE;AACzF,UAAU,IAAI,EAAE;AAChB,YAAY,gFAAgF;AAC5F,YAAY,sEAAsE;AAClF,YAAY,0DAA0D;AACtE,YAAY;AACZ;AACA,SAAS;AACT,QAAQ,YAAY,EAAE,0MAA0M;AAChO,QAAQ,UAAU,EAAE;AACpB,UAAU,KAAK,EAAE,2BAA2B;AAC5C,UAAU,IAAI,EAAE;AAChB,YAAY,8DAA8D;AAC1E,YAAY,yEAAyE;AACrF,YAAY,kEAAkE;AAC9E,YAAY,mEAAmE;AAC/E,YAAY;AACZ;AACA;AACA,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,KAAK,EAAE,kBAAkB;AACjC,QAAQ,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE;AACnE,QAAQ,SAAS,EAAE,aAAa;AAChC,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,KAAK,EAAE,OAAO;AACtB,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ,YAAY,EAAE,eAAe;AACrC,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP,MAAM,eAAe,EAAE,EAAE,KAAK,EAAE,0BAA0B,EAAE;AAC5D,MAAM,cAAc,EAAE,EAAE,KAAK,EAAE,iBAAiB;AAChD,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,KAAK,EAAE,kCAAkC;AAC/C,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE,UAAU;AACzB,QAAQ,WAAW,EAAE,6GAA6G;AAClI,QAAQ,cAAc,EAAE;AACxB,UAAU,WAAW,EAAE,qFAAqF;AAC5G,UAAU,IAAI,EAAE;AAChB,YAAY,qFAAqF;AACjG,YAAY,kEAAkE;AAC9E,YAAY,4DAA4D;AACxE,YAAY;AACZ;AACA;AACA,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,KAAK,EAAE,cAAc;AAC7B,QAAQ,WAAW,EAAE,mFAAmF;AACxG,QAAQ,QAAQ,EAAE;AAClB,UAAU,WAAW,EAAE,qEAAqE;AAC5F,UAAU,IAAI,EAAE;AAChB,YAAY,0EAA0E;AACtF,YAAY,qEAAqE;AACjF,YAAY,uEAAuE;AACnF,YAAY;AACZ;AACA;AACA,OAAO;AACP,MAAM,oBAAoB,EAAE;AAC5B,QAAQ,KAAK,EAAE,yBAAyB;AACxC,QAAQ,WAAW,EAAE,UAAU;AAC/B,QAAQ,YAAY,EAAE,mCAAmC;AACzD,QAAQ,aAAa,EAAE,iCAAiC;AACxD,QAAQ,IAAI,EAAE;AACd,UAAU;AACV,YAAY,KAAK,EAAE,uCAAuC;AAC1D,YAAY,WAAW,EAAE,idAAid;AAC1e,YAAY,YAAY,EAAE;AAC1B,cAAc,sEAAsE;AACpF,cAAc,yEAAyE;AACvF,cAAc;AACd,aAAa;AACb,YAAY,aAAa,EAAE;AAC3B,cAAc,uEAAuE;AACrF,cAAc,mEAAmE;AACjF,cAAc;AACd;AACA,WAAW;AACX,UAAU;AACV,YAAY,KAAK,EAAE,mBAAmB;AACtC,YAAY,WAAW,EAAE,wVAAwV;AACjX,YAAY,YAAY,EAAE;AAC1B,cAAc,+EAA+E;AAC7F,cAAc;AACd,aAAa;AACb,YAAY,aAAa,EAAE;AAC3B,cAAc,yDAAyD;AACvE,cAAc;AACd;AACA,WAAW;AACX,UAAU;AACV,YAAY,KAAK,EAAE,0BAA0B;AAC7C,YAAY,WAAW,EAAE,mUAAmU;AAC5V,YAAY,YAAY,EAAE;AAC1B,cAAc,iFAAiF;AAC/F,cAAc,mEAAmE;AACjF,cAAc;AACd,aAAa;AACb,YAAY,aAAa,EAAE;AAC3B,cAAc,gFAAgF;AAC9F,cAAc,0EAA0E;AACxF,cAAc,qEAAqE;AACnF,cAAc;AACd;AACA,WAAW;AACX,UAAU;AACV,YAAY,KAAK,EAAE,uBAAuB;AAC1C,YAAY,WAAW,EAAE,2WAA2W;AACpY,YAAY,YAAY,EAAE;AAC1B,cAAc;AACd,aAAa;AACb,YAAY,aAAa,EAAE;AAC3B,cAAc,iDAAiD;AAC/D,cAAc;AACd;AACA,WAAW;AACX,UAAU;AACV,YAAY,KAAK,EAAE,kCAAkC;AACrD,YAAY,WAAW,EAAE,gXAAgX;AACzY,YAAY,YAAY,EAAE;AAC1B,cAAc;AACd,aAAa;AACb,YAAY,aAAa,EAAE;AAC3B,cAAc;AACd;AACA;AACA;AACA,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,KAAK,EAAE,YAAY;AAC3B,QAAQ,WAAW,EAAE,yEAAyE;AAC9F,QAAQ,SAAS,EAAE;AACnB,UAAU,WAAW,EAAE,iEAAiE;AACxF,UAAU,IAAI,EAAE;AAChB,YAAY,kFAAkF;AAC9F,YAAY,kFAAkF;AAC9F,YAAY,uDAAuD;AACnE,YAAY;AACZ;AACA,SAAS;AACT,QAAQ,YAAY,EAAE,gMAAgM;AACtN,QAAQ,UAAU,EAAE;AACpB,UAAU,KAAK,EAAE,6BAA6B;AAC9C,UAAU,IAAI,EAAE;AAChB,YAAY,6DAA6D;AACzE,YAAY,iEAAiE;AAC7E,YAAY,iEAAiE;AAC7E,YAAY,2CAA2C;AACvD,YAAY;AACZ;AACA;AACA,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,KAAK,EAAE,SAAS;AACxB,QAAQ,UAAU,EAAE;AACpB,UAAU,KAAK,EAAE,gBAAgB;AACjC,UAAU,GAAG,EAAE,IAAI;AACnB,UAAU,EAAE,EAAE;AACd,SAAS;AACT,QAAQ,SAAS,EAAE,WAAW;AAC9B,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,KAAK,EAAE,OAAO;AACtB,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,YAAY,EAAE,gBAAgB;AACtC,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP,MAAM,eAAe,EAAE;AACvB,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,cAAc,EAAE;AACtB,QAAQ,KAAK,EAAE;AACf;AACA;AACA,GAAG;AACH,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;AACzB,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;AACrE,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;AAClE,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;AACpE,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC;AACrE,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACrE,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+EAA+E,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC/W,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;AACvD,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8DAA8D,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACpS,EAAE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,IAAI,IAAI,IAAI,GAAG,YAAY,CAAC,SAAS,CAAC;AACtC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;AACvD,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8DAA8D,EAAE,WAAW,CAAC,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;AACxI,EAAE,SAAS,CAAC,SAAS,EAAE;AACvB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,MAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC;AACzE,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,MAAM,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AACjG,QAAQ,IAAI,IAAI,GAAG,YAAY,CAAC,SAAS,CAAC;AAC1C,QAAQ,cAAc,CAAC,UAAU,EAAE;AACnC,UAAU,KAAK,EAAE,IAAI,CAAC,KAAK;AAC3B,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC;AACrE,YAAY,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC;AACtE,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;AAC9F,cAAc,EAAE,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAC7I,YAAY,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AACzG,cAAc,IAAI,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC;AACjD,cAAc,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AACnE,YAAY;AACZ,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAC1I,YAAY,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AACzG,cAAc,IAAI,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC;AACjD,cAAc,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AACnE,YAAY;AACZ,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AAChD,UAAU;AACV,SAAS,CAAC;AACV,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,iBAAiB,CAAC,CAAC;AACrR,EAAE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,IAAI,IAAI,IAAI,GAAG,YAAY,CAAC,SAAS,CAAC;AACtC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;AACvD,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,2BAA2B,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC1K,EAAE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,IAAI,IAAI,IAAI,GAAG,YAAY,CAAC,SAAS,CAAC;AACtC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;AACvD,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oEAAoE,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;AAClI,EAAE,SAAS,CAAC,SAAS,EAAE;AACvB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,MAAM,YAAY,GAAG,iBAAiB,CAAC,gBAAgB,CAAC;AAC9D,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,MAAM,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AACjG,QAAQ,IAAI,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,QAAQ,cAAc,CAAC,UAAU,EAAE;AACnC,UAAU,KAAK,EAAE,CAAC,EAAE,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;AAChG,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6EAA6E,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACzJ,oBAAoB,EAAE,WAAW,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AACnK,oBAAoB,EAAE,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACrG,oBAAoB,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxG,oBAAoB,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACzG,oBAAoB,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;AAChH,oBAAoB,EAAE,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC9G,oBAAoB,EAAE,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,CAAC;AAC7E,UAAU;AACV,SAAS,CAAC;AACV,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;AACjH,EAAE,gBAAgB,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC;AACzC,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;AAChH,EAAE,eAAe,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC;AACxC,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6BAA6B,CAAC,CAAC;AACrD,EAAE,GAAG,EAAE;AACP;;;;"}