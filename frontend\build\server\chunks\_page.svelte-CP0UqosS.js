import { u as push, Q as copy_payload, T as assign_payload, w as pop, x as head, z as escape_html, N as ensure_array_like, J as attr_class, y as attr, K as stringify } from './index-0Ke2LYl0.js';
import { a as consts_exports } from './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import { P as Post_card, R as Right_menu, C as Create_post_modal } from './right-menu-LUsku24I.js';
import './schema-CmMg_B_X.js';
import './client-BUddp2Wf.js';
import './index2-DkUtb91y.js';
import './modal-BDhz9azZ.js';
import './localized-input-BFX4O5ct.js';
import './editor-DhPp1GEa.js';
import './reactor-hub-picker-B9joYKuZ.js';
import './html-FW6Ia4bL.js';

function Lens_modal($$payload, $$props) {
  push();
  const i18n = {
    en: {
      createLens: "Create Lens",
      editLens: "Edit Lens",
      lensGuide: "Lens Guide",
      cancel: "Cancel",
      create: "Create",
      update: "Update",
      backToForm: "Back to Form",
      showGuide: "Show Guide",
      name: "Name",
      namePlaceholder: "Enter lens name...",
      code: "Code",
      nameRequired: "Name is required",
      codeRequired: "Code is required",
      createSuccess: "Lens created successfully!",
      updateSuccess: "Lens updated successfully!",
      createError: "Failed to create lens",
      updateError: "Failed to update lens",
      // Guide translations
      guideTitle: "Lens Code Guide",
      fieldsOperatorsTitle: "Available Fields & Operators",
      fieldColumn: "Field",
      operatorsColumn: "Operators",
      valueTypeColumn: "Value Type",
      descriptionColumn: "Description",
      hubDescription: "Filter by hub",
      communityDescription: "Filter by community",
      authorDescription: "Filter by author",
      ratingDescription: "Post rating",
      usefulnessDescription: "Post usefulness",
      tagDescription: "Filter by tags",
      titleDescription: "Search in title (like operator)",
      bodyDescription: "Search in body (like operator)",
      ageDescription: 'Post age (e.g., "3d", "2w", "1mo")',
      logicalOperatorsTitle: "Logical Operators",
      andOperator: "AND operator (both conditions must be true)",
      orOperator: "OR operator (either condition can be true)",
      parenthesesOperator: "Parentheses for grouping expressions",
      ageDurationTitle: "Age Duration Units",
      minutesUnit: "minutes",
      hoursUnit: "hours",
      daysUnit: "days",
      weeksUnit: "weeks",
      monthsUnit: "months",
      yearsUnit: "years",
      simpleExamplesTitle: "Simple Examples",
      highRatedExample: "High rated posts:",
      recentPostsExample: "Recent posts:",
      titleSearchExample: "Search in title:",
      usefulRecentExample: "Useful and recent:",
      complexExampleTitle: "Complex Example",
      complexExampleSubtitle: "High-quality recent posts with educational content:",
      complexExampleDescription: 'This filters for posts that meet one of two criteria: either posts from specific hubs that have a rating of 100 or higher, OR posts from a specific community that have a usefulness score of 7 or higher. Additionally, all results must be posted within the last 2 weeks and contain either "tutorial" in the title or "guide" in the body content.',
      // Value types
      arrayOfIds: "Array of IDs",
      integer: "Integer",
      integerRange: "Integer (0-10)",
      string: "String",
      durationString: "Duration string"
    },
    ru: {
      createLens: "Создать линзу",
      editLens: "Редактировать линзу",
      lensGuide: "Руководство по линзам",
      cancel: "Отмена",
      create: "Создать",
      update: "Обновить",
      backToForm: "Вернуться к форме",
      showGuide: "Показать руководство",
      name: "Название",
      namePlaceholder: "Введите название линзы...",
      code: "Код",
      nameRequired: "Название обязательно",
      codeRequired: "Код обязателен",
      createSuccess: "Линза успешно создана!",
      updateSuccess: "Линза успешно обновлена!",
      createError: "Не удалось создать линзу",
      updateError: "Не удалось обновить линзу",
      // Guide translations
      guideTitle: "Руководство по коду линз",
      fieldsOperatorsTitle: "Доступные поля и операторы",
      fieldColumn: "Поле",
      operatorsColumn: "Операторы",
      valueTypeColumn: "Тип значения",
      descriptionColumn: "Описание",
      hubDescription: "Фильтр по хабу",
      communityDescription: "Фильтр по сообществу",
      authorDescription: "Фильтр по автору",
      ratingDescription: "Рейтинг поста",
      usefulnessDescription: "Полезность поста",
      tagDescription: "Фильтр по тегам",
      titleDescription: "Поиск в заголовке (оператор подобия)",
      bodyDescription: "Поиск в тексте (оператор подобия)",
      ageDescription: 'Возраст поста (например, "3d", "2w", "1mo")',
      logicalOperatorsTitle: "Логические операторы",
      andOperator: "Оператор И (оба условия должны быть истинными)",
      orOperator: "Оператор ИЛИ (любое из условий может быть истинным)",
      parenthesesOperator: "Скобки для группировки выражений",
      ageDurationTitle: "Единицы времени для возраста",
      minutesUnit: "минуты",
      hoursUnit: "часы",
      daysUnit: "дни",
      weeksUnit: "недели",
      monthsUnit: "месяцы",
      yearsUnit: "годы",
      simpleExamplesTitle: "Простые примеры",
      highRatedExample: "Посты с высоким рейтингом:",
      recentPostsExample: "Недавние посты:",
      titleSearchExample: "Поиск в заголовке:",
      usefulRecentExample: "Полезные и недавние:",
      complexExampleTitle: "Сложный пример",
      complexExampleSubtitle: "Качественные недавние посты с образовательным контентом:",
      complexExampleDescription: 'Этот фильтр отбирает посты, которые соответствуют одному из двух критериев: либо посты из определенных хабов с рейтингом 100 или выше, ЛИБО посты из определенного сообщества с оценкой полезности 7 или выше. Дополнительно все результаты должны быть опубликованы в течение последних 2 недель и содержать либо "tutorial" в заголовке, либо "guide" в тексте.',
      // Value types
      arrayOfIds: "Массив ID",
      integer: "Целое число",
      integerRange: "Целое число (0-10)",
      string: "Строка",
      durationString: "Строка времени"
    }
  };
  const { fetcher: api } = getClient();
  const { locale } = $$props;
  const t = i18n[locale];
  t.createLens;
  t.create;
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function Left_menu($$payload, $$props) {
  push();
  const i18n = {
    en: {
      lens: "Lens",
      lensing: "Lensing",
      createLens: "Create lens",
      editLens: "Edit lens",
      deleteLens: "Delete lens",
      noLens: "No lens",
      before: "Before",
      from: "From",
      now: "now",
      showRead: "Show read",
      toggleLensing: "Toggle lensing",
      toggleDateRange: "Toggle date range",
      confirmDelete: "Are you sure you want to delete this lens?",
      deleteSuccess: "Lens deleted successfully!",
      deleteError: "Failed to delete lens"
    },
    ru: {
      lens: "Линза",
      lensing: "Линзирование",
      createLens: "Создать линзу",
      editLens: "Редактировать линзу",
      deleteLens: "Удалить линзу",
      noLens: "Без линзы",
      before: "До",
      from: "От",
      now: "сейчас",
      showRead: "Прочитанное",
      toggleLensing: "Переключить линзирование",
      toggleDateRange: "Переключить диапазон дат",
      confirmDelete: "Вы уверены, что хотите удалить эту линзу?",
      deleteSuccess: "Линза успешно удалена!",
      deleteError: "Не удалось удалить линзу"
    }
  };
  const { fetcher: api } = getClient();
  const {
    locale
  } = $$props;
  const t = i18n[locale];
  $$payload.out.push(`<div${attr_class(`left-menu ${stringify("collapsed")}`, "svelte-12keyqc")}><div class="left-menu-header d-flex justify-content-between align-items-center p-3 bg-light svelte-12keyqc"><h6 class="mb-0">${escape_html(t.lensing)}</h6> <button class="btn btn-sm btn-link p-0"${attr("aria-label", t.toggleLensing)}${attr("title", t.toggleLensing)}><i${attr_class(`bi bi-chevron-${stringify("down")}`)}></i></button></div> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div> `);
  Lens_modal($$payload, {
    locale
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Feed — Reactor" },
      createPost: "Create Post",
      noPosts: "No posts found",
      loadingMore: "Loading more posts..."
    },
    ru: {
      _page: {
        title: "Лента — Реактор"
      },
      createPost: "Создать пост",
      noPosts: "Посты не найдены",
      loadingMore: "Загружаем больше постов..."
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const { locale, toLocaleHref, getAppropriateLocalization } = data;
  const t = i18n[locale];
  let posts = data.posts;
  data.lenses;
  let isHasMorePosts = data.isHasMorePosts;
  let isLoadingMore = false;
  let error = null;
  let selectedLensId = null;
  let isRightMenuExpanded = false;
  let showCreatePostModal = false;
  function closeCreatePostModal() {
    showCreatePostModal = false;
  }
  function handlePostCreated() {
    refetchPosts();
  }
  async function refetchPosts() {
    isLoadingMore = true;
    error = null;
    try {
      const newPosts = await api.reactor.post.list.get({ pagination: { page: 1 }, lensId: selectedLensId });
      posts = newPosts;
      isHasMorePosts = newPosts.length === consts_exports.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : "An error occurred while fetching posts";
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>${escape_html(t._page.title)}</title>`;
    });
    $$payload2.out.push(`<div class="row g-4 mt-3"><div class="col-1"></div> <div class="col-2">`);
    Left_menu($$payload2, {
      locale
    });
    $$payload2.out.push(`<!----></div> <div class="col-6"><div class="feed svelte-wnb4vd">`);
    if (posts.length === 0) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="text-center py-5"><p class="text-muted">${escape_html(t.noPosts)}</p></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      const each_array = ensure_array_like(posts);
      $$payload2.out.push(`<!--[-->`);
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let post = each_array[$$index];
        Post_card($$payload2, { post, locale, toLocaleHref, getAppropriateLocalization });
      }
      $$payload2.out.push(`<!--]-->`);
    }
    $$payload2.out.push(`<!--]--> `);
    if (isHasMorePosts) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="text-center py-3">`);
      if (isLoadingMore) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">${escape_html(t.loadingMore)}</span></div> <p class="text-muted mt-2 mb-0">${escape_html(t.loadingMore)}</p>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (error) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div class="alert alert-danger" role="alert">${escape_html(error)}</div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--></div></div> <div class="col-2"><div${attr_class(`mb-3 create-post-btn-container ${stringify(isRightMenuExpanded ? "with-right-menu-expanded" : "")}`, "svelte-wnb4vd")}><button class="btn btn-outline-secondary w-100 create-post-btn svelte-wnb4vd"${attr("aria-label", t.createPost)}><i class="bi bi-plus-circle me-2"></i> ${escape_html(t.createPost)}</button></div> `);
    Right_menu($$payload2, {
      locale,
      toLocaleHref,
      get isExpanded() {
        return isRightMenuExpanded;
      },
      set isExpanded($$value) {
        isRightMenuExpanded = $$value;
        $$settled = false;
      }
    });
    $$payload2.out.push(`<!----></div></div> `);
    Create_post_modal($$payload2, {
      show: showCreatePostModal,
      locale,
      toLocaleHref,
      onClose: closeCreatePostModal,
      onPostCreated: handlePostCreated
    });
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CP0UqosS.js.map
