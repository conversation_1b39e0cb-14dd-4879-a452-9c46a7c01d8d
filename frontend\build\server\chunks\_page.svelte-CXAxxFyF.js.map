{"version": 3, "file": "_page.svelte-CXAxxFyF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/users/_page.svelte.js"], "sourcesContent": ["import { x as head, z as escape_html, K as ensure_array_like, y as attr, w as pop, u as push } from \"../../../../../chunks/index.js\";\nimport \"../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../chunks/acrpc.js\";\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Users — Commune\" },\n      users: \"Users\",\n      loading: \"Loading...\",\n      noUsersFound: \"No users found\",\n      errorFetchingUsers: \"Failed to fetch users\",\n      errorOccurred: \"An error occurred while fetching users\",\n      loadingMore: \"Loading more users...\",\n      noImage: \"No image\",\n      userImageAlt: \"User image\",\n      role: { admin: \"Admin\" }\n    },\n    ru: {\n      _page: {\n        title: \"Пользователи — Коммуна\"\n      },\n      users: \"Пользователи\",\n      loading: \"Загрузка...\",\n      noUsersFound: \"Пользователи не найдены\",\n      errorFetchingUsers: \"Не удалось загрузить пользователей\",\n      errorOccurred: \"Произошла ошибка при загрузке пользователей\",\n      loadingMore: \"Загружаем больше пользователей...\",\n      noImage: \"Нет изображения\",\n      userImageAlt: \"Изображение пользователя\",\n      role: { admin: \"Админ\" }\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const { locale, toLocaleHref, getAppropriateLocalization } = data;\n  const t = i18n[locale];\n  let users = data.users;\n  let isHasMoreUsers = data.isHasMoreUsers;\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;\n  });\n  $$payload.out.push(`<div class=\"container my-4 mb-5\"><div class=\"d-flex justify-content-between align-items-center my-4\"><h1>${escape_html(t.users)}</h1></div> `);\n  if (users.length === 0) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-5\"><p class=\"text-muted\">${escape_html(t.noUsersFound)}</p></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    const each_array = ensure_array_like(users);\n    $$payload.out.push(`<div class=\"row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-4\"><!--[-->`);\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let user = each_array[$$index];\n      $$payload.out.push(`<div class=\"col\"><div class=\"card h-100 shadow-sm hover-card svelte-1g1c3uj\"><a${attr(\"href\", toLocaleHref(`/users/${user.id}`))} class=\"text-decoration-none text-black\">`);\n      if (user.image) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<div style=\"height: 140px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;\"><img${attr(\"src\", `/images/${user.image}`)}${attr(\"alt\", `${t.userImageAlt}`)} style=\"width: 100%; height: 100%; object-fit: cover;\"/></div>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<div class=\"bg-light text-center d-flex align-items-center justify-content-center\" style=\"height: 140px;\"><span class=\"text-muted\">${escape_html(t.noImage)}</span></div>`);\n      }\n      $$payload.out.push(`<!--]--> <div class=\"card-body d-flex flex-column\"><h5 class=\"card-title fs-5 text-truncate\">${escape_html(getAppropriateLocalization(user.name))}</h5> <p class=\"card-text text-muted small\" style=\"height: 3rem; overflow: hidden\">${escape_html(getAppropriateLocalization(user.description) || \"\")}</p> <div class=\"mt-auto\"><div class=\"small text-muted\">`);\n      if (user.role === \"admin\") {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<span class=\"badge bg-danger\">${escape_html(t.role.admin)}</span>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n      }\n      $$payload.out.push(`<!--]--></div></div></div></a></div></div>`);\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  }\n  $$payload.out.push(`<!--]--> `);\n  if (isHasMoreUsers) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-3\">`);\n    {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;AAGA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE;AACzC,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM,kBAAkB,EAAE,uBAAuB;AACjD,MAAM,aAAa,EAAE,wCAAwC;AAC7D,MAAM,WAAW,EAAE,uBAAuB;AAC1C,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,YAAY,EAAE,YAAY;AAChC,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,OAAO;AAC5B,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,YAAY,EAAE,yBAAyB;AAC7C,MAAM,kBAAkB,EAAE,oCAAoC;AAC9D,MAAM,aAAa,EAAE,6CAA6C;AAClE,MAAM,WAAW,EAAE,mCAAmC;AACtD,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,YAAY,EAAE,0BAA0B;AAC9C,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,OAAO;AAC5B;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,0BAA0B,EAAE,GAAG,IAAI;AACnE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;AACxB,EAAE,IAAI,cAAc,GAAG,IAAI,CAAC,cAAc;AAC1C,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACrE,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yGAAyG,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC;AACpK,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1B,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC;AACtH,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC;AAC/C,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kFAAkF,CAAC,CAAC;AAC5G,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+EAA+E,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,yCAAyC,CAAC,CAAC;AACtM,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE;AACtB,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wHAAwH,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,8DAA8D,CAAC,CAAC;AAC9R,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mIAAmI,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AACvM,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6FAA6F,EAAE,WAAW,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mFAAmF,EAAE,WAAW,CAAC,0BAA0B,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,wDAAwD,CAAC,CAAC;AAC3X,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACjC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;AAC/F,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0CAA0C,CAAC,CAAC;AACtE,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AACxD,IAAI;AACJ,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,GAAG,EAAE;AACP;;;;"}