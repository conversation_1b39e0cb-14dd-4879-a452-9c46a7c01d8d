import { u as push, x as head, y as attr, z as escape_html, N as ensure_array_like, w as pop, J as attr_class, Q as copy_payload, T as assign_payload } from './index-0Ke2LYl0.js';
import { p as page } from './index3-DDIiUUg1.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import './current-user-BM0W6LNm.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import { a as formatDatetime } from './format-date-DgRnEWcB.js';
import { M as Modal } from './modal-BDhz9azZ.js';
import { L as Localized_input } from './localized-input-BFX4O5ct.js';
import { L as Localized_textarea } from './localized-textarea-SdDnJXwN.js';
import './client2-BuT6F0Df.js';
import './client-BUddp2Wf.js';
import './index2-DkUtb91y.js';
import './schema-CmMg_B_X.js';

function Member_card($$payload, $$props) {
  push();
  const i18n = {
    en: {
      head: "Head",
      joined: "Joined",
      remove: "Remove",
      removing: "Removing...",
      transferHead: "Make Head",
      transferring: "Transferring...",
      confirmRemoval: "Confirm Removal",
      confirmRemoveMessage: "Are you sure you want to remove",
      fromCommune: "from this commune?",
      cannotUndo: "This action cannot be undone.",
      confirmTransfer: "Transfer Head Status",
      confirmTransferMessage: "Are you sure you want to transfer head status to",
      transferWarning: "This will make them the new head of the commune and remove your head privileges. This action cannot be undone.",
      transferSuccess: "Head status transferred successfully",
      cancel: "Cancel",
      removeMember: "Remove Member",
      transferHeadStatus: "Transfer Head Status",
      errorRemovingMember: "Failed to remove member",
      errorTransferringHead: "Failed to transfer head status",
      errorOccurred: "An error occurred while removing member",
      dateFormatLocale: "en-US",
      noImage: "No image",
      userImageAlt: "User image"
    },
    ru: {
      head: "Глава",
      joined: "Присоединился",
      remove: "Удалить",
      removing: "Удаление...",
      transferHead: "Сделать главой",
      transferring: "Передача...",
      confirmRemoval: "Подтвердите удаление",
      confirmRemoveMessage: "Вы уверены, что хотите удалить",
      fromCommune: "из этой коммуны?",
      cannotUndo: "Это действие нельзя отменить.",
      confirmTransfer: "Передача статуса главы",
      confirmTransferMessage: "Вы уверены, что хотите передать статус главы пользователю",
      transferWarning: "Это сделает их новым главой коммуны и лишит вас привилегий главы. Это действие нельзя отменить.",
      transferSuccess: "Статус главы успешно передан",
      cancel: "Отмена",
      removeMember: "Удалить участника",
      transferHeadStatus: "Передать статус главы",
      errorRemovingMember: "Не удалось удалить участника",
      errorTransferringHead: "Не удалось передать статус главы",
      errorOccurred: "Произошла ошибка при удалении участника",
      dateFormatLocale: "ru-RU",
      noImage: "Нет изображения",
      userImageAlt: "Изображение пользователя"
    }
  };
  const { fetcher: api } = getClient();
  const {
    actorType,
    actorId,
    name,
    isHead,
    createdAt,
    isCurrentUserHead,
    isCurrentUserAdmin,
    locale,
    image,
    toLocaleHref,
    getAppropriateLocalization
  } = $$props;
  const t = i18n[locale];
  const memberName = getAppropriateLocalization(name);
  const profileUrl = actorType === "user" ? `/users/${actorId}` : `/communes/${actorId}`;
  const formattedDate = createdAt.toLocaleDateString(t.dateFormatLocale, { year: "numeric", month: "long", day: "numeric" });
  const canRemove = (isCurrentUserHead || isCurrentUserAdmin) && !isHead;
  const canTransferHead = (isCurrentUserHead || isCurrentUserAdmin) && !isHead && actorType === "user";
  $$payload.out.push(`<div${attr_class(`card h-100 shadow-sm ${isHead ? "head-member" : ""}`, "svelte-8za3j")}>`);
  if (image) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="image-container svelte-8za3j"><img${attr("src", `/images/${image}`)}${attr("alt", `${t.userImageAlt}`)} class="svelte-8za3j"/></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div class="bg-light text-center d-flex align-items-center justify-content-center" style="height: 140px;"><span class="text-muted">${escape_html(t.noImage)}</span></div>`);
  }
  $$payload.out.push(`<!--]--> <a${attr("href", toLocaleHref(profileUrl))} class="text-decoration-none text-black"><div class="card-body d-flex flex-column"><h5 class="card-title fs-5 text-truncate">${escape_html(memberName)}</h5> <div class="mt-auto d-flex justify-content-between align-items-center"><small class="text-muted">${escape_html(t.joined)}
          ${escape_html(formattedDate)}</small></div> `);
  if (canTransferHead || canRemove) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="mt-2 d-flex flex-column gap-1">`);
    if (canTransferHead) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<button class="btn btn-outline-warning btn-sm">${escape_html(t.transferHead)}</button>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--> `);
    if (canRemove) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<button class="btn btn-outline-danger btn-sm">${escape_html(t.remove)}</button>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></a></div> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function Invite_user_modal($$payload, $$props) {
  push();
  const { fetcher: api } = getClient();
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]-->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
function Edit_commune_modal($$payload, $$props) {
  push();
  const i18n = {
    en: {
      editCommune: "Edit Commune",
      communeUpdatedSuccess: "Commune updated successfully!",
      name: "Name",
      enterCommuneName: "Enter commune name",
      description: "Description (optional)",
      enterCommuneDescription: "Enter commune description",
      cancel: "Cancel",
      save: "Save Changes",
      saving: "Saving...",
      provideName: "Please provide a name for the commune.",
      failedToUpdate: "Failed to update commune",
      unexpectedError: "An unexpected error occurred. Please try again."
    },
    ru: {
      editCommune: "Редактировать коммуну",
      communeUpdatedSuccess: "Коммуна успешно обновлена!",
      name: "Название",
      enterCommuneName: "Введите название коммуны",
      description: "Описание (опционально)",
      enterCommuneDescription: "Введите описание коммуны",
      cancel: "Отмена",
      save: "Сохранить изменения",
      saving: "Сохранение...",
      provideName: "Пожалуйста, укажите название коммуны.",
      failedToUpdate: "Не удалось обновить коммуну",
      unexpectedError: "Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова."
    }
  };
  const { fetcher: api } = getClient();
  const { show, onHide, locale, communeData, onCommuneUpdated } = $$props;
  const t = i18n[locale];
  let name = [];
  let description = [];
  let error = "";
  let isSubmitting = false;
  let submitSuccess = false;
  async function handleSubmit() {
    if (!name.some((item) => item.value.trim().length)) {
      error = t.provideName;
      return;
    }
    isSubmitting = true;
    error = "";
    try {
      await api.commune.patch({ id: communeData?.id, name, description });
      submitSuccess = true;
      onCommuneUpdated();
      setTimeout(
        () => {
          handleClose();
        },
        1500
      );
    } catch (err) {
      error = err instanceof Error ? err.message : t.unexpectedError;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  }
  function handleClose() {
    name = [];
    description = [];
    error = "";
    submitSuccess = false;
    onHide();
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Modal($$payload2, {
      show,
      title: t.editCommune,
      onClose: handleClose,
      onSubmit: handleSubmit,
      submitText: isSubmitting ? t.saving : t.save,
      cancelText: t.cancel,
      submitDisabled: !name.some((item) => item.value.trim().length) || isSubmitting,
      cancelDisabled: isSubmitting,
      isSubmitting,
      children: ($$payload3) => {
        if (submitSuccess) {
          $$payload3.out.push("<!--[-->");
          $$payload3.out.push(`<div class="alert alert-success mb-3">${escape_html(t.communeUpdatedSuccess)}</div>`);
        } else {
          $$payload3.out.push("<!--[!-->");
        }
        $$payload3.out.push(`<!--]--> `);
        if (error) {
          $$payload3.out.push("<!--[-->");
          $$payload3.out.push(`<div class="alert alert-danger mb-3">${escape_html(error)}</div>`);
        } else {
          $$payload3.out.push("<!--[!-->");
        }
        $$payload3.out.push(`<!--]--> <form>`);
        Localized_input($$payload3, {
          id: "communeName",
          label: t.name,
          placeholder: t.enterCommuneName,
          required: true,
          locale,
          get value() {
            return name;
          },
          set value($$value) {
            name = $$value;
            $$settled = false;
          }
        });
        $$payload3.out.push(`<!----> `);
        Localized_textarea($$payload3, {
          id: "communeDescription",
          label: t.description,
          placeholder: t.enterCommuneDescription,
          rows: 4,
          locale,
          get value() {
            return description;
          },
          set value($$value) {
            description = $$value;
            $$settled = false;
          }
        });
        $$payload3.out.push(`<!----></form>`);
      }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "— Commune" },
      loading: "Loading commune details...",
      communeNotFound: "Commune not found",
      communeDetails: "Commune Details",
      edit: "Edit",
      delete: "Delete Commune",
      members: "Members",
      member: "member",
      members_plural: "members",
      headMember: "Head Member",
      created: "Created",
      addMember: "Add Member",
      invite: "Invite User",
      manageInvitations: "Manage Invitations",
      manageJoinRequests: "Manage Join Requests",
      requestToJoin: "Request to Join",
      requestPending: "Join Request Pending",
      noMembers: "No members found",
      errorFetchingCommune: "Failed to fetch commune",
      errorFetchingMembers: "Failed to fetch members",
      errorOccurred: "An error occurred while fetching data",
      errorSendingJoinRequest: "Failed to send join request",
      joinRequestSent: "Join request sent successfully!",
      sendingJoinRequest: "Sending request...",
      interestedInJoining: "Interested in joining this commune?",
      joinRequestDescription: "Send a join request to the commune head for approval.",
      joinRequestPendingDescription: "Your join request is awaiting approval from the commune head.",
      confirmDelete: "Delete Commune",
      confirmDeleteMessage: "Are you sure you want to delete this commune?",
      deleteWarning: "This will permanently delete the commune and all its data, including members, invitations, and join requests. This action cannot be undone.",
      deleting: "Deleting...",
      communeDeleted: "Commune deleted successfully",
      errorDeletingCommune: "Failed to delete commune",
      cancel: "Cancel",
      dateFormatLocale: "en-US",
      noImages: "No images available",
      communeImageAlt: "Commune image",
      uploadImage: "Upload Image",
      deleteImage: "Delete Image",
      uploadImageTitle: "Upload Commune Image",
      upload: "Upload",
      uploading: "Uploading...",
      imageUploadedSuccess: "Image uploaded successfully!",
      errorUploadingImage: "Failed to upload image",
      pleaseSelectImage: "Please select an image to upload",
      invalidFileType: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: "File is too large. Maximum size is 5MB.",
      uploadImageMaxSize: "Upload an image (JPG, PNG, WebP), max 5MB.",
      confirmDeleteImage: "Are you sure you want to delete this image?",
      deleteImageTitle: "Delete Image",
      deleteImageButton: "Delete",
      deletingImage: "Deleting...",
      imageDeletedSuccess: "Image deleted successfully!",
      errorDeletingImage: "Failed to delete image"
    },
    ru: {
      _page: { title: "— Коммуна" },
      loading: "Загрузка данных коммуны...",
      communeNotFound: "Коммуна не найдена",
      communeDetails: "Информация о коммуне",
      edit: "Редактировать",
      delete: "Удалить коммуну",
      members: "Участники",
      member: "участник",
      members_plural: "участников",
      headMember: "Глава",
      created: "Создана",
      addMember: "Добавить участника",
      invite: "Пригласить пользователя",
      manageInvitations: "Управление приглашениями",
      manageJoinRequests: "Управление заявками",
      requestToJoin: "Подать заявку",
      requestPending: "Заявка на рассмотрении",
      noMembers: "Участники не найдены",
      errorFetchingCommune: "Не удалось загрузить коммуну",
      errorFetchingMembers: "Не удалось загрузить участников",
      errorOccurred: "Произошла ошибка при загрузке данных",
      errorSendingJoinRequest: "Не удалось отправить заявку",
      joinRequestSent: "Заявка отправлена успешно!",
      sendingJoinRequest: "Отправляем заявку...",
      interestedInJoining: "Хотите присоединиться к этой коммуне?",
      joinRequestDescription: "Отправьте заявку главе коммуны для одобрения.",
      joinRequestPendingDescription: "Ваша заявка ожидает одобрения главы коммуны.",
      confirmDelete: "Удалить коммуну",
      confirmDeleteMessage: "Вы уверены, что хотите удалить эту коммуну?",
      deleteWarning: "Это навсегда удалит коммуну и все её данные, включая участников, приглашения и заявки. Это действие нельзя отменить.",
      deleting: "Удаление...",
      communeDeleted: "Коммуна успешно удалена",
      errorDeletingCommune: "Не удалось удалить коммуну",
      cancel: "Отмена",
      dateFormatLocale: "ru-RU",
      noImages: "Нет доступных изображений",
      communeImageAlt: "Изображение коммуны",
      uploadImage: "Загрузить изображение",
      deleteImage: "Удалить изображение",
      uploadImageTitle: "Загрузить изображение коммуны",
      upload: "Загрузить",
      uploading: "Загрузка...",
      imageUploadedSuccess: "Изображение загружено успешно!",
      errorUploadingImage: "Не удалось загрузить изображение",
      pleaseSelectImage: "Пожалуйста, выберите изображение для загрузки",
      invalidFileType: "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: "Файл слишком большой. Максимальный размер - 5MB.",
      uploadImageMaxSize: "Загрузите изображение (JPG, PNG, WebP), максимальный размер - 5MB.",
      confirmDeleteImage: "Вы уверены, что хотите удалить это изображение?",
      deleteImageTitle: "Удалить изображение",
      deleteImageButton: "Удалить",
      deletingImage: "Удаление...",
      imageDeletedSuccess: "Изображение удалено успешно!",
      errorDeletingImage: "Не удалось удалить изображение"
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const { locale, toLocaleHref, getAppropriateLocalization } = data;
  const communeId = page.params.id;
  const t = i18n[locale];
  const user = data.user;
  let commune = data.commune;
  let members = data.members;
  let showEditModal = false;
  let isSendingJoinRequest = false;
  const userPermissions = data.userPermissions;
  function refresh() {
    window.location.reload();
  }
  function handleEditModalClose() {
    showEditModal = false;
    refresh();
  }
  const communeName = getAppropriateLocalization(commune.name);
  const communeDescription = getAppropriateLocalization(commune.description);
  const isCurrentUserHead = user ? commune.headMember.actorType === "user" && commune.headMember.actorId === user.id : false;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(communeName)} ${escape_html(t._page.title)}</title>`;
  });
  $$payload.out.push(`<div class="container py-4">`);
  {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div class="row"><div class="col-lg-8"><div class="commune-image-container svelte-1qaz19a">`);
    if (commune.image) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<img${attr("src", `/images/${commune.image}`)}${attr("alt", `${t.communeImageAlt}`)} class="commune-image svelte-1qaz19a"/>`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<div class="commune-image-placeholder svelte-1qaz19a"><span class="text-muted">${escape_html(t.noImages)}</span></div>`);
    }
    $$payload.out.push(`<!--]--></div> `);
    if (isCurrentUserHead || userPermissions.isAdmin) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="mt-3 d-flex gap-2"><button class="btn btn-outline-primary btn-sm"><i class="bi bi-upload me-1"></i> ${escape_html(t.uploadImage)}</button> `);
      if (commune.image) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<button class="btn btn-outline-danger btn-sm"><i class="bi bi-trash me-1"></i> ${escape_html(t.deleteImage)}</button>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--> <div class="mb-4"><div class="d-flex justify-content-between align-items-center mb-3"><h2 class="mb-0">${escape_html(communeName)}</h2></div> <p class="lead text-muted">${escape_html(communeDescription || "")}</p></div></div> <div class="col-lg-4"><div class="card shadow-sm mb-4"><div class="card-body"><div class="d-flex justify-content-between align-items-center mb-2"><h5 class="card-title mb-0">${escape_html(t.communeDetails)}</h5> `);
    if (isCurrentUserHead || userPermissions.isAdmin) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="d-flex gap-1"><button class="btn btn-outline-primary btn-sm">${escape_html(t.edit)}</button> <button class="btn btn-outline-danger btn-sm">${escape_html(t.delete)}</button></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> <hr/> <div class="d-flex justify-content-between mb-2"><span>${escape_html(t.members)}:</span> <span class="badge bg-primary">${escape_html(commune.memberCount)}</span></div> <div class="d-flex justify-content-between mb-2"><span>${escape_html(t.headMember)}:</span> <span class="text-muted">${escape_html(getAppropriateLocalization(commune.headMember.name))}</span></div> <div class="d-flex justify-content-between"><span>${escape_html(t.created)}:</span> <span class="text-muted">${escape_html(formatDatetime(new Date(commune.createdAt), locale))}</span></div></div></div></div></div> `);
    if (userPermissions.canRequestJoin) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="alert alert-info d-flex justify-content-between align-items-center mt-4"><div><strong>${escape_html(t.interestedInJoining)}</strong> <p class="mb-0 small text-muted">${escape_html(t.joinRequestDescription)}</p></div> <button class="btn btn-success"${attr("disabled", isSendingJoinRequest, true)}>`);
      {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`${escape_html(t.requestToJoin)}`);
      }
      $$payload.out.push(`<!--]--></button></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
      if (userPermissions.hasPendingJoinRequest) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="alert alert-warning mt-4"><strong>${escape_html(t.requestPending)}</strong> <p class="mb-0 small">${escape_html(t.joinRequestPendingDescription)}</p></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]-->`);
    }
    $$payload.out.push(`<!--]--> <div class="d-flex justify-content-between align-items-center mt-5 mb-4"><h3 class="mb-0">${escape_html(t.members)} (${escape_html(members.length)})</h3> `);
    if (userPermissions.canInvite) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="d-flex gap-2 flex-wrap"><a${attr("href", toLocaleHref(`/communes/${communeId}/invitations`))} class="btn btn-outline-info">${escape_html(t.manageInvitations)}</a> <a${attr("href", toLocaleHref(`/communes/${communeId}/join-requests`))} class="btn btn-outline-warning">${escape_html(t.manageJoinRequests)}</a> <button class="btn btn-outline-primary">${escape_html(t.invite)}</button></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> `);
    if (members.length === 0) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="alert alert-info">${escape_html(t.noMembers)}</div>`);
    } else {
      $$payload.out.push("<!--[!-->");
      const each_array = ensure_array_like(members);
      $$payload.out.push(`<div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4"><!--[-->`);
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let member = each_array[$$index];
        $$payload.out.push(`<div class="col">`);
        Member_card($$payload, {
          id: member.id,
          actorType: member.actorType,
          actorId: member.actorId,
          name: member.name ?? [],
          isHead: member.actorType === commune.headMember.actorType && member.actorId === commune.headMember.actorId,
          createdAt: member.createdAt,
          isCurrentUserHead,
          isCurrentUserAdmin: userPermissions.isAdmin,
          locale,
          image: member.image,
          toLocaleHref,
          getAppropriateLocalization
        });
        $$payload.out.push(`<!----></div>`);
      }
      $$payload.out.push(`<!--]--></div>`);
    }
    $$payload.out.push(`<!--]--> `);
    Edit_commune_modal($$payload, {
      show: showEditModal,
      onHide: handleEditModalClose,
      communeData: commune,
      locale,
      onCommuneUpdated: refresh
    });
    $$payload.out.push(`<!----> `);
    Invite_user_modal($$payload);
    $$payload.out.push(`<!---->`);
  }
  $$payload.out.push(`<!--]--></div> `);
  if (isCurrentUserHead || userPermissions.isAdmin) {
    $$payload.out.push("<!--[-->");
    {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]-->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if ((isCurrentUserHead || userPermissions.isAdmin) && commune.image) {
    $$payload.out.push("<!--[-->");
    {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]-->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-Ca1oPZ3L.js.map
