{"version": 3, "file": "_page.svelte-Ca1oPZ3L.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/communes/_id_/_page.svelte.js"], "sourcesContent": ["import { G as attr_class, y as attr, z as escape_html, w as pop, u as push, O as copy_payload, P as assign_payload, x as head, K as ensure_array_like } from \"../../../../../../chunks/index.js\";\nimport { p as page } from \"../../../../../../chunks/index3.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../../../chunks/exports.js\";\nimport \"../../../../../../chunks/state.svelte.js\";\nimport { g as getClient } from \"../../../../../../chunks/acrpc.js\";\nimport \"../../../../../../chunks/current-user.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { a as formatDatetime } from \"../../../../../../chunks/format-date.js\";\nimport \"clsx\";\n/* empty css                                                                              */\nimport { M as Modal } from \"../../../../../../chunks/modal.js\";\nimport { L as Localized_input } from \"../../../../../../chunks/localized-input.js\";\nimport { L as Localized_textarea } from \"../../../../../../chunks/localized-textarea.js\";\nfunction Member_card($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      head: \"Head\",\n      joined: \"Joined\",\n      remove: \"Remove\",\n      removing: \"Removing...\",\n      transferHead: \"Make Head\",\n      transferring: \"Transferring...\",\n      confirmRemoval: \"Confirm Removal\",\n      confirmRemoveMessage: \"Are you sure you want to remove\",\n      fromCommune: \"from this commune?\",\n      cannotUndo: \"This action cannot be undone.\",\n      confirmTransfer: \"Transfer Head Status\",\n      confirmTransferMessage: \"Are you sure you want to transfer head status to\",\n      transferWarning: \"This will make them the new head of the commune and remove your head privileges. This action cannot be undone.\",\n      transferSuccess: \"Head status transferred successfully\",\n      cancel: \"Cancel\",\n      removeMember: \"Remove Member\",\n      transferHeadStatus: \"Transfer Head Status\",\n      errorRemovingMember: \"Failed to remove member\",\n      errorTransferringHead: \"Failed to transfer head status\",\n      errorOccurred: \"An error occurred while removing member\",\n      dateFormatLocale: \"en-US\",\n      noImage: \"No image\",\n      userImageAlt: \"User image\"\n    },\n    ru: {\n      head: \"Глава\",\n      joined: \"Присоединился\",\n      remove: \"Удалить\",\n      removing: \"Удаление...\",\n      transferHead: \"Сделать главой\",\n      transferring: \"Передача...\",\n      confirmRemoval: \"Подтвердите удаление\",\n      confirmRemoveMessage: \"Вы уверены, что хотите удалить\",\n      fromCommune: \"из этой коммуны?\",\n      cannotUndo: \"Это действие нельзя отменить.\",\n      confirmTransfer: \"Передача статуса главы\",\n      confirmTransferMessage: \"Вы уверены, что хотите передать статус главы пользователю\",\n      transferWarning: \"Это сделает их новым главой коммуны и лишит вас привилегий главы. Это действие нельзя отменить.\",\n      transferSuccess: \"Статус главы успешно передан\",\n      cancel: \"Отмена\",\n      removeMember: \"Удалить участника\",\n      transferHeadStatus: \"Передать статус главы\",\n      errorRemovingMember: \"Не удалось удалить участника\",\n      errorTransferringHead: \"Не удалось передать статус главы\",\n      errorOccurred: \"Произошла ошибка при удалении участника\",\n      dateFormatLocale: \"ru-RU\",\n      noImage: \"Нет изображения\",\n      userImageAlt: \"Изображение пользователя\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const {\n    actorType,\n    actorId,\n    name,\n    isHead,\n    createdAt,\n    isCurrentUserHead,\n    isCurrentUserAdmin,\n    locale,\n    image,\n    toLocaleHref,\n    getAppropriateLocalization\n  } = $$props;\n  const t = i18n[locale];\n  const memberName = getAppropriateLocalization(name);\n  const profileUrl = actorType === \"user\" ? `/users/${actorId}` : `/communes/${actorId}`;\n  const formattedDate = createdAt.toLocaleDateString(t.dateFormatLocale, { year: \"numeric\", month: \"long\", day: \"numeric\" });\n  const canRemove = (isCurrentUserHead || isCurrentUserAdmin) && !isHead;\n  const canTransferHead = (isCurrentUserHead || isCurrentUserAdmin) && !isHead && actorType === \"user\";\n  $$payload.out.push(`<div${attr_class(`card h-100 shadow-sm ${isHead ? \"head-member\" : \"\"}`, \"svelte-8za3j\")}>`);\n  if (image) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"image-container svelte-8za3j\"><img${attr(\"src\", `/images/${image}`)}${attr(\"alt\", `${t.userImageAlt}`)} class=\"svelte-8za3j\"/></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    $$payload.out.push(`<div class=\"bg-light text-center d-flex align-items-center justify-content-center\" style=\"height: 140px;\"><span class=\"text-muted\">${escape_html(t.noImage)}</span></div>`);\n  }\n  $$payload.out.push(`<!--]--> <a${attr(\"href\", toLocaleHref(profileUrl))} class=\"text-decoration-none text-black\"><div class=\"card-body d-flex flex-column\"><h5 class=\"card-title fs-5 text-truncate\">${escape_html(memberName)}</h5> <div class=\"mt-auto d-flex justify-content-between align-items-center\"><small class=\"text-muted\">${escape_html(t.joined)}\n          ${escape_html(formattedDate)}</small></div> `);\n  if (canTransferHead || canRemove) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"mt-2 d-flex flex-column gap-1\">`);\n    if (canTransferHead) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<button class=\"btn btn-outline-warning btn-sm\">${escape_html(t.transferHead)}</button>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--> `);\n    if (canRemove) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<button class=\"btn btn-outline-danger btn-sm\">${escape_html(t.remove)}</button>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div></a></div> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]-->`);\n  pop();\n}\nfunction Invite_user_modal($$payload, $$props) {\n  push();\n  const { fetcher: api } = getClient();\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]-->`);\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nfunction Edit_commune_modal($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      editCommune: \"Edit Commune\",\n      communeUpdatedSuccess: \"Commune updated successfully!\",\n      name: \"Name\",\n      enterCommuneName: \"Enter commune name\",\n      description: \"Description (optional)\",\n      enterCommuneDescription: \"Enter commune description\",\n      cancel: \"Cancel\",\n      save: \"Save Changes\",\n      saving: \"Saving...\",\n      provideName: \"Please provide a name for the commune.\",\n      failedToUpdate: \"Failed to update commune\",\n      unexpectedError: \"An unexpected error occurred. Please try again.\"\n    },\n    ru: {\n      editCommune: \"Редактировать коммуну\",\n      communeUpdatedSuccess: \"Коммуна успешно обновлена!\",\n      name: \"Название\",\n      enterCommuneName: \"Введите название коммуны\",\n      description: \"Описание (опционально)\",\n      enterCommuneDescription: \"Введите описание коммуны\",\n      cancel: \"Отмена\",\n      save: \"Сохранить изменения\",\n      saving: \"Сохранение...\",\n      provideName: \"Пожалуйста, укажите название коммуны.\",\n      failedToUpdate: \"Не удалось обновить коммуну\",\n      unexpectedError: \"Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова.\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { show, onHide, locale, communeData, onCommuneUpdated } = $$props;\n  const t = i18n[locale];\n  let name = [];\n  let description = [];\n  let error = \"\";\n  let isSubmitting = false;\n  let submitSuccess = false;\n  async function handleSubmit() {\n    if (!name.some((item) => item.value.trim().length)) {\n      error = t.provideName;\n      return;\n    }\n    isSubmitting = true;\n    error = \"\";\n    try {\n      await api.commune.patch({ id: communeData?.id, name, description });\n      submitSuccess = true;\n      onCommuneUpdated();\n      setTimeout(\n        () => {\n          handleClose();\n        },\n        1500\n      );\n    } catch (err) {\n      error = err instanceof Error ? err.message : t.unexpectedError;\n      console.error(err);\n    } finally {\n      isSubmitting = false;\n    }\n  }\n  function handleClose() {\n    name = [];\n    description = [];\n    error = \"\";\n    submitSuccess = false;\n    onHide();\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    Modal($$payload2, {\n      show,\n      title: t.editCommune,\n      onClose: handleClose,\n      onSubmit: handleSubmit,\n      submitText: isSubmitting ? t.saving : t.save,\n      cancelText: t.cancel,\n      submitDisabled: !name.some((item) => item.value.trim().length) || isSubmitting,\n      cancelDisabled: isSubmitting,\n      isSubmitting,\n      children: ($$payload3) => {\n        if (submitSuccess) {\n          $$payload3.out.push(\"<!--[-->\");\n          $$payload3.out.push(`<div class=\"alert alert-success mb-3\">${escape_html(t.communeUpdatedSuccess)}</div>`);\n        } else {\n          $$payload3.out.push(\"<!--[!-->\");\n        }\n        $$payload3.out.push(`<!--]--> `);\n        if (error) {\n          $$payload3.out.push(\"<!--[-->\");\n          $$payload3.out.push(`<div class=\"alert alert-danger mb-3\">${escape_html(error)}</div>`);\n        } else {\n          $$payload3.out.push(\"<!--[!-->\");\n        }\n        $$payload3.out.push(`<!--]--> <form>`);\n        Localized_input($$payload3, {\n          id: \"communeName\",\n          label: t.name,\n          placeholder: t.enterCommuneName,\n          required: true,\n          locale,\n          get value() {\n            return name;\n          },\n          set value($$value) {\n            name = $$value;\n            $$settled = false;\n          }\n        });\n        $$payload3.out.push(`<!----> `);\n        Localized_textarea($$payload3, {\n          id: \"communeDescription\",\n          label: t.description,\n          placeholder: t.enterCommuneDescription,\n          rows: 4,\n          locale,\n          get value() {\n            return description;\n          },\n          set value($$value) {\n            description = $$value;\n            $$settled = false;\n          }\n        });\n        $$payload3.out.push(`<!----></form>`);\n      }\n    });\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"— Commune\" },\n      loading: \"Loading commune details...\",\n      communeNotFound: \"Commune not found\",\n      communeDetails: \"Commune Details\",\n      edit: \"Edit\",\n      delete: \"Delete Commune\",\n      members: \"Members\",\n      member: \"member\",\n      members_plural: \"members\",\n      headMember: \"Head Member\",\n      created: \"Created\",\n      addMember: \"Add Member\",\n      invite: \"Invite User\",\n      manageInvitations: \"Manage Invitations\",\n      manageJoinRequests: \"Manage Join Requests\",\n      requestToJoin: \"Request to Join\",\n      requestPending: \"Join Request Pending\",\n      noMembers: \"No members found\",\n      errorFetchingCommune: \"Failed to fetch commune\",\n      errorFetchingMembers: \"Failed to fetch members\",\n      errorOccurred: \"An error occurred while fetching data\",\n      errorSendingJoinRequest: \"Failed to send join request\",\n      joinRequestSent: \"Join request sent successfully!\",\n      sendingJoinRequest: \"Sending request...\",\n      interestedInJoining: \"Interested in joining this commune?\",\n      joinRequestDescription: \"Send a join request to the commune head for approval.\",\n      joinRequestPendingDescription: \"Your join request is awaiting approval from the commune head.\",\n      confirmDelete: \"Delete Commune\",\n      confirmDeleteMessage: \"Are you sure you want to delete this commune?\",\n      deleteWarning: \"This will permanently delete the commune and all its data, including members, invitations, and join requests. This action cannot be undone.\",\n      deleting: \"Deleting...\",\n      communeDeleted: \"Commune deleted successfully\",\n      errorDeletingCommune: \"Failed to delete commune\",\n      cancel: \"Cancel\",\n      dateFormatLocale: \"en-US\",\n      noImages: \"No images available\",\n      communeImageAlt: \"Commune image\",\n      uploadImage: \"Upload Image\",\n      deleteImage: \"Delete Image\",\n      uploadImageTitle: \"Upload Commune Image\",\n      upload: \"Upload\",\n      uploading: \"Uploading...\",\n      imageUploadedSuccess: \"Image uploaded successfully!\",\n      errorUploadingImage: \"Failed to upload image\",\n      pleaseSelectImage: \"Please select an image to upload\",\n      invalidFileType: \"Invalid file type. Please upload a JPG, PNG, or WebP image.\",\n      fileTooLarge: \"File is too large. Maximum size is 5MB.\",\n      uploadImageMaxSize: \"Upload an image (JPG, PNG, WebP), max 5MB.\",\n      confirmDeleteImage: \"Are you sure you want to delete this image?\",\n      deleteImageTitle: \"Delete Image\",\n      deleteImageButton: \"Delete\",\n      deletingImage: \"Deleting...\",\n      imageDeletedSuccess: \"Image deleted successfully!\",\n      errorDeletingImage: \"Failed to delete image\"\n    },\n    ru: {\n      _page: { title: \"— Коммуна\" },\n      loading: \"Загрузка данных коммуны...\",\n      communeNotFound: \"Коммуна не найдена\",\n      communeDetails: \"Информация о коммуне\",\n      edit: \"Редактировать\",\n      delete: \"Удалить коммуну\",\n      members: \"Участники\",\n      member: \"участник\",\n      members_plural: \"участников\",\n      headMember: \"Глава\",\n      created: \"Создана\",\n      addMember: \"Добавить участника\",\n      invite: \"Пригласить пользователя\",\n      manageInvitations: \"Управление приглашениями\",\n      manageJoinRequests: \"Управление заявками\",\n      requestToJoin: \"Подать заявку\",\n      requestPending: \"Заявка на рассмотрении\",\n      noMembers: \"Участники не найдены\",\n      errorFetchingCommune: \"Не удалось загрузить коммуну\",\n      errorFetchingMembers: \"Не удалось загрузить участников\",\n      errorOccurred: \"Произошла ошибка при загрузке данных\",\n      errorSendingJoinRequest: \"Не удалось отправить заявку\",\n      joinRequestSent: \"Заявка отправлена успешно!\",\n      sendingJoinRequest: \"Отправляем заявку...\",\n      interestedInJoining: \"Хотите присоединиться к этой коммуне?\",\n      joinRequestDescription: \"Отправьте заявку главе коммуны для одобрения.\",\n      joinRequestPendingDescription: \"Ваша заявка ожидает одобрения главы коммуны.\",\n      confirmDelete: \"Удалить коммуну\",\n      confirmDeleteMessage: \"Вы уверены, что хотите удалить эту коммуну?\",\n      deleteWarning: \"Это навсегда удалит коммуну и все её данные, включая участников, приглашения и заявки. Это действие нельзя отменить.\",\n      deleting: \"Удаление...\",\n      communeDeleted: \"Коммуна успешно удалена\",\n      errorDeletingCommune: \"Не удалось удалить коммуну\",\n      cancel: \"Отмена\",\n      dateFormatLocale: \"ru-RU\",\n      noImages: \"Нет доступных изображений\",\n      communeImageAlt: \"Изображение коммуны\",\n      uploadImage: \"Загрузить изображение\",\n      deleteImage: \"Удалить изображение\",\n      uploadImageTitle: \"Загрузить изображение коммуны\",\n      upload: \"Загрузить\",\n      uploading: \"Загрузка...\",\n      imageUploadedSuccess: \"Изображение загружено успешно!\",\n      errorUploadingImage: \"Не удалось загрузить изображение\",\n      pleaseSelectImage: \"Пожалуйста, выберите изображение для загрузки\",\n      invalidFileType: \"Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.\",\n      fileTooLarge: \"Файл слишком большой. Максимальный размер - 5MB.\",\n      uploadImageMaxSize: \"Загрузите изображение (JPG, PNG, WebP), максимальный размер - 5MB.\",\n      confirmDeleteImage: \"Вы уверены, что хотите удалить это изображение?\",\n      deleteImageTitle: \"Удалить изображение\",\n      deleteImageButton: \"Удалить\",\n      deletingImage: \"Удаление...\",\n      imageDeletedSuccess: \"Изображение удалено успешно!\",\n      errorDeletingImage: \"Не удалось удалить изображение\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const { locale, toLocaleHref, getAppropriateLocalization } = data;\n  const communeId = page.params.id;\n  const t = i18n[locale];\n  const user = data.user;\n  let commune = data.commune;\n  let members = data.members;\n  let showEditModal = false;\n  let isSendingJoinRequest = false;\n  const userPermissions = data.userPermissions;\n  function refresh() {\n    window.location.reload();\n  }\n  function handleEditModalClose() {\n    showEditModal = false;\n    refresh();\n  }\n  const communeName = getAppropriateLocalization(commune.name);\n  const communeDescription = getAppropriateLocalization(commune.description);\n  const isCurrentUserHead = user ? commune.headMember.actorType === \"user\" && commune.headMember.actorId === user.id : false;\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(communeName)} ${escape_html(t._page.title)}</title>`;\n  });\n  $$payload.out.push(`<div class=\"container py-4\">`);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n    $$payload.out.push(`<div class=\"row\"><div class=\"col-lg-8\"><div class=\"commune-image-container svelte-1qaz19a\">`);\n    if (commune.image) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<img${attr(\"src\", `/images/${commune.image}`)}${attr(\"alt\", `${t.communeImageAlt}`)} class=\"commune-image svelte-1qaz19a\"/>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      $$payload.out.push(`<div class=\"commune-image-placeholder svelte-1qaz19a\"><span class=\"text-muted\">${escape_html(t.noImages)}</span></div>`);\n    }\n    $$payload.out.push(`<!--]--></div> `);\n    if (isCurrentUserHead || userPermissions.isAdmin) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"mt-3 d-flex gap-2\"><button class=\"btn btn-outline-primary btn-sm\"><i class=\"bi bi-upload me-1\"></i> ${escape_html(t.uploadImage)}</button> `);\n      if (commune.image) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<button class=\"btn btn-outline-danger btn-sm\"><i class=\"bi bi-trash me-1\"></i> ${escape_html(t.deleteImage)}</button>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n      }\n      $$payload.out.push(`<!--]--></div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--> <div class=\"mb-4\"><div class=\"d-flex justify-content-between align-items-center mb-3\"><h2 class=\"mb-0\">${escape_html(communeName)}</h2></div> <p class=\"lead text-muted\">${escape_html(communeDescription || \"\")}</p></div></div> <div class=\"col-lg-4\"><div class=\"card shadow-sm mb-4\"><div class=\"card-body\"><div class=\"d-flex justify-content-between align-items-center mb-2\"><h5 class=\"card-title mb-0\">${escape_html(t.communeDetails)}</h5> `);\n    if (isCurrentUserHead || userPermissions.isAdmin) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"d-flex gap-1\"><button class=\"btn btn-outline-primary btn-sm\">${escape_html(t.edit)}</button> <button class=\"btn btn-outline-danger btn-sm\">${escape_html(t.delete)}</button></div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div> <hr/> <div class=\"d-flex justify-content-between mb-2\"><span>${escape_html(t.members)}:</span> <span class=\"badge bg-primary\">${escape_html(commune.memberCount)}</span></div> <div class=\"d-flex justify-content-between mb-2\"><span>${escape_html(t.headMember)}:</span> <span class=\"text-muted\">${escape_html(getAppropriateLocalization(commune.headMember.name))}</span></div> <div class=\"d-flex justify-content-between\"><span>${escape_html(t.created)}:</span> <span class=\"text-muted\">${escape_html(formatDatetime(new Date(commune.createdAt), locale))}</span></div></div></div></div></div> `);\n    if (userPermissions.canRequestJoin) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"alert alert-info d-flex justify-content-between align-items-center mt-4\"><div><strong>${escape_html(t.interestedInJoining)}</strong> <p class=\"mb-0 small text-muted\">${escape_html(t.joinRequestDescription)}</p></div> <button class=\"btn btn-success\"${attr(\"disabled\", isSendingJoinRequest, true)}>`);\n      {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`${escape_html(t.requestToJoin)}`);\n      }\n      $$payload.out.push(`<!--]--></button></div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      if (userPermissions.hasPendingJoinRequest) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<div class=\"alert alert-warning mt-4\"><strong>${escape_html(t.requestPending)}</strong> <p class=\"mb-0 small\">${escape_html(t.joinRequestPendingDescription)}</p></div>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n      }\n      $$payload.out.push(`<!--]-->`);\n    }\n    $$payload.out.push(`<!--]--> <div class=\"d-flex justify-content-between align-items-center mt-5 mb-4\"><h3 class=\"mb-0\">${escape_html(t.members)} (${escape_html(members.length)})</h3> `);\n    if (userPermissions.canInvite) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"d-flex gap-2 flex-wrap\"><a${attr(\"href\", toLocaleHref(`/communes/${communeId}/invitations`))} class=\"btn btn-outline-info\">${escape_html(t.manageInvitations)}</a> <a${attr(\"href\", toLocaleHref(`/communes/${communeId}/join-requests`))} class=\"btn btn-outline-warning\">${escape_html(t.manageJoinRequests)}</a> <button class=\"btn btn-outline-primary\">${escape_html(t.invite)}</button></div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div> `);\n    if (members.length === 0) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"alert alert-info\">${escape_html(t.noMembers)}</div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      const each_array = ensure_array_like(members);\n      $$payload.out.push(`<div class=\"row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4\"><!--[-->`);\n      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n        let member = each_array[$$index];\n        $$payload.out.push(`<div class=\"col\">`);\n        Member_card($$payload, {\n          id: member.id,\n          actorType: member.actorType,\n          actorId: member.actorId,\n          name: member.name ?? [],\n          isHead: member.actorType === commune.headMember.actorType && member.actorId === commune.headMember.actorId,\n          createdAt: member.createdAt,\n          isCurrentUserHead,\n          isCurrentUserAdmin: userPermissions.isAdmin,\n          locale,\n          image: member.image,\n          toLocaleHref,\n          getAppropriateLocalization\n        });\n        $$payload.out.push(`<!----></div>`);\n      }\n      $$payload.out.push(`<!--]--></div>`);\n    }\n    $$payload.out.push(`<!--]--> `);\n    Edit_commune_modal($$payload, {\n      show: showEditModal,\n      onHide: handleEditModalClose,\n      communeData: commune,\n      locale,\n      onCommuneUpdated: refresh\n    });\n    $$payload.out.push(`<!----> `);\n    Invite_user_modal($$payload);\n    $$payload.out.push(`<!---->`);\n  }\n  $$payload.out.push(`<!--]--></div> `);\n  if (isCurrentUserHead || userPermissions.isAdmin) {\n    $$payload.out.push(\"<!--[-->\");\n    {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]-->`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  if ((isCurrentUserHead || userPermissions.isAdmin) && commune.image) {\n    $$payload.out.push(\"<!--[-->\");\n    {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]-->`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]-->`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAeA,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;AACzC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,YAAY,EAAE,WAAW;AAC/B,MAAM,YAAY,EAAE,iBAAiB;AACrC,MAAM,cAAc,EAAE,iBAAiB;AACvC,MAAM,oBAAoB,EAAE,iCAAiC;AAC7D,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,UAAU,EAAE,+BAA+B;AACjD,MAAM,eAAe,EAAE,sBAAsB;AAC7C,MAAM,sBAAsB,EAAE,kDAAkD;AAChF,MAAM,eAAe,EAAE,gHAAgH;AACvI,MAAM,eAAe,EAAE,sCAAsC;AAC7D,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,YAAY,EAAE,eAAe;AACnC,MAAM,kBAAkB,EAAE,sBAAsB;AAChD,MAAM,mBAAmB,EAAE,yBAAyB;AACpD,MAAM,qBAAqB,EAAE,gCAAgC;AAC7D,MAAM,aAAa,EAAE,yCAAyC;AAC9D,MAAM,gBAAgB,EAAE,OAAO;AAC/B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,YAAY,EAAE;AACpB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM,YAAY,EAAE,aAAa;AACjC,MAAM,cAAc,EAAE,sBAAsB;AAC5C,MAAM,oBAAoB,EAAE,gCAAgC;AAC5D,MAAM,WAAW,EAAE,kBAAkB;AACrC,MAAM,UAAU,EAAE,+BAA+B;AACjD,MAAM,eAAe,EAAE,wBAAwB;AAC/C,MAAM,sBAAsB,EAAE,2DAA2D;AACzF,MAAM,eAAe,EAAE,iGAAiG;AACxH,MAAM,eAAe,EAAE,8BAA8B;AACrD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,YAAY,EAAE,mBAAmB;AACvC,MAAM,kBAAkB,EAAE,uBAAuB;AACjD,MAAM,mBAAmB,EAAE,8BAA8B;AACzD,MAAM,qBAAqB,EAAE,kCAAkC;AAC/D,MAAM,aAAa,EAAE,yCAAyC;AAC9D,MAAM,gBAAgB,EAAE,OAAO;AAC/B,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,YAAY,EAAE;AACpB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,MAAM;AACV,IAAI,KAAK;AACT,IAAI,YAAY;AAChB,IAAI;AACJ,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,MAAM,UAAU,GAAG,0BAA0B,CAAC,IAAI,CAAC;AACrD,EAAE,MAAM,UAAU,GAAG,SAAS,KAAK,MAAM,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACxF,EAAE,MAAM,aAAa,GAAG,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC;AAC5H,EAAE,MAAM,SAAS,GAAG,CAAC,iBAAiB,IAAI,kBAAkB,KAAK,CAAC,MAAM;AACxE,EAAE,MAAM,eAAe,GAAG,CAAC,iBAAiB,IAAI,kBAAkB,KAAK,CAAC,MAAM,IAAI,SAAS,KAAK,MAAM;AACtG,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,qBAAqB,EAAE,MAAM,GAAG,aAAa,GAAG,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;AACjH,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8CAA8C,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC;AAC1K,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mIAAmI,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AACnM,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,6HAA6H,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,uGAAuG,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;AAC/V,UAAU,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;AACxD,EAAE,IAAI,eAAe,IAAI,SAAS,EAAE;AACpC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2CAA2C,CAAC,CAAC;AACrE,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+CAA+C,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC;AAClH,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACnC,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8CAA8C,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC;AAC3G,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,CAAC;AACjD,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI;AACJ,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACnC,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;AACA,SAAS,kBAAkB,CAAC,SAAS,EAAE,OAAO,EAAE;AAChD,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,qBAAqB,EAAE,+BAA+B;AAC5D,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,gBAAgB,EAAE,oBAAoB;AAC5C,MAAM,WAAW,EAAE,wBAAwB;AAC3C,MAAM,uBAAuB,EAAE,2BAA2B;AAC1D,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,WAAW,EAAE,wCAAwC;AAC3D,MAAM,cAAc,EAAE,0BAA0B;AAChD,MAAM,eAAe,EAAE;AACvB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,WAAW,EAAE,uBAAuB;AAC1C,MAAM,qBAAqB,EAAE,4BAA4B;AACzD,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,gBAAgB,EAAE,0BAA0B;AAClD,MAAM,WAAW,EAAE,wBAAwB;AAC3C,MAAM,uBAAuB,EAAE,0BAA0B;AACzD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,WAAW,EAAE,uCAAuC;AAC1D,MAAM,cAAc,EAAE,6BAA6B;AACnD,MAAM,eAAe,EAAE;AACvB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG,OAAO;AACzE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,IAAI,GAAG,EAAE;AACf,EAAE,IAAI,WAAW,GAAG,EAAE;AACtB,EAAE,IAAI,KAAK,GAAG,EAAE;AAChB,EAAE,IAAI,YAAY,GAAG,KAAK;AAC1B,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,eAAe,YAAY,GAAG;AAChC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE;AACxD,MAAM,KAAK,GAAG,CAAC,CAAC,WAAW;AAC3B,MAAM;AACN,IAAI;AACJ,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,KAAK,GAAG,EAAE;AACd,IAAI,IAAI;AACR,MAAM,MAAM,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;AACzE,MAAM,aAAa,GAAG,IAAI;AAC1B,MAAM,gBAAgB,EAAE;AACxB,MAAM,UAAU;AAChB,QAAQ,MAAM;AACd,UAAU,WAAW,EAAE;AACvB,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO;AACP,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,KAAK,GAAG,GAAG,YAAY,KAAK,GAAG,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,eAAe;AACpE,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AACxB,IAAI,CAAC,SAAS;AACd,MAAM,YAAY,GAAG,KAAK;AAC1B,IAAI;AACJ,EAAE;AACF,EAAE,SAAS,WAAW,GAAG;AACzB,IAAI,IAAI,GAAG,EAAE;AACb,IAAI,WAAW,GAAG,EAAE;AACpB,IAAI,KAAK,GAAG,EAAE;AACd,IAAI,aAAa,GAAG,KAAK;AACzB,IAAI,MAAM,EAAE;AACZ,EAAE;AACF,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,IAAI;AACV,MAAM,KAAK,EAAE,CAAC,CAAC,WAAW;AAC1B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,UAAU,EAAE,YAAY,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI;AAClD,MAAM,UAAU,EAAE,CAAC,CAAC,MAAM;AAC1B,MAAM,cAAc,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,IAAI,YAAY;AACpF,MAAM,cAAc,EAAE,YAAY;AAClC,MAAM,YAAY;AAClB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,IAAI,aAAa,EAAE;AAC3B,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,CAAC;AACpH,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACxC,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;AACjG,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC9C,QAAQ,eAAe,CAAC,UAAU,EAAE;AACpC,UAAU,EAAE,EAAE,aAAa;AAC3B,UAAU,KAAK,EAAE,CAAC,CAAC,IAAI;AACvB,UAAU,WAAW,EAAE,CAAC,CAAC,gBAAgB;AACzC,UAAU,QAAQ,EAAE,IAAI;AACxB,UAAU,MAAM;AAChB,UAAU,IAAI,KAAK,GAAG;AACtB,YAAY,OAAO,IAAI;AACvB,UAAU,CAAC;AACX,UAAU,IAAI,KAAK,CAAC,OAAO,EAAE;AAC7B,YAAY,IAAI,GAAG,OAAO;AAC1B,YAAY,SAAS,GAAG,KAAK;AAC7B,UAAU;AACV,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvC,QAAQ,kBAAkB,CAAC,UAAU,EAAE;AACvC,UAAU,EAAE,EAAE,oBAAoB;AAClC,UAAU,KAAK,EAAE,CAAC,CAAC,WAAW;AAC9B,UAAU,WAAW,EAAE,CAAC,CAAC,uBAAuB;AAChD,UAAU,IAAI,EAAE,CAAC;AACjB,UAAU,MAAM;AAChB,UAAU,IAAI,KAAK,GAAG;AACtB,YAAY,OAAO,WAAW;AAC9B,UAAU,CAAC;AACX,UAAU,IAAI,KAAK,CAAC,OAAO,EAAE;AAC7B,YAAY,WAAW,GAAG,OAAO;AACjC,YAAY,SAAS,GAAG,KAAK;AAC7B,UAAU;AACV,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC7C,MAAM;AACN,KAAK,CAAC;AACN,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE;AACnC,MAAM,OAAO,EAAE,4BAA4B;AAC3C,MAAM,eAAe,EAAE,mBAAmB;AAC1C,MAAM,cAAc,EAAE,iBAAiB;AACvC,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,cAAc,EAAE,SAAS;AAC/B,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,iBAAiB,EAAE,oBAAoB;AAC7C,MAAM,kBAAkB,EAAE,sBAAsB;AAChD,MAAM,aAAa,EAAE,iBAAiB;AACtC,MAAM,cAAc,EAAE,sBAAsB;AAC5C,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,oBAAoB,EAAE,yBAAyB;AACrD,MAAM,oBAAoB,EAAE,yBAAyB;AACrD,MAAM,aAAa,EAAE,uCAAuC;AAC5D,MAAM,uBAAuB,EAAE,6BAA6B;AAC5D,MAAM,eAAe,EAAE,iCAAiC;AACxD,MAAM,kBAAkB,EAAE,oBAAoB;AAC9C,MAAM,mBAAmB,EAAE,qCAAqC;AAChE,MAAM,sBAAsB,EAAE,uDAAuD;AACrF,MAAM,6BAA6B,EAAE,+DAA+D;AACpG,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,oBAAoB,EAAE,+CAA+C;AAC3E,MAAM,aAAa,EAAE,6IAA6I;AAClK,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,cAAc,EAAE,8BAA8B;AACpD,MAAM,oBAAoB,EAAE,0BAA0B;AACtD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,gBAAgB,EAAE,OAAO;AAC/B,MAAM,QAAQ,EAAE,qBAAqB;AACrC,MAAM,eAAe,EAAE,eAAe;AACtC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,gBAAgB,EAAE,sBAAsB;AAC9C,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,oBAAoB,EAAE,8BAA8B;AAC1D,MAAM,mBAAmB,EAAE,wBAAwB;AACnD,MAAM,iBAAiB,EAAE,kCAAkC;AAC3D,MAAM,eAAe,EAAE,6DAA6D;AACpF,MAAM,YAAY,EAAE,yCAAyC;AAC7D,MAAM,kBAAkB,EAAE,4CAA4C;AACtE,MAAM,kBAAkB,EAAE,6CAA6C;AACvE,MAAM,gBAAgB,EAAE,cAAc;AACtC,MAAM,iBAAiB,EAAE,QAAQ;AACjC,MAAM,aAAa,EAAE,aAAa;AAClC,MAAM,mBAAmB,EAAE,6BAA6B;AACxD,MAAM,kBAAkB,EAAE;AAC1B,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE;AACnC,MAAM,OAAO,EAAE,4BAA4B;AAC3C,MAAM,eAAe,EAAE,oBAAoB;AAC3C,MAAM,cAAc,EAAE,sBAAsB;AAC5C,MAAM,IAAI,EAAE,eAAe;AAC3B,MAAM,MAAM,EAAE,iBAAiB;AAC/B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,cAAc,EAAE,YAAY;AAClC,MAAM,UAAU,EAAE,OAAO;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,MAAM,EAAE,yBAAyB;AACvC,MAAM,iBAAiB,EAAE,0BAA0B;AACnD,MAAM,kBAAkB,EAAE,qBAAqB;AAC/C,MAAM,aAAa,EAAE,eAAe;AACpC,MAAM,cAAc,EAAE,wBAAwB;AAC9C,MAAM,SAAS,EAAE,sBAAsB;AACvC,MAAM,oBAAoB,EAAE,8BAA8B;AAC1D,MAAM,oBAAoB,EAAE,iCAAiC;AAC7D,MAAM,aAAa,EAAE,sCAAsC;AAC3D,MAAM,uBAAuB,EAAE,6BAA6B;AAC5D,MAAM,eAAe,EAAE,4BAA4B;AACnD,MAAM,kBAAkB,EAAE,sBAAsB;AAChD,MAAM,mBAAmB,EAAE,uCAAuC;AAClE,MAAM,sBAAsB,EAAE,+CAA+C;AAC7E,MAAM,6BAA6B,EAAE,8CAA8C;AACnF,MAAM,aAAa,EAAE,iBAAiB;AACtC,MAAM,oBAAoB,EAAE,6CAA6C;AACzE,MAAM,aAAa,EAAE,sHAAsH;AAC3I,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,cAAc,EAAE,yBAAyB;AAC/C,MAAM,oBAAoB,EAAE,4BAA4B;AACxD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,gBAAgB,EAAE,OAAO;AAC/B,MAAM,QAAQ,EAAE,2BAA2B;AAC3C,MAAM,eAAe,EAAE,qBAAqB;AAC5C,MAAM,WAAW,EAAE,uBAAuB;AAC1C,MAAM,WAAW,EAAE,qBAAqB;AACxC,MAAM,gBAAgB,EAAE,+BAA+B;AACvD,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,oBAAoB,EAAE,gCAAgC;AAC5D,MAAM,mBAAmB,EAAE,kCAAkC;AAC7D,MAAM,iBAAiB,EAAE,+CAA+C;AACxE,MAAM,eAAe,EAAE,0EAA0E;AACjG,MAAM,YAAY,EAAE,kDAAkD;AACtE,MAAM,kBAAkB,EAAE,oEAAoE;AAC9F,MAAM,kBAAkB,EAAE,iDAAiD;AAC3E,MAAM,gBAAgB,EAAE,qBAAqB;AAC7C,MAAM,iBAAiB,EAAE,SAAS;AAClC,MAAM,aAAa,EAAE,aAAa;AAClC,MAAM,mBAAmB,EAAE,8BAA8B;AACzD,MAAM,kBAAkB,EAAE;AAC1B;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,0BAA0B,EAAE,GAAG,IAAI;AACnE,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE;AAClC,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI;AACxB,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO;AAC5B,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO;AAC5B,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,IAAI,oBAAoB,GAAG,KAAK;AAClC,EAAE,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe;AAC9C,EAAE,SAAS,OAAO,GAAG;AACrB,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE;AAC5B,EAAE;AACF,EAAE,SAAS,oBAAoB,GAAG;AAClC,IAAI,aAAa,GAAG,KAAK;AACzB,IAAI,OAAO,EAAE;AACb,EAAE;AACF,EAAE,MAAM,WAAW,GAAG,0BAA0B,CAAC,OAAO,CAAC,IAAI,CAAC;AAC9D,EAAE,MAAM,kBAAkB,GAAG,0BAA0B,CAAC,OAAO,CAAC,WAAW,CAAC;AAC5E,EAAE,MAAM,iBAAiB,GAAG,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,SAAS,KAAK,MAAM,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,KAAK,IAAI,CAAC,EAAE,GAAG,KAAK;AAC5H,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACjG,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,CAAC;AACpD,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2FAA2F,CAAC,CAAC;AACrH,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE;AACvB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,uCAAuC,CAAC,CAAC;AACvJ,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+EAA+E,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;AAClJ,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AACzC,IAAI,IAAI,iBAAiB,IAAI,eAAe,CAAC,OAAO,EAAE;AACtD,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gHAAgH,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC;AACnL,MAAM,IAAI,OAAO,CAAC,KAAK,EAAE;AACzB,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+EAA+E,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC;AACnJ,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC1C,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gHAAgH,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,uCAAuC,EAAE,WAAW,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,+LAA+L,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC;AACzd,IAAI,IAAI,iBAAiB,IAAI,eAAe,CAAC,OAAO,EAAE;AACtD,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yEAAyE,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,wDAAwD,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC;AAC1N,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4EAA4E,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,wCAAwC,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,qEAAqE,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,kCAAkC,EAAE,WAAW,CAAC,0BAA0B,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,gEAAgE,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,kCAAkC,EAAE,WAAW,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,sCAAsC,CAAC,CAAC;AACpnB,IAAI,IAAI,eAAe,CAAC,cAAc,EAAE;AACxC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kGAAkG,EAAE,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,2CAA2C,EAAE,WAAW,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,0CAA0C,EAAE,IAAI,CAAC,UAAU,EAAE,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAChV,MAAM;AACN,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AAC7D,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACnD,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,IAAI,eAAe,CAAC,qBAAqB,EAAE;AACjD,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8CAA8C,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,UAAU,CAAC,CAAC;AACrM,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mGAAmG,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;AAC7L,IAAI,IAAI,eAAe,CAAC,SAAS,EAAE;AACnC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,8BAA8B,EAAE,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,iCAAiC,EAAE,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,6CAA6C,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC;AAC3a,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AACzC,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC;AAC3F,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,MAAM,UAAU,GAAG,iBAAiB,CAAC,OAAO,CAAC;AACnD,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kFAAkF,CAAC,CAAC;AAC9G,MAAM,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACzF,QAAQ,IAAI,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC;AACxC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,CAAC;AAC/C,QAAQ,WAAW,CAAC,SAAS,EAAE;AAC/B,UAAU,EAAE,EAAE,MAAM,CAAC,EAAE;AACvB,UAAU,SAAS,EAAE,MAAM,CAAC,SAAS;AACrC,UAAU,OAAO,EAAE,MAAM,CAAC,OAAO;AACjC,UAAU,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE;AACjC,UAAU,MAAM,EAAE,MAAM,CAAC,SAAS,KAAK,OAAO,CAAC,UAAU,CAAC,SAAS,IAAI,MAAM,CAAC,OAAO,KAAK,OAAO,CAAC,UAAU,CAAC,OAAO;AACpH,UAAU,SAAS,EAAE,MAAM,CAAC,SAAS;AACrC,UAAU,iBAAiB;AAC3B,UAAU,kBAAkB,EAAE,eAAe,CAAC,OAAO;AACrD,UAAU,MAAM;AAChB,UAAU,KAAK,EAAE,MAAM,CAAC,KAAK;AAC7B,UAAU,YAAY;AACtB,UAAU;AACV,SAAS,CAAC;AACV,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AAC3C,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC1C,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACnC,IAAI,kBAAkB,CAAC,SAAS,EAAE;AAClC,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,MAAM;AACZ,MAAM,gBAAgB,EAAE;AACxB,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,IAAI,iBAAiB,CAAC,SAAS,CAAC;AAChC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;AACjC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AACvC,EAAE,IAAI,iBAAiB,IAAI,eAAe,CAAC,OAAO,EAAE;AACpD,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI;AACJ,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE,IAAI,CAAC,iBAAiB,IAAI,eAAe,CAAC,OAAO,KAAK,OAAO,CAAC,KAAK,EAAE;AACvE,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI;AACJ,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,GAAG,EAAE;AACP;;;;"}