{"version": 3, "file": "_page.svelte-DGkTY4P9.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/communes/_id_/invitations/_page.svelte.js"], "sourcesContent": ["import { x as head, z as escape_html, y as attr, K as ensure_array_like, G as attr_class, w as pop, u as push } from \"../../../../../../../chunks/index.js\";\nimport \"../../../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../../../chunks/acrpc.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../../../../chunks/exports.js\";\nimport \"../../../../../../../chunks/state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { f as formatDate } from \"../../../../../../../chunks/format-date.js\";\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Commune Invitations — Commune\" },\n      communeInvitations: \"Commune Invitations\",\n      loading: \"Loading...\",\n      noInvitations: \"No invitations found\",\n      errorFetchingInvitations: \"Failed to fetch invitations\",\n      errorOccurred: \"An error occurred while fetching invitations\",\n      loadingMore: \"Loading more invitations...\",\n      cancel: \"Cancel Invitation\",\n      pending: \"Pending\",\n      accepted: \"Accepted\",\n      rejected: \"Rejected\",\n      expired: \"Expired\",\n      sentOn: \"Sent on\",\n      cancelingInvitation: \"Canceling...\",\n      errorCancelingInvitation: \"Failed to cancel invitation\",\n      invitationCanceled: \"Invitation canceled\",\n      backToCommune: \"Back to Commune\",\n      invitedUser: \"Invited User\",\n      status: \"Status\",\n      actions: \"Actions\",\n      confirmCancel: \"Are you sure you want to cancel this invitation?\"\n    },\n    ru: {\n      _page: {\n        title: \"Приглашения коммуны — Коммуна\"\n      },\n      communeInvitations: \"Приглашения коммуны\",\n      loading: \"Загрузка...\",\n      noInvitations: \"Приглашения не найдены\",\n      errorFetchingInvitations: \"Не удалось загрузить приглашения\",\n      errorOccurred: \"Произошла ошибка при загрузке приглашений\",\n      loadingMore: \"Загружаем больше приглашений...\",\n      cancel: \"Отменить приглашение\",\n      pending: \"Ожидает\",\n      accepted: \"Принято\",\n      rejected: \"Отклонено\",\n      expired: \"Истекло\",\n      sentOn: \"Отправлено\",\n      cancelingInvitation: \"Отменяем...\",\n      errorCancelingInvitation: \"Не удалось отменить приглашение\",\n      invitationCanceled: \"Приглашение отменено\",\n      backToCommune: \"Назад к коммуне\",\n      invitedUser: \"Приглашенный пользователь\",\n      status: \"Статус\",\n      actions: \"Действия\",\n      confirmCancel: \"Вы уверены, что хотите отменить это приглашение?\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const {\n    locale,\n    routeLocale,\n    toLocaleHref,\n    getAppropriateLocalization\n  } = data;\n  const t = i18n[locale];\n  let invitations = data.invitations;\n  let isHasMoreInvitations = data.isHasMoreInvitations;\n  let loadingStates = {};\n  function getStatusBadgeClass(status) {\n    switch (status) {\n      case \"pending\":\n        return \"bg-warning text-dark\";\n      case \"accepted\":\n        return \"bg-success\";\n      case \"rejected\":\n        return \"bg-danger\";\n      case \"expired\":\n        return \"bg-secondary\";\n      default:\n        return \"bg-secondary\";\n    }\n  }\n  function getStatusText(status) {\n    switch (status) {\n      case \"pending\":\n        return t.pending;\n      case \"accepted\":\n        return t.accepted;\n      case \"rejected\":\n        return t.rejected;\n      case \"expired\":\n        return t.expired;\n      default:\n        return status;\n    }\n  }\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;\n  });\n  $$payload.out.push(`<div class=\"container my-4 mb-5\"><div class=\"d-flex justify-content-between align-items-center my-4\"><div><h1>${escape_html(t.communeInvitations)}</h1> <p class=\"text-muted mb-0\">${escape_html(getAppropriateLocalization(data.commune.name))}</p></div> <a${attr(\"href\", toLocaleHref(`/communes/${data.commune.id}`))} class=\"btn btn-outline-secondary\">${escape_html(t.backToCommune)}</a></div> `);\n  if (invitations.length === 0) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-5\"><p class=\"text-muted\">${escape_html(t.noInvitations)}</p></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    const each_array = ensure_array_like(invitations);\n    const each_array_1 = ensure_array_like(invitations);\n    $$payload.out.push(`<div class=\"d-none d-md-block\"><div class=\"table-responsive\"><table class=\"table table-hover\"><thead><tr><th>${escape_html(t.invitedUser)}</th><th>${escape_html(t.status)}</th><th>${escape_html(t.sentOn)}</th><th>${escape_html(t.actions)}</th></tr></thead><tbody><!--[-->`);\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let invitation = each_array[$$index];\n      $$payload.out.push(`<tr><td><div class=\"d-flex align-items-center\">`);\n      if (invitation.user.image) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<img${attr(\"src\", `/images/${invitation.user.image}`)} alt=\"User avatar\" class=\"rounded-circle me-2\" style=\"width: 32px; height: 32px; object-fit: cover;\"/>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<div class=\"rounded-circle bg-secondary d-flex align-items-center justify-content-center me-2\" style=\"width: 32px; height: 32px;\"><i class=\"bi bi-person text-white\"></i></div>`);\n      }\n      $$payload.out.push(`<!--]--> <div><div class=\"fw-medium\">${escape_html(getAppropriateLocalization(invitation.user.name))}</div></div></div></td><td><span${attr_class(`badge ${getStatusBadgeClass(invitation.status)}`)}>${escape_html(getStatusText(invitation.status))}</span></td><td class=\"text-muted\">${escape_html(formatDate(invitation.createdAt, locale))}</td><td>`);\n      if (invitation.status === \"pending\") {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<button class=\"btn btn-sm btn-outline-danger\"${attr(\"disabled\", loadingStates[invitation.id] === \"canceling\", true)}>`);\n        if (loadingStates[invitation.id] === \"canceling\") {\n          $$payload.out.push(\"<!--[-->\");\n          $$payload.out.push(`<span class=\"spinner-border spinner-border-sm me-1\" role=\"status\"></span> ${escape_html(t.cancelingInvitation)}`);\n        } else {\n          $$payload.out.push(\"<!--[!-->\");\n          $$payload.out.push(`${escape_html(t.cancel)}`);\n        }\n        $$payload.out.push(`<!--]--></button>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<span class=\"text-muted\">—</span>`);\n      }\n      $$payload.out.push(`<!--]--></td></tr>`);\n    }\n    $$payload.out.push(`<!--]--></tbody></table></div></div> <div class=\"d-md-none\"><div class=\"row g-3\"><!--[-->`);\n    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n      let invitation = each_array_1[$$index_1];\n      $$payload.out.push(`<div class=\"col-12\"><div class=\"card\"><div class=\"card-body\"><div class=\"d-flex align-items-start justify-content-between mb-3\"><div class=\"d-flex align-items-center\">`);\n      if (invitation.user.image) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<img${attr(\"src\", `/images/${invitation.user.image}`)} alt=\"User avatar\" class=\"rounded-circle me-3\" style=\"width: 48px; height: 48px; object-fit: cover;\"/>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<div class=\"rounded-circle bg-secondary d-flex align-items-center justify-content-center me-3\" style=\"width: 48px; height: 48px;\"><i class=\"bi bi-person text-white\"></i></div>`);\n      }\n      $$payload.out.push(`<!--]--> <div><div class=\"fw-medium\">${escape_html(getAppropriateLocalization(invitation.user.name))}</div></div></div> <span${attr_class(`badge ${getStatusBadgeClass(invitation.status)}`)}>${escape_html(getStatusText(invitation.status))}</span></div> <div class=\"d-flex justify-content-between align-items-center\"><small class=\"text-muted\">${escape_html(t.sentOn)}\n                    ${escape_html(formatDate(invitation.createdAt, locale))}</small> `);\n      if (invitation.status === \"pending\") {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<button class=\"btn btn-sm btn-outline-danger\"${attr(\"disabled\", loadingStates[invitation.id] === \"canceling\", true)}>`);\n        if (loadingStates[invitation.id] === \"canceling\") {\n          $$payload.out.push(\"<!--[-->\");\n          $$payload.out.push(`<span class=\"spinner-border spinner-border-sm me-1\" role=\"status\"></span> ${escape_html(t.cancelingInvitation)}`);\n        } else {\n          $$payload.out.push(\"<!--[!-->\");\n          $$payload.out.push(`${escape_html(t.cancel)}`);\n        }\n        $$payload.out.push(`<!--]--></button>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n      }\n      $$payload.out.push(`<!--]--></div></div></div></div>`);\n    }\n    $$payload.out.push(`<!--]--></div></div>`);\n  }\n  $$payload.out.push(`<!--]--> `);\n  if (isHasMoreInvitations) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-3\">`);\n    {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;AASA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE;AACvD,MAAM,kBAAkB,EAAE,qBAAqB;AAC/C,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,aAAa,EAAE,sBAAsB;AAC3C,MAAM,wBAAwB,EAAE,6BAA6B;AAC7D,MAAM,aAAa,EAAE,8CAA8C;AACnE,MAAM,WAAW,EAAE,6BAA6B;AAChD,MAAM,MAAM,EAAE,mBAAmB;AACjC,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,mBAAmB,EAAE,cAAc;AACzC,MAAM,wBAAwB,EAAE,6BAA6B;AAC7D,MAAM,kBAAkB,EAAE,qBAAqB;AAC/C,MAAM,aAAa,EAAE,iBAAiB;AACtC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,aAAa,EAAE;AACrB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,kBAAkB,EAAE,qBAAqB;AAC/C,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,aAAa,EAAE,wBAAwB;AAC7C,MAAM,wBAAwB,EAAE,kCAAkC;AAClE,MAAM,aAAa,EAAE,2CAA2C;AAChE,MAAM,WAAW,EAAE,iCAAiC;AACpD,MAAM,MAAM,EAAE,sBAAsB;AACpC,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,mBAAmB,EAAE,aAAa;AACxC,MAAM,wBAAwB,EAAE,iCAAiC;AACjE,MAAM,kBAAkB,EAAE,sBAAsB;AAChD,MAAM,aAAa,EAAE,iBAAiB;AACtC,MAAM,WAAW,EAAE,2BAA2B;AAC9C,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,aAAa,EAAE;AACrB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM;AACR,IAAI,MAAM;AACV,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI;AACJ,GAAG,GAAG,IAAI;AACV,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW;AACpC,EAAE,IAAI,oBAAoB,GAAG,IAAI,CAAC,oBAAoB;AACtD,EAAE,IAAI,aAAa,GAAG,EAAE;AACxB,EAAE,SAAS,mBAAmB,CAAC,MAAM,EAAE;AACvC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,sBAAsB;AACrC,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,YAAY;AAC3B,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,WAAW;AAC1B,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,cAAc;AAC7B,MAAM;AACN,QAAQ,OAAO,cAAc;AAC7B;AACA,EAAE;AACF,EAAE,SAAS,aAAa,CAAC,MAAM,EAAE;AACjC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,CAAC,CAAC,OAAO;AACxB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,CAAC,CAAC,QAAQ;AACzB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,CAAC,CAAC,QAAQ;AACzB,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,CAAC,CAAC,OAAO;AACxB,MAAM;AACN,QAAQ,OAAO,MAAM;AACrB;AACA,EAAE;AACF,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACrE,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8GAA8G,EAAE,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,iCAAiC,EAAE,WAAW,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,mCAAmC,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;AAC/Z,EAAE,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AAChC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC;AACvH,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC;AACrD,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,WAAW,CAAC;AACvD,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6GAA6G,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,iCAAiC,CAAC,CAAC;AACzS,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC;AAC1C,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+CAA+C,CAAC,CAAC;AAC3E,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE;AACjC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,sGAAsG,CAAC,CAAC;AAC1L,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+KAA+K,CAAC,CAAC;AAC7M,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,0BAA0B,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gCAAgC,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,mBAAmB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,mCAAmC,EAAE,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AACtX,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,EAAE;AAC3C,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6CAA6C,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACnJ,QAAQ,IAAI,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE;AAC1D,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAC/I,QAAQ,CAAC,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxD,QAAQ;AACR,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,CAAC;AAC/C,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iCAAiC,CAAC,CAAC;AAC/D,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC9C,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yFAAyF,CAAC,CAAC;AACnH,IAAI,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC/F,MAAM,IAAI,UAAU,GAAG,YAAY,CAAC,SAAS,CAAC;AAC9C,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uKAAuK,CAAC,CAAC;AACnM,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE;AACjC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,sGAAsG,CAAC,CAAC;AAC1L,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+KAA+K,CAAC,CAAC;AAC7M,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,0BAA0B,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,mBAAmB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,uGAAuG,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;AACtY,oBAAoB,EAAE,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AACvF,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,EAAE;AAC3C,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6CAA6C,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACnJ,QAAQ,IAAI,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE;AAC1D,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAC/I,QAAQ,CAAC,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxD,QAAQ;AACR,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,CAAC;AAC/C,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gCAAgC,CAAC,CAAC;AAC5D,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC9C,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE,IAAI,oBAAoB,EAAE;AAC5B,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AACxD,IAAI;AACJ,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,GAAG,EAAE;AACP;;;;"}