{"version": 3, "file": "_page.svelte-DMeRQior.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/users/_id_/karma/_page.svelte.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, w as pop, u as push, x as head, z as escape_html, y as attr, K as ensure_array_like, G as attr_class, J as stringify } from \"../../../../../../../chunks/index.js\";\nimport \"../../../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../../../chunks/acrpc.js\";\n/* empty css                                                                                 */\nimport \"@sveltejs/kit/internal\";\nimport \"../../../../../../../chunks/exports.js\";\nimport \"../../../../../../../chunks/state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Karma — Commune\" },\n      userNotFound: \"User not found\",\n      karmaHistory: \"Karma History\",\n      changeKarma: \"Change Karma\",\n      karmaModalTitle: \"Change Karma\",\n      giveKarma: \"Give Karma (+1)\",\n      takeKarma: \"Take Karma (-1)\",\n      comment: \"Comment\",\n      commentPlaceholder: \"Enter your comment...\",\n      cancel: \"Cancel\",\n      submit: \"Submit\",\n      submitting: \"Submitting...\",\n      success: \"Karma changed successfully\",\n      errorSubmitting: \"Error submitting karma change\",\n      noKarmaChanges: \"No karma changes found\",\n      loadingMore: \"Loading more...\",\n      errorOccurred: \"An error occurred\",\n      errorFetchingKarma: \"Error fetching karma changes\",\n      insufficientPoints: {\n        title: \"Insufficient Karma Points\",\n        message: \"You don't have enough spendable karma points to perform this action.\",\n        explanation: \"You need at least one spendable karma point to give or take karma from other users.\",\n        howToEarn: \"You get one karma point per week.\",\n        backToProfile: \"Back to Profile\"\n      }\n    },\n    ru: {\n      _page: {\n        title: \"Карма — Коммуна\"\n      },\n      userNotFound: \"Пользователь не найден\",\n      karmaHistory: \"История кармы\",\n      changeKarma: \"Изменить карму\",\n      karmaModalTitle: \"Изменить карму\",\n      giveKarma: \"Дать карму (+1)\",\n      takeKarma: \"Забрать карму (-1)\",\n      comment: \"Комментарий\",\n      commentPlaceholder: \"Введите ваш комментарий...\",\n      cancel: \"Отмена\",\n      submit: \"Отправить\",\n      submitting: \"Отправка...\",\n      success: \"Карма успешно изменена\",\n      errorSubmitting: \"Ошибка при изменении кармы\",\n      noKarmaChanges: \"Изменения кармы не найдены\",\n      loadingMore: \"Загрузка...\",\n      errorOccurred: \"Произошла ошибка\",\n      errorFetchingKarma: \"Ошибка загрузки изменений кармы\",\n      insufficientPoints: {\n        title: \"Недостаточно очков кармы\",\n        message: \"У вас недостаточно доступных очков кармы для выполнения этого действия.\",\n        explanation: \"Вам нужно как минимум одно доступное очко кармы, чтобы давать или забирать карму у других пользователей.\",\n        howToEarn: \"Вы получаете одно очко кармы в неделю.\",\n        backToProfile: \"Вернуться к профилю\"\n      }\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const { me, user, locale, getAppropriateLocalization } = data;\n  const t = i18n[locale];\n  let karmaPoints = data.karmaPoints;\n  let isHasMoreKarma = data.isHasMoreKarma;\n  const userName = getAppropriateLocalization(user.name);\n  const getAuthorDisplayName = (author) => {\n    return getAppropriateLocalization(author.name);\n  };\n  const formatQuantity = (quantity) => {\n    return quantity > 0 ? `+${quantity}` : `${quantity}`;\n  };\n  const getQuantityColorClass = (quantity) => {\n    return quantity > 0 ? \"text-success\" : \"text-danger\";\n  };\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    head($$payload2, ($$payload3) => {\n      $$payload3.title = `<title>${escape_html(userName)} ${escape_html(t._page.title)}</title>`;\n    });\n    $$payload2.out.push(`<div class=\"container py-4\"><div class=\"responsive-container\">`);\n    if (!user) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"alert alert-danger\" role=\"alert\">${escape_html(t.userNotFound)}</div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      $$payload2.out.push(`<div class=\"d-flex justify-content-between align-items-center mb-4\"><div><h2 class=\"mb-1\">${escape_html(userName)}</h2> <p class=\"text-muted mb-0\">${escape_html(t.karmaHistory)}</p></div> `);\n      if (me.id !== user.id) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<div><button class=\"btn btn-primary btn-sm\"${attr(\"aria-label\", t.changeKarma)}><i class=\"bi bi-arrow-up-down me-1\"></i> ${escape_html(t.changeKarma)}</button></div>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--></div> `);\n      if (karmaPoints.length === 0) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<div class=\"text-center py-5\"><p class=\"text-muted\">${escape_html(t.noKarmaChanges)}</p></div>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n        const each_array = ensure_array_like(karmaPoints);\n        $$payload2.out.push(`<!--[-->`);\n        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n          let karmaPoint = each_array[$$index];\n          $$payload2.out.push(`<div class=\"card mb-3 shadow-sm svelte-5uit7u\"><div class=\"card-body\"><div class=\"d-flex align-items-start\"><div class=\"me-3\">`);\n          if (karmaPoint.author.image) {\n            $$payload2.out.push(\"<!--[-->\");\n            $$payload2.out.push(`<img${attr(\"src\", `/images/${karmaPoint.author.image}`)}${attr(\"alt\", getAuthorDisplayName(karmaPoint.author))} class=\"rounded-circle\" style=\"width: 48px; height: 48px; object-fit: cover;\"/>`);\n          } else {\n            $$payload2.out.push(\"<!--[!-->\");\n            $$payload2.out.push(`<div class=\"rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white\" style=\"width: 48px; height: 48px;\"><i class=\"bi bi-person-fill\"></i></div>`);\n          }\n          $$payload2.out.push(`<!--]--></div> <div class=\"flex-grow-1\"><div class=\"d-flex justify-content-between align-items-start mb-2\"><div><h6 class=\"mb-1\">${escape_html(getAuthorDisplayName(karmaPoint.author))}</h6></div> <span${attr_class(`badge ${stringify(getQuantityColorClass(karmaPoint.quantity))} fs-6`, \"svelte-5uit7u\")}>${escape_html(formatQuantity(karmaPoint.quantity))}</span></div> <p class=\"mb-0 text-muted\">${escape_html(getAppropriateLocalization(karmaPoint.comment))}</p></div></div></div></div>`);\n        }\n        $$payload2.out.push(`<!--]-->`);\n      }\n      $$payload2.out.push(`<!--]--> `);\n      if (isHasMoreKarma) {\n        $$payload2.out.push(\"<!--[-->\");\n        $$payload2.out.push(`<div class=\"text-center py-3\">`);\n        {\n          $$payload2.out.push(\"<!--[!-->\");\n        }\n        $$payload2.out.push(`<!--]--></div>`);\n      } else {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--> `);\n      {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]-->`);\n    }\n    $$payload2.out.push(`<!--]--></div> `);\n    {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div>`);\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AASA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE;AACzC,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM,YAAY,EAAE,eAAe;AACnC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,eAAe,EAAE,cAAc;AACrC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,kBAAkB,EAAE,uBAAuB;AACjD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,OAAO,EAAE,4BAA4B;AAC3C,MAAM,eAAe,EAAE,+BAA+B;AACtD,MAAM,cAAc,EAAE,wBAAwB;AAC9C,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,aAAa,EAAE,mBAAmB;AACxC,MAAM,kBAAkB,EAAE,8BAA8B;AACxD,MAAM,kBAAkB,EAAE;AAC1B,QAAQ,KAAK,EAAE,2BAA2B;AAC1C,QAAQ,OAAO,EAAE,sEAAsE;AACvF,QAAQ,WAAW,EAAE,qFAAqF;AAC1G,QAAQ,SAAS,EAAE,mCAAmC;AACtD,QAAQ,aAAa,EAAE;AACvB;AACA,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,YAAY,EAAE,wBAAwB;AAC5C,MAAM,YAAY,EAAE,eAAe;AACnC,MAAM,WAAW,EAAE,gBAAgB;AACnC,MAAM,eAAe,EAAE,gBAAgB;AACvC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,kBAAkB,EAAE,4BAA4B;AACtD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,eAAe,EAAE,4BAA4B;AACnD,MAAM,cAAc,EAAE,4BAA4B;AAClD,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,aAAa,EAAE,kBAAkB;AACvC,MAAM,kBAAkB,EAAE,iCAAiC;AAC3D,MAAM,kBAAkB,EAAE;AAC1B,QAAQ,KAAK,EAAE,0BAA0B;AACzC,QAAQ,OAAO,EAAE,yEAAyE;AAC1F,QAAQ,WAAW,EAAE,0GAA0G;AAC/H,QAAQ,SAAS,EAAE,wCAAwC;AAC3D,QAAQ,aAAa,EAAE;AACvB;AACA;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,0BAA0B,EAAE,GAAG,IAAI;AAC/D,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW;AACpC,EAAE,IAAI,cAAc,GAAG,IAAI,CAAC,cAAc;AAC1C,EAAE,MAAM,QAAQ,GAAG,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC;AACxD,EAAE,MAAM,oBAAoB,GAAG,CAAC,MAAM,KAAK;AAC3C,IAAI,OAAO,0BAA0B,CAAC,MAAM,CAAC,IAAI,CAAC;AAClD,EAAE,CAAC;AACH,EAAE,MAAM,cAAc,GAAG,CAAC,QAAQ,KAAK;AACvC,IAAI,OAAO,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;AACxD,EAAE,CAAC;AACH,EAAE,MAAM,qBAAqB,GAAG,CAAC,QAAQ,KAAK;AAC9C,IAAI,OAAO,QAAQ,GAAG,CAAC,GAAG,cAAc,GAAG,aAAa;AACxD,EAAE,CAAC;AACH,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,KAAK;AACrC,MAAM,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AAChG,IAAI,CAAC,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8DAA8D,CAAC,CAAC;AACzF,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6CAA6C,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC;AAC9G,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0FAA0F,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,iCAAiC,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AACzN,MAAM,IAAI,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;AAC7B,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2CAA2C,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,0CAA0C,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,CAAC;AACpM,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC5C,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AACpC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,CAAC;AAC7H,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,QAAQ,MAAM,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC;AACzD,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvC,QAAQ,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AAC3F,UAAU,IAAI,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC;AAC9C,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8HAA8H,CAAC,CAAC;AAC/J,UAAU,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE;AACvC,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,+EAA+E,CAAC,CAAC;AACjO,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+KAA+K,CAAC,CAAC;AAClN,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iIAAiI,EAAE,WAAW,CAAC,oBAAoB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,0BAA0B,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC;AACtgB,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACtC,MAAM,IAAI,cAAc,EAAE;AAC1B,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AAC7D,QAAQ;AACR,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC7C,MAAM,CAAC,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACtC,MAAM;AACN,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC1C,IAAI;AACJ,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACzC,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;;;;"}