import { u as push, N as ensure_array_like, x as head, z as escape_html, G as attr_style, y as attr, w as pop } from './index-0Ke2LYl0.js';
import { h as html } from './html-FW6Ia4bL.js';

const lightDictionaryColors = {
  cambridge: "A8CF92",
  // green
  opendict: "F5BE6A",
  // orange
  any: "EBBACB"
  // pink
};
const darkDictionaryColors = {
  cambridge: "7B966B",
  // darker green
  opendict: "B58C4E",
  // darker orange
  any: "B08B98"
  // darker pink
};
const examples = [
  /**
    1. approach (C /əˈproʊtʃ/, O /əˈpɹoʊtʃ/) - aprōč
      1. ə - a
      2. p - p
      3. r/ɹ - r
      4. oʊ - ō
      5. tʃ - č
   */
  {
    english: "approach",
    transcriptions: {
      cambridge: "əˈproʊtʃ",
      opendict: "əˈpɹoʊtʃ"
    },
    newEnglish: "aprō<PERSON>",
    breakdown: [
      {
        sounds: ["ə"],
        mapping: "a"
      },
      {
        sounds: ["p"],
        mapping: "p"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r"
        }
      ],
      {
        sounds: ["oʊ"],
        mapping: "ō"
      },
      {
        sounds: ["tʃ"],
        mapping: "č"
      }
    ]
  },
  /**
    2. pig (C /pɪɡ/, O /ˈpɪɡ/) - pik
      1. p - p
      2. ɪ - i
      3. ɡ$ - k
   */
  {
    english: "pig",
    transcriptions: {
      cambridge: "pɪɡ",
      opendict: "ˈpɪɡ"
    },
    newEnglish: "pik",
    breakdown: [
      {
        sounds: ["p"],
        mapping: "p"
      },
      {
        sounds: ["ɪ"],
        mapping: "i"
      },
      {
        sounds: ["ɡ$"],
        mapping: "k"
      }
    ]
  },
  /**
    3. dog (C /dɑːɡ/, O /ˈdɔɡ/) - dak
      1. d - d
      2. ɑː/ɔ - a
      3. ɡ$ - k
   */
  {
    english: "dog",
    transcriptions: {
      cambridge: "dɑːɡ",
      opendict: "ˈdɔɡ"
    },
    newEnglish: "dak",
    breakdown: [
      {
        sounds: ["d"],
        mapping: "d"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɑː"],
          mapping: "a"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɔ"],
          mapping: "a"
        }
      ],
      {
        sounds: ["ɡ$"],
        mapping: "k"
      }
    ]
  },
  /**
    4. red (C /red/, O /ˈɹɛd/) - ret
      1. r/ɹ - r
      2. e/ɛ - e
      3. d$ - t
   */
  {
    english: "red",
    transcriptions: {
      cambridge: "red",
      opendict: "ˈɹɛd"
    },
    newEnglish: "ret",
    breakdown: [
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r"
        }
      ],
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["e"],
          mapping: "e"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɛ"],
          mapping: "e"
        }
      ],
      {
        sounds: ["d$"],
        mapping: "t"
      }
    ]
  },
  /**
    5. rat (C /ræt/, O /ˈɹæt/) - rāt
      1. r/ɹ - r
      2. æ - ā
      3. t - t
   */
  {
    english: "rat",
    transcriptions: {
      cambridge: "ræt",
      opendict: "ˈɹæt"
    },
    newEnglish: "rāt",
    breakdown: [
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r"
        }
      ],
      {
        sounds: ["æ"],
        mapping: "ā"
      },
      {
        sounds: ["t"],
        mapping: "t"
      }
    ]
  },
  /**
    6. turtle (C /ˈtɝː.t̬əl/, O /ˈtɝtəɫ/) - tëtl
      2. t - t
      3. ɝ - ë
      4. t̬/t - t
      5. əl/əɫ$ - l
   */
  {
    english: "turtle",
    transcriptions: {
      cambridge: "ˈtɝː.t̬əl",
      opendict: "ˈtɝtəɫ"
    },
    newEnglish: "tëtl",
    breakdown: [
      {
        sounds: ["t"],
        mapping: "t"
      },
      {
        sounds: ["ɝ"],
        mapping: "ë"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["t̬"],
          mapping: "t"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["t"],
          mapping: "t"
        }
      ],
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["əl$"],
          mapping: "l"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["əɫ$"],
          mapping: "l"
        }
      ]
    ]
  },
  /**
    7. goat (C /ɡoʊt/, O /ˈɡoʊt/) - gōt
      1. ɡ - g
      2. oʊ - ō
      3. t - t
   */
  {
    english: "goat",
    transcriptions: {
      cambridge: "ɡoʊt",
      opendict: "ˈɡoʊt"
    },
    newEnglish: "gōt",
    breakdown: [
      {
        sounds: ["ɡ"],
        mapping: "g"
      },
      {
        sounds: ["oʊ"],
        mapping: "ō"
      },
      {
        sounds: ["t"],
        mapping: "t"
      }
    ]
  },
  /**
    8. bear (C /ber/, O /ˈbɛɹ/) - be
      1. b - b
      2. e/ɛ - e
      3. r/ɹ$ - *
   */
  {
    english: "bear",
    transcriptions: {
      cambridge: "ber",
      opendict: "ˈbɛɹ"
    },
    newEnglish: "be",
    breakdown: [
      {
        sounds: ["b"],
        mapping: "b"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["e"],
          mapping: "e"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɛ"],
          mapping: "e"
        }
      ],
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r$"],
          mapping: ""
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ$"],
          mapping: ""
        }
      ]
    ]
  },
  /**
    9. panther (C /ˈpæn.θɚ/, O /ˈpænθɝ/) - pānfe
      1. p - p
      2. æ - ā
      3. n - n
      4. θ - f
      5. ɚ/ɝ$ - e
   */
  {
    english: "panther",
    transcriptions: {
      cambridge: "ˈpæn.θɚ",
      opendict: "ˈpænθɝ"
    },
    newEnglish: "pānfe",
    breakdown: [
      {
        sounds: ["p"],
        mapping: "p"
      },
      {
        sounds: ["æ"],
        mapping: "ā"
      },
      {
        sounds: ["n"],
        mapping: "n"
      },
      {
        sounds: ["θ"],
        mapping: "f"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɚ"],
          mapping: "e"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɝ$"],
          mapping: "e"
        }
      ]
    ]
  },
  /**
    10. frog (C /frɑːɡ/, O /ˈfɹɑɡ/) - frak
      1. f - f
      2. r/ɹ - r
      3. ɑ - a
      4. ɡ$ - k
   */
  {
    english: "frog",
    transcriptions: {
      cambridge: "frɑːɡ",
      opendict: "ˈfɹɑɡ"
    },
    newEnglish: "frak",
    breakdown: [
      {
        sounds: ["f"],
        mapping: "f"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r"
        }
      ],
      {
        sounds: ["ɑ"],
        mapping: "a"
      },
      {
        sounds: ["ɡ$"],
        mapping: "k"
      }
    ]
  },
  /**
    11. feather (C /ˈfeð.ɚ/, O /ˈfɛðɝ/) - feve
      1. f - f
      2. e/ɛ - e
      3. ð - v
      4. ɚ/ɝ - e
   */
  {
    english: "feather",
    transcriptions: {
      cambridge: "ˈfeð.ɚ",
      opendict: "ˈfɛðɝ"
    },
    newEnglish: "feve",
    breakdown: [
      {
        sounds: ["f"],
        mapping: "f"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["e"],
          mapping: "e"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɛ"],
          mapping: "e"
        }
      ],
      {
        sounds: ["ð"],
        mapping: "v"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɚ"],
          mapping: "e"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɝ"],
          mapping: "e"
        }
      ]
    ]
  },
  /**
    12. beaver (C /ˈbiː.vɚ/, O /ˈbivɝ/) - bive
      1. b - b
      2. i - i
      3. v - v
      4. ɚ/ɝ - e
   */
  {
    english: "beaver",
    transcriptions: {
      cambridge: "ˈbiː.vɚ",
      opendict: "ˈbivɝ"
    },
    newEnglish: "bive",
    breakdown: [
      {
        sounds: ["b"],
        mapping: "b"
      },
      {
        sounds: ["i"],
        mapping: "i"
      },
      {
        sounds: ["v"],
        mapping: "v"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɚ"],
          mapping: "e"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɝ"],
          mapping: "e"
        }
      ]
    ]
  },
  /**
    13. snake (C /sneɪk/, O /ˈsneɪk/) - sneík
      1. s - s
      2. n - n
      3. eɪ - eí
      4. k - k
   */
  {
    english: "snake",
    transcriptions: {
      cambridge: "sneɪk",
      opendict: "ˈsneɪk"
    },
    newEnglish: "sneík",
    breakdown: [
      {
        sounds: ["s"],
        mapping: "s"
      },
      {
        sounds: ["n"],
        mapping: "n"
      },
      {
        sounds: ["eɪ"],
        mapping: "eí"
      },
      {
        sounds: ["k"],
        mapping: "k"
      }
    ]
  },
  /**
    14. sheep (C /ʃiːp/, O /ˈʃip/) - šip
      1. ʃ - š
      2. i - i
      3. p - p
   */
  {
    english: "sheep",
    transcriptions: {
      cambridge: "ʃiːp",
      opendict: "ˈʃip"
    },
    newEnglish: "šip",
    breakdown: [
      {
        sounds: ["ʃ"],
        mapping: "š"
      },
      {
        sounds: ["i"],
        mapping: "i"
      },
      {
        sounds: ["p"],
        mapping: "p"
      }
    ]
  },
  /**
    15. chicken (C /ˈtʃɪk.ɪn/, O /ˈtʃɪkən/) - čikn
      1. tʃ - č
      2. ɪ - i
      3. k - k
      4. ɪn/$, ən - n
   */
  {
    english: "chicken",
    transcriptions: {
      cambridge: "ˈtʃɪk.ɪn",
      opendict: "ˈtʃɪkən"
    },
    newEnglish: "čikn",
    breakdown: [
      {
        sounds: ["tʃ"],
        mapping: "č"
      },
      {
        sounds: ["ɪ"],
        mapping: "i"
      },
      {
        sounds: ["k"],
        mapping: "k"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɪn$"],
          mapping: "n"
        },
        {
          dictionaryKey: "any",
          sounds: ["ən"],
          mapping: "n"
        }
      ]
    ]
  },
  /**
    16. zebra (C /ˈziː.brə/, O /ˈzibɹə/) - zibra
      1. z - z
      2. i - i
      3. b - b
      4. r/ɹ - r
      5. ə$ - a
   */
  {
    english: "zebra",
    transcriptions: {
      cambridge: "ˈziː.brə",
      opendict: "ˈzibɹə"
    },
    newEnglish: "zibra",
    breakdown: [
      {
        sounds: ["z"],
        mapping: "z"
      },
      {
        sounds: ["i"],
        mapping: "i"
      },
      {
        sounds: ["b"],
        mapping: "b"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r"
        }
      ],
      {
        sounds: ["ə$"],
        mapping: "a"
      }
    ]
  },
  /**
    17. television (C /ˈtel.ə.vɪʒ.ən/, O /ˈtɛɫəˌvɪʒən/) - televijn
      1. t - t
      2. e/ɛ - e
      3. lə/ɫə - le
      4. v - v
      5. ɪ - i
      6. ʒ - j
      7. ən - n
   */
  {
    english: "television",
    transcriptions: {
      cambridge: "ˈtel.ə.vɪʒ.ən",
      opendict: "ˈtɛɫəˌvɪʒən"
    },
    newEnglish: "televijn",
    breakdown: [
      {
        sounds: ["t"],
        mapping: "t"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["e"],
          mapping: "e"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɛ"],
          mapping: "e"
        }
      ],
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["lə"],
          mapping: "le"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɫə"],
          mapping: "le"
        }
      ],
      {
        sounds: ["v"],
        mapping: "v"
      },
      {
        sounds: ["ɪ"],
        mapping: "i"
      },
      {
        sounds: ["ʒ"],
        mapping: "j"
      },
      {
        sounds: ["ən"],
        mapping: "n"
      }
    ]
  },
  /**
    18. giraffe (C /dʒɪˈræf/, O /dʒɝˈæf/) - djirāf
      1. d - d
      2. ʒ - j
      3. ɪˈr/ɝˈ - ir
      4. æ - ā
      5. f - f
   */
  {
    english: "giraffe",
    transcriptions: {
      cambridge: "dʒɪˈræf",
      opendict: "dʒɝˈæf"
    },
    newEnglish: "djirāf",
    breakdown: [
      {
        sounds: ["d"],
        mapping: "d"
      },
      {
        sounds: ["ʒ"],
        mapping: "j"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɪˈr"],
          mapping: "ir"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɝˈ"],
          mapping: "ir"
        }
      ],
      {
        sounds: ["æ"],
        mapping: "ā"
      },
      {
        sounds: ["f"],
        mapping: "f"
      }
    ]
  },
  /**
    19. wolf (C /wʊlf/, O /ˈwʊɫf/) - wolf
      1. w - w
      2. ʊ - o
      3. l/ɫ - l
      4. f - f
   */
  {
    english: "wolf",
    transcriptions: {
      cambridge: "wʊlf",
      opendict: "ˈwʊɫf"
    },
    newEnglish: "wolf",
    breakdown: [
      {
        sounds: ["w"],
        mapping: "w"
      },
      {
        sounds: ["ʊ"],
        mapping: "o"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["l"],
          mapping: "l"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɫ"],
          mapping: "l"
        }
      ],
      {
        sounds: ["f"],
        mapping: "f"
      }
    ]
  },
  /**
    20. lion (C /ˈlaɪ.ən/, O /ˈɫaɪən/) - laín
      1. l/ɫ - l
      2. aɪ - aí
      3. ən - n
   */
  {
    english: "lion",
    transcriptions: {
      cambridge: "ˈlaɪ.ən",
      opendict: "ˈɫaɪən"
    },
    newEnglish: "laín",
    breakdown: [
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["l"],
          mapping: "l"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɫ"],
          mapping: "l"
        }
      ],
      {
        sounds: ["aɪ"],
        mapping: "aí"
      },
      {
        sounds: ["ən"],
        mapping: "n"
      }
    ]
  },
  /**
    21. mouse (C /maʊs/, O /ˈmaʊs/) - mays
      1. m - m
      2. aʊ - ay
      3. s - s
   */
  {
    english: "mouse",
    transcriptions: {
      cambridge: "maʊs",
      opendict: "ˈmaʊs"
    },
    newEnglish: "mays",
    breakdown: [
      {
        sounds: ["m"],
        mapping: "m"
      },
      {
        sounds: ["aʊ"],
        mapping: "ay"
      },
      {
        sounds: ["s"],
        mapping: "s"
      }
    ]
  },
  /**
    22. dinosaur (C /ˈdaɪ.nə.sɔːr/, O /ˈdaɪnəˌsɔɹ/) - daínaso
      1. d - d
      2. aɪ - aí
      3. n - n
      4. ə - a
      5. s - s
      6. ɔːr/ɔɹ - o
   */
  {
    english: "dinosaur",
    transcriptions: {
      cambridge: "ˈdaɪ.nə.sɔːr",
      opendict: "ˈdaɪnəˌsɔɹ"
    },
    newEnglish: "daínaso",
    breakdown: [
      {
        sounds: ["d"],
        mapping: "d"
      },
      {
        sounds: ["aɪ"],
        mapping: "aí"
      },
      {
        sounds: ["n"],
        mapping: "n"
      },
      {
        sounds: ["ə"],
        mapping: "a"
      },
      {
        sounds: ["s"],
        mapping: "s"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɔːr"],
          mapping: "o"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɔɹ"],
          mapping: "o"
        }
      ]
    ]
  },
  /**
    23. penguin (C /ˈpeŋ.ɡwɪn/, O /ˈpɛŋɡwən/) - pengwn
      1. p - p
      2. e/ɛ - e
      3. ŋ - n
      4. ɡ - g
      5. w - w
      6. ɪn$/, ən - n
   */
  {
    english: "penguin",
    transcriptions: {
      cambridge: "ˈpeŋ.ɡwɪn",
      opendict: "ˈpɛŋɡwən"
    },
    newEnglish: "pengwn",
    breakdown: [
      {
        sounds: ["p"],
        mapping: "p"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["e"],
          mapping: "e"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɛ"],
          mapping: "e"
        }
      ],
      {
        sounds: ["ŋ"],
        mapping: "n"
      },
      {
        sounds: ["ɡ"],
        mapping: "g"
      },
      {
        sounds: ["w"],
        mapping: "w"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɪn$"],
          mapping: "n"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ən"],
          mapping: "n"
        }
      ]
    ]
  },
  /**
    24. rabbit (C /ˈræb.ɪt/, O /ˈɹæbət/, /ˈɹæbɪt/) - rābit
      1. r/ɹ - r
      2. æ - ā
      3. bi//bə/bɪ - bi
      4. t - t
   */
  {
    english: "rabbit",
    transcriptions: {
      cambridge: "ˈræb.ɪt",
      opendict: "ˈɹæbət"
    },
    newEnglish: "rābit",
    breakdown: [
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r"
        }
      ],
      {
        sounds: ["æ"],
        mapping: "ā"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["bi"],
          mapping: "bi"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["bə"],
          mapping: "bi"
        }
      ],
      {
        sounds: ["t"],
        mapping: "t"
      }
    ]
  },
  /**
    25. yak (C /jæk/, O /ˈjæk/) - íāk
      1. j - í
      2. æ - ā
      3. k - k
   */
  {
    english: "yak",
    transcriptions: {
      cambridge: "jæk",
      opendict: "ˈjæk"
    },
    newEnglish: "íāk",
    breakdown: [
      {
        sounds: ["j"],
        mapping: "í"
      },
      {
        sounds: ["æ"],
        mapping: "ā"
      },
      {
        sounds: ["k"],
        mapping: "k"
      }
    ]
  },
  /**
    26. horse (C /hɔːrs/, O /ˈhɔɹs/) - hos
      1. h - h
      2. ɔːr/ɔɹ - o
      3. s - s
   */
  {
    english: "horse",
    transcriptions: {
      cambridge: "hɔːrs",
      opendict: "ˈhɔɹs"
    },
    newEnglish: "hos",
    breakdown: [
      {
        sounds: ["h"],
        mapping: "h"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɔːr"],
          mapping: "o"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɔɹ"],
          mapping: "o"
        }
      ],
      {
        sounds: ["s"],
        mapping: "s"
      }
    ]
  },
  /**
    27. green (C /ɡriːn/, O /ˈɡɹin/) - grin
      1. ɡ - g
      2. r/ɹ - r
      3. i - i
      4. n - n
   */
  {
    english: "green",
    transcriptions: {
      cambridge: "ɡriːn",
      opendict: "ˈɡɹin"
    },
    newEnglish: "grin",
    breakdown: [
      {
        sounds: ["ɡ"],
        mapping: "g"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r"
        }
      ],
      {
        sounds: ["i"],
        mapping: "i"
      },
      {
        sounds: ["n"],
        mapping: "n"
      }
    ]
  },
  /**
    28. pink (C /pɪŋk/, O /ˈpɪŋk/) - pink
      1. p - p
      2. ɪ - i
      3. ŋ - n
      4. k - k
   */
  {
    english: "pink",
    transcriptions: {
      cambridge: "pɪŋk",
      opendict: "ˈpɪŋk"
    },
    newEnglish: "pink",
    breakdown: [
      {
        sounds: ["p"],
        mapping: "p"
      },
      {
        sounds: ["ɪ"],
        mapping: "i"
      },
      {
        sounds: ["ŋ"],
        mapping: "n"
      },
      {
        sounds: ["k"],
        mapping: "k"
      }
    ]
  },
  /**
    29. wood (C /wʊd/, O /ˈwʊd/) - wot
      1. w - w
      2. ʊ - o
      3. d$ - t
   */
  {
    english: "wood",
    transcriptions: {
      cambridge: "wʊd",
      opendict: "ˈwʊd"
    },
    newEnglish: "wot",
    breakdown: [
      {
        sounds: ["w"],
        mapping: "w"
      },
      {
        sounds: ["ʊ"],
        mapping: "o"
      },
      {
        sounds: ["d$"],
        mapping: "t"
      }
    ]
  },
  /**
    30. blue (C /bluː/, O /ˈbɫu/) - blu
      1. b - b
      2. l/ɫ - l
      3. u - u
   */
  {
    english: "blue",
    transcriptions: {
      cambridge: "bluː",
      opendict: "ˈbɫu"
    },
    newEnglish: "blu",
    breakdown: [
      {
        sounds: ["b"],
        mapping: "b"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["l"],
          mapping: "l"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɫ"],
          mapping: "l"
        }
      ],
      {
        sounds: ["u"],
        mapping: "u"
      }
    ]
  },
  /**
    31. dust (C /dʌst/, O /ˈdəst/) - dast
      1. dʌ/də - da
      2. s - s
      3. t - t
   */
  {
    english: "dust",
    transcriptions: {
      cambridge: "dʌst",
      opendict: "ˈdəst"
    },
    newEnglish: "dast",
    breakdown: [
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["dʌ"],
          mapping: "da"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["də"],
          mapping: "da"
        }
      ],
      {
        sounds: ["s"],
        mapping: "s"
      },
      {
        sounds: ["t"],
        mapping: "t"
      }
    ]
  },
  /**
    32. purple (C /ˈpɝː.pəl/, O /ˈpɝpəɫ/) - pëpl
      1. p - p
      2. ɝ - ë
      3. p - p
      4. əl/əɫ$ - l
   */
  {
    english: "purple",
    transcriptions: {
      cambridge: "ˈpɝː.pəl",
      opendict: "ˈpɝpəɫ"
    },
    newEnglish: "pëpl",
    breakdown: [
      {
        sounds: ["p"],
        mapping: "p"
      },
      {
        sounds: ["ɝ"],
        mapping: "ë"
      },
      {
        sounds: ["p"],
        mapping: "p"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["əl$"],
          mapping: "l"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["əɫ$"],
          mapping: "l"
        }
      ]
    ]
  },
  /**
    33. mauve (C /moʊv/, O /ˈmɔv/) - mōf, maf
      1. m - m
      2. oʊ - ō, ɔ - a
      3. v$ - f
   */
  {
    english: "mauve",
    transcriptions: {
      cambridge: "moʊv",
      opendict: "ˈmɔv"
    },
    newEnglish: {
      cambridge: "mōf",
      opendict: "maf"
    },
    breakdown: [
      {
        sounds: ["m"],
        mapping: "m"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["oʊ"],
          mapping: "ō"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɔ"],
          mapping: "a"
        }
      ],
      {
        sounds: ["v$"],
        mapping: "f"
      }
    ]
  },
  /**
    34. sand (C /sænd/, O /ˈsænd/) - sānt
      1. s - s
      2. æ - ā
      3. n - n
      4. d$ - t
   */
  {
    english: "sand",
    transcriptions: {
      cambridge: "sænd",
      opendict: "ˈsænd"
    },
    newEnglish: "sānt",
    breakdown: [
      {
        sounds: ["s"],
        mapping: "s"
      },
      {
        sounds: ["æ"],
        mapping: "ā"
      },
      {
        sounds: ["n"],
        mapping: "n"
      },
      {
        sounds: ["d$"],
        mapping: "t"
      }
    ]
  },
  /**
    35. coffee (C /ˈkɑː.fi/, O /ˈkɑfi/, /ˈkɔfi/) - kafi
      1. k - k
      2. ɑ, //ɔ  - a
      3. f - f
      4. i - i
   */
  {
    english: "coffee",
    transcriptions: {
      cambridge: "ˈkɑː.fi",
      opendict: "ˈkɑfi"
    },
    newEnglish: "kafi",
    breakdown: [
      {
        sounds: ["k"],
        mapping: "k"
      },
      [
        {
          dictionaryKey: "opendict",
          sounds: ["ɔ"],
          mapping: "a"
        },
        {
          dictionaryKey: "any",
          sounds: ["ɑ"],
          mapping: "a"
        }
      ],
      {
        sounds: ["f"],
        mapping: "f"
      },
      {
        sounds: ["i"],
        mapping: "i"
      }
    ]
  },
  /**
    36. jade (C /dʒeɪd/, O /ˈdʒeɪd/) - djeit
      1. d - d
      2. ʒ - j
      3. eɪ - eí
      4. d$ - t
   */
  {
    english: "jade",
    transcriptions: {
      cambridge: "dʒeɪd",
      opendict: "ˈdʒeɪd"
    },
    newEnglish: "djeit",
    breakdown: [
      {
        sounds: ["d"],
        mapping: "d"
      },
      {
        sounds: ["ʒ"],
        mapping: "j"
      },
      {
        sounds: ["eɪ"],
        mapping: "eí"
      },
      {
        sounds: ["d$"],
        mapping: "t"
      }
    ]
  },
  /**
    37. gold (C /ɡoʊld/, O /ˈɡoʊɫd/) - gōlt
      1. ɡ - g
      2. oʊ - ō
      3. l/ɫ - l
      4. d$ - t
   */
  {
    english: "gold",
    transcriptions: {
      cambridge: "ɡoʊld",
      opendict: "ˈɡoʊɫd"
    },
    newEnglish: "gōlt",
    breakdown: [
      {
        sounds: ["ɡ"],
        mapping: "g"
      },
      {
        sounds: ["oʊ"],
        mapping: "ō"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["l"],
          mapping: "l"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɫ"],
          mapping: "l"
        }
      ],
      {
        sounds: ["d$"],
        mapping: "t"
      }
    ]
  },
  /**
    38. turquoise (C /ˈtɝː.kɔɪz/, O /ˈtɝkwɔɪz/) - tëkoís
      1. t - t
      2. ɝ - ë
      3. k - k
      4. ɔɪ - oí
      5. z$ - s
   */
  {
    english: "turquoise",
    transcriptions: {
      cambridge: "ˈtɝː.kɔɪz",
      opendict: "ˈtɝkwɔɪz"
    },
    newEnglish: "tëkoís",
    breakdown: [
      {
        sounds: ["t"],
        mapping: "t"
      },
      {
        sounds: ["ɝ"],
        mapping: "ë"
      },
      {
        sounds: ["k"],
        mapping: "k"
      },
      {
        sounds: ["ɔɪ"],
        mapping: "oí"
      },
      {
        sounds: ["z$"],
        mapping: "s"
      }
    ]
  },
  /**
    39. lime (C /laɪm/, O /ˈɫaɪm/) - laím
      1. l/ɫ - l
      2. aɪ - aí
      3. m - m
   */
  {
    english: "lime",
    transcriptions: {
      cambridge: "laɪm",
      opendict: "ˈɫaɪm"
    },
    newEnglish: "laím",
    breakdown: [
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["l"],
          mapping: "l"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɫ"],
          mapping: "l"
        }
      ],
      {
        sounds: ["aɪ"],
        mapping: "aí"
      },
      {
        sounds: ["m"],
        mapping: "m"
      }
    ]
  },
  /**
    40. brown (C /braʊn/, O /ˈbɹaʊn/) - brayn
      1. b - b
      2. r/ɹ - r
      3. aʊ - ay
      4. n - n
   */
  {
    english: "brown",
    transcriptions: {
      cambridge: "braʊn",
      opendict: "ˈbɹaʊn"
    },
    newEnglish: "brayn",
    breakdown: [
      {
        sounds: ["b"],
        mapping: "b"
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r"
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r"
        }
      ],
      {
        sounds: ["aʊ"],
        mapping: "ay"
      },
      {
        sounds: ["n"],
        mapping: "n"
      }
    ]
  }
];
function mappings($$payload, item) {
  const firstMapping = item[0].mapping;
  const allSameMapping = item.every((variant) => variant.mapping === firstMapping);
  if (allSameMapping) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<span${attr_style("", { color: "#000000" })}>${escape_html(firstMapping)}</span>`);
  } else {
    $$payload.out.push("<!--[!-->");
    const each_array = ensure_array_like(item);
    $$payload.out.push(`<!--[-->`);
    for (let i = 0, $$length = each_array.length; i < $$length; i++) {
      let variant = each_array[i];
      if (i > 0) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`,`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--> <span${attr_style("", { color: `#${darkDictionaryColors[variant.dictionaryKey]}` })}>${escape_html(variant.mapping)}</span>`);
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]-->`);
}
function Examples_section($$payload, $$props) {
  push();
  const i18n = {
    en: {
      title: "Word Examples",
      description: "See how common English words transform in our phonetic system",
      hideDetails: "Hide Details",
      showDetails: "Show Details",
      soundBreakdown: "Sound Breakdown",
      phoneticSounds: "Phonetic Sounds",
      newEnglishLetter: "New English Letter(s)"
    },
    ru: {
      title: "Примеры слов",
      description: "Посмотрите, как обычные английские слова преобразуются в нашей фонетической системе",
      hideDetails: "Скрыть детали",
      showDetails: "Показать детали",
      soundBreakdown: "Разбор звуков",
      phoneticSounds: "Фонетические звуки",
      newEnglishLetter: "Буквы Нового английского"
    }
  };
  const { locale } = $$props;
  const t = i18n[locale];
  let expandedExample = null;
  const each_array_1 = ensure_array_like(examples);
  $$payload.out.push(`<section class="mb-5"><h2>${escape_html(t.title)}</h2> <div class="card"><div class="card-body"><p class="lead">${escape_html(t.description)}:</p> <ul class="list-group list-group-flush"><!--[-->`);
  for (let index = 0, $$length = each_array_1.length; index < $$length; index++) {
    let example = each_array_1[index];
    const each_array_2 = ensure_array_like(Object.entries(example.transcriptions));
    $$payload.out.push(`<li class="list-group-item"><div class="d-flex justify-content-between align-items-center cursor-pointer"${attr("role", expandedExample === index ? t.hideDetails : t.showDetails)}${attr_style("", { cursor: "pointer" })}><div><strong>${escape_html(example.english)}</strong> <span class="ms-2"><!--[-->`);
    for (let i = 0, $$length2 = each_array_2.length; i < $$length2; i++) {
      let [dict, transcription] = each_array_2[i];
      if (i > 0) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`,`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--> <span${attr_style("", { color: `#${darkDictionaryColors[dict]}` })}>${escape_html(transcription)}</span>`);
    }
    $$payload.out.push(`<!--]--></span> <span class="ms-2">→</span> <span class="ms-2 fw-bold">`);
    if (typeof example.newEnglish === "string") {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<span class="text-dark">${escape_html(example.newEnglish)}</span>`);
    } else {
      $$payload.out.push("<!--[!-->");
      const each_array_3 = ensure_array_like(Object.entries(example.newEnglish));
      $$payload.out.push(`<!--[-->`);
      for (let i = 0, $$length2 = each_array_3.length; i < $$length2; i++) {
        let [dict, translation] = each_array_3[i];
        if (i > 0) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`,`);
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]--> <span${attr_style("", { color: `#${darkDictionaryColors[dict]}` })}>${escape_html(translation)}</span>`);
      }
      $$payload.out.push(`<!--]-->`);
    }
    $$payload.out.push(`<!--]--></span></div> <button class="btn btn-sm btn-outline-primary">${escape_html(expandedExample === index ? t.hideDetails : t.showDetails)}</button></div> `);
    if (expandedExample === index) {
      $$payload.out.push("<!--[-->");
      const each_array_4 = ensure_array_like(example.breakdown);
      $$payload.out.push(`<div class="mt-3"><h6>${escape_html(t.soundBreakdown)}:</h6> <table class="table table-sm"><thead><tr><th>${escape_html(t.phoneticSounds)}</th><th>${escape_html(t.newEnglishLetter)}</th></tr></thead><tbody><!--[-->`);
      for (let $$index_6 = 0, $$length2 = each_array_4.length; $$index_6 < $$length2; $$index_6++) {
        let item = each_array_4[$$index_6];
        if (Array.isArray(item)) {
          $$payload.out.push("<!--[-->");
          const each_array_5 = ensure_array_like(item);
          $$payload.out.push(`<tr><td><!--[-->`);
          for (let $$index_4 = 0, $$length3 = each_array_5.length; $$index_4 < $$length3; $$index_4++) {
            let variant = each_array_5[$$index_4];
            const each_array_6 = ensure_array_like(variant.sounds);
            $$payload.out.push(`<span class="me-2"><!--[-->`);
            for (let $$index_3 = 0, $$length4 = each_array_6.length; $$index_3 < $$length4; $$index_3++) {
              let sound = each_array_6[$$index_3];
              $$payload.out.push(`<span class="badge rounded-pill me-1 font-monospace"${attr_style("", {
                "background-color": `#${lightDictionaryColors[variant.dictionaryKey]}`,
                color: "#000000",
                padding: "6px 12px",
                "margin-bottom": "6px",
                "font-size": "1.2rem",
                "font-weight": "normal"
              })}>${escape_html(sound)}</span>`);
            }
            $$payload.out.push(`<!--]--></span>`);
          }
          $$payload.out.push(`<!--]--></td><td><strong${attr_style("", {
            "font-size": "1.5rem",
            display: "inline-block",
            padding: "4px 0"
          })}>`);
          mappings($$payload, item);
          $$payload.out.push(`<!----></strong></td></tr>`);
        } else {
          $$payload.out.push("<!--[!-->");
          const each_array_7 = ensure_array_like(item.sounds);
          $$payload.out.push(`<tr><td><!--[-->`);
          for (let $$index_5 = 0, $$length3 = each_array_7.length; $$index_5 < $$length3; $$index_5++) {
            let sound = each_array_7[$$index_5];
            $$payload.out.push(`<span class="badge rounded-pill me-2 font-monospace"${attr_style("", {
              "background-color": `#${lightDictionaryColors["any"]}`,
              color: "#000000",
              border: "1px solid #CCCCCC",
              padding: "6px 12px",
              "margin-bottom": "6px",
              "font-size": "1.2rem",
              "font-weight": "normal"
            })}>${escape_html(sound)}</span>`);
          }
          $$payload.out.push(`<!--]--></td><td><strong${attr_style("", {
            "font-size": "1.5rem",
            display: "inline-block",
            padding: "4px 0"
          })}>${escape_html(item.mapping)}</strong></td></tr>`);
        }
        $$payload.out.push(`<!--]-->`);
      }
      $$payload.out.push(`<!--]--></tbody></table></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></li>`);
  }
  $$payload.out.push(`<!--]--></ul></div></div></section>`);
  pop();
}
const rules = [
  /*
    [a, ɑ, /ɔ, ʌ, ə$] - a
    aɪ - aí
    aʊ - ay
    æ - ā
    b - b
    bə - bi
  */
  {
    sources: [
      {
        dictionaryKey: "opendict",
        sounds: ["ɔ"]
      },
      {
        dictionaryKey: "any",
        sounds: ["a", "ɑ", "ʌ", "ə$"]
      }
    ],
    mapping: "a",
    examples: ["d*u*st", "b*u*lk"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["aɪ"]
      }
    ],
    mapping: "aí",
    examples: ["l*i*me", "d*i*nosaur"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["aʊ"]
      }
    ],
    mapping: "ay",
    examples: ["m*ou*se", "br*ow*n"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["æ"]
      }
    ],
    mapping: "ā",
    examples: ["r*a*t", "c*a*t"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["b"]
      }
    ],
    mapping: "b",
    examples: ["*b*ear", "ze*b*ra"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["bə"]
      }
    ],
    mapping: "bi",
    examples: ["*be*aver", "rab*bi*t"]
  },
  /*
    tʃ - č
    d - d
    də - da
    [e, /ɛ, ə, ɚ/ɝ$] - e
    eɪ - eí
    ɝ - ë
  */
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["tʃ"]
      }
    ],
    mapping: "č",
    examples: ["approa*ch*", "*ch*icken"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["d"]
      }
    ],
    mapping: "d",
    examples: ["*d*og", "*d*inosaur"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["də"]
      }
    ],
    mapping: "da",
    examples: ["*du*st", "aque*du*ct"]
  },
  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["ɚ$"]
      },
      {
        dictionaryKey: "opendict",
        sounds: ["ɛ", "ɝ$"]
      },
      {
        dictionaryKey: "any",
        sounds: ["e", "ə"]
      }
    ],
    mapping: "e",
    examples: ["r*e*d", "b*e*ar"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["eɪ"]
      }
    ],
    mapping: "eí",
    examples: ["sn*a*ke", "sh*a*pe"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["ɝ"]
      }
    ],
    mapping: "ë",
    examples: ["t*ur*tle", "p*ur*ple"]
  },
  /*
    [f, θ, v$] - f
    ɡ - g
    h - h
    [i, ɪ] - i
    ɪˈr/ɝˈ - ir
    əs$ - is
  */
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["f", "θ", "v$"]
      }
    ],
    mapping: "f",
    examples: ["*f*rog", "*f*eather"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["ɡ"]
      }
    ],
    mapping: "g",
    examples: ["*g*oat", "*g*reen"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["h"]
      }
    ],
    mapping: "h",
    examples: ["*h*orse", "*h*ouse"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["i", "ɪ"]
      }
    ],
    mapping: "i",
    examples: ["p*i*g", "z*e*bra"]
  },
  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["ɪˈr"]
      },
      {
        dictionaryKey: "opendict",
        sounds: ["ɝˈ"]
      }
    ],
    mapping: "ir",
    examples: ["g*ir*affe", "b*ur*rito"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["əs$"]
      }
    ],
    mapping: "is",
    examples: ["vers*us*"]
  },
  /*
    j - í
    ʒ - j
    [k, ɡ$] - k
    [l/ɫ, əl/əɫ$] - l
    lə - le
    m - m
  */
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["j"]
      }
    ],
    mapping: "í",
    examples: ["*y*ak", "*y*acht"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["ʒ"]
      }
    ],
    mapping: "j",
    examples: ["televi*sio*n", "*j*ade"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["k", "ɡ$"]
      }
    ],
    mapping: "k",
    examples: ["pin*k*", "*c*offee"]
  },
  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["l", "əl$"]
      },
      {
        dictionaryKey: "opendict",
        sounds: ["ɫ", "əɫ$"]
      }
    ],
    mapping: "l",
    examples: ["*l*ion", "go*l*d"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["lə"]
      }
    ],
    mapping: "le",
    examples: ["te*le*vision"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["m"]
      }
    ],
    mapping: "m",
    examples: ["*m*auve", "*m*ouse"]
  },
  /*
    [n, ŋ, ən, ɪn/$] - n
    nə - na
    [ʊ, ɔːr/ɔɹ] - o
    ɔɪ - oí
    oʊ - ō
    [p, b$] - p
  */
  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["ɪn$"]
      },
      {
        dictionaryKey: "any",
        sounds: ["n", "ŋ", "ən"]
      }
    ],
    mapping: "n",
    examples: ["brow*n*", "sa*n*d"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["nə"]
      }
    ],
    mapping: "na",
    examples: ["di*no*saur"]
  },
  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["ɔːr"]
      },
      {
        dictionaryKey: "opendict",
        sounds: ["ɔɹ"]
      },
      {
        dictionaryKey: "any",
        sounds: ["ʊ"]
      }
    ],
    mapping: "o",
    examples: ["w*o*lf", "h*o*rse"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["ɔɪ"]
      }
    ],
    mapping: "oí",
    examples: ["turq*uoi*se"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["oʊ"]
      }
    ],
    mapping: "ō",
    examples: ["g*oa*t", "appr*oa*ch"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["p", "b$"]
      }
    ],
    mapping: "p",
    examples: ["*p*ig", "*p*enguin"]
  },
  /*
    r/ɹ - r
    rə - ra
    r/ɹ$ - *
    [s, z$] - s
    ʃ - š
    [t, t̬/, d$] - t
  */
  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["r"]
      },
      {
        dictionaryKey: "opendict",
        sounds: ["ɹ"]
      }
    ],
    mapping: "r",
    examples: ["*r*ed", "f*r*og"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["rə"]
      }
    ],
    mapping: "ra",
    examples: ["zeb*ra*"]
  },
  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["r$"]
      },
      {
        dictionaryKey: "opendict",
        sounds: ["ɹ$"]
      }
    ],
    mapping: "",
    examples: ["bea*r*"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["s", "z$"]
      }
    ],
    mapping: "s",
    examples: ["*s*nake", "*s*un"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["ʃ"]
      }
    ],
    mapping: "š",
    examples: ["*sh*eep", "*sh*epherd"]
  },
  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["t̬"]
      },
      {
        dictionaryKey: "any",
        sounds: ["t", "d$"]
      }
    ],
    mapping: "t",
    examples: ["re*d*", "goa*t*"]
  },
  /*
    [u, jə] - u
    [v, ð] - v
    w - w
    wə - we
    z - z
  */
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["u", "jə"]
      }
    ],
    mapping: "u",
    examples: ["bl*ue*"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["v", "ð"]
      }
    ],
    mapping: "v",
    examples: ["fea*th*er", "bea*v*er"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["w"]
      }
    ],
    mapping: "w",
    examples: ["*w*olf", "*w*ood"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["wə"]
      }
    ],
    mapping: "we",
    examples: ["aq*ue*duct"]
  },
  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["z"]
      }
    ],
    mapping: "z",
    examples: ["*z*ebra", "*z*oo"]
  }
  // {
  //   sources: [
  //     {
  //       dictionaryKey: "",
  //       sounds: [],
  //     },
  //   ],
  //   mapping: "",
  //   examples: [],
  // },
];
function dictionaryItem($$payload, key, name, description, url) {
  $$payload.out.push(`<li class="list-group-item"${attr_style("", { "border-left": `4px solid #${lightDictionaryColors[key]}` })}><strong><a${attr("href", url)} target="_blank" rel="noopener noreferrer">${escape_html(name)}</a></strong> <p class="mb-0 text-muted">${escape_html(description)}</p></li>`);
}
function sourceItem($$payload, name, description, url) {
  $$payload.out.push(`<li class="list-group-item"><strong><a${attr("href", url)} target="_blank" rel="noopener noreferrer">${escape_html(name)}</a></strong> <p class="mb-0 text-muted">${escape_html(description)}</p></li>`);
}
function dialectItem($$payload, name, description, priority) {
  $$payload.out.push(`<li class="list-group-item"><div class="d-flex align-items-center"><span class="badge bg-primary me-2">${escape_html(priority)}</span> <div><strong>${escape_html(name)}</strong> <p class="mb-0 text-muted">${escape_html(description)}</p></div></div></li>`);
}
function highlightedExample($$payload, example) {
  $$payload.out.push(`${html((() => {
    const [before, middle, after] = example.split("*");
    return [
      `<span>${before}</span>`,
      `<strong style="color:#d63384">${middle}</strong>`,
      `<span>${after}</span>`
    ].join("");
  })())}`);
}
function _page($$payload, $$props) {
  push();
  const alphabet = [
    "Aa",
    "Āā",
    "Bb",
    "Čč",
    "Dd",
    "Ee",
    "Ëë",
    "Ff",
    "Gg",
    "Hh",
    "Ii",
    "Íí",
    "Jj",
    "Kk",
    "Ll",
    "Mm",
    "Nn",
    "Oo",
    "Ōō",
    "Pp",
    "Rr",
    "Ss",
    "Šš",
    "Tt",
    "Uu",
    "Vv",
    "Ww",
    "Yy",
    "Zz"
  ];
  const i18n = {
    en: {
      _page: { title: "New English — Commune" },
      title: "New English: A Phonetic Revolution",
      problem: {
        title: "The Challenge",
        description: "English writing and pronunciation have diverged significantly over centuries, creating numerous challenges.",
        difficulties: {
          description: "The current English language presents several difficulties",
          list: [
            "Significant difference between written and spoken forms",
            "Inconsistent pronunciation rules with many exceptions",
            "Difficult accents that vary across regions",
            "Overcomplicated spelling rules that don't match pronunciation",
            "Natural reduction in spoken language not reflected in writing",
            "Redundant elements like multiple ways to represent the same sound"
          ]
        }
      },
      idea: {
        title: "Early Attempts",
        description: {
          1: "New English: Learning from Early Challenges in Phonetic Reform",
          2: `Our journey began with a simple question: "How might English evolve over time?" Our first attempt at creating New English faced significant challenges, as it incorporated overly aggressive simplifications that ultimately compromised the system's integrity.`,
          3: "This early version relied on online translator pronunciations rather than established dictionaries. This approach proved problematic, as it lacked the linguistic foundation necessary to establish consistent rules. The resulting translations were unstable, inconsistent across platforms, and resistant to systematic automation."
        }
      },
      implementation: {
        title: "Our Implementation",
        description: "A systematic approach to reforming English spelling and pronunciation.",
        features: {
          description: "Our New English implementation features",
          list: [
            "Words written exactly as they are pronounced",
            "Reduction of indefinite articles to their essential forms",
            'Simplification of definite articles and demonstratives to "da"',
            "Consistent phonetic representation for all sounds",
            "Elimination of silent letters and redundant spellings",
            "Preservation of word recognition while improving logical consistency"
          ]
        }
      },
      dictionaries: {
        title: "Reference Dictionaries",
        description: "Our system is based on established phonetic standards from these authoritative sources",
        cambridge: {
          name: "Cambridge English Dictionary",
          description: "A comprehensive dictionary of English with phonetic transcriptions"
        },
        opendict: {
          name: "Open Dictionary",
          description: "Open-licensed dictionary data"
        }
      },
      sources: {
        title: "Research Sources",
        description: "Our approach is informed by these linguistic resources",
        englishPhonology: {
          name: "English Phonology - Wikipedia",
          description: "Overview of the sound system of the English language"
        },
        internationalPhoneticAlphabet: {
          name: "International Phonetic Alphabet",
          description: "Standardized representation of sounds in written form"
        },
        americanIpaChart: {
          name: "American IPA Chart",
          description: "Chart of the American IPA"
        }
      },
      dialects: {
        title: "Reference Dialects",
        description: "Our phonetic system prioritizes these dialects in descending order",
        generalAmerican: {
          name: "General American",
          description: "The accent of American English most commonly perceived as neutral"
        },
        standardEnglish: {
          name: "Standard English",
          description: "The standard accent of Standard English in the United Kingdom"
        },
        localDialect: {
          name: "Local Dialect",
          description: "The form of English used in the local area"
        }
      },
      alphabet: { title: "New English Alphabet" },
      rules: {
        title: "Phonetic Rules",
        description: "Each sound is consistently represented by specific letter(s)",
        phoneticSounds: "Phonetic Sounds",
        newEnglishLetter: "New English Letter(s)",
        exampleWords: "Example Words"
      },
      nuances: {
        title: "Language Nuances",
        description: "New English includes several practical modifications to improve consistency and pronunciation flow",
        pronoun: {
          title: "Personal Pronouns",
          description: "New English uses 'mi' instead of 'I'/'me' for consistency with other pronoun forms and to eliminate the arbitrary capitalization rule."
        },
        interrogatives: {
          title: "Question Words",
          description: "Question words often use the '-sa' suffix to avoid consonant clusters and improve pronunciation flow:",
          tableHeaders: {
            original: "Original",
            preferred: "Preferred",
            expected: "Expected"
          },
          examples: [
            {
              before: "what",
              after: { preferred: "wotsa", expected: "wot" }
            },
            { before: "who", after: { preferred: "hysa", expected: "hy" } },
            {
              before: "when",
              after: { preferred: "wensa", expected: "wen" }
            },
            {
              before: "where",
              after: { preferred: "wersa", expected: "wer" }
            },
            {
              before: "which",
              after: { preferred: "wičsa", expected: "wič" }
            },
            {
              before: "why",
              after: { preferred: "waísa", expected: "waí" }
            },
            {
              before: "how",
              after: { preferred: "haysa", expected: "hay" }
            },
            {
              before: "there",
              after: { preferred: "tersa", expected: "ter" }
            },
            {
              before: "then",
              after: { preferred: "tensa", expected: "ten" }
            }
          ],
          note: `The '-sa' suffix originated from early language development and helps avoid difficult consonant combinations in speech. Its original meaning was to use ending from "there's a".`
        },
        irregularVerbs: {
          title: "Regular Verb Forms",
          description: "New English uses regular '-it' endings for past tense instead of irregular forms, creating consistent conjugation patterns.",
          tableHeaders: {
            english: "English",
            newEnglish: "New English",
            common: "Common",
            past: "Past",
            pastParticiple: "Past Participle"
          },
          examples: [
            {
              english: { common: "cut", past: "cut", pastParticiple: "cut" },
              newEnglish: { common: "kat", past: "katit" }
            },
            {
              english: { common: "speak", past: "spoke", pastParticiple: "spoken" },
              newEnglish: { common: "spik", past: "spikit" }
            },
            {
              english: { common: "know", past: "knew", pastParticiple: "known" },
              newEnglish: { common: "nō", past: "nōit" }
            },
            {
              english: { common: "bring", past: "brought", pastParticiple: "brought" },
              newEnglish: { common: "brin", past: "brinit" }
            },
            {
              english: { common: "see", past: "saw", pastParticiple: "seen" },
              newEnglish: {
                common: "si",
                past: "sit",
                note: "double letter cannot be preserved because of language mechanics"
              }
            }
          ]
        }
      }
    },
    ru: {
      _page: {
        title: "Новый английский — Коммуна"
      },
      title: "Новый английский: фонетическая революция",
      problem: {
        title: "Проблема",
        description: "Правописание и произношение в английском языке значительно разошлись за века, создавая множество трудностей.",
        difficulties: {
          description: "Современный английский язык создаёт ряд затруднений",
          list: [
            "Существенное расхождение между письменной и устной формами",
            "Непоследовательные правила произношения с множеством исключений",
            "Сложные акценты, сильно различающиеся по регионам",
            "Сложные правила орфографии, не соответствующие произношению",
            "Естественные сокращения в устной речи не отражаются на письме",
            "Избыточные элементы, такие как разные способы обозначения одного и того же звука"
          ]
        }
      },
      idea: {
        title: "Ранние попытки",
        description: {
          1: "Новый английский: уроки ранних трудностей фонетической реформы",
          2: "Наш путь начался с простого вопроса: «Как английский может развиваться со временем?» Первая попытка создать Новый английский столкнулась с серьёзными трудностями из-за чрезмерных упрощений, которые в итоге подорвали целостность системы.",
          3: "Ранняя версия опиралась на произношения онлайн-переводчиков вместо признанных словарей. Такой подход оказался проблематичным, поскольку не имел лингвистической базы для создания последовательных правил. В результате переводы были нестабильны, непоследовательны между платформами и не поддавались автоматизации."
        }
      },
      implementation: {
        title: "Наша реализация",
        description: "Системный подход к реформе английской орфографии и произношения.",
        features: {
          description: "Особенности нашей реализации Нового английского",
          list: [
            "Слова пишутся точно так, как произносятся",
            "Неопределённые артикли сведены к их сути",
            "Определённые артикли и указательные слова упрощены до «da»",
            "Последовательное фонетическое обозначение всех звуков",
            "Исключение немых букв и избыточных написаний",
            "Сохранение узнаваемости слов при улучшении логической согласованности"
          ]
        }
      },
      dictionaries: {
        title: "Справочные словари",
        description: "Наша система основывается на признанных фонетических стандартах из этих авторитетных источников",
        cambridge: {
          name: "Кембриджский словарь английского языка",
          description: "Обширный словарь английского языка с фонетическими транскрипциями"
        },
        opendict: {
          name: "OpenDict",
          description: "Свободно-лицензированные данные словаря"
        }
      },
      sources: {
        title: "Исследовательские источники",
        description: "Наш подход основан на следующих лингвистических ресурсах",
        englishPhonology: {
          name: "Фонология английского языка — Википедия",
          description: "Обзор звуковой системы английского языка"
        },
        internationalPhoneticAlphabet: {
          name: "Международный фонетический алфавит",
          description: "Стандартизированное представление звуков в письменной форме"
        },
        americanIpaChart: {
          name: "IPA-таблица американского английского",
          description: "Таблица фонем американского английского"
        }
      },
      dialects: {
        title: "Базовые диалекты",
        description: "Наша фонетическая система отдаёт приоритет этим диалектам в порядке убывания",
        generalAmerican: {
          name: "Общий американский",
          description: "Акцент американского английского, воспринимаемый как нейтральный"
        },
        standardEnglish: {
          name: "Стандартный английский",
          description: "Стандартный акцент британского английского"
        },
        localDialect: {
          name: "Местный диалект",
          description: "Форма английского языка, используемая в конкретной местности"
        }
      },
      alphabet: {
        title: "Алфавит Нового английского"
      },
      rules: {
        title: "Фонетические правила",
        description: "Каждый звук последовательно обозначается определённой буквой или буквами",
        phoneticSounds: "Фонетические звуки",
        newEnglishLetter: "Буквы Нового английского",
        exampleWords: "Примеры слов"
      },
      nuances: {
        title: "Особенности языка",
        description: "Новый английский включает несколько практических изменений для улучшения согласованности и удобства произношения",
        pronoun: {
          title: "Личные местоимения",
          description: "Новый английский использует 'mi' вместо 'I'/'me' для согласованности с другими формами местоимений и устранения произвольного правила заглавных букв."
        },
        interrogatives: {
          title: "Вопросительные слова",
          description: "Вопросительные слова часто используют суффикс '-sa' для избежания скопления согласных и улучшения произношения:",
          tableHeaders: {
            original: "Оригинал",
            preferred: "Предпочтительно",
            expected: "Ожидаемо"
          },
          examples: [
            {
              before: "what",
              after: { preferred: "wotsa", expected: "wot" }
            },
            { before: "who", after: { preferred: "hysa", expected: "hy" } },
            {
              before: "when",
              after: { preferred: "wensa", expected: "wen" }
            },
            {
              before: "where",
              after: { preferred: "wersa", expected: "wer" }
            },
            {
              before: "which",
              after: { preferred: "wičsa", expected: "wič" }
            },
            {
              before: "why",
              after: { preferred: "waísa", expected: "waí" }
            },
            {
              before: "how",
              after: { preferred: "haysa", expected: "hay" }
            },
            {
              before: "there",
              after: { preferred: "tersa", expected: "ter" }
            },
            {
              before: "then",
              after: { preferred: "tensa", expected: "ten" }
            }
          ],
          note: `Суффикс '-sa' возник при ранней разработке языка и помогает избежать сложных сочетаний согласных в речи. Его первоначальным значением было использование окончания от "there's a".`
        },
        irregularVerbs: {
          title: "Правильные формы глаголов",
          description: "Новый английский использует регулярные окончания '-it' для прошедшего времени вместо неправильных форм, создавая последовательные модели спряжения.",
          tableHeaders: {
            english: "Английский",
            newEnglish: "Новый английский",
            common: "Обычная форма",
            past: "Прошедшее время",
            pastParticiple: "Причастие прошедшего времени"
          },
          examples: [
            {
              english: { common: "cut", past: "cut", pastParticiple: "cut" },
              newEnglish: { common: "kat", past: "katit" }
            },
            {
              english: { common: "speak", past: "spoke", pastParticiple: "spoken" },
              newEnglish: { common: "spik", past: "spikit" }
            },
            {
              english: { common: "know", past: "knew", pastParticiple: "known" },
              newEnglish: { common: "nō", past: "nōit" }
            },
            {
              english: { common: "bring", past: "brought", pastParticiple: "brought" },
              newEnglish: { common: "brin", past: "brinit" }
            },
            {
              english: { common: "see", past: "saw", pastParticiple: "seen" },
              newEnglish: {
                common: "si",
                past: "sit",
                note: "сдвоенный звук не может быть сохранён из-за механики языка"
              }
            }
          ]
        }
      }
    }
  };
  const { data } = $$props;
  const { locale } = data;
  const t = i18n[locale];
  const each_array = ensure_array_like(t.problem.difficulties.list);
  const each_array_1 = ensure_array_like(t.implementation.features.list);
  const each_array_2 = ensure_array_like(alphabet);
  const each_array_3 = ensure_array_like(rules);
  const each_array_7 = ensure_array_like(t.nuances.interrogatives.examples);
  const each_array_8 = ensure_array_like(t.nuances.irregularVerbs.examples);
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;
  });
  $$payload.out.push(`<div class="container my-5"><div class="responsive-container"><h1 class="mb-4">${escape_html(t.title)}</h1> <section class="mb-5"><h2>${escape_html(t.problem.title)}</h2> <div class="card"><div class="card-body"><p class="lead">${escape_html(t.problem.description)}</p> <p>${escape_html(t.problem.difficulties.description)}:</p> <ul><!--[-->`);
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let difficulty = each_array[$$index];
    $$payload.out.push(`<li>${escape_html(difficulty)}</li>`);
  }
  $$payload.out.push(`<!--]--></ul></div></div></section> <section class="mb-5"><h2>${escape_html(t.idea.title)}</h2> <div class="card"><div class="card-body"><p class="lead">${escape_html(t.idea.description[1])}</p> <p>${escape_html(t.idea.description[2])}</p> <p>${escape_html(t.idea.description[3])}</p></div></div></section> <section class="mb-5"><h2>${escape_html(t.implementation.title)}</h2> <div class="card"><div class="card-body"><p class="lead">${escape_html(t.implementation.description)}</p> <p>${escape_html(t.implementation.features.description)}:</p> <ul><!--[-->`);
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let feature = each_array_1[$$index_1];
    $$payload.out.push(`<li>${escape_html(feature)}</li>`);
  }
  $$payload.out.push(`<!--]--></ul></div></div></section> <section class="mb-5"><h2>${escape_html(t.dictionaries.title)}</h2> <div class="card"><div class="card-body"><p class="lead">${escape_html(t.dictionaries.description)}:</p> <ul class="list-group list-group-flush">`);
  dictionaryItem($$payload, "cambridge", t.dictionaries.cambridge.name, t.dictionaries.cambridge.description, "https://dictionary.cambridge.org");
  $$payload.out.push(`<!----> `);
  dictionaryItem($$payload, "opendict", t.dictionaries.opendict.name, t.dictionaries.opendict.description, "https://open-dict-data.github.io");
  $$payload.out.push(`<!----></ul></div></div></section> <section class="mb-5"><h2>${escape_html(t.sources.title)}</h2> <div class="card"><div class="card-body"><p class="lead">${escape_html(t.sources.description)}:</p> <ul class="list-group list-group-flush">`);
  sourceItem($$payload, t.sources.englishPhonology.name, t.sources.englishPhonology.description, "https://en.wikipedia.org/wiki/English_phonology");
  $$payload.out.push(`<!----> `);
  sourceItem($$payload, t.sources.internationalPhoneticAlphabet.name, t.sources.internationalPhoneticAlphabet.description, "https://www.internationalphoneticalphabet.org/");
  $$payload.out.push(`<!----> `);
  sourceItem($$payload, t.sources.americanIpaChart.name, t.sources.americanIpaChart.description, "https://americanipachart.com/");
  $$payload.out.push(`<!----></ul></div></div></section> <section class="mb-5"><h2>${escape_html(t.dialects.title)}</h2> <div class="card"><div class="card-body"><p class="lead">${escape_html(t.dialects.description)}:</p> <ul class="list-group list-group-flush">`);
  dialectItem($$payload, t.dialects.generalAmerican.name, t.dialects.generalAmerican.description, 1);
  $$payload.out.push(`<!----> `);
  dialectItem($$payload, t.dialects.standardEnglish.name, t.dialects.standardEnglish.description, 2);
  $$payload.out.push(`<!----> `);
  dialectItem($$payload, t.dialects.localDialect.name, t.dialects.localDialect.description, 3);
  $$payload.out.push(`<!----></ul></div></div></section> <section class="mb-5"><h2>${escape_html(t.alphabet.title)}</h2> <div class="card"><div class="card-body"><div class="row row-cols-2 row-cols-md-3 row-cols-lg-6 g-2"><!--[-->`);
  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
    let item = each_array_2[$$index_2];
    $$payload.out.push(`<div class="col"><div class="card h-100"><div class="card-body text-center"><h3 class="card-title">${escape_html(item)}</h3></div></div></div>`);
  }
  $$payload.out.push(`<!--]--></div></div></div></section> <section class="mb-5"><h2>${escape_html(t.rules.title)}</h2> <div class="card"><div class="card-body"><p class="lead">${escape_html(t.rules.description)}:</p> <div class="table-responsive"><table class="table table-striped"><thead><tr><th>${escape_html(t.rules.phoneticSounds)}</th><th>${escape_html(t.rules.newEnglishLetter)}</th><th>${escape_html(t.rules.exampleWords)}</th></tr></thead><tbody><!--[-->`);
  for (let $$index_6 = 0, $$length = each_array_3.length; $$index_6 < $$length; $$index_6++) {
    let rule = each_array_3[$$index_6];
    const each_array_4 = ensure_array_like(rule.sources);
    const each_array_6 = ensure_array_like(rule.examples);
    $$payload.out.push(`<tr><td><!--[-->`);
    for (let $$index_4 = 0, $$length2 = each_array_4.length; $$index_4 < $$length2; $$index_4++) {
      let source = each_array_4[$$index_4];
      const each_array_5 = ensure_array_like(source.sounds);
      $$payload.out.push(`<div class="mb-1"><!--[-->`);
      for (let $$index_3 = 0, $$length3 = each_array_5.length; $$index_3 < $$length3; $$index_3++) {
        let sound = each_array_5[$$index_3];
        $$payload.out.push(`<span class="badge rounded-pill me-2 font-freemono"${attr_style("", {
          "background-color": `#${lightDictionaryColors[source.dictionaryKey]}`,
          color: "#000000",
          padding: "6px 12px",
          "margin-bottom": "6px",
          "font-size": "1.2rem",
          "font-weight": "normal"
        })}>${escape_html(sound)}</span>`);
      }
      $$payload.out.push(`<!--]--></div>`);
    }
    $$payload.out.push(`<!--]--></td><td><strong${attr_style("", {
      "font-size": "1.5rem",
      display: "inline-block",
      padding: "4px 0"
    })}>${escape_html(rule.mapping)}</strong></td><td><!--[-->`);
    for (let i = 0, $$length2 = each_array_6.length; i < $$length2; i++) {
      let example = each_array_6[i];
      $$payload.out.push(`<span>`);
      if (i > 0) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`,`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--> `);
      highlightedExample($$payload, example);
      $$payload.out.push(`<!----></span>`);
    }
    $$payload.out.push(`<!--]--></td></tr>`);
  }
  $$payload.out.push(`<!--]--></tbody></table></div></div></div></section> `);
  Examples_section($$payload, { locale });
  $$payload.out.push(`<!----> <section class="mb-5"><h2>${escape_html(t.nuances.title)}</h2> <div class="card"><div class="card-body"><p class="lead">${escape_html(t.nuances.description)}:</p> <div class="mb-4"><h4 class="h5">${escape_html(t.nuances.pronoun.title)}</h4> <p>${escape_html(t.nuances.pronoun.description)}</p></div> <div class="mb-4"><h4 class="h5">${escape_html(t.nuances.interrogatives.title)}</h4> <p>${escape_html(t.nuances.interrogatives.description)}</p> <div class="d-inline-block"><table class="table table-sm table-striped" style="width: auto; min-width: 400px;"><thead><tr><th>${escape_html(t.nuances.interrogatives.tableHeaders.original)}</th><th>${escape_html(t.nuances.interrogatives.tableHeaders.preferred)}</th><th>${escape_html(t.nuances.interrogatives.tableHeaders.expected)}</th></tr></thead><tbody><!--[-->`);
  for (let $$index_7 = 0, $$length = each_array_7.length; $$index_7 < $$length; $$index_7++) {
    let example = each_array_7[$$index_7];
    $$payload.out.push(`<tr><td><code class="text-dark">${escape_html(example.before)}</code></td><td><code class="text-primary fw-bold">${escape_html(example.after.preferred)}</code></td><td><code class="text-secondary">${escape_html(example.after.expected)}</code></td></tr>`);
  }
  $$payload.out.push(`<!--]--></tbody></table></div> <div class="alert alert-info mt-2"><small><strong>Note:</strong> ${escape_html(t.nuances.interrogatives.note)}</small></div></div> <div class="mb-4"><h4 class="h5">${escape_html(t.nuances.irregularVerbs.title)}</h4> <p>${escape_html(t.nuances.irregularVerbs.description)}</p> <div class="table-responsive"><table class="table table-sm table-striped"><thead><tr><th colspan="3" class="text-center bg-light">${escape_html(t.nuances.irregularVerbs.tableHeaders.english)}</th><th colspan="2" class="text-center bg-primary text-white">${escape_html(t.nuances.irregularVerbs.tableHeaders.newEnglish)}</th></tr><tr><th>${escape_html(t.nuances.irregularVerbs.tableHeaders.common)}</th><th>${escape_html(t.nuances.irregularVerbs.tableHeaders.past)}</th><th>${escape_html(t.nuances.irregularVerbs.tableHeaders.pastParticiple)}</th><th>${escape_html(t.nuances.irregularVerbs.tableHeaders.common)}</th><th>${escape_html(t.nuances.irregularVerbs.tableHeaders.past)}</th></tr></thead><tbody><!--[-->`);
  for (let $$index_8 = 0, $$length = each_array_8.length; $$index_8 < $$length; $$index_8++) {
    let example = each_array_8[$$index_8];
    $$payload.out.push(`<tr><td><code class="text-muted">${escape_html(example.english.common)}</code></td><td><code class="text-muted">${escape_html(example.english.past)}</code></td><td><code class="text-muted">${escape_html(example.english.pastParticiple)}</code></td><td><code class="text-primary fw-bold">${escape_html(example.newEnglish.common)}</code></td><td><code class="text-primary fw-bold">${escape_html(example.newEnglish.past)}</code> `);
    if (example.newEnglish.past === "sit" && example.newEnglish.note) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<span class="ms-1 text-info" style="cursor: help;"${attr("title", example.newEnglish.note)}><i class="bi bi-question-circle"></i></span>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></td></tr>`);
  }
  $$payload.out.push(`<!--]--></tbody></table></div></div></div></div></section></div></div>`);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DWL71IfN.js.map
