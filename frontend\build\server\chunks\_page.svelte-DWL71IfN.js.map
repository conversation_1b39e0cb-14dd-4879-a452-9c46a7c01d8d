{"version": 3, "file": "_page.svelte-DWL71IfN.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/new-english/_page.svelte.js"], "sourcesContent": ["import { K as ensure_array_like, z as escape_html, y as attr, F as attr_style, w as pop, u as push, x as head } from \"../../../../../chunks/index.js\";\nimport { h as html } from \"../../../../../chunks/html.js\";\nconst lightDictionaryColors = {\n  cambridge: \"A8CF92\",\n  // green\n  opendict: \"F5BE6A\",\n  // orange\n  any: \"EBBACB\"\n  // pink\n};\nconst darkDictionaryColors = {\n  cambridge: \"7B966B\",\n  // darker green\n  opendict: \"B58C4E\",\n  // darker orange\n  any: \"B08B98\"\n  // darker pink\n};\nconst examples = [\n  /**\n    1. approach (C /əˈproʊtʃ/, O /əˈpɹoʊtʃ/) - aprōč\n      1. ə - a\n      2. p - p\n      3. r/ɹ - r\n      4. oʊ - ō\n      5. tʃ - č\n   */\n  {\n    english: \"approach\",\n    transcriptions: {\n      cambridge: \"əˈproʊtʃ\",\n      opendict: \"əˈpɹoʊtʃ\"\n    },\n    newEnglish: \"ap<PERSON><PERSON>\",\n    breakdown: [\n      {\n        sounds: [\"ə\"],\n        mapping: \"a\"\n      },\n      {\n        sounds: [\"p\"],\n        mapping: \"p\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"r\"],\n          mapping: \"r\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɹ\"],\n          mapping: \"r\"\n        }\n      ],\n      {\n        sounds: [\"oʊ\"],\n        mapping: \"ō\"\n      },\n      {\n        sounds: [\"tʃ\"],\n        mapping: \"č\"\n      }\n    ]\n  },\n  /**\n    2. pig (C /pɪɡ/, O /ˈpɪɡ/) - pik\n      1. p - p\n      2. ɪ - i\n      3. ɡ$ - k\n   */\n  {\n    english: \"pig\",\n    transcriptions: {\n      cambridge: \"pɪɡ\",\n      opendict: \"ˈpɪɡ\"\n    },\n    newEnglish: \"pik\",\n    breakdown: [\n      {\n        sounds: [\"p\"],\n        mapping: \"p\"\n      },\n      {\n        sounds: [\"ɪ\"],\n        mapping: \"i\"\n      },\n      {\n        sounds: [\"ɡ$\"],\n        mapping: \"k\"\n      }\n    ]\n  },\n  /**\n    3. dog (C /dɑːɡ/, O /ˈdɔɡ/) - dak\n      1. d - d\n      2. ɑː/ɔ - a\n      3. ɡ$ - k\n   */\n  {\n    english: \"dog\",\n    transcriptions: {\n      cambridge: \"dɑːɡ\",\n      opendict: \"ˈdɔɡ\"\n    },\n    newEnglish: \"dak\",\n    breakdown: [\n      {\n        sounds: [\"d\"],\n        mapping: \"d\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"ɑː\"],\n          mapping: \"a\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɔ\"],\n          mapping: \"a\"\n        }\n      ],\n      {\n        sounds: [\"ɡ$\"],\n        mapping: \"k\"\n      }\n    ]\n  },\n  /**\n    4. red (C /red/, O /ˈɹɛd/) - ret\n      1. r/ɹ - r\n      2. e/ɛ - e\n      3. d$ - t\n   */\n  {\n    english: \"red\",\n    transcriptions: {\n      cambridge: \"red\",\n      opendict: \"ˈɹɛd\"\n    },\n    newEnglish: \"ret\",\n    breakdown: [\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"r\"],\n          mapping: \"r\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɹ\"],\n          mapping: \"r\"\n        }\n      ],\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"e\"],\n          mapping: \"e\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɛ\"],\n          mapping: \"e\"\n        }\n      ],\n      {\n        sounds: [\"d$\"],\n        mapping: \"t\"\n      }\n    ]\n  },\n  /**\n    5. rat (C /ræt/, O /ˈɹæt/) - rāt\n      1. r/ɹ - r\n      2. æ - ā\n      3. t - t\n   */\n  {\n    english: \"rat\",\n    transcriptions: {\n      cambridge: \"ræt\",\n      opendict: \"ˈɹæt\"\n    },\n    newEnglish: \"rāt\",\n    breakdown: [\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"r\"],\n          mapping: \"r\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɹ\"],\n          mapping: \"r\"\n        }\n      ],\n      {\n        sounds: [\"æ\"],\n        mapping: \"ā\"\n      },\n      {\n        sounds: [\"t\"],\n        mapping: \"t\"\n      }\n    ]\n  },\n  /**\n    6. turtle (C /ˈtɝː.t̬əl/, O /ˈtɝtəɫ/) - tëtl\n      2. t - t\n      3. ɝ - ë\n      4. t̬/t - t\n      5. əl/əɫ$ - l\n   */\n  {\n    english: \"turtle\",\n    transcriptions: {\n      cambridge: \"ˈtɝː.t̬əl\",\n      opendict: \"ˈtɝtəɫ\"\n    },\n    newEnglish: \"tëtl\",\n    breakdown: [\n      {\n        sounds: [\"t\"],\n        mapping: \"t\"\n      },\n      {\n        sounds: [\"ɝ\"],\n        mapping: \"ë\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"t̬\"],\n          mapping: \"t\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"t\"],\n          mapping: \"t\"\n        }\n      ],\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"əl$\"],\n          mapping: \"l\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"əɫ$\"],\n          mapping: \"l\"\n        }\n      ]\n    ]\n  },\n  /**\n    7. goat (C /ɡoʊt/, O /ˈɡoʊt/) - gōt\n      1. ɡ - g\n      2. oʊ - ō\n      3. t - t\n   */\n  {\n    english: \"goat\",\n    transcriptions: {\n      cambridge: \"ɡoʊt\",\n      opendict: \"ˈɡoʊt\"\n    },\n    newEnglish: \"gōt\",\n    breakdown: [\n      {\n        sounds: [\"ɡ\"],\n        mapping: \"g\"\n      },\n      {\n        sounds: [\"oʊ\"],\n        mapping: \"ō\"\n      },\n      {\n        sounds: [\"t\"],\n        mapping: \"t\"\n      }\n    ]\n  },\n  /**\n    8. bear (C /ber/, O /ˈbɛɹ/) - be\n      1. b - b\n      2. e/ɛ - e\n      3. r/ɹ$ - *\n   */\n  {\n    english: \"bear\",\n    transcriptions: {\n      cambridge: \"ber\",\n      opendict: \"ˈbɛɹ\"\n    },\n    newEnglish: \"be\",\n    breakdown: [\n      {\n        sounds: [\"b\"],\n        mapping: \"b\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"e\"],\n          mapping: \"e\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɛ\"],\n          mapping: \"e\"\n        }\n      ],\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"r$\"],\n          mapping: \"\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɹ$\"],\n          mapping: \"\"\n        }\n      ]\n    ]\n  },\n  /**\n    9. panther (C /ˈpæn.θɚ/, O /ˈpænθɝ/) - pānfe\n      1. p - p\n      2. æ - ā\n      3. n - n\n      4. θ - f\n      5. ɚ/ɝ$ - e\n   */\n  {\n    english: \"panther\",\n    transcriptions: {\n      cambridge: \"ˈpæn.θɚ\",\n      opendict: \"ˈpænθɝ\"\n    },\n    newEnglish: \"pānfe\",\n    breakdown: [\n      {\n        sounds: [\"p\"],\n        mapping: \"p\"\n      },\n      {\n        sounds: [\"æ\"],\n        mapping: \"ā\"\n      },\n      {\n        sounds: [\"n\"],\n        mapping: \"n\"\n      },\n      {\n        sounds: [\"θ\"],\n        mapping: \"f\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"ɚ\"],\n          mapping: \"e\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɝ$\"],\n          mapping: \"e\"\n        }\n      ]\n    ]\n  },\n  /**\n    10. frog (C /frɑːɡ/, O /ˈfɹɑɡ/) - frak\n      1. f - f\n      2. r/ɹ - r\n      3. ɑ - a\n      4. ɡ$ - k\n   */\n  {\n    english: \"frog\",\n    transcriptions: {\n      cambridge: \"frɑːɡ\",\n      opendict: \"ˈfɹɑɡ\"\n    },\n    newEnglish: \"frak\",\n    breakdown: [\n      {\n        sounds: [\"f\"],\n        mapping: \"f\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"r\"],\n          mapping: \"r\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɹ\"],\n          mapping: \"r\"\n        }\n      ],\n      {\n        sounds: [\"ɑ\"],\n        mapping: \"a\"\n      },\n      {\n        sounds: [\"ɡ$\"],\n        mapping: \"k\"\n      }\n    ]\n  },\n  /**\n    11. feather (C /ˈfeð.ɚ/, O /ˈfɛðɝ/) - feve\n      1. f - f\n      2. e/ɛ - e\n      3. ð - v\n      4. ɚ/ɝ - e\n   */\n  {\n    english: \"feather\",\n    transcriptions: {\n      cambridge: \"ˈfeð.ɚ\",\n      opendict: \"ˈfɛðɝ\"\n    },\n    newEnglish: \"feve\",\n    breakdown: [\n      {\n        sounds: [\"f\"],\n        mapping: \"f\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"e\"],\n          mapping: \"e\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɛ\"],\n          mapping: \"e\"\n        }\n      ],\n      {\n        sounds: [\"ð\"],\n        mapping: \"v\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"ɚ\"],\n          mapping: \"e\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɝ\"],\n          mapping: \"e\"\n        }\n      ]\n    ]\n  },\n  /**\n    12. beaver (C /ˈbiː.vɚ/, O /ˈbivɝ/) - bive\n      1. b - b\n      2. i - i\n      3. v - v\n      4. ɚ/ɝ - e\n   */\n  {\n    english: \"beaver\",\n    transcriptions: {\n      cambridge: \"ˈbiː.vɚ\",\n      opendict: \"ˈbivɝ\"\n    },\n    newEnglish: \"bive\",\n    breakdown: [\n      {\n        sounds: [\"b\"],\n        mapping: \"b\"\n      },\n      {\n        sounds: [\"i\"],\n        mapping: \"i\"\n      },\n      {\n        sounds: [\"v\"],\n        mapping: \"v\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"ɚ\"],\n          mapping: \"e\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɝ\"],\n          mapping: \"e\"\n        }\n      ]\n    ]\n  },\n  /**\n    13. snake (C /sneɪk/, O /ˈsneɪk/) - sneík\n      1. s - s\n      2. n - n\n      3. eɪ - eí\n      4. k - k\n   */\n  {\n    english: \"snake\",\n    transcriptions: {\n      cambridge: \"sneɪk\",\n      opendict: \"ˈsneɪk\"\n    },\n    newEnglish: \"sneík\",\n    breakdown: [\n      {\n        sounds: [\"s\"],\n        mapping: \"s\"\n      },\n      {\n        sounds: [\"n\"],\n        mapping: \"n\"\n      },\n      {\n        sounds: [\"eɪ\"],\n        mapping: \"eí\"\n      },\n      {\n        sounds: [\"k\"],\n        mapping: \"k\"\n      }\n    ]\n  },\n  /**\n    14. sheep (C /ʃiːp/, O /ˈʃip/) - šip\n      1. ʃ - š\n      2. i - i\n      3. p - p\n   */\n  {\n    english: \"sheep\",\n    transcriptions: {\n      cambridge: \"ʃiːp\",\n      opendict: \"ˈʃip\"\n    },\n    newEnglish: \"šip\",\n    breakdown: [\n      {\n        sounds: [\"ʃ\"],\n        mapping: \"š\"\n      },\n      {\n        sounds: [\"i\"],\n        mapping: \"i\"\n      },\n      {\n        sounds: [\"p\"],\n        mapping: \"p\"\n      }\n    ]\n  },\n  /**\n    15. chicken (C /ˈtʃɪk.ɪn/, O /ˈtʃɪkən/) - čikn\n      1. tʃ - č\n      2. ɪ - i\n      3. k - k\n      4. ɪn/$, ən - n\n   */\n  {\n    english: \"chicken\",\n    transcriptions: {\n      cambridge: \"ˈtʃɪk.ɪn\",\n      opendict: \"ˈtʃɪkən\"\n    },\n    newEnglish: \"čikn\",\n    breakdown: [\n      {\n        sounds: [\"tʃ\"],\n        mapping: \"č\"\n      },\n      {\n        sounds: [\"ɪ\"],\n        mapping: \"i\"\n      },\n      {\n        sounds: [\"k\"],\n        mapping: \"k\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"ɪn$\"],\n          mapping: \"n\"\n        },\n        {\n          dictionaryKey: \"any\",\n          sounds: [\"ən\"],\n          mapping: \"n\"\n        }\n      ]\n    ]\n  },\n  /**\n    16. zebra (C /ˈziː.brə/, O /ˈzibɹə/) - zibra\n      1. z - z\n      2. i - i\n      3. b - b\n      4. r/ɹ - r\n      5. ə$ - a\n   */\n  {\n    english: \"zebra\",\n    transcriptions: {\n      cambridge: \"ˈziː.brə\",\n      opendict: \"ˈzibɹə\"\n    },\n    newEnglish: \"zibra\",\n    breakdown: [\n      {\n        sounds: [\"z\"],\n        mapping: \"z\"\n      },\n      {\n        sounds: [\"i\"],\n        mapping: \"i\"\n      },\n      {\n        sounds: [\"b\"],\n        mapping: \"b\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"r\"],\n          mapping: \"r\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɹ\"],\n          mapping: \"r\"\n        }\n      ],\n      {\n        sounds: [\"ə$\"],\n        mapping: \"a\"\n      }\n    ]\n  },\n  /**\n    17. television (C /ˈtel.ə.vɪʒ.ən/, O /ˈtɛɫəˌvɪʒən/) - televijn\n      1. t - t\n      2. e/ɛ - e\n      3. lə/ɫə - le\n      4. v - v\n      5. ɪ - i\n      6. ʒ - j\n      7. ən - n\n   */\n  {\n    english: \"television\",\n    transcriptions: {\n      cambridge: \"ˈtel.ə.vɪʒ.ən\",\n      opendict: \"ˈtɛɫəˌvɪʒən\"\n    },\n    newEnglish: \"televijn\",\n    breakdown: [\n      {\n        sounds: [\"t\"],\n        mapping: \"t\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"e\"],\n          mapping: \"e\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɛ\"],\n          mapping: \"e\"\n        }\n      ],\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"lə\"],\n          mapping: \"le\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɫə\"],\n          mapping: \"le\"\n        }\n      ],\n      {\n        sounds: [\"v\"],\n        mapping: \"v\"\n      },\n      {\n        sounds: [\"ɪ\"],\n        mapping: \"i\"\n      },\n      {\n        sounds: [\"ʒ\"],\n        mapping: \"j\"\n      },\n      {\n        sounds: [\"ən\"],\n        mapping: \"n\"\n      }\n    ]\n  },\n  /**\n    18. giraffe (C /dʒɪˈræf/, O /dʒɝˈæf/) - djirāf\n      1. d - d\n      2. ʒ - j\n      3. ɪˈr/ɝˈ - ir\n      4. æ - ā\n      5. f - f\n   */\n  {\n    english: \"giraffe\",\n    transcriptions: {\n      cambridge: \"dʒɪˈræf\",\n      opendict: \"dʒɝˈæf\"\n    },\n    newEnglish: \"djirāf\",\n    breakdown: [\n      {\n        sounds: [\"d\"],\n        mapping: \"d\"\n      },\n      {\n        sounds: [\"ʒ\"],\n        mapping: \"j\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"ɪˈr\"],\n          mapping: \"ir\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɝˈ\"],\n          mapping: \"ir\"\n        }\n      ],\n      {\n        sounds: [\"æ\"],\n        mapping: \"ā\"\n      },\n      {\n        sounds: [\"f\"],\n        mapping: \"f\"\n      }\n    ]\n  },\n  /**\n    19. wolf (C /wʊlf/, O /ˈwʊɫf/) - wolf\n      1. w - w\n      2. ʊ - o\n      3. l/ɫ - l\n      4. f - f\n   */\n  {\n    english: \"wolf\",\n    transcriptions: {\n      cambridge: \"wʊlf\",\n      opendict: \"ˈwʊɫf\"\n    },\n    newEnglish: \"wolf\",\n    breakdown: [\n      {\n        sounds: [\"w\"],\n        mapping: \"w\"\n      },\n      {\n        sounds: [\"ʊ\"],\n        mapping: \"o\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"l\"],\n          mapping: \"l\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɫ\"],\n          mapping: \"l\"\n        }\n      ],\n      {\n        sounds: [\"f\"],\n        mapping: \"f\"\n      }\n    ]\n  },\n  /**\n    20. lion (C /ˈlaɪ.ən/, O /ˈɫaɪən/) - laín\n      1. l/ɫ - l\n      2. aɪ - aí\n      3. ən - n\n   */\n  {\n    english: \"lion\",\n    transcriptions: {\n      cambridge: \"ˈlaɪ.ən\",\n      opendict: \"ˈɫaɪən\"\n    },\n    newEnglish: \"laín\",\n    breakdown: [\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"l\"],\n          mapping: \"l\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɫ\"],\n          mapping: \"l\"\n        }\n      ],\n      {\n        sounds: [\"aɪ\"],\n        mapping: \"aí\"\n      },\n      {\n        sounds: [\"ən\"],\n        mapping: \"n\"\n      }\n    ]\n  },\n  /**\n    21. mouse (C /maʊs/, O /ˈmaʊs/) - mays\n      1. m - m\n      2. aʊ - ay\n      3. s - s\n   */\n  {\n    english: \"mouse\",\n    transcriptions: {\n      cambridge: \"maʊs\",\n      opendict: \"ˈmaʊs\"\n    },\n    newEnglish: \"mays\",\n    breakdown: [\n      {\n        sounds: [\"m\"],\n        mapping: \"m\"\n      },\n      {\n        sounds: [\"aʊ\"],\n        mapping: \"ay\"\n      },\n      {\n        sounds: [\"s\"],\n        mapping: \"s\"\n      }\n    ]\n  },\n  /**\n    22. dinosaur (C /ˈdaɪ.nə.sɔːr/, O /ˈdaɪnəˌsɔɹ/) - daínaso\n      1. d - d\n      2. aɪ - aí\n      3. n - n\n      4. ə - a\n      5. s - s\n      6. ɔːr/ɔɹ - o\n   */\n  {\n    english: \"dinosaur\",\n    transcriptions: {\n      cambridge: \"ˈdaɪ.nə.sɔːr\",\n      opendict: \"ˈdaɪnəˌsɔɹ\"\n    },\n    newEnglish: \"daínaso\",\n    breakdown: [\n      {\n        sounds: [\"d\"],\n        mapping: \"d\"\n      },\n      {\n        sounds: [\"aɪ\"],\n        mapping: \"aí\"\n      },\n      {\n        sounds: [\"n\"],\n        mapping: \"n\"\n      },\n      {\n        sounds: [\"ə\"],\n        mapping: \"a\"\n      },\n      {\n        sounds: [\"s\"],\n        mapping: \"s\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"ɔːr\"],\n          mapping: \"o\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɔɹ\"],\n          mapping: \"o\"\n        }\n      ]\n    ]\n  },\n  /**\n    23. penguin (C /ˈpeŋ.ɡwɪn/, O /ˈpɛŋɡwən/) - pengwn\n      1. p - p\n      2. e/ɛ - e\n      3. ŋ - n\n      4. ɡ - g\n      5. w - w\n      6. ɪn$/, ən - n\n   */\n  {\n    english: \"penguin\",\n    transcriptions: {\n      cambridge: \"ˈpeŋ.ɡwɪn\",\n      opendict: \"ˈpɛŋɡwən\"\n    },\n    newEnglish: \"pengwn\",\n    breakdown: [\n      {\n        sounds: [\"p\"],\n        mapping: \"p\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"e\"],\n          mapping: \"e\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɛ\"],\n          mapping: \"e\"\n        }\n      ],\n      {\n        sounds: [\"ŋ\"],\n        mapping: \"n\"\n      },\n      {\n        sounds: [\"ɡ\"],\n        mapping: \"g\"\n      },\n      {\n        sounds: [\"w\"],\n        mapping: \"w\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"ɪn$\"],\n          mapping: \"n\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ən\"],\n          mapping: \"n\"\n        }\n      ]\n    ]\n  },\n  /**\n    24. rabbit (C /ˈræb.ɪt/, O /ˈɹæbət/, /ˈɹæbɪt/) - rābit\n      1. r/ɹ - r\n      2. æ - ā\n      3. bi//bə/bɪ - bi\n      4. t - t\n   */\n  {\n    english: \"rabbit\",\n    transcriptions: {\n      cambridge: \"ˈræb.ɪt\",\n      opendict: \"ˈɹæbət\"\n    },\n    newEnglish: \"rābit\",\n    breakdown: [\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"r\"],\n          mapping: \"r\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɹ\"],\n          mapping: \"r\"\n        }\n      ],\n      {\n        sounds: [\"æ\"],\n        mapping: \"ā\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"bi\"],\n          mapping: \"bi\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"bə\"],\n          mapping: \"bi\"\n        }\n      ],\n      {\n        sounds: [\"t\"],\n        mapping: \"t\"\n      }\n    ]\n  },\n  /**\n    25. yak (C /jæk/, O /ˈjæk/) - íāk\n      1. j - í\n      2. æ - ā\n      3. k - k\n   */\n  {\n    english: \"yak\",\n    transcriptions: {\n      cambridge: \"jæk\",\n      opendict: \"ˈjæk\"\n    },\n    newEnglish: \"íāk\",\n    breakdown: [\n      {\n        sounds: [\"j\"],\n        mapping: \"í\"\n      },\n      {\n        sounds: [\"æ\"],\n        mapping: \"ā\"\n      },\n      {\n        sounds: [\"k\"],\n        mapping: \"k\"\n      }\n    ]\n  },\n  /**\n    26. horse (C /hɔːrs/, O /ˈhɔɹs/) - hos\n      1. h - h\n      2. ɔːr/ɔɹ - o\n      3. s - s\n   */\n  {\n    english: \"horse\",\n    transcriptions: {\n      cambridge: \"hɔːrs\",\n      opendict: \"ˈhɔɹs\"\n    },\n    newEnglish: \"hos\",\n    breakdown: [\n      {\n        sounds: [\"h\"],\n        mapping: \"h\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"ɔːr\"],\n          mapping: \"o\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɔɹ\"],\n          mapping: \"o\"\n        }\n      ],\n      {\n        sounds: [\"s\"],\n        mapping: \"s\"\n      }\n    ]\n  },\n  /**\n    27. green (C /ɡriːn/, O /ˈɡɹin/) - grin\n      1. ɡ - g\n      2. r/ɹ - r\n      3. i - i\n      4. n - n\n   */\n  {\n    english: \"green\",\n    transcriptions: {\n      cambridge: \"ɡriːn\",\n      opendict: \"ˈɡɹin\"\n    },\n    newEnglish: \"grin\",\n    breakdown: [\n      {\n        sounds: [\"ɡ\"],\n        mapping: \"g\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"r\"],\n          mapping: \"r\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɹ\"],\n          mapping: \"r\"\n        }\n      ],\n      {\n        sounds: [\"i\"],\n        mapping: \"i\"\n      },\n      {\n        sounds: [\"n\"],\n        mapping: \"n\"\n      }\n    ]\n  },\n  /**\n    28. pink (C /pɪŋk/, O /ˈpɪŋk/) - pink\n      1. p - p\n      2. ɪ - i\n      3. ŋ - n\n      4. k - k\n   */\n  {\n    english: \"pink\",\n    transcriptions: {\n      cambridge: \"pɪŋk\",\n      opendict: \"ˈpɪŋk\"\n    },\n    newEnglish: \"pink\",\n    breakdown: [\n      {\n        sounds: [\"p\"],\n        mapping: \"p\"\n      },\n      {\n        sounds: [\"ɪ\"],\n        mapping: \"i\"\n      },\n      {\n        sounds: [\"ŋ\"],\n        mapping: \"n\"\n      },\n      {\n        sounds: [\"k\"],\n        mapping: \"k\"\n      }\n    ]\n  },\n  /**\n    29. wood (C /wʊd/, O /ˈwʊd/) - wot\n      1. w - w\n      2. ʊ - o\n      3. d$ - t\n   */\n  {\n    english: \"wood\",\n    transcriptions: {\n      cambridge: \"wʊd\",\n      opendict: \"ˈwʊd\"\n    },\n    newEnglish: \"wot\",\n    breakdown: [\n      {\n        sounds: [\"w\"],\n        mapping: \"w\"\n      },\n      {\n        sounds: [\"ʊ\"],\n        mapping: \"o\"\n      },\n      {\n        sounds: [\"d$\"],\n        mapping: \"t\"\n      }\n    ]\n  },\n  /**\n    30. blue (C /bluː/, O /ˈbɫu/) - blu\n      1. b - b\n      2. l/ɫ - l\n      3. u - u\n   */\n  {\n    english: \"blue\",\n    transcriptions: {\n      cambridge: \"bluː\",\n      opendict: \"ˈbɫu\"\n    },\n    newEnglish: \"blu\",\n    breakdown: [\n      {\n        sounds: [\"b\"],\n        mapping: \"b\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"l\"],\n          mapping: \"l\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɫ\"],\n          mapping: \"l\"\n        }\n      ],\n      {\n        sounds: [\"u\"],\n        mapping: \"u\"\n      }\n    ]\n  },\n  /**\n    31. dust (C /dʌst/, O /ˈdəst/) - dast\n      1. dʌ/də - da\n      2. s - s\n      3. t - t\n   */\n  {\n    english: \"dust\",\n    transcriptions: {\n      cambridge: \"dʌst\",\n      opendict: \"ˈdəst\"\n    },\n    newEnglish: \"dast\",\n    breakdown: [\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"dʌ\"],\n          mapping: \"da\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"də\"],\n          mapping: \"da\"\n        }\n      ],\n      {\n        sounds: [\"s\"],\n        mapping: \"s\"\n      },\n      {\n        sounds: [\"t\"],\n        mapping: \"t\"\n      }\n    ]\n  },\n  /**\n    32. purple (C /ˈpɝː.pəl/, O /ˈpɝpəɫ/) - pëpl\n      1. p - p\n      2. ɝ - ë\n      3. p - p\n      4. əl/əɫ$ - l\n   */\n  {\n    english: \"purple\",\n    transcriptions: {\n      cambridge: \"ˈpɝː.pəl\",\n      opendict: \"ˈpɝpəɫ\"\n    },\n    newEnglish: \"pëpl\",\n    breakdown: [\n      {\n        sounds: [\"p\"],\n        mapping: \"p\"\n      },\n      {\n        sounds: [\"ɝ\"],\n        mapping: \"ë\"\n      },\n      {\n        sounds: [\"p\"],\n        mapping: \"p\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"əl$\"],\n          mapping: \"l\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"əɫ$\"],\n          mapping: \"l\"\n        }\n      ]\n    ]\n  },\n  /**\n    33. mauve (C /moʊv/, O /ˈmɔv/) - mōf, maf\n      1. m - m\n      2. oʊ - ō, ɔ - a\n      3. v$ - f\n   */\n  {\n    english: \"mauve\",\n    transcriptions: {\n      cambridge: \"moʊv\",\n      opendict: \"ˈmɔv\"\n    },\n    newEnglish: {\n      cambridge: \"mōf\",\n      opendict: \"maf\"\n    },\n    breakdown: [\n      {\n        sounds: [\"m\"],\n        mapping: \"m\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"oʊ\"],\n          mapping: \"ō\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɔ\"],\n          mapping: \"a\"\n        }\n      ],\n      {\n        sounds: [\"v$\"],\n        mapping: \"f\"\n      }\n    ]\n  },\n  /**\n    34. sand (C /sænd/, O /ˈsænd/) - sānt\n      1. s - s\n      2. æ - ā\n      3. n - n\n      4. d$ - t\n   */\n  {\n    english: \"sand\",\n    transcriptions: {\n      cambridge: \"sænd\",\n      opendict: \"ˈsænd\"\n    },\n    newEnglish: \"sānt\",\n    breakdown: [\n      {\n        sounds: [\"s\"],\n        mapping: \"s\"\n      },\n      {\n        sounds: [\"æ\"],\n        mapping: \"ā\"\n      },\n      {\n        sounds: [\"n\"],\n        mapping: \"n\"\n      },\n      {\n        sounds: [\"d$\"],\n        mapping: \"t\"\n      }\n    ]\n  },\n  /**\n    35. coffee (C /ˈkɑː.fi/, O /ˈkɑfi/, /ˈkɔfi/) - kafi\n      1. k - k\n      2. ɑ, //ɔ  - a\n      3. f - f\n      4. i - i\n   */\n  {\n    english: \"coffee\",\n    transcriptions: {\n      cambridge: \"ˈkɑː.fi\",\n      opendict: \"ˈkɑfi\"\n    },\n    newEnglish: \"kafi\",\n    breakdown: [\n      {\n        sounds: [\"k\"],\n        mapping: \"k\"\n      },\n      [\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɔ\"],\n          mapping: \"a\"\n        },\n        {\n          dictionaryKey: \"any\",\n          sounds: [\"ɑ\"],\n          mapping: \"a\"\n        }\n      ],\n      {\n        sounds: [\"f\"],\n        mapping: \"f\"\n      },\n      {\n        sounds: [\"i\"],\n        mapping: \"i\"\n      }\n    ]\n  },\n  /**\n    36. jade (C /dʒeɪd/, O /ˈdʒeɪd/) - djeit\n      1. d - d\n      2. ʒ - j\n      3. eɪ - eí\n      4. d$ - t\n   */\n  {\n    english: \"jade\",\n    transcriptions: {\n      cambridge: \"dʒeɪd\",\n      opendict: \"ˈdʒeɪd\"\n    },\n    newEnglish: \"djeit\",\n    breakdown: [\n      {\n        sounds: [\"d\"],\n        mapping: \"d\"\n      },\n      {\n        sounds: [\"ʒ\"],\n        mapping: \"j\"\n      },\n      {\n        sounds: [\"eɪ\"],\n        mapping: \"eí\"\n      },\n      {\n        sounds: [\"d$\"],\n        mapping: \"t\"\n      }\n    ]\n  },\n  /**\n    37. gold (C /ɡoʊld/, O /ˈɡoʊɫd/) - gōlt\n      1. ɡ - g\n      2. oʊ - ō\n      3. l/ɫ - l\n      4. d$ - t\n   */\n  {\n    english: \"gold\",\n    transcriptions: {\n      cambridge: \"ɡoʊld\",\n      opendict: \"ˈɡoʊɫd\"\n    },\n    newEnglish: \"gōlt\",\n    breakdown: [\n      {\n        sounds: [\"ɡ\"],\n        mapping: \"g\"\n      },\n      {\n        sounds: [\"oʊ\"],\n        mapping: \"ō\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"l\"],\n          mapping: \"l\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɫ\"],\n          mapping: \"l\"\n        }\n      ],\n      {\n        sounds: [\"d$\"],\n        mapping: \"t\"\n      }\n    ]\n  },\n  /**\n    38. turquoise (C /ˈtɝː.kɔɪz/, O /ˈtɝkwɔɪz/) - tëkoís\n      1. t - t\n      2. ɝ - ë\n      3. k - k\n      4. ɔɪ - oí\n      5. z$ - s\n   */\n  {\n    english: \"turquoise\",\n    transcriptions: {\n      cambridge: \"ˈtɝː.kɔɪz\",\n      opendict: \"ˈtɝkwɔɪz\"\n    },\n    newEnglish: \"tëkoís\",\n    breakdown: [\n      {\n        sounds: [\"t\"],\n        mapping: \"t\"\n      },\n      {\n        sounds: [\"ɝ\"],\n        mapping: \"ë\"\n      },\n      {\n        sounds: [\"k\"],\n        mapping: \"k\"\n      },\n      {\n        sounds: [\"ɔɪ\"],\n        mapping: \"oí\"\n      },\n      {\n        sounds: [\"z$\"],\n        mapping: \"s\"\n      }\n    ]\n  },\n  /**\n    39. lime (C /laɪm/, O /ˈɫaɪm/) - laím\n      1. l/ɫ - l\n      2. aɪ - aí\n      3. m - m\n   */\n  {\n    english: \"lime\",\n    transcriptions: {\n      cambridge: \"laɪm\",\n      opendict: \"ˈɫaɪm\"\n    },\n    newEnglish: \"laím\",\n    breakdown: [\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"l\"],\n          mapping: \"l\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɫ\"],\n          mapping: \"l\"\n        }\n      ],\n      {\n        sounds: [\"aɪ\"],\n        mapping: \"aí\"\n      },\n      {\n        sounds: [\"m\"],\n        mapping: \"m\"\n      }\n    ]\n  },\n  /**\n    40. brown (C /braʊn/, O /ˈbɹaʊn/) - brayn\n      1. b - b\n      2. r/ɹ - r\n      3. aʊ - ay\n      4. n - n\n   */\n  {\n    english: \"brown\",\n    transcriptions: {\n      cambridge: \"braʊn\",\n      opendict: \"ˈbɹaʊn\"\n    },\n    newEnglish: \"brayn\",\n    breakdown: [\n      {\n        sounds: [\"b\"],\n        mapping: \"b\"\n      },\n      [\n        {\n          dictionaryKey: \"cambridge\",\n          sounds: [\"r\"],\n          mapping: \"r\"\n        },\n        {\n          dictionaryKey: \"opendict\",\n          sounds: [\"ɹ\"],\n          mapping: \"r\"\n        }\n      ],\n      {\n        sounds: [\"aʊ\"],\n        mapping: \"ay\"\n      },\n      {\n        sounds: [\"n\"],\n        mapping: \"n\"\n      }\n    ]\n  }\n];\nfunction mappings($$payload, item) {\n  const firstMapping = item[0].mapping;\n  const allSameMapping = item.every((variant) => variant.mapping === firstMapping);\n  if (allSameMapping) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<span${attr_style(\"\", { color: \"#000000\" })}>${escape_html(firstMapping)}</span>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    const each_array = ensure_array_like(item);\n    $$payload.out.push(`<!--[-->`);\n    for (let i = 0, $$length = each_array.length; i < $$length; i++) {\n      let variant = each_array[i];\n      if (i > 0) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`,`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n      }\n      $$payload.out.push(`<!--]--> <span${attr_style(\"\", { color: `#${darkDictionaryColors[variant.dictionaryKey]}` })}>${escape_html(variant.mapping)}</span>`);\n    }\n    $$payload.out.push(`<!--]-->`);\n  }\n  $$payload.out.push(`<!--]-->`);\n}\nfunction Examples_section($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      title: \"Word Examples\",\n      description: \"See how common English words transform in our phonetic system\",\n      hideDetails: \"Hide Details\",\n      showDetails: \"Show Details\",\n      soundBreakdown: \"Sound Breakdown\",\n      phoneticSounds: \"Phonetic Sounds\",\n      newEnglishLetter: \"New English Letter(s)\"\n    },\n    ru: {\n      title: \"Примеры слов\",\n      description: \"Посмотрите, как обычные английские слова преобразуются в нашей фонетической системе\",\n      hideDetails: \"Скрыть детали\",\n      showDetails: \"Показать детали\",\n      soundBreakdown: \"Разбор звуков\",\n      phoneticSounds: \"Фонетические звуки\",\n      newEnglishLetter: \"Буквы Нового английского\"\n    }\n  };\n  const { locale } = $$props;\n  const t = i18n[locale];\n  let expandedExample = null;\n  const each_array_1 = ensure_array_like(examples);\n  $$payload.out.push(`<section class=\"mb-5\"><h2>${escape_html(t.title)}</h2> <div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.description)}:</p> <ul class=\"list-group list-group-flush\"><!--[-->`);\n  for (let index = 0, $$length = each_array_1.length; index < $$length; index++) {\n    let example = each_array_1[index];\n    const each_array_2 = ensure_array_like(Object.entries(example.transcriptions));\n    $$payload.out.push(`<li class=\"list-group-item\"><div class=\"d-flex justify-content-between align-items-center cursor-pointer\"${attr(\"role\", expandedExample === index ? t.hideDetails : t.showDetails)}${attr_style(\"\", { cursor: \"pointer\" })}><div><strong>${escape_html(example.english)}</strong> <span class=\"ms-2\"><!--[-->`);\n    for (let i = 0, $$length2 = each_array_2.length; i < $$length2; i++) {\n      let [dict, transcription] = each_array_2[i];\n      if (i > 0) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`,`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n      }\n      $$payload.out.push(`<!--]--> <span${attr_style(\"\", { color: `#${darkDictionaryColors[dict]}` })}>${escape_html(transcription)}</span>`);\n    }\n    $$payload.out.push(`<!--]--></span> <span class=\"ms-2\">→</span> <span class=\"ms-2 fw-bold\">`);\n    if (typeof example.newEnglish === \"string\") {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<span class=\"text-dark\">${escape_html(example.newEnglish)}</span>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      const each_array_3 = ensure_array_like(Object.entries(example.newEnglish));\n      $$payload.out.push(`<!--[-->`);\n      for (let i = 0, $$length2 = each_array_3.length; i < $$length2; i++) {\n        let [dict, translation] = each_array_3[i];\n        if (i > 0) {\n          $$payload.out.push(\"<!--[-->\");\n          $$payload.out.push(`,`);\n        } else {\n          $$payload.out.push(\"<!--[!-->\");\n        }\n        $$payload.out.push(`<!--]--> <span${attr_style(\"\", { color: `#${darkDictionaryColors[dict]}` })}>${escape_html(translation)}</span>`);\n      }\n      $$payload.out.push(`<!--]-->`);\n    }\n    $$payload.out.push(`<!--]--></span></div> <button class=\"btn btn-sm btn-outline-primary\">${escape_html(expandedExample === index ? t.hideDetails : t.showDetails)}</button></div> `);\n    if (expandedExample === index) {\n      $$payload.out.push(\"<!--[-->\");\n      const each_array_4 = ensure_array_like(example.breakdown);\n      $$payload.out.push(`<div class=\"mt-3\"><h6>${escape_html(t.soundBreakdown)}:</h6> <table class=\"table table-sm\"><thead><tr><th>${escape_html(t.phoneticSounds)}</th><th>${escape_html(t.newEnglishLetter)}</th></tr></thead><tbody><!--[-->`);\n      for (let $$index_6 = 0, $$length2 = each_array_4.length; $$index_6 < $$length2; $$index_6++) {\n        let item = each_array_4[$$index_6];\n        if (Array.isArray(item)) {\n          $$payload.out.push(\"<!--[-->\");\n          const each_array_5 = ensure_array_like(item);\n          $$payload.out.push(`<tr><td><!--[-->`);\n          for (let $$index_4 = 0, $$length3 = each_array_5.length; $$index_4 < $$length3; $$index_4++) {\n            let variant = each_array_5[$$index_4];\n            const each_array_6 = ensure_array_like(variant.sounds);\n            $$payload.out.push(`<span class=\"me-2\"><!--[-->`);\n            for (let $$index_3 = 0, $$length4 = each_array_6.length; $$index_3 < $$length4; $$index_3++) {\n              let sound = each_array_6[$$index_3];\n              $$payload.out.push(`<span class=\"badge rounded-pill me-1 font-monospace\"${attr_style(\"\", {\n                \"background-color\": `#${lightDictionaryColors[variant.dictionaryKey]}`,\n                color: \"#000000\",\n                padding: \"6px 12px\",\n                \"margin-bottom\": \"6px\",\n                \"font-size\": \"1.2rem\",\n                \"font-weight\": \"normal\"\n              })}>${escape_html(sound)}</span>`);\n            }\n            $$payload.out.push(`<!--]--></span>`);\n          }\n          $$payload.out.push(`<!--]--></td><td><strong${attr_style(\"\", {\n            \"font-size\": \"1.5rem\",\n            display: \"inline-block\",\n            padding: \"4px 0\"\n          })}>`);\n          mappings($$payload, item);\n          $$payload.out.push(`<!----></strong></td></tr>`);\n        } else {\n          $$payload.out.push(\"<!--[!-->\");\n          const each_array_7 = ensure_array_like(item.sounds);\n          $$payload.out.push(`<tr><td><!--[-->`);\n          for (let $$index_5 = 0, $$length3 = each_array_7.length; $$index_5 < $$length3; $$index_5++) {\n            let sound = each_array_7[$$index_5];\n            $$payload.out.push(`<span class=\"badge rounded-pill me-2 font-monospace\"${attr_style(\"\", {\n              \"background-color\": `#${lightDictionaryColors[\"any\"]}`,\n              color: \"#000000\",\n              border: \"1px solid #CCCCCC\",\n              padding: \"6px 12px\",\n              \"margin-bottom\": \"6px\",\n              \"font-size\": \"1.2rem\",\n              \"font-weight\": \"normal\"\n            })}>${escape_html(sound)}</span>`);\n          }\n          $$payload.out.push(`<!--]--></td><td><strong${attr_style(\"\", {\n            \"font-size\": \"1.5rem\",\n            display: \"inline-block\",\n            padding: \"4px 0\"\n          })}>${escape_html(item.mapping)}</strong></td></tr>`);\n        }\n        $$payload.out.push(`<!--]-->`);\n      }\n      $$payload.out.push(`<!--]--></tbody></table></div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></li>`);\n  }\n  $$payload.out.push(`<!--]--></ul></div></div></section>`);\n  pop();\n}\nconst rules = [\n  /*\n    [a, ɑ, /ɔ, ʌ, ə$] - a\n    aɪ - aí\n    aʊ - ay\n    æ - ā\n    b - b\n    bə - bi\n  */\n  {\n    sources: [\n      {\n        dictionaryKey: \"opendict\",\n        sounds: [\"ɔ\"]\n      },\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"a\", \"ɑ\", \"ʌ\", \"ə$\"]\n      }\n    ],\n    mapping: \"a\",\n    examples: [\"d*u*st\", \"b*u*lk\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"aɪ\"]\n      }\n    ],\n    mapping: \"aí\",\n    examples: [\"l*i*me\", \"d*i*nosaur\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"aʊ\"]\n      }\n    ],\n    mapping: \"ay\",\n    examples: [\"m*ou*se\", \"br*ow*n\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"æ\"]\n      }\n    ],\n    mapping: \"ā\",\n    examples: [\"r*a*t\", \"c*a*t\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"b\"]\n      }\n    ],\n    mapping: \"b\",\n    examples: [\"*b*ear\", \"ze*b*ra\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"bə\"]\n      }\n    ],\n    mapping: \"bi\",\n    examples: [\"*be*aver\", \"rab*bi*t\"]\n  },\n  /*\n    tʃ - č\n    d - d\n    də - da\n    [e, /ɛ, ə, ɚ/ɝ$] - e\n    eɪ - eí\n    ɝ - ë\n  */\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"tʃ\"]\n      }\n    ],\n    mapping: \"č\",\n    examples: [\"approa*ch*\", \"*ch*icken\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"d\"]\n      }\n    ],\n    mapping: \"d\",\n    examples: [\"*d*og\", \"*d*inosaur\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"də\"]\n      }\n    ],\n    mapping: \"da\",\n    examples: [\"*du*st\", \"aque*du*ct\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"cambridge\",\n        sounds: [\"ɚ$\"]\n      },\n      {\n        dictionaryKey: \"opendict\",\n        sounds: [\"ɛ\", \"ɝ$\"]\n      },\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"e\", \"ə\"]\n      }\n    ],\n    mapping: \"e\",\n    examples: [\"r*e*d\", \"b*e*ar\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"eɪ\"]\n      }\n    ],\n    mapping: \"eí\",\n    examples: [\"sn*a*ke\", \"sh*a*pe\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"ɝ\"]\n      }\n    ],\n    mapping: \"ë\",\n    examples: [\"t*ur*tle\", \"p*ur*ple\"]\n  },\n  /*\n    [f, θ, v$] - f\n    ɡ - g\n    h - h\n    [i, ɪ] - i\n    ɪˈr/ɝˈ - ir\n    əs$ - is\n  */\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"f\", \"θ\", \"v$\"]\n      }\n    ],\n    mapping: \"f\",\n    examples: [\"*f*rog\", \"*f*eather\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"ɡ\"]\n      }\n    ],\n    mapping: \"g\",\n    examples: [\"*g*oat\", \"*g*reen\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"h\"]\n      }\n    ],\n    mapping: \"h\",\n    examples: [\"*h*orse\", \"*h*ouse\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"i\", \"ɪ\"]\n      }\n    ],\n    mapping: \"i\",\n    examples: [\"p*i*g\", \"z*e*bra\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"cambridge\",\n        sounds: [\"ɪˈr\"]\n      },\n      {\n        dictionaryKey: \"opendict\",\n        sounds: [\"ɝˈ\"]\n      }\n    ],\n    mapping: \"ir\",\n    examples: [\"g*ir*affe\", \"b*ur*rito\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"əs$\"]\n      }\n    ],\n    mapping: \"is\",\n    examples: [\"vers*us*\"]\n  },\n  /*\n    j - í\n    ʒ - j\n    [k, ɡ$] - k\n    [l/ɫ, əl/əɫ$] - l\n    lə - le\n    m - m\n  */\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"j\"]\n      }\n    ],\n    mapping: \"í\",\n    examples: [\"*y*ak\", \"*y*acht\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"ʒ\"]\n      }\n    ],\n    mapping: \"j\",\n    examples: [\"televi*sio*n\", \"*j*ade\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"k\", \"ɡ$\"]\n      }\n    ],\n    mapping: \"k\",\n    examples: [\"pin*k*\", \"*c*offee\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"cambridge\",\n        sounds: [\"l\", \"əl$\"]\n      },\n      {\n        dictionaryKey: \"opendict\",\n        sounds: [\"ɫ\", \"əɫ$\"]\n      }\n    ],\n    mapping: \"l\",\n    examples: [\"*l*ion\", \"go*l*d\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"lə\"]\n      }\n    ],\n    mapping: \"le\",\n    examples: [\"te*le*vision\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"m\"]\n      }\n    ],\n    mapping: \"m\",\n    examples: [\"*m*auve\", \"*m*ouse\"]\n  },\n  /*\n    [n, ŋ, ən, ɪn/$] - n\n    nə - na\n    [ʊ, ɔːr/ɔɹ] - o\n    ɔɪ - oí\n    oʊ - ō\n    [p, b$] - p\n  */\n  {\n    sources: [\n      {\n        dictionaryKey: \"cambridge\",\n        sounds: [\"ɪn$\"]\n      },\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"n\", \"ŋ\", \"ən\"]\n      }\n    ],\n    mapping: \"n\",\n    examples: [\"brow*n*\", \"sa*n*d\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"nə\"]\n      }\n    ],\n    mapping: \"na\",\n    examples: [\"di*no*saur\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"cambridge\",\n        sounds: [\"ɔːr\"]\n      },\n      {\n        dictionaryKey: \"opendict\",\n        sounds: [\"ɔɹ\"]\n      },\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"ʊ\"]\n      }\n    ],\n    mapping: \"o\",\n    examples: [\"w*o*lf\", \"h*o*rse\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"ɔɪ\"]\n      }\n    ],\n    mapping: \"oí\",\n    examples: [\"turq*uoi*se\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"oʊ\"]\n      }\n    ],\n    mapping: \"ō\",\n    examples: [\"g*oa*t\", \"appr*oa*ch\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"p\", \"b$\"]\n      }\n    ],\n    mapping: \"p\",\n    examples: [\"*p*ig\", \"*p*enguin\"]\n  },\n  /*\n    r/ɹ - r\n    rə - ra\n    r/ɹ$ - *\n    [s, z$] - s\n    ʃ - š\n    [t, t̬/, d$] - t\n  */\n  {\n    sources: [\n      {\n        dictionaryKey: \"cambridge\",\n        sounds: [\"r\"]\n      },\n      {\n        dictionaryKey: \"opendict\",\n        sounds: [\"ɹ\"]\n      }\n    ],\n    mapping: \"r\",\n    examples: [\"*r*ed\", \"f*r*og\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"rə\"]\n      }\n    ],\n    mapping: \"ra\",\n    examples: [\"zeb*ra*\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"cambridge\",\n        sounds: [\"r$\"]\n      },\n      {\n        dictionaryKey: \"opendict\",\n        sounds: [\"ɹ$\"]\n      }\n    ],\n    mapping: \"\",\n    examples: [\"bea*r*\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"s\", \"z$\"]\n      }\n    ],\n    mapping: \"s\",\n    examples: [\"*s*nake\", \"*s*un\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"ʃ\"]\n      }\n    ],\n    mapping: \"š\",\n    examples: [\"*sh*eep\", \"*sh*epherd\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"cambridge\",\n        sounds: [\"t̬\"]\n      },\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"t\", \"d$\"]\n      }\n    ],\n    mapping: \"t\",\n    examples: [\"re*d*\", \"goa*t*\"]\n  },\n  /*\n    [u, jə] - u\n    [v, ð] - v\n    w - w\n    wə - we\n    z - z\n  */\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"u\", \"jə\"]\n      }\n    ],\n    mapping: \"u\",\n    examples: [\"bl*ue*\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"v\", \"ð\"]\n      }\n    ],\n    mapping: \"v\",\n    examples: [\"fea*th*er\", \"bea*v*er\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"w\"]\n      }\n    ],\n    mapping: \"w\",\n    examples: [\"*w*olf\", \"*w*ood\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"wə\"]\n      }\n    ],\n    mapping: \"we\",\n    examples: [\"aq*ue*duct\"]\n  },\n  {\n    sources: [\n      {\n        dictionaryKey: \"any\",\n        sounds: [\"z\"]\n      }\n    ],\n    mapping: \"z\",\n    examples: [\"*z*ebra\", \"*z*oo\"]\n  }\n  // {\n  //   sources: [\n  //     {\n  //       dictionaryKey: \"\",\n  //       sounds: [],\n  //     },\n  //   ],\n  //   mapping: \"\",\n  //   examples: [],\n  // },\n];\nfunction dictionaryItem($$payload, key, name, description, url) {\n  $$payload.out.push(`<li class=\"list-group-item\"${attr_style(\"\", { \"border-left\": `4px solid #${lightDictionaryColors[key]}` })}><strong><a${attr(\"href\", url)} target=\"_blank\" rel=\"noopener noreferrer\">${escape_html(name)}</a></strong> <p class=\"mb-0 text-muted\">${escape_html(description)}</p></li>`);\n}\nfunction sourceItem($$payload, name, description, url) {\n  $$payload.out.push(`<li class=\"list-group-item\"><strong><a${attr(\"href\", url)} target=\"_blank\" rel=\"noopener noreferrer\">${escape_html(name)}</a></strong> <p class=\"mb-0 text-muted\">${escape_html(description)}</p></li>`);\n}\nfunction dialectItem($$payload, name, description, priority) {\n  $$payload.out.push(`<li class=\"list-group-item\"><div class=\"d-flex align-items-center\"><span class=\"badge bg-primary me-2\">${escape_html(priority)}</span> <div><strong>${escape_html(name)}</strong> <p class=\"mb-0 text-muted\">${escape_html(description)}</p></div></div></li>`);\n}\nfunction highlightedExample($$payload, example) {\n  $$payload.out.push(`${html((() => {\n    const [before, middle, after] = example.split(\"*\");\n    return [\n      `<span>${before}</span>`,\n      `<strong style=\"color:#d63384\">${middle}</strong>`,\n      `<span>${after}</span>`\n    ].join(\"\");\n  })())}`);\n}\nfunction _page($$payload, $$props) {\n  push();\n  const alphabet = [\n    \"Aa\",\n    \"Āā\",\n    \"Bb\",\n    \"Čč\",\n    \"Dd\",\n    \"Ee\",\n    \"Ëë\",\n    \"Ff\",\n    \"Gg\",\n    \"Hh\",\n    \"Ii\",\n    \"Íí\",\n    \"Jj\",\n    \"Kk\",\n    \"Ll\",\n    \"Mm\",\n    \"Nn\",\n    \"Oo\",\n    \"Ōō\",\n    \"Pp\",\n    \"Rr\",\n    \"Ss\",\n    \"Šš\",\n    \"Tt\",\n    \"Uu\",\n    \"Vv\",\n    \"Ww\",\n    \"Yy\",\n    \"Zz\"\n  ];\n  const i18n = {\n    en: {\n      _page: { title: \"New English — Commune\" },\n      title: \"New English: A Phonetic Revolution\",\n      problem: {\n        title: \"The Challenge\",\n        description: \"English writing and pronunciation have diverged significantly over centuries, creating numerous challenges.\",\n        difficulties: {\n          description: \"The current English language presents several difficulties\",\n          list: [\n            \"Significant difference between written and spoken forms\",\n            \"Inconsistent pronunciation rules with many exceptions\",\n            \"Difficult accents that vary across regions\",\n            \"Overcomplicated spelling rules that don't match pronunciation\",\n            \"Natural reduction in spoken language not reflected in writing\",\n            \"Redundant elements like multiple ways to represent the same sound\"\n          ]\n        }\n      },\n      idea: {\n        title: \"Early Attempts\",\n        description: {\n          1: \"New English: Learning from Early Challenges in Phonetic Reform\",\n          2: `Our journey began with a simple question: \"How might English evolve over time?\" Our first attempt at creating New English faced significant challenges, as it incorporated overly aggressive simplifications that ultimately compromised the system's integrity.`,\n          3: \"This early version relied on online translator pronunciations rather than established dictionaries. This approach proved problematic, as it lacked the linguistic foundation necessary to establish consistent rules. The resulting translations were unstable, inconsistent across platforms, and resistant to systematic automation.\"\n        }\n      },\n      implementation: {\n        title: \"Our Implementation\",\n        description: \"A systematic approach to reforming English spelling and pronunciation.\",\n        features: {\n          description: \"Our New English implementation features\",\n          list: [\n            \"Words written exactly as they are pronounced\",\n            \"Reduction of indefinite articles to their essential forms\",\n            'Simplification of definite articles and demonstratives to \"da\"',\n            \"Consistent phonetic representation for all sounds\",\n            \"Elimination of silent letters and redundant spellings\",\n            \"Preservation of word recognition while improving logical consistency\"\n          ]\n        }\n      },\n      dictionaries: {\n        title: \"Reference Dictionaries\",\n        description: \"Our system is based on established phonetic standards from these authoritative sources\",\n        cambridge: {\n          name: \"Cambridge English Dictionary\",\n          description: \"A comprehensive dictionary of English with phonetic transcriptions\"\n        },\n        opendict: {\n          name: \"Open Dictionary\",\n          description: \"Open-licensed dictionary data\"\n        }\n      },\n      sources: {\n        title: \"Research Sources\",\n        description: \"Our approach is informed by these linguistic resources\",\n        englishPhonology: {\n          name: \"English Phonology - Wikipedia\",\n          description: \"Overview of the sound system of the English language\"\n        },\n        internationalPhoneticAlphabet: {\n          name: \"International Phonetic Alphabet\",\n          description: \"Standardized representation of sounds in written form\"\n        },\n        americanIpaChart: {\n          name: \"American IPA Chart\",\n          description: \"Chart of the American IPA\"\n        }\n      },\n      dialects: {\n        title: \"Reference Dialects\",\n        description: \"Our phonetic system prioritizes these dialects in descending order\",\n        generalAmerican: {\n          name: \"General American\",\n          description: \"The accent of American English most commonly perceived as neutral\"\n        },\n        standardEnglish: {\n          name: \"Standard English\",\n          description: \"The standard accent of Standard English in the United Kingdom\"\n        },\n        localDialect: {\n          name: \"Local Dialect\",\n          description: \"The form of English used in the local area\"\n        }\n      },\n      alphabet: { title: \"New English Alphabet\" },\n      rules: {\n        title: \"Phonetic Rules\",\n        description: \"Each sound is consistently represented by specific letter(s)\",\n        phoneticSounds: \"Phonetic Sounds\",\n        newEnglishLetter: \"New English Letter(s)\",\n        exampleWords: \"Example Words\"\n      },\n      nuances: {\n        title: \"Language Nuances\",\n        description: \"New English includes several practical modifications to improve consistency and pronunciation flow\",\n        pronoun: {\n          title: \"Personal Pronouns\",\n          description: \"New English uses 'mi' instead of 'I'/'me' for consistency with other pronoun forms and to eliminate the arbitrary capitalization rule.\"\n        },\n        interrogatives: {\n          title: \"Question Words\",\n          description: \"Question words often use the '-sa' suffix to avoid consonant clusters and improve pronunciation flow:\",\n          tableHeaders: {\n            original: \"Original\",\n            preferred: \"Preferred\",\n            expected: \"Expected\"\n          },\n          examples: [\n            {\n              before: \"what\",\n              after: { preferred: \"wotsa\", expected: \"wot\" }\n            },\n            { before: \"who\", after: { preferred: \"hysa\", expected: \"hy\" } },\n            {\n              before: \"when\",\n              after: { preferred: \"wensa\", expected: \"wen\" }\n            },\n            {\n              before: \"where\",\n              after: { preferred: \"wersa\", expected: \"wer\" }\n            },\n            {\n              before: \"which\",\n              after: { preferred: \"wičsa\", expected: \"wič\" }\n            },\n            {\n              before: \"why\",\n              after: { preferred: \"waísa\", expected: \"waí\" }\n            },\n            {\n              before: \"how\",\n              after: { preferred: \"haysa\", expected: \"hay\" }\n            },\n            {\n              before: \"there\",\n              after: { preferred: \"tersa\", expected: \"ter\" }\n            },\n            {\n              before: \"then\",\n              after: { preferred: \"tensa\", expected: \"ten\" }\n            }\n          ],\n          note: `The '-sa' suffix originated from early language development and helps avoid difficult consonant combinations in speech. Its original meaning was to use ending from \"there's a\".`\n        },\n        irregularVerbs: {\n          title: \"Regular Verb Forms\",\n          description: \"New English uses regular '-it' endings for past tense instead of irregular forms, creating consistent conjugation patterns.\",\n          tableHeaders: {\n            english: \"English\",\n            newEnglish: \"New English\",\n            common: \"Common\",\n            past: \"Past\",\n            pastParticiple: \"Past Participle\"\n          },\n          examples: [\n            {\n              english: { common: \"cut\", past: \"cut\", pastParticiple: \"cut\" },\n              newEnglish: { common: \"kat\", past: \"katit\" }\n            },\n            {\n              english: { common: \"speak\", past: \"spoke\", pastParticiple: \"spoken\" },\n              newEnglish: { common: \"spik\", past: \"spikit\" }\n            },\n            {\n              english: { common: \"know\", past: \"knew\", pastParticiple: \"known\" },\n              newEnglish: { common: \"nō\", past: \"nōit\" }\n            },\n            {\n              english: { common: \"bring\", past: \"brought\", pastParticiple: \"brought\" },\n              newEnglish: { common: \"brin\", past: \"brinit\" }\n            },\n            {\n              english: { common: \"see\", past: \"saw\", pastParticiple: \"seen\" },\n              newEnglish: {\n                common: \"si\",\n                past: \"sit\",\n                note: \"double letter cannot be preserved because of language mechanics\"\n              }\n            }\n          ]\n        }\n      }\n    },\n    ru: {\n      _page: {\n        title: \"Новый английский — Коммуна\"\n      },\n      title: \"Новый английский: фонетическая революция\",\n      problem: {\n        title: \"Проблема\",\n        description: \"Правописание и произношение в английском языке значительно разошлись за века, создавая множество трудностей.\",\n        difficulties: {\n          description: \"Современный английский язык создаёт ряд затруднений\",\n          list: [\n            \"Существенное расхождение между письменной и устной формами\",\n            \"Непоследовательные правила произношения с множеством исключений\",\n            \"Сложные акценты, сильно различающиеся по регионам\",\n            \"Сложные правила орфографии, не соответствующие произношению\",\n            \"Естественные сокращения в устной речи не отражаются на письме\",\n            \"Избыточные элементы, такие как разные способы обозначения одного и того же звука\"\n          ]\n        }\n      },\n      idea: {\n        title: \"Ранние попытки\",\n        description: {\n          1: \"Новый английский: уроки ранних трудностей фонетической реформы\",\n          2: \"Наш путь начался с простого вопроса: «Как английский может развиваться со временем?» Первая попытка создать Новый английский столкнулась с серьёзными трудностями из-за чрезмерных упрощений, которые в итоге подорвали целостность системы.\",\n          3: \"Ранняя версия опиралась на произношения онлайн-переводчиков вместо признанных словарей. Такой подход оказался проблематичным, поскольку не имел лингвистической базы для создания последовательных правил. В результате переводы были нестабильны, непоследовательны между платформами и не поддавались автоматизации.\"\n        }\n      },\n      implementation: {\n        title: \"Наша реализация\",\n        description: \"Системный подход к реформе английской орфографии и произношения.\",\n        features: {\n          description: \"Особенности нашей реализации Нового английского\",\n          list: [\n            \"Слова пишутся точно так, как произносятся\",\n            \"Неопределённые артикли сведены к их сути\",\n            \"Определённые артикли и указательные слова упрощены до «da»\",\n            \"Последовательное фонетическое обозначение всех звуков\",\n            \"Исключение немых букв и избыточных написаний\",\n            \"Сохранение узнаваемости слов при улучшении логической согласованности\"\n          ]\n        }\n      },\n      dictionaries: {\n        title: \"Справочные словари\",\n        description: \"Наша система основывается на признанных фонетических стандартах из этих авторитетных источников\",\n        cambridge: {\n          name: \"Кембриджский словарь английского языка\",\n          description: \"Обширный словарь английского языка с фонетическими транскрипциями\"\n        },\n        opendict: {\n          name: \"OpenDict\",\n          description: \"Свободно-лицензированные данные словаря\"\n        }\n      },\n      sources: {\n        title: \"Исследовательские источники\",\n        description: \"Наш подход основан на следующих лингвистических ресурсах\",\n        englishPhonology: {\n          name: \"Фонология английского языка — Википедия\",\n          description: \"Обзор звуковой системы английского языка\"\n        },\n        internationalPhoneticAlphabet: {\n          name: \"Международный фонетический алфавит\",\n          description: \"Стандартизированное представление звуков в письменной форме\"\n        },\n        americanIpaChart: {\n          name: \"IPA-таблица американского английского\",\n          description: \"Таблица фонем американского английского\"\n        }\n      },\n      dialects: {\n        title: \"Базовые диалекты\",\n        description: \"Наша фонетическая система отдаёт приоритет этим диалектам в порядке убывания\",\n        generalAmerican: {\n          name: \"Общий американский\",\n          description: \"Акцент американского английского, воспринимаемый как нейтральный\"\n        },\n        standardEnglish: {\n          name: \"Стандартный английский\",\n          description: \"Стандартный акцент британского английского\"\n        },\n        localDialect: {\n          name: \"Местный диалект\",\n          description: \"Форма английского языка, используемая в конкретной местности\"\n        }\n      },\n      alphabet: {\n        title: \"Алфавит Нового английского\"\n      },\n      rules: {\n        title: \"Фонетические правила\",\n        description: \"Каждый звук последовательно обозначается определённой буквой или буквами\",\n        phoneticSounds: \"Фонетические звуки\",\n        newEnglishLetter: \"Буквы Нового английского\",\n        exampleWords: \"Примеры слов\"\n      },\n      nuances: {\n        title: \"Особенности языка\",\n        description: \"Новый английский включает несколько практических изменений для улучшения согласованности и удобства произношения\",\n        pronoun: {\n          title: \"Личные местоимения\",\n          description: \"Новый английский использует 'mi' вместо 'I'/'me' для согласованности с другими формами местоимений и устранения произвольного правила заглавных букв.\"\n        },\n        interrogatives: {\n          title: \"Вопросительные слова\",\n          description: \"Вопросительные слова часто используют суффикс '-sa' для избежания скопления согласных и улучшения произношения:\",\n          tableHeaders: {\n            original: \"Оригинал\",\n            preferred: \"Предпочтительно\",\n            expected: \"Ожидаемо\"\n          },\n          examples: [\n            {\n              before: \"what\",\n              after: { preferred: \"wotsa\", expected: \"wot\" }\n            },\n            { before: \"who\", after: { preferred: \"hysa\", expected: \"hy\" } },\n            {\n              before: \"when\",\n              after: { preferred: \"wensa\", expected: \"wen\" }\n            },\n            {\n              before: \"where\",\n              after: { preferred: \"wersa\", expected: \"wer\" }\n            },\n            {\n              before: \"which\",\n              after: { preferred: \"wičsa\", expected: \"wič\" }\n            },\n            {\n              before: \"why\",\n              after: { preferred: \"waísa\", expected: \"waí\" }\n            },\n            {\n              before: \"how\",\n              after: { preferred: \"haysa\", expected: \"hay\" }\n            },\n            {\n              before: \"there\",\n              after: { preferred: \"tersa\", expected: \"ter\" }\n            },\n            {\n              before: \"then\",\n              after: { preferred: \"tensa\", expected: \"ten\" }\n            }\n          ],\n          note: `Суффикс '-sa' возник при ранней разработке языка и помогает избежать сложных сочетаний согласных в речи. Его первоначальным значением было использование окончания от \"there's a\".`\n        },\n        irregularVerbs: {\n          title: \"Правильные формы глаголов\",\n          description: \"Новый английский использует регулярные окончания '-it' для прошедшего времени вместо неправильных форм, создавая последовательные модели спряжения.\",\n          tableHeaders: {\n            english: \"Английский\",\n            newEnglish: \"Новый английский\",\n            common: \"Обычная форма\",\n            past: \"Прошедшее время\",\n            pastParticiple: \"Причастие прошедшего времени\"\n          },\n          examples: [\n            {\n              english: { common: \"cut\", past: \"cut\", pastParticiple: \"cut\" },\n              newEnglish: { common: \"kat\", past: \"katit\" }\n            },\n            {\n              english: { common: \"speak\", past: \"spoke\", pastParticiple: \"spoken\" },\n              newEnglish: { common: \"spik\", past: \"spikit\" }\n            },\n            {\n              english: { common: \"know\", past: \"knew\", pastParticiple: \"known\" },\n              newEnglish: { common: \"nō\", past: \"nōit\" }\n            },\n            {\n              english: { common: \"bring\", past: \"brought\", pastParticiple: \"brought\" },\n              newEnglish: { common: \"brin\", past: \"brinit\" }\n            },\n            {\n              english: { common: \"see\", past: \"saw\", pastParticiple: \"seen\" },\n              newEnglish: {\n                common: \"si\",\n                past: \"sit\",\n                note: \"сдвоенный звук не может быть сохранён из-за механики языка\"\n              }\n            }\n          ]\n        }\n      }\n    }\n  };\n  const { data } = $$props;\n  const { locale } = data;\n  const t = i18n[locale];\n  const each_array = ensure_array_like(t.problem.difficulties.list);\n  const each_array_1 = ensure_array_like(t.implementation.features.list);\n  const each_array_2 = ensure_array_like(alphabet);\n  const each_array_3 = ensure_array_like(rules);\n  const each_array_7 = ensure_array_like(t.nuances.interrogatives.examples);\n  const each_array_8 = ensure_array_like(t.nuances.irregularVerbs.examples);\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;\n  });\n  $$payload.out.push(`<div class=\"container my-5\"><div class=\"responsive-container\"><h1 class=\"mb-4\">${escape_html(t.title)}</h1> <section class=\"mb-5\"><h2>${escape_html(t.problem.title)}</h2> <div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.problem.description)}</p> <p>${escape_html(t.problem.difficulties.description)}:</p> <ul><!--[-->`);\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let difficulty = each_array[$$index];\n    $$payload.out.push(`<li>${escape_html(difficulty)}</li>`);\n  }\n  $$payload.out.push(`<!--]--></ul></div></div></section> <section class=\"mb-5\"><h2>${escape_html(t.idea.title)}</h2> <div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.idea.description[1])}</p> <p>${escape_html(t.idea.description[2])}</p> <p>${escape_html(t.idea.description[3])}</p></div></div></section> <section class=\"mb-5\"><h2>${escape_html(t.implementation.title)}</h2> <div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.implementation.description)}</p> <p>${escape_html(t.implementation.features.description)}:</p> <ul><!--[-->`);\n  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n    let feature = each_array_1[$$index_1];\n    $$payload.out.push(`<li>${escape_html(feature)}</li>`);\n  }\n  $$payload.out.push(`<!--]--></ul></div></div></section> <section class=\"mb-5\"><h2>${escape_html(t.dictionaries.title)}</h2> <div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.dictionaries.description)}:</p> <ul class=\"list-group list-group-flush\">`);\n  dictionaryItem($$payload, \"cambridge\", t.dictionaries.cambridge.name, t.dictionaries.cambridge.description, \"https://dictionary.cambridge.org\");\n  $$payload.out.push(`<!----> `);\n  dictionaryItem($$payload, \"opendict\", t.dictionaries.opendict.name, t.dictionaries.opendict.description, \"https://open-dict-data.github.io\");\n  $$payload.out.push(`<!----></ul></div></div></section> <section class=\"mb-5\"><h2>${escape_html(t.sources.title)}</h2> <div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.sources.description)}:</p> <ul class=\"list-group list-group-flush\">`);\n  sourceItem($$payload, t.sources.englishPhonology.name, t.sources.englishPhonology.description, \"https://en.wikipedia.org/wiki/English_phonology\");\n  $$payload.out.push(`<!----> `);\n  sourceItem($$payload, t.sources.internationalPhoneticAlphabet.name, t.sources.internationalPhoneticAlphabet.description, \"https://www.internationalphoneticalphabet.org/\");\n  $$payload.out.push(`<!----> `);\n  sourceItem($$payload, t.sources.americanIpaChart.name, t.sources.americanIpaChart.description, \"https://americanipachart.com/\");\n  $$payload.out.push(`<!----></ul></div></div></section> <section class=\"mb-5\"><h2>${escape_html(t.dialects.title)}</h2> <div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.dialects.description)}:</p> <ul class=\"list-group list-group-flush\">`);\n  dialectItem($$payload, t.dialects.generalAmerican.name, t.dialects.generalAmerican.description, 1);\n  $$payload.out.push(`<!----> `);\n  dialectItem($$payload, t.dialects.standardEnglish.name, t.dialects.standardEnglish.description, 2);\n  $$payload.out.push(`<!----> `);\n  dialectItem($$payload, t.dialects.localDialect.name, t.dialects.localDialect.description, 3);\n  $$payload.out.push(`<!----></ul></div></div></section> <section class=\"mb-5\"><h2>${escape_html(t.alphabet.title)}</h2> <div class=\"card\"><div class=\"card-body\"><div class=\"row row-cols-2 row-cols-md-3 row-cols-lg-6 g-2\"><!--[-->`);\n  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {\n    let item = each_array_2[$$index_2];\n    $$payload.out.push(`<div class=\"col\"><div class=\"card h-100\"><div class=\"card-body text-center\"><h3 class=\"card-title\">${escape_html(item)}</h3></div></div></div>`);\n  }\n  $$payload.out.push(`<!--]--></div></div></div></section> <section class=\"mb-5\"><h2>${escape_html(t.rules.title)}</h2> <div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.rules.description)}:</p> <div class=\"table-responsive\"><table class=\"table table-striped\"><thead><tr><th>${escape_html(t.rules.phoneticSounds)}</th><th>${escape_html(t.rules.newEnglishLetter)}</th><th>${escape_html(t.rules.exampleWords)}</th></tr></thead><tbody><!--[-->`);\n  for (let $$index_6 = 0, $$length = each_array_3.length; $$index_6 < $$length; $$index_6++) {\n    let rule = each_array_3[$$index_6];\n    const each_array_4 = ensure_array_like(rule.sources);\n    const each_array_6 = ensure_array_like(rule.examples);\n    $$payload.out.push(`<tr><td><!--[-->`);\n    for (let $$index_4 = 0, $$length2 = each_array_4.length; $$index_4 < $$length2; $$index_4++) {\n      let source = each_array_4[$$index_4];\n      const each_array_5 = ensure_array_like(source.sounds);\n      $$payload.out.push(`<div class=\"mb-1\"><!--[-->`);\n      for (let $$index_3 = 0, $$length3 = each_array_5.length; $$index_3 < $$length3; $$index_3++) {\n        let sound = each_array_5[$$index_3];\n        $$payload.out.push(`<span class=\"badge rounded-pill me-2 font-freemono\"${attr_style(\"\", {\n          \"background-color\": `#${lightDictionaryColors[source.dictionaryKey]}`,\n          color: \"#000000\",\n          padding: \"6px 12px\",\n          \"margin-bottom\": \"6px\",\n          \"font-size\": \"1.2rem\",\n          \"font-weight\": \"normal\"\n        })}>${escape_html(sound)}</span>`);\n      }\n      $$payload.out.push(`<!--]--></div>`);\n    }\n    $$payload.out.push(`<!--]--></td><td><strong${attr_style(\"\", {\n      \"font-size\": \"1.5rem\",\n      display: \"inline-block\",\n      padding: \"4px 0\"\n    })}>${escape_html(rule.mapping)}</strong></td><td><!--[-->`);\n    for (let i = 0, $$length2 = each_array_6.length; i < $$length2; i++) {\n      let example = each_array_6[i];\n      $$payload.out.push(`<span>`);\n      if (i > 0) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`,`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n      }\n      $$payload.out.push(`<!--]--> `);\n      highlightedExample($$payload, example);\n      $$payload.out.push(`<!----></span>`);\n    }\n    $$payload.out.push(`<!--]--></td></tr>`);\n  }\n  $$payload.out.push(`<!--]--></tbody></table></div></div></div></section> `);\n  Examples_section($$payload, { locale });\n  $$payload.out.push(`<!----> <section class=\"mb-5\"><h2>${escape_html(t.nuances.title)}</h2> <div class=\"card\"><div class=\"card-body\"><p class=\"lead\">${escape_html(t.nuances.description)}:</p> <div class=\"mb-4\"><h4 class=\"h5\">${escape_html(t.nuances.pronoun.title)}</h4> <p>${escape_html(t.nuances.pronoun.description)}</p></div> <div class=\"mb-4\"><h4 class=\"h5\">${escape_html(t.nuances.interrogatives.title)}</h4> <p>${escape_html(t.nuances.interrogatives.description)}</p> <div class=\"d-inline-block\"><table class=\"table table-sm table-striped\" style=\"width: auto; min-width: 400px;\"><thead><tr><th>${escape_html(t.nuances.interrogatives.tableHeaders.original)}</th><th>${escape_html(t.nuances.interrogatives.tableHeaders.preferred)}</th><th>${escape_html(t.nuances.interrogatives.tableHeaders.expected)}</th></tr></thead><tbody><!--[-->`);\n  for (let $$index_7 = 0, $$length = each_array_7.length; $$index_7 < $$length; $$index_7++) {\n    let example = each_array_7[$$index_7];\n    $$payload.out.push(`<tr><td><code class=\"text-dark\">${escape_html(example.before)}</code></td><td><code class=\"text-primary fw-bold\">${escape_html(example.after.preferred)}</code></td><td><code class=\"text-secondary\">${escape_html(example.after.expected)}</code></td></tr>`);\n  }\n  $$payload.out.push(`<!--]--></tbody></table></div> <div class=\"alert alert-info mt-2\"><small><strong>Note:</strong> ${escape_html(t.nuances.interrogatives.note)}</small></div></div> <div class=\"mb-4\"><h4 class=\"h5\">${escape_html(t.nuances.irregularVerbs.title)}</h4> <p>${escape_html(t.nuances.irregularVerbs.description)}</p> <div class=\"table-responsive\"><table class=\"table table-sm table-striped\"><thead><tr><th colspan=\"3\" class=\"text-center bg-light\">${escape_html(t.nuances.irregularVerbs.tableHeaders.english)}</th><th colspan=\"2\" class=\"text-center bg-primary text-white\">${escape_html(t.nuances.irregularVerbs.tableHeaders.newEnglish)}</th></tr><tr><th>${escape_html(t.nuances.irregularVerbs.tableHeaders.common)}</th><th>${escape_html(t.nuances.irregularVerbs.tableHeaders.past)}</th><th>${escape_html(t.nuances.irregularVerbs.tableHeaders.pastParticiple)}</th><th>${escape_html(t.nuances.irregularVerbs.tableHeaders.common)}</th><th>${escape_html(t.nuances.irregularVerbs.tableHeaders.past)}</th></tr></thead><tbody><!--[-->`);\n  for (let $$index_8 = 0, $$length = each_array_8.length; $$index_8 < $$length; $$index_8++) {\n    let example = each_array_8[$$index_8];\n    $$payload.out.push(`<tr><td><code class=\"text-muted\">${escape_html(example.english.common)}</code></td><td><code class=\"text-muted\">${escape_html(example.english.past)}</code></td><td><code class=\"text-muted\">${escape_html(example.english.pastParticiple)}</code></td><td><code class=\"text-primary fw-bold\">${escape_html(example.newEnglish.common)}</code></td><td><code class=\"text-primary fw-bold\">${escape_html(example.newEnglish.past)}</code> `);\n    if (example.newEnglish.past === \"sit\" && example.newEnglish.note) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<span class=\"ms-1 text-info\" style=\"cursor: help;\"${attr(\"title\", example.newEnglish.note)}><i class=\"bi bi-question-circle\"></i></span>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></td></tr>`);\n  }\n  $$payload.out.push(`<!--]--></tbody></table></div></div></div></div></section></div></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;AAEA,MAAM,qBAAqB,GAAG;AAC9B,EAAE,SAAS,EAAE,QAAQ;AACrB;AACA,EAAE,QAAQ,EAAE,QAAQ;AACpB;AACA,EAAE,GAAG,EAAE;AACP;AACA,CAAC;AACD,MAAM,oBAAoB,GAAG;AAC7B,EAAE,SAAS,EAAE,QAAQ;AACrB;AACA,EAAE,QAAQ,EAAE,QAAQ;AACpB;AACA,EAAE,GAAG,EAAE;AACP;AACA,CAAC;AACD,MAAM,QAAQ,GAAG;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,UAAU;AACvB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,OAAO;AACvB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,KAAK;AAClB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,KAAK;AAClB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,KAAK;AAClB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,KAAK;AAClB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,QAAQ;AACrB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,KAAK,CAAC;AACzB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,KAAK,CAAC;AACzB,UAAU,OAAO,EAAE;AACnB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,IAAI;AACpB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,SAAS;AAC1B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,OAAO;AACvB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,QAAQ;AACzB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,QAAQ;AACrB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,SAAS;AAC1B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,OAAO;AACvB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,KAAK,CAAC;AACzB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,KAAK;AAC9B,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,OAAO;AACvB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,YAAY;AACzB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,eAAe;AAChC,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,UAAU;AAC1B,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,SAAS;AAC1B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,QAAQ;AACxB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,KAAK,CAAC;AACzB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,SAAS;AAC1B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,UAAU;AACvB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,SAAS;AACzB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,KAAK,CAAC;AACzB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,QAAQ;AACxB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,KAAK,CAAC;AACzB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,QAAQ;AACrB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,SAAS;AAC1B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,OAAO;AACvB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,KAAK;AAClB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,KAAK,CAAC;AACzB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,QAAQ;AACrB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,KAAK,CAAC;AACzB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,KAAK,CAAC;AACzB,UAAU,OAAO,EAAE;AACnB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,QAAQ;AACrB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,SAAS;AAC1B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,KAAK;AAC9B,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,OAAO;AACvB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,WAAW;AACxB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,QAAQ;AACxB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,MAAM;AACvB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,cAAc,EAAE;AACpB,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,UAAU,EAAE,OAAO;AACvB,IAAI,SAAS,EAAE;AACf,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ;AACR,UAAU,aAAa,EAAE,WAAW;AACpC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,UAAU;AACnC,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC;AACtB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC,GAAG,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA;AACA;AACA,CAAC;AACD,SAAS,QAAQ,CAAC,SAAS,EAAE,IAAI,EAAE;AACnC,EAAE,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO;AACtC,EAAE,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,KAAK,YAAY,CAAC;AAClF,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC;AAC1G,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAC9C,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACrE,MAAM,IAAI,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC;AACjC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE;AACjB,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,oBAAoB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;AAChK,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC;AACA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,WAAW,EAAE,+DAA+D;AAClF,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,cAAc,EAAE,iBAAiB;AACvC,MAAM,cAAc,EAAE,iBAAiB;AACvC,MAAM,gBAAgB,EAAE;AACxB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,WAAW,EAAE,qFAAqF;AACxG,MAAM,WAAW,EAAE,eAAe;AAClC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,cAAc,EAAE,eAAe;AACrC,MAAM,cAAc,EAAE,oBAAoB;AAC1C,MAAM,gBAAgB,EAAE;AACxB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO;AAC5B,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,eAAe,GAAG,IAAI;AAC5B,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,QAAQ,CAAC;AAClD,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0BAA0B,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,sDAAsD,CAAC,CAAC;AAC3N,EAAE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,KAAK,GAAG,QAAQ,EAAE,KAAK,EAAE,EAAE;AACjF,IAAI,IAAI,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC;AACrC,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AAClF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yGAAyG,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,KAAK,KAAK,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,cAAc,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,qCAAqC,CAAC,CAAC;AACvU,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;AACzE,MAAM,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;AACjD,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE;AACjB,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC;AAC7I,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uEAAuE,CAAC,CAAC;AACjG,IAAI,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,QAAQ,EAAE;AAChD,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wBAAwB,EAAE,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC;AAC7F,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,MAAM,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAChF,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACpC,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;AAC3E,QAAQ,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;AACjD,QAAQ,IAAI,CAAC,GAAG,CAAC,EAAE;AACnB,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,QAAQ,CAAC,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC,QAAQ;AACR,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC;AAC7I,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qEAAqE,EAAE,WAAW,CAAC,eAAe,KAAK,KAAK,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACxL,IAAI,IAAI,eAAe,KAAK,KAAK,EAAE;AACnC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,MAAM,YAAY,GAAG,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC;AAC/D,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,iCAAiC,CAAC,CAAC;AAClP,MAAM,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AACnG,QAAQ,IAAI,IAAI,GAAG,YAAY,CAAC,SAAS,CAAC;AAC1C,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACjC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,UAAU,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC;AACtD,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,CAAC;AAChD,UAAU,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AACvG,YAAY,IAAI,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC;AACjD,YAAY,MAAM,YAAY,GAAG,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC;AAClE,YAAY,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2BAA2B,CAAC,CAAC;AAC7D,YAAY,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AACzG,cAAc,IAAI,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC;AACjD,cAAc,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,UAAU,CAAC,EAAE,EAAE;AACvG,gBAAgB,kBAAkB,EAAE,CAAC,CAAC,EAAE,qBAAqB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;AACtF,gBAAgB,KAAK,EAAE,SAAS;AAChC,gBAAgB,OAAO,EAAE,UAAU;AACnC,gBAAgB,eAAe,EAAE,KAAK;AACtC,gBAAgB,WAAW,EAAE,QAAQ;AACrC,gBAAgB,aAAa,EAAE;AAC/B,eAAe,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;AAChD,YAAY;AACZ,YAAY,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AACjD,UAAU;AACV,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wBAAwB,EAAE,UAAU,CAAC,EAAE,EAAE;AACvE,YAAY,WAAW,EAAE,QAAQ;AACjC,YAAY,OAAO,EAAE,cAAc;AACnC,YAAY,OAAO,EAAE;AACrB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB,UAAU,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC;AACnC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAC1D,QAAQ,CAAC,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC,UAAU,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7D,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,CAAC;AAChD,UAAU,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AACvG,YAAY,IAAI,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC;AAC/C,YAAY,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,UAAU,CAAC,EAAE,EAAE;AACrG,cAAc,kBAAkB,EAAE,CAAC,CAAC,EAAE,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC;AACpE,cAAc,KAAK,EAAE,SAAS;AAC9B,cAAc,MAAM,EAAE,mBAAmB;AACzC,cAAc,OAAO,EAAE,UAAU;AACjC,cAAc,eAAe,EAAE,KAAK;AACpC,cAAc,WAAW,EAAE,QAAQ;AACnC,cAAc,aAAa,EAAE;AAC7B,aAAa,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;AAC9C,UAAU;AACV,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wBAAwB,EAAE,UAAU,CAAC,EAAE,EAAE;AACvE,YAAY,WAAW,EAAE,QAAQ;AACjC,YAAY,OAAO,EAAE,cAAc;AACnC,YAAY,OAAO,EAAE;AACrB,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAC,CAAC;AAC/D,QAAQ;AACR,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACtC,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AAC1D,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AACvC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mCAAmC,CAAC,CAAC;AAC3D,EAAE,GAAG,EAAE;AACP;AACA,MAAM,KAAK,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;AACpC;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ;AACjC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY;AACrC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS;AACnC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO;AAC/B,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS;AAClC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU;AACrC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,YAAY,EAAE,WAAW;AACxC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,OAAO,EAAE,YAAY;AACpC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY;AACrC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,WAAW;AAClC,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI;AAC1B,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG;AACzB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ;AAChC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS;AACnC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU;AACrC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI;AAC/B;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,QAAQ,EAAE,WAAW;AACpC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS;AAClC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS;AACnC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG;AACzB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS;AACjC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,WAAW;AAClC,QAAQ,MAAM,EAAE,CAAC,KAAK;AACtB,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW;AACvC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,KAAK;AACtB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,QAAQ,EAAE,CAAC,UAAU;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS;AACjC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,cAAc,EAAE,QAAQ;AACvC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI;AAC1B;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,QAAQ,EAAE,UAAU;AACnC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,WAAW;AAClC,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,KAAK;AAC3B,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,KAAK;AAC3B;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ;AACjC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,QAAQ,EAAE,CAAC,cAAc;AAC7B,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS;AACnC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,WAAW;AAClC,QAAQ,MAAM,EAAE,CAAC,KAAK;AACtB,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI;AAC/B;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,SAAS,EAAE,QAAQ;AAClC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,QAAQ,EAAE,CAAC,YAAY;AAC3B,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,WAAW;AAClC,QAAQ,MAAM,EAAE,CAAC,KAAK;AACtB,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS;AAClC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,QAAQ,EAAE,CAAC,aAAa;AAC5B,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY;AACrC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI;AAC1B;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,OAAO,EAAE,WAAW;AACnC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,WAAW;AAClC,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ;AAChC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,QAAQ,EAAE,CAAC,SAAS;AACxB,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,WAAW;AAClC,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,EAAE;AACf,IAAI,QAAQ,EAAE,CAAC,QAAQ;AACvB,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI;AAC1B;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,SAAS,EAAE,OAAO;AACjC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,SAAS,EAAE,YAAY;AACtC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,WAAW;AAClC,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI;AAC1B;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ;AAChC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI;AAC1B;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,QAAQ;AACvB,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG;AACzB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,WAAW,EAAE,UAAU;AACtC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ;AACjC,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,QAAQ,EAAE,CAAC,YAAY;AAC3B,GAAG;AACH,EAAE;AACF,IAAI,OAAO,EAAE;AACb,MAAM;AACN,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,CAAC,SAAS,EAAE,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,SAAS,cAAc,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE;AAChE,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2BAA2B,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC,WAAW,EAAE,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,2CAA2C,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC;AAC9S;AACA,SAAS,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE;AACvD,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,2CAA2C,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC;AAC9N;AACA,SAAS,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE;AAC7D,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uGAAuG,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,qBAAqB,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,qBAAqB,CAAC,CAAC;AACrR;AACA,SAAS,kBAAkB,CAAC,SAAS,EAAE,OAAO,EAAE;AAChD,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM;AACpC,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AACtD,IAAI,OAAO;AACX,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC;AAC9B,MAAM,CAAC,8BAA8B,EAAE,MAAM,CAAC,SAAS,CAAC;AACxD,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO;AAC5B,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AACd,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACV;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI;AACJ,GAAG;AACH,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAC/C,MAAM,KAAK,EAAE,oCAAoC;AACjD,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE,eAAe;AAC9B,QAAQ,WAAW,EAAE,6GAA6G;AAClI,QAAQ,YAAY,EAAE;AACtB,UAAU,WAAW,EAAE,4DAA4D;AACnF,UAAU,IAAI,EAAE;AAChB,YAAY,yDAAyD;AACrE,YAAY,uDAAuD;AACnE,YAAY,4CAA4C;AACxD,YAAY,+DAA+D;AAC3E,YAAY,+DAA+D;AAC3E,YAAY;AACZ;AACA;AACA,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,gBAAgB;AAC/B,QAAQ,WAAW,EAAE;AACrB,UAAU,CAAC,EAAE,gEAAgE;AAC7E,UAAU,CAAC,EAAE,CAAC,gQAAgQ,CAAC;AAC/Q,UAAU,CAAC,EAAE;AACb;AACA,OAAO;AACP,MAAM,cAAc,EAAE;AACtB,QAAQ,KAAK,EAAE,oBAAoB;AACnC,QAAQ,WAAW,EAAE,wEAAwE;AAC7F,QAAQ,QAAQ,EAAE;AAClB,UAAU,WAAW,EAAE,yCAAyC;AAChE,UAAU,IAAI,EAAE;AAChB,YAAY,8CAA8C;AAC1D,YAAY,2DAA2D;AACvE,YAAY,gEAAgE;AAC5E,YAAY,mDAAmD;AAC/D,YAAY,uDAAuD;AACnE,YAAY;AACZ;AACA;AACA,OAAO;AACP,MAAM,YAAY,EAAE;AACpB,QAAQ,KAAK,EAAE,wBAAwB;AACvC,QAAQ,WAAW,EAAE,wFAAwF;AAC7G,QAAQ,SAAS,EAAE;AACnB,UAAU,IAAI,EAAE,8BAA8B;AAC9C,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ,QAAQ,EAAE;AAClB,UAAU,IAAI,EAAE,iBAAiB;AACjC,UAAU,WAAW,EAAE;AACvB;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE,kBAAkB;AACjC,QAAQ,WAAW,EAAE,wDAAwD;AAC7E,QAAQ,gBAAgB,EAAE;AAC1B,UAAU,IAAI,EAAE,+BAA+B;AAC/C,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ,6BAA6B,EAAE;AACvC,UAAU,IAAI,EAAE,iCAAiC;AACjD,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ,gBAAgB,EAAE;AAC1B,UAAU,IAAI,EAAE,oBAAoB;AACpC,UAAU,WAAW,EAAE;AACvB;AACA,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,KAAK,EAAE,oBAAoB;AACnC,QAAQ,WAAW,EAAE,oEAAoE;AACzF,QAAQ,eAAe,EAAE;AACzB,UAAU,IAAI,EAAE,kBAAkB;AAClC,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,UAAU,IAAI,EAAE,kBAAkB;AAClC,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,UAAU,IAAI,EAAE,eAAe;AAC/B,UAAU,WAAW,EAAE;AACvB;AACA,OAAO;AACP,MAAM,QAAQ,EAAE,EAAE,KAAK,EAAE,sBAAsB,EAAE;AACjD,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,gBAAgB;AAC/B,QAAQ,WAAW,EAAE,8DAA8D;AACnF,QAAQ,cAAc,EAAE,iBAAiB;AACzC,QAAQ,gBAAgB,EAAE,uBAAuB;AACjD,QAAQ,YAAY,EAAE;AACtB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE,kBAAkB;AACjC,QAAQ,WAAW,EAAE,oGAAoG;AACzH,QAAQ,OAAO,EAAE;AACjB,UAAU,KAAK,EAAE,mBAAmB;AACpC,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,UAAU,KAAK,EAAE,gBAAgB;AACjC,UAAU,WAAW,EAAE,uGAAuG;AAC9H,UAAU,YAAY,EAAE;AACxB,YAAY,QAAQ,EAAE,UAAU;AAChC,YAAY,SAAS,EAAE,WAAW;AAClC,YAAY,QAAQ,EAAE;AACtB,WAAW;AACX,UAAU,QAAQ,EAAE;AACpB,YAAY;AACZ,cAAc,MAAM,EAAE,MAAM;AAC5B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;AAC3E,YAAY;AACZ,cAAc,MAAM,EAAE,MAAM;AAC5B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,MAAM,EAAE,OAAO;AAC7B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,MAAM,EAAE,OAAO;AAC7B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,MAAM,EAAE,KAAK;AAC3B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,MAAM,EAAE,KAAK;AAC3B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,MAAM,EAAE,OAAO;AAC7B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,MAAM,EAAE,MAAM;AAC5B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D;AACA,WAAW;AACX,UAAU,IAAI,EAAE,CAAC,gLAAgL;AACjM,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,UAAU,KAAK,EAAE,oBAAoB;AACrC,UAAU,WAAW,EAAE,6HAA6H;AACpJ,UAAU,YAAY,EAAE;AACxB,YAAY,OAAO,EAAE,SAAS;AAC9B,YAAY,UAAU,EAAE,aAAa;AACrC,YAAY,MAAM,EAAE,QAAQ;AAC5B,YAAY,IAAI,EAAE,MAAM;AACxB,YAAY,cAAc,EAAE;AAC5B,WAAW;AACX,UAAU,QAAQ,EAAE;AACpB,YAAY;AACZ,cAAc,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE;AAC5E,cAAc,UAAU,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO;AACxD,aAAa;AACb,YAAY;AACZ,cAAc,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE;AACnF,cAAc,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,OAAO,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE;AAChF,cAAc,UAAU,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM;AACtD,aAAa;AACb,YAAY;AACZ,cAAc,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE;AACtF,cAAc,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE;AAC7E,cAAc,UAAU,EAAE;AAC1B,gBAAgB,MAAM,EAAE,IAAI;AAC5B,gBAAgB,IAAI,EAAE,KAAK;AAC3B,gBAAgB,IAAI,EAAE;AACtB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,KAAK,EAAE,0CAA0C;AACvD,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE,UAAU;AACzB,QAAQ,WAAW,EAAE,8GAA8G;AACnI,QAAQ,YAAY,EAAE;AACtB,UAAU,WAAW,EAAE,qDAAqD;AAC5E,UAAU,IAAI,EAAE;AAChB,YAAY,4DAA4D;AACxE,YAAY,iEAAiE;AAC7E,YAAY,mDAAmD;AAC/D,YAAY,6DAA6D;AACzE,YAAY,+DAA+D;AAC3E,YAAY;AACZ;AACA;AACA,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,gBAAgB;AAC/B,QAAQ,WAAW,EAAE;AACrB,UAAU,CAAC,EAAE,gEAAgE;AAC7E,UAAU,CAAC,EAAE,8OAA8O;AAC3P,UAAU,CAAC,EAAE;AACb;AACA,OAAO;AACP,MAAM,cAAc,EAAE;AACtB,QAAQ,KAAK,EAAE,iBAAiB;AAChC,QAAQ,WAAW,EAAE,kEAAkE;AACvF,QAAQ,QAAQ,EAAE;AAClB,UAAU,WAAW,EAAE,iDAAiD;AACxE,UAAU,IAAI,EAAE;AAChB,YAAY,2CAA2C;AACvD,YAAY,0CAA0C;AACtD,YAAY,4DAA4D;AACxE,YAAY,uDAAuD;AACnE,YAAY,8CAA8C;AAC1D,YAAY;AACZ;AACA;AACA,OAAO;AACP,MAAM,YAAY,EAAE;AACpB,QAAQ,KAAK,EAAE,oBAAoB;AACnC,QAAQ,WAAW,EAAE,iGAAiG;AACtH,QAAQ,SAAS,EAAE;AACnB,UAAU,IAAI,EAAE,wCAAwC;AACxD,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ,QAAQ,EAAE;AAClB,UAAU,IAAI,EAAE,UAAU;AAC1B,UAAU,WAAW,EAAE;AACvB;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE,6BAA6B;AAC5C,QAAQ,WAAW,EAAE,0DAA0D;AAC/E,QAAQ,gBAAgB,EAAE;AAC1B,UAAU,IAAI,EAAE,yCAAyC;AACzD,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ,6BAA6B,EAAE;AACvC,UAAU,IAAI,EAAE,oCAAoC;AACpD,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ,gBAAgB,EAAE;AAC1B,UAAU,IAAI,EAAE,uCAAuC;AACvD,UAAU,WAAW,EAAE;AACvB;AACA,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,KAAK,EAAE,kBAAkB;AACjC,QAAQ,WAAW,EAAE,8EAA8E;AACnG,QAAQ,eAAe,EAAE;AACzB,UAAU,IAAI,EAAE,oBAAoB;AACpC,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,UAAU,IAAI,EAAE,wBAAwB;AACxC,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,UAAU,IAAI,EAAE,iBAAiB;AACjC,UAAU,WAAW,EAAE;AACvB;AACA,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,sBAAsB;AACrC,QAAQ,WAAW,EAAE,0EAA0E;AAC/F,QAAQ,cAAc,EAAE,oBAAoB;AAC5C,QAAQ,gBAAgB,EAAE,0BAA0B;AACpD,QAAQ,YAAY,EAAE;AACtB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE,mBAAmB;AAClC,QAAQ,WAAW,EAAE,kHAAkH;AACvI,QAAQ,OAAO,EAAE;AACjB,UAAU,KAAK,EAAE,oBAAoB;AACrC,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,UAAU,KAAK,EAAE,sBAAsB;AACvC,UAAU,WAAW,EAAE,iHAAiH;AACxI,UAAU,YAAY,EAAE;AACxB,YAAY,QAAQ,EAAE,UAAU;AAChC,YAAY,SAAS,EAAE,iBAAiB;AACxC,YAAY,QAAQ,EAAE;AACtB,WAAW;AACX,UAAU,QAAQ,EAAE;AACpB,YAAY;AACZ,cAAc,MAAM,EAAE,MAAM;AAC5B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;AAC3E,YAAY;AACZ,cAAc,MAAM,EAAE,MAAM;AAC5B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,MAAM,EAAE,OAAO;AAC7B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,MAAM,EAAE,OAAO;AAC7B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,MAAM,EAAE,KAAK;AAC3B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,MAAM,EAAE,KAAK;AAC3B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,MAAM,EAAE,OAAO;AAC7B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,MAAM,EAAE,MAAM;AAC5B,cAAc,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AAC1D;AACA,WAAW;AACX,UAAU,IAAI,EAAE,CAAC,kLAAkL;AACnM,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,UAAU,KAAK,EAAE,2BAA2B;AAC5C,UAAU,WAAW,EAAE,qJAAqJ;AAC5K,UAAU,YAAY,EAAE;AACxB,YAAY,OAAO,EAAE,YAAY;AACjC,YAAY,UAAU,EAAE,kBAAkB;AAC1C,YAAY,MAAM,EAAE,eAAe;AACnC,YAAY,IAAI,EAAE,iBAAiB;AACnC,YAAY,cAAc,EAAE;AAC5B,WAAW;AACX,UAAU,QAAQ,EAAE;AACpB,YAAY;AACZ,cAAc,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE;AAC5E,cAAc,UAAU,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO;AACxD,aAAa;AACb,YAAY;AACZ,cAAc,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE;AACnF,cAAc,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,OAAO,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE;AAChF,cAAc,UAAU,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM;AACtD,aAAa;AACb,YAAY;AACZ,cAAc,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE;AACtF,cAAc,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ;AAC1D,aAAa;AACb,YAAY;AACZ,cAAc,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE;AAC7E,cAAc,UAAU,EAAE;AAC1B,gBAAgB,MAAM,EAAE,IAAI;AAC5B,gBAAgB,IAAI,EAAE,KAAK;AAC3B,gBAAgB,IAAI,EAAE;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;AACzB,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;AACnE,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC;AACxE,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,QAAQ,CAAC;AAClD,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,KAAK,CAAC;AAC/C,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC;AAC3E,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC;AAC3E,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACrE,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+EAA+E,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC7W,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC;AACxC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC;AAC7D,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8DAA8D,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,qDAAqD,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACrkB,EAAE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,IAAI,IAAI,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC;AACzC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC;AAC1D,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8DAA8D,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,8CAA8C,CAAC,CAAC;AACjR,EAAE,cAAc,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,EAAE,kCAAkC,CAAC;AACjJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,cAAc,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,kCAAkC,CAAC;AAC9I,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6DAA6D,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,8CAA8C,CAAC,CAAC;AACtQ,EAAE,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,iDAAiD,CAAC;AACnJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,WAAW,EAAE,gDAAgD,CAAC;AAC5K,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,+BAA+B,CAAC;AACjI,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6DAA6D,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,8CAA8C,CAAC,CAAC;AACxQ,EAAE,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC,CAAC;AACpG,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC,CAAC;AACpG,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC;AAC9F,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6DAA6D,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,mHAAmH,CAAC,CAAC;AACxO,EAAE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,IAAI,IAAI,IAAI,GAAG,YAAY,CAAC,SAAS,CAAC;AACtC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mGAAmG,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACxK,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,sFAAsF,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,iCAAiC,CAAC,CAAC;AACjd,EAAE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,IAAI,IAAI,IAAI,GAAG,YAAY,CAAC,SAAS,CAAC;AACtC,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC;AACxD,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC;AACzD,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,CAAC;AAC1C,IAAI,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AACjG,MAAM,IAAI,MAAM,GAAG,YAAY,CAAC,SAAS,CAAC;AAC1C,MAAM,MAAM,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC;AAC3D,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0BAA0B,CAAC,CAAC;AACtD,MAAM,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AACnG,QAAQ,IAAI,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC;AAC3C,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mDAAmD,EAAE,UAAU,CAAC,EAAE,EAAE;AAChG,UAAU,kBAAkB,EAAE,CAAC,CAAC,EAAE,qBAAqB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;AAC/E,UAAU,KAAK,EAAE,SAAS;AAC1B,UAAU,OAAO,EAAE,UAAU;AAC7B,UAAU,eAAe,EAAE,KAAK;AAChC,UAAU,WAAW,EAAE,QAAQ;AAC/B,UAAU,aAAa,EAAE;AACzB,SAAS,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;AAC1C,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC1C,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wBAAwB,EAAE,UAAU,CAAC,EAAE,EAAE;AACjE,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,OAAO,EAAE;AACf,KAAK,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAChE,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;AACzE,MAAM,IAAI,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC;AACnC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;AAClC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE;AACjB,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACrC,MAAM,kBAAkB,CAAC,SAAS,EAAE,OAAO,CAAC;AAC5C,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC1C,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC5C,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qDAAqD,CAAC,CAAC;AAC7E,EAAE,gBAAgB,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC;AACzC,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,uCAAuC,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,mIAAmI,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,iCAAiC,CAAC,CAAC;AACz0B,EAAE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,IAAI,IAAI,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC;AACzC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,mDAAmD,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,6CAA6C,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;AACtR,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gGAAgG,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,sDAAsD,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,uIAAuI,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,iCAAiC,CAAC,CAAC;AAChhC,EAAE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,IAAI,IAAI,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC;AACzC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,mDAAmD,EAAE,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,mDAAmD,EAAE,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACnc,IAAI,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,KAAK,KAAK,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE;AACtE,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kDAAkD,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,6CAA6C,CAAC,CAAC;AACpK,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC5C,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sEAAsE,CAAC,CAAC;AAC9F,EAAE,GAAG,EAAE;AACP;;;;"}