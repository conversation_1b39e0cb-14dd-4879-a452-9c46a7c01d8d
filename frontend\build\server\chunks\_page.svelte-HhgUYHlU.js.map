{"version": 3, "file": "_page.svelte-HhgUYHlU.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/test/tag/_page.svelte.js"], "sourcesContent": ["import { K as ensure_array_like, z as escape_html, w as pop, u as push } from \"../../../../../chunks/index.js\";\nimport \"@sveltejs/kit\";\nimport \"../../../../../chunks/schema.js\";\nimport \"../../../../../chunks/current-user.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let tags = [];\n  const each_array = ensure_array_like(tags);\n  $$payload.out.push(`<div>Test!!</div> <button>Create new tag</button> <h2>Tags:</h2> <table><thead><tr><th>ID</th><th>Locale</th><th>Value</th></tr></thead><tbody><!--[-->`);\n  for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {\n    let tag = each_array[$$index_1];\n    const each_array_1 = ensure_array_like(tag.name);\n    $$payload.out.push(`<!--[-->`);\n    for (let i = 0, $$length2 = each_array_1.length; i < $$length2; i++) {\n      let name = each_array_1[i];\n      $$payload.out.push(`<tr><td>${escape_html(i === 0 ? tag.id : \"\")}</td><td>${escape_html(name.locale)}</td><td>${escape_html(name.value)}</td></tr>`);\n    }\n    $$payload.out.push(`<!--]-->`);\n  }\n  $$payload.out.push(`<!--]--></tbody></table>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;AAIA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,EAAE;AACf,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAC5C,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uJAAuJ,CAAC,CAAC;AAC/K,EAAE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC3F,IAAI,IAAI,GAAG,GAAG,UAAU,CAAC,SAAS,CAAC;AACnC,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC;AACpD,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;AACzE,MAAM,IAAI,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC;AAChC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC;AAC1J,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wBAAwB,CAAC,CAAC;AAChD,EAAE,GAAG,EAAE;AACP;;;;"}