{"version": 3, "file": "_page.svelte-RKwh3Aco.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/reactor/hubs/_page.svelte.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, w as pop, u as push, x as head, z as escape_html, y as attr, K as ensure_array_like } from \"../../../../../chunks/index.js\";\nimport \"../../../../../chunks/current-user.js\";\nimport { g as goto } from \"../../../../../chunks/client.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { f as formatDate } from \"../../../../../chunks/format-date.js\";\nimport { g as getClient } from \"../../../../../chunks/acrpc.js\";\nimport { M as Modal } from \"../../../../../chunks/modal.js\";\nimport { L as Localized_input } from \"../../../../../chunks/localized-input.js\";\nimport { L as Localized_textarea } from \"../../../../../chunks/localized-textarea.js\";\n/* empty css                                                                           */\nimport { U as User_picker } from \"../../../../../chunks/user-picker.js\";\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Hubs — Reactor of Commune\" },\n      hubs: \"Hubs\",\n      createHub: \"Create Hub\",\n      noHubs: \"No hubs found\",\n      head: \"Head\",\n      errorFetchingHubs: \"Failed to fetch hubs\",\n      errorOccurred: \"An error occurred while fetching hubs\",\n      loadingMore: \"Loading more hubs...\",\n      createdOn: \"Created on\",\n      createHubTitle: \"Create New Hub\",\n      hubName: \"Hub Name\",\n      hubDescription: \"Hub Description\",\n      headUserPlaceholder: \"Leave empty to use current user\",\n      hubNamePlaceholder: \"Enter hub name\",\n      hubDescriptionPlaceholder: \"Enter hub description\",\n      create: \"Create\",\n      cancel: \"Cancel\",\n      creating: \"Creating...\",\n      hubCreatedSuccess: \"Hub created successfully!\",\n      errorCreatingHub: \"Failed to create hub\",\n      required: \"This field is required\",\n      searchPlaceholder: \"Search hubs...\"\n    },\n    ru: {\n      _page: {\n        title: \"Хабы — Реактор Коммуны\"\n      },\n      hubs: \"Хабы\",\n      createHub: \"Создать хаб\",\n      noHubs: \"Хабы не найдены\",\n      head: \"Глава\",\n      errorFetchingHubs: \"Не удалось загрузить хабы\",\n      errorOccurred: \"Произошла ошибка при загрузке хабов\",\n      loadingMore: \"Загружаем больше хабов...\",\n      createdOn: \"Создан\",\n      createHubTitle: \"Создать новый хаб\",\n      hubName: \"Название хаба\",\n      hubDescription: \"Описание хаба\",\n      headUserPlaceholder: \"Оставьте пустым для использования текущего пользователя\",\n      hubNamePlaceholder: \"Введите название хаба\",\n      hubDescriptionPlaceholder: \"Введите описание хаба\",\n      create: \"Создать\",\n      cancel: \"Отмена\",\n      creating: \"Создаём...\",\n      hubCreatedSuccess: \"Хаб успешно создан!\",\n      errorCreatingHub: \"Не удалось создать хаб\",\n      required: \"Это поле обязательно\",\n      searchPlaceholder: \"Поиск хабов...\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const { locale, toLocaleHref, getAppropriateLocalization } = data;\n  const t = i18n[locale];\n  let hubs = data.hubs;\n  let showCreateModal = false;\n  let searchInputValue = data.searchQuery || \"\";\n  let isHasMoreHubs = data.isHasMoreHubs;\n  let isCreating = false;\n  let createError = null;\n  let createSuccess = null;\n  let hubName = [];\n  let hubDescription = [];\n  let headUserId = null;\n  function closeCreateModal() {\n    showCreateModal = false;\n  }\n  function validateCreateForm() {\n    if (!hubName.some((item) => item.value.trim().length > 0)) {\n      createError = t.required;\n      return false;\n    }\n    if (!hubDescription.some((item) => item.value.trim().length > 0)) {\n      createError = t.required;\n      return false;\n    }\n    return true;\n  }\n  async function handleCreateHub() {\n    if (!validateCreateForm()) return;\n    isCreating = true;\n    createError = null;\n    createSuccess = null;\n    try {\n      const { id } = await api.reactor.hub.post({\n        headUserId: headUserId || data.user?.id || \"\",\n        name: hubName,\n        description: hubDescription\n      });\n      createSuccess = t.hubCreatedSuccess;\n      setTimeout(\n        () => {\n          goto(toLocaleHref(`/reactor/hubs/${id}`));\n        },\n        1500\n      );\n    } catch (err) {\n      createError = err instanceof Error ? err.message : t.errorCreatingHub;\n      console.error(err);\n    } finally {\n      isCreating = false;\n    }\n  }\n  function truncateDescription(text, maxLength = 200) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    head($$payload2, ($$payload3) => {\n      $$payload3.title = `<title>${escape_html(t._page.title)}</title>`;\n    });\n    $$payload2.out.push(`<div class=\"container my-4 mb-5\"><div class=\"d-flex justify-content-between align-items-center my-4 gap-3\"><h1 class=\"mb-0\">${escape_html(t.hubs)}</h1> <div class=\"d-flex align-items-center gap-3\"><div class=\"search-container svelte-si10zv\"><input type=\"text\" class=\"form-control svelte-si10zv\"${attr(\"placeholder\", t.searchPlaceholder)}${attr(\"value\", searchInputValue)} style=\"min-width: 250px;\"/></div> `);\n    if (data.user?.role === \"admin\") {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<button class=\"btn btn-primary\">${escape_html(t.createHub)}</button>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div></div> `);\n    if (hubs.length === 0) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"text-center py-5\"><p class=\"text-muted\">${escape_html(t.noHubs)}</p></div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n      const each_array = ensure_array_like(hubs);\n      $$payload2.out.push(`<div class=\"row g-4\"><!--[-->`);\n      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n        let hub = each_array[$$index];\n        $$payload2.out.push(`<div class=\"col-12\"><div class=\"card shadow-sm h-100 svelte-si10zv\"><div class=\"row g-0 h-100\"><div class=\"col-md-3 col-lg-2\"><div class=\"hub-image-container svelte-si10zv\">`);\n        if (hub.image) {\n          $$payload2.out.push(\"<!--[-->\");\n          $$payload2.out.push(`<img${attr(\"src\", `/images/${hub.image}`)}${attr(\"alt\", getAppropriateLocalization(hub.name) || \"Hub\")} class=\"hub-image svelte-si10zv\"/>`);\n        } else {\n          $$payload2.out.push(\"<!--[!-->\");\n          $$payload2.out.push(`<div class=\"hub-image-placeholder svelte-si10zv\"><i class=\"bi bi-collection fs-1 text-muted\"></i></div>`);\n        }\n        $$payload2.out.push(`<!--]--></div></div> <div class=\"col-md-9 col-lg-10\"><div class=\"card-body d-flex flex-column h-100 p-4\"><div class=\"d-flex justify-content-between align-items-start mb-3\"><h4 class=\"card-title mb-0 flex-grow-1\"><a${attr(\"href\", toLocaleHref(`/reactor/hubs/${hub.id}`))} style=\"text-decoration: none;\">${escape_html(getAppropriateLocalization(hub.name) || \"No name?\")}</a></h4> <small class=\"text-muted ms-3\">${escape_html(t.createdOn)}\n                      ${escape_html(formatDate(hub.createdAt, locale))}</small></div> <p class=\"card-text text-muted mb-3 flex-grow-1\">${escape_html(truncateDescription(getAppropriateLocalization(hub.description) || \"\"))}</p> <div class=\"d-flex align-items-center\"><div class=\"me-3\">`);\n        if (hub.headUser.image) {\n          $$payload2.out.push(\"<!--[-->\");\n          $$payload2.out.push(`<img${attr(\"src\", `/images/${hub.headUser.image}`)}${attr(\"alt\", getAppropriateLocalization(hub.headUser.name))} class=\"rounded-circle\" style=\"width: 48px; height: 48px; object-fit: cover;\"/>`);\n        } else {\n          $$payload2.out.push(\"<!--[!-->\");\n          $$payload2.out.push(`<div class=\"rounded-circle bg-secondary d-flex align-items-center justify-content-center\" style=\"width: 48px; height: 48px;\"><i class=\"bi bi-person-fill text-white\"></i></div>`);\n        }\n        $$payload2.out.push(`<!--]--></div> <div><a${attr(\"href\", toLocaleHref(`/users/${hub.headUser.id}`))} class=\"fw-medium\" style=\"text-decoration: none;\">${escape_html(getAppropriateLocalization(hub.headUser.name))}</a></div></div></div></div></div></div></div>`);\n      }\n      $$payload2.out.push(`<!--]--></div>`);\n    }\n    $$payload2.out.push(`<!--]--> `);\n    if (isHasMoreHubs) {\n      $$payload2.out.push(\"<!--[-->\");\n      $$payload2.out.push(`<div class=\"text-center py-3\">`);\n      {\n        $$payload2.out.push(\"<!--[!-->\");\n      }\n      $$payload2.out.push(`<!--]--></div>`);\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--> `);\n    {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]--></div> `);\n    if (data.user?.role === \"admin\") {\n      $$payload2.out.push(\"<!--[-->\");\n      Modal($$payload2, {\n        show: showCreateModal,\n        title: t.createHubTitle,\n        onClose: closeCreateModal,\n        onSubmit: handleCreateHub,\n        submitText: isCreating ? t.creating : t.create,\n        cancelText: t.cancel,\n        submitDisabled: isCreating || !hubName.some((item) => item.value.trim().length > 0) || !hubDescription.some((item) => item.value.trim().length > 0),\n        isSubmitting: isCreating,\n        children: ($$payload3) => {\n          if (createError) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"alert alert-danger mb-3\">${escape_html(createError)}</div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n          }\n          $$payload3.out.push(`<!--]--> `);\n          if (createSuccess) {\n            $$payload3.out.push(\"<!--[-->\");\n            $$payload3.out.push(`<div class=\"alert alert-success mb-3\">${escape_html(createSuccess)}</div>`);\n          } else {\n            $$payload3.out.push(\"<!--[!-->\");\n          }\n          $$payload3.out.push(`<!--]--> <form>`);\n          User_picker($$payload3, {\n            locale,\n            label: t.head,\n            placeholder: t.headUserPlaceholder,\n            get selectedUserId() {\n              return headUserId;\n            },\n            set selectedUserId($$value) {\n              headUserId = $$value;\n              $$settled = false;\n            }\n          });\n          $$payload3.out.push(`<!----> <div class=\"form-text mb-3\">${escape_html(t.headUserPlaceholder)}</div> `);\n          Localized_input($$payload3, {\n            locale,\n            id: \"hub-name\",\n            label: t.hubName,\n            placeholder: t.hubNamePlaceholder,\n            required: true,\n            get value() {\n              return hubName;\n            },\n            set value($$value) {\n              hubName = $$value;\n              $$settled = false;\n            }\n          });\n          $$payload3.out.push(`<!----> `);\n          Localized_textarea($$payload3, {\n            locale,\n            id: \"hub-description\",\n            label: t.hubDescription,\n            placeholder: t.hubDescriptionPlaceholder,\n            rows: 4,\n            required: true,\n            get value() {\n              return hubDescription;\n            },\n            set value($$value) {\n              hubDescription = $$value;\n              $$settled = false;\n            }\n          });\n          $$payload3.out.push(`<!----></form>`);\n        }\n      });\n    } else {\n      $$payload2.out.push(\"<!--[!-->\");\n    }\n    $$payload2.out.push(`<!--]-->`);\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAYA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE;AACnD,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,iBAAiB,EAAE,sBAAsB;AAC/C,MAAM,aAAa,EAAE,uCAAuC;AAC5D,MAAM,WAAW,EAAE,sBAAsB;AACzC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,cAAc,EAAE,gBAAgB;AACtC,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,cAAc,EAAE,iBAAiB;AACvC,MAAM,mBAAmB,EAAE,iCAAiC;AAC5D,MAAM,kBAAkB,EAAE,gBAAgB;AAC1C,MAAM,yBAAyB,EAAE,uBAAuB;AACxD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,iBAAiB,EAAE,2BAA2B;AACpD,MAAM,gBAAgB,EAAE,sBAAsB;AAC9C,MAAM,QAAQ,EAAE,wBAAwB;AACxC,MAAM,iBAAiB,EAAE;AACzB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,MAAM,EAAE,iBAAiB;AAC/B,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,iBAAiB,EAAE,2BAA2B;AACpD,MAAM,aAAa,EAAE,qCAAqC;AAC1D,MAAM,WAAW,EAAE,2BAA2B;AAC9C,MAAM,SAAS,EAAE,QAAQ;AACzB,MAAM,cAAc,EAAE,mBAAmB;AACzC,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,cAAc,EAAE,eAAe;AACrC,MAAM,mBAAmB,EAAE,yDAAyD;AACpF,MAAM,kBAAkB,EAAE,uBAAuB;AACjD,MAAM,yBAAyB,EAAE,uBAAuB;AACxD,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,iBAAiB,EAAE,qBAAqB;AAC9C,MAAM,gBAAgB,EAAE,wBAAwB;AAChD,MAAM,QAAQ,EAAE,sBAAsB;AACtC,MAAM,iBAAiB,EAAE;AACzB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,0BAA0B,EAAE,GAAG,IAAI;AACnE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;AACtB,EAAE,IAAI,eAAe,GAAG,KAAK;AAC7B,EAAE,IAAI,gBAAgB,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE;AAC/C,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa;AACxC,EAAE,IAAI,UAAU,GAAG,KAAK;AACxB,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,IAAI,aAAa,GAAG,IAAI;AAC1B,EAAE,IAAI,OAAO,GAAG,EAAE;AAClB,EAAE,IAAI,cAAc,GAAG,EAAE;AACzB,EAAE,IAAI,UAAU,GAAG,IAAI;AACvB,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,eAAe,GAAG,KAAK;AAC3B,EAAE;AACF,EAAE,SAAS,kBAAkB,GAAG;AAChC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;AAC/D,MAAM,WAAW,GAAG,CAAC,CAAC,QAAQ;AAC9B,MAAM,OAAO,KAAK;AAClB,IAAI;AACJ,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;AACtE,MAAM,WAAW,GAAG,CAAC,CAAC,QAAQ;AAC9B,MAAM,OAAO,KAAK;AAClB,IAAI;AACJ,IAAI,OAAO,IAAI;AACf,EAAE;AACF,EAAE,eAAe,eAAe,GAAG;AACnC,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;AAC/B,IAAI,UAAU,GAAG,IAAI;AACrB,IAAI,WAAW,GAAG,IAAI;AACtB,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,IAAI;AACR,MAAM,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;AAChD,QAAQ,UAAU,EAAE,UAAU,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;AACrD,QAAQ,IAAI,EAAE,OAAO;AACrB,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR,MAAM,aAAa,GAAG,CAAC,CAAC,iBAAiB;AACzC,MAAM,UAAU;AAChB,QAAQ,MAAM;AACd,UAAU,IAAI,CAAC,YAAY,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACnD,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO;AACP,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,WAAW,GAAG,GAAG,YAAY,KAAK,GAAG,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,gBAAgB;AAC3E,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AACxB,IAAI,CAAC,SAAS;AACd,MAAM,UAAU,GAAG,KAAK;AACxB,IAAI;AACJ,EAAE;AACF,EAAE,SAAS,mBAAmB,CAAC,IAAI,EAAE,SAAS,GAAG,GAAG,EAAE;AACtD,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,OAAO,IAAI;AAC7C,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,KAAK;AAC3C,EAAE;AACF,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,KAAK;AACrC,MAAM,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACvE,IAAI,CAAC,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4HAA4H,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,oJAAoJ,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC,mCAAmC,CAAC,CAAC;AACjb,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,OAAO,EAAE;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC;AACjG,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,CAAC;AAChD,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3B,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC;AACnH,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,MAAM,MAAM,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAChD,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6BAA6B,CAAC,CAAC;AAC1D,MAAM,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACzF,QAAQ,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC;AACrC,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6KAA6K,CAAC,CAAC;AAC5M,QAAQ,IAAI,GAAG,CAAC,KAAK,EAAE;AACvB,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,kCAAkC,CAAC,CAAC;AAC1K,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uGAAuG,CAAC,CAAC;AACxI,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sNAAsN,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,cAAc,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;AAChd,sBAAsB,EAAE,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,gEAAgE,EAAE,WAAW,CAAC,mBAAmB,CAAC,0BAA0B,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,8DAA8D,CAAC,CAAC;AAC7R,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE;AAChC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,+EAA+E,CAAC,CAAC;AAChO,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+KAA+K,CAAC,CAAC;AAChN,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,kDAAkD,EAAE,WAAW,CAAC,0BAA0B,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,8CAA8C,CAAC,CAAC;AAC5Q,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AAC3D,MAAM;AACN,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI;AACJ,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC1C,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,OAAO,EAAE;AACrC,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACrC,MAAM,KAAK,CAAC,UAAU,EAAE;AACxB,QAAQ,IAAI,EAAE,eAAe;AAC7B,QAAQ,KAAK,EAAE,CAAC,CAAC,cAAc;AAC/B,QAAQ,OAAO,EAAE,gBAAgB;AACjC,QAAQ,QAAQ,EAAE,eAAe;AACjC,QAAQ,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM;AACtD,QAAQ,UAAU,EAAE,CAAC,CAAC,MAAM;AAC5B,QAAQ,cAAc,EAAE,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3J,QAAQ,YAAY,EAAE,UAAU;AAChC,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,IAAI,WAAW,EAAE;AAC3B,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;AACzG,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AAC1C,UAAU,IAAI,aAAa,EAAE;AAC7B,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC;AAC5G,UAAU,CAAC,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,UAAU;AACV,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAChD,UAAU,WAAW,CAAC,UAAU,EAAE;AAClC,YAAY,MAAM;AAClB,YAAY,KAAK,EAAE,CAAC,CAAC,IAAI;AACzB,YAAY,WAAW,EAAE,CAAC,CAAC,mBAAmB;AAC9C,YAAY,IAAI,cAAc,GAAG;AACjC,cAAc,OAAO,UAAU;AAC/B,YAAY,CAAC;AACb,YAAY,IAAI,cAAc,CAAC,OAAO,EAAE;AACxC,cAAc,UAAU,GAAG,OAAO;AAClC,cAAc,SAAS,GAAG,KAAK;AAC/B,YAAY;AACZ,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oCAAoC,EAAE,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,CAAC;AACjH,UAAU,eAAe,CAAC,UAAU,EAAE;AACtC,YAAY,MAAM;AAClB,YAAY,EAAE,EAAE,UAAU;AAC1B,YAAY,KAAK,EAAE,CAAC,CAAC,OAAO;AAC5B,YAAY,WAAW,EAAE,CAAC,CAAC,kBAAkB;AAC7C,YAAY,QAAQ,EAAE,IAAI;AAC1B,YAAY,IAAI,KAAK,GAAG;AACxB,cAAc,OAAO,OAAO;AAC5B,YAAY,CAAC;AACb,YAAY,IAAI,KAAK,CAAC,OAAO,EAAE;AAC/B,cAAc,OAAO,GAAG,OAAO;AAC/B,cAAc,SAAS,GAAG,KAAK;AAC/B,YAAY;AACZ,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACzC,UAAU,kBAAkB,CAAC,UAAU,EAAE;AACzC,YAAY,MAAM;AAClB,YAAY,EAAE,EAAE,iBAAiB;AACjC,YAAY,KAAK,EAAE,CAAC,CAAC,cAAc;AACnC,YAAY,WAAW,EAAE,CAAC,CAAC,yBAAyB;AACpD,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,QAAQ,EAAE,IAAI;AAC1B,YAAY,IAAI,KAAK,GAAG;AACxB,cAAc,OAAO,cAAc;AACnC,YAAY,CAAC;AACb,YAAY,IAAI,KAAK,CAAC,OAAO,EAAE;AAC/B,cAAc,cAAc,GAAG,OAAO;AACtC,cAAc,SAAS,GAAG,KAAK;AAC/B,YAAY;AACZ,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC/C,QAAQ;AACR,OAAO,CAAC;AACR,IAAI,CAAC,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI;AACJ,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACnC,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;;;;"}