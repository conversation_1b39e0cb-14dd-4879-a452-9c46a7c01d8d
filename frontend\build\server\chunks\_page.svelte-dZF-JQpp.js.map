{"version": 3, "file": "_page.svelte-dZF-JQpp.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/rules/_page.svelte.js"], "sourcesContent": ["import { x as head, F as attr_style, z as escape_html, K as ensure_array_like, G as attr_class } from \"../../../../../chunks/index.js\";\nfunction section($$payload, title, description, points, titleContainerStyle, icon, iconColor) {\n  const each_array = ensure_array_like(points);\n  $$payload.out.push(`<section class=\"mb-5\"><div${attr_class(`d-flex align-items-center p-3 rounded-top ${titleContainerStyle}`)}><i${attr_class(`bi ${icon} text-${iconColor} me-3 fs-4`)}></i> <h2 class=\"h3 mb-0\">${escape_html(title)}</h2></div> <div class=\"p-3 bg-light border-start border-end border-bottom\"><p class=\"fst-italic\">${escape_html(description)}</p></div> <div class=\"card shadow-sm border-success\"><div class=\"card-body mt-3 p-4\"><ul class=\"mb-0 ps-4\"><!--[-->`);\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let { title: title2, description: description2 } = each_array[$$index];\n    $$payload.out.push(`<li class=\"ps-2 mb-3\"><div class=\"d-flex\"><div><h5 class=\"fw-bold mb-1\">${escape_html(title2)}</h5> <p class=\"mb-0\">${escape_html(description2)}</p></div></div></li>`);\n  }\n  $$payload.out.push(`<!--]--></ul></div></div></section>`);\n}\nfunction _page($$payload, $$props) {\n  const i18n = {\n    ru: {\n      _page: {\n        title: \"Правила — Коммуна\"\n      },\n      title: \"Правила сообщества\",\n      description: \"Правила являются практическим руководством для участников Коммуны и детализируют применение фундаментальных принципов, изложенных в Праве. Они направлены на поддержание среды, способствующей реализации принципов Рациональности, Свободы, Ответственности, Справедливости и Взаимоуважения в цифровом пространстве. Соблюдение Правил и основополагающих принципов Права является ответственностью каждого участника сообщества.\",\n      sections: {\n        recommended: {\n          title: \"Рекомендуется\",\n          description: \"Эти пункты описывают желательное поведение, способствующее здоровой, продуктивной и дружелюбной атмосфере в сообществе, в полном соответствии с духом Права.\",\n          points: [\n            {\n              title: \"Конструктивное общение\",\n              description: \"Поддерживайте содержательный диалог, предлагайте обоснованную критику и полезные советы.\"\n            },\n            {\n              title: \"Ясность и релевантность\",\n              description: \"Излагайте мысли четко, по существу обсуждаемой темы.\"\n            },\n            {\n              title: \"Качественный контент\",\n              description: \"Делитесь проверенной информацией и опытом, при необходимости указывая источники.\"\n            },\n            {\n              title: \"Читабельность\",\n              description: \"Используйте форматирование (абзацы, списки) для удобства восприятия ваших сообщений.\"\n            },\n            {\n              title: \"Взаимопомощь и сотрудничество\",\n              description: \"Помогайте другим участникам, способствуйте развитию сообщества.\"\n            },\n            {\n              title: \"Поддержка новичков\",\n              description: \"Помогайте новым участникам адаптироваться в сообществе.\"\n            },\n            {\n              title: \"Благодарность\",\n              description: \"Выражайте признательность за помощь и ценный контент.\"\n            }\n          ]\n        },\n        notRecommended: {\n          title: \"Не рекомендуется\",\n          description: \"Действия, которые могут мешать конструктивному взаимодействию, создавать информационный шум или иным образом снижать качество общения, потенциально нарушая принципы Рациональности и Уважения. Систематическое нарушение может привести к ограничениям.\",\n          points: [\n            {\n              title: \"Информационный шум\",\n              description: \"Избегайте спама, флуда, чрезмерного оффтопа и многократного дублирования сообщений (кросспостинг).\"\n            },\n            {\n              title: \"Навязчивая реклама\",\n              description: \"Воздерживайтесь от саморекламы или скрытого маркетинга, не несущих ценности для обсуждения.\"\n            },\n            {\n              title: \"Злоупотребление оформлением\",\n              description: \"Не используйте избыточное форматирование, мешающее чтению (кричащие заголовки, ЗАГЛАВНЫЕ БУКВЫ, обилие эмодзи).\"\n            },\n            {\n              title: \"Неконструктивная лексика\",\n              description: \"Используйте нецензурную лексику только в исключительных случаях, если это оправдано контекстом, избегая её для оскорблений или пустословия.\"\n            },\n            {\n              title: \"Низкокачественный контент\",\n              description: \"Не распространяйте намеренно бессодержательный или нерелевантный контент.\"\n            },\n            {\n              title: \"Дублирование тем\",\n              description: \"Перед созданием новой темы убедитесь (например, с помощью поиска), что подобная не обсуждается.\"\n            }\n          ]\n        },\n        prohibited: {\n          title: \"Запрещено\",\n          description: \"Действия, представляющие собой прямое и серьёзное нарушение прав других участников (Принципы Достоинства, Свободы, Ответственности, Справедливости), такие как посягательство на частную жизнь, травля и обман. Нарушения могут повлечь временные или постоянные ограничения.\",\n          points: [\n            {\n              title: \"Имперсонация\",\n              description: \"Выдача себя за другое лицо с целью обмана, мошенничества или нанесения вреда репутации.\"\n            }\n          ]\n        },\n        strictlyProhibited: {\n          title: \"Строго запрещено\",\n          description: \"Действия, грубо попирающие базовые принципы Права, представляющие угрозу безопасности участников, стабильности сообщества или нарушающие законодательство. Такие нарушения ведут к немедленному и, как правило, постоянному исключению из сообщества.\",\n          points: [\n            {\n              title: \"Нелегальная деятельность\",\n              description: \"Распространение или пропаганда запрещенных веществ, услуг или контента, нарушающего законодательство.\"\n            },\n            {\n              title: \"Пропаганда ненависти и насилия\",\n              description: \"Распространение идеологий, основанных на ненависти, дискриминации, призывы к насилию, экстремизму, терроризму.\"\n            },\n            {\n              title: \"Обход ограничений\",\n              description: \"Создание дополнительных аккаунтов или использование иных методов для обхода блокировок или ограничений, наложенных администрацией.\"\n            },\n            {\n              title: \"Вредоносная активность\",\n              description: \"Распространение вредоносного ПО, фишинговых ссылок, использование уязвимостей платформы или иные действия, направленные на техническое нанесение ущерба сообществу или его участникам.\"\n            },\n            {\n              title: \"Манипуляции\",\n              description: \"Использование автоматизированных скриптов (ботов) для нечестной манипуляции контентом, голосованиями или иными механизмами сообщества.\"\n            },\n            {\n              title: \"Эксплуатация уязвимостей\",\n              description: \"Использование ошибок или недоработок системы для получения несправедливого преимущества или нанесения вреда.\"\n            }\n          ]\n        }\n      }\n    },\n    en: {\n      _page: { title: \"Rules — Commune\" },\n      title: \"Rules of Society\",\n      description: \"Rules serve as a practical guide for members of Commune and detail the application of fundamental principles outlined in The Law. They aim to maintain an environment that promotes the principles of Rationality, Freedom, Responsibility, Justice, and Mutual Respect in the digital space. Adherence to these Rules and the fundamental principles of The Law is the responsibility of each community member.\",\n      sections: {\n        recommended: {\n          title: \"Recommended\",\n          description: \"These points describe desirable behavior that contributes to a healthy, productive, and friendly atmosphere in the community, fully aligned with the spirit of The Law.\",\n          points: [\n            {\n              title: \"Constructive Communication\",\n              description: \"Maintain meaningful dialogue, offer reasoned criticism and helpful advice.\"\n            },\n            {\n              title: \"Clarity and Relevance\",\n              description: \"Express thoughts clearly and stay on topic in discussions.\"\n            },\n            {\n              title: \"Quality Content\",\n              description: \"Share verified information and experiences, citing sources when appropriate.\"\n            },\n            {\n              title: \"Readability\",\n              description: \"Use formatting (paragraphs, lists) to make your messages easier to read.\"\n            },\n            {\n              title: \"Mutual Help and Collaboration\",\n              description: \"Assist other members and contribute to community development.\"\n            },\n            {\n              title: \"Supporting Newcomers\",\n              description: \"Help new members adapt to the community.\"\n            },\n            {\n              title: \"Gratitude\",\n              description: \"Express appreciation for help and valuable content.\"\n            }\n          ]\n        },\n        notRecommended: {\n          title: \"Not Recommended\",\n          description: \"Actions that may interfere with constructive interaction, create information noise, or otherwise reduce the quality of communication, potentially violating the principles of Rationality and Respect. Systematic violations may lead to restrictions.\",\n          points: [\n            {\n              title: \"Information Noise\",\n              description: \"Avoid spam, flooding, excessive off-topic content, and repeated cross-posting.\"\n            },\n            {\n              title: \"Intrusive Advertising\",\n              description: \"Refrain from self-promotion or hidden marketing that adds no value to discussions.\"\n            },\n            {\n              title: \"Formatting Abuse\",\n              description: \"Don't use excessive formatting that hinders readability (screaming headlines, ALL CAPS, emoji overuse).\"\n            },\n            {\n              title: \"Unconstructive Language\",\n              description: \"Use profanity only when contextually justified, avoiding it for insults or empty talk.\"\n            },\n            {\n              title: \"Low-Quality Content\",\n              description: \"Don't deliberately share meaningless or irrelevant content.\"\n            },\n            {\n              title: \"Duplicate Topics\",\n              description: \"Before creating a new topic, check (using search) that a similar one isn't already being discussed.\"\n            }\n          ]\n        },\n        prohibited: {\n          title: \"Prohibited\",\n          description: \"Actions that constitute a direct and serious violation of the rights of other participants (Principles of Dignity, Freedom, Responsibility, Justice), such as invasion of privacy, harassment, and deception. Violations may result in temporary or permanent restrictions.\",\n          points: [\n            {\n              title: \"Impersonation\",\n              description: \"Pretending to be someone else to deceive, defraud, or damage reputation.\"\n            }\n          ]\n        },\n        strictlyProhibited: {\n          title: \"Strictly Prohibited\",\n          description: \"Actions that grossly violate the basic principles of The Law, posing a threat to the safety of participants, community stability, or violating legislation. Such violations typically lead to immediate and permanent exclusion from the community.\",\n          points: [\n            {\n              title: \"Illegal Activity\",\n              description: \"Distribution or promotion of illegal substances, services, or content that violates legislation.\"\n            },\n            {\n              title: \"Hate Speech and Violence\",\n              description: \"Spreading ideologies based on hatred, discrimination, or calls for violence, extremism, terrorism.\"\n            },\n            {\n              title: \"Circumventing Restrictions\",\n              description: \"Creating additional accounts or using other methods to bypass blocks or limitations imposed by administration.\"\n            },\n            {\n              title: \"Malicious Activity\",\n              description: \"Distributing malware, phishing links, exploiting platform vulnerabilities, or other actions aimed at technically harming the community or its members.\"\n            },\n            {\n              title: \"Manipulation\",\n              description: \"Using automated scripts (bots) for unfair manipulation of content, voting, or other community mechanisms.\"\n            },\n            {\n              title: \"Exploiting Vulnerabilities\",\n              description: \"Using errors or flaws in the system to gain unfair advantage or cause harm.\"\n            }\n          ]\n        }\n      }\n    }\n  };\n  const { data } = $$props;\n  const { locale } = data;\n  const t = i18n[locale];\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;\n  });\n  $$payload.out.push(`<div class=\"container my-5\"><div class=\"mx-auto\"${attr_style(\"\", { width: \"100%\", \"max-width\": \"100%\" })}><div class=\"row justify-content-center\"><div class=\"col-12 col-md-10 col-lg-8\"><div class=\"text-center mb-5\"><h1 class=\"display-4 fw-bold mb-4\"><i class=\"bi bi-book me-3 text-primary\"></i> ${escape_html(t.title)}</h1> <div class=\"card shadow border-0 bg-light\"><div class=\"card-body p-4\"><p class=\"lead mb-0\">${escape_html(t.description)}</p></div></div></div> `);\n  section($$payload, t.sections.recommended.title, t.sections.recommended.description, t.sections.recommended.points, \"bg-success-subtle\", \"bi-check-circle-fill\", \"success\");\n  $$payload.out.push(`<!----> `);\n  section($$payload, t.sections.notRecommended.title, t.sections.notRecommended.description, t.sections.notRecommended.points, \"bg-warning-subtle\", \"bi-exclamation-triangle-fill\", \"warning\");\n  $$payload.out.push(`<!----> `);\n  section($$payload, t.sections.prohibited.title, t.sections.prohibited.description, t.sections.prohibited.points, \"bg-danger-subtle\", \"bi-x-octagon-fill\", \"danger\");\n  $$payload.out.push(`<!----> `);\n  section($$payload, t.sections.strictlyProhibited.title, t.sections.strictlyProhibited.description, t.sections.strictlyProhibited.points, \"bg-dark text-white\", \"bi-slash-circle-fill\", \"white\");\n  $$payload.out.push(`<!----></div></div></div></div>`);\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;AACA,SAAS,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;AAC9F,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM,CAAC;AAC9C,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0BAA0B,EAAE,UAAU,CAAC,CAAC,0CAA0C,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,0BAA0B,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,kGAAkG,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,oHAAoH,CAAC,CAAC;AAC7d,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC;AAC1E,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wEAAwE,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,qBAAqB,CAAC,CAAC;AAC/L,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mCAAmC,CAAC,CAAC;AAC3D;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,KAAK,EAAE,oBAAoB;AACjC,MAAM,WAAW,EAAE,qaAAqa;AACxb,MAAM,QAAQ,EAAE;AAChB,QAAQ,WAAW,EAAE;AACrB,UAAU,KAAK,EAAE,eAAe;AAChC,UAAU,WAAW,EAAE,8JAA8J;AACrL,UAAU,MAAM,EAAE;AAClB,YAAY;AACZ,cAAc,KAAK,EAAE,wBAAwB;AAC7C,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,yBAAyB;AAC9C,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,sBAAsB;AAC3C,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,eAAe;AACpC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,+BAA+B;AACpD,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,oBAAoB;AACzC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,eAAe;AACpC,cAAc,WAAW,EAAE;AAC3B;AACA;AACA,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,UAAU,KAAK,EAAE,kBAAkB;AACnC,UAAU,WAAW,EAAE,0PAA0P;AACjR,UAAU,MAAM,EAAE;AAClB,YAAY;AACZ,cAAc,KAAK,EAAE,oBAAoB;AACzC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,oBAAoB;AACzC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,6BAA6B;AAClD,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,0BAA0B;AAC/C,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,2BAA2B;AAChD,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,kBAAkB;AACvC,cAAc,WAAW,EAAE;AAC3B;AACA;AACA,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,UAAU,KAAK,EAAE,WAAW;AAC5B,UAAU,WAAW,EAAE,+QAA+Q;AACtS,UAAU,MAAM,EAAE;AAClB,YAAY;AACZ,cAAc,KAAK,EAAE,cAAc;AACnC,cAAc,WAAW,EAAE;AAC3B;AACA;AACA,SAAS;AACT,QAAQ,kBAAkB,EAAE;AAC5B,UAAU,KAAK,EAAE,kBAAkB;AACnC,UAAU,WAAW,EAAE,uPAAuP;AAC9Q,UAAU,MAAM,EAAE;AAClB,YAAY;AACZ,cAAc,KAAK,EAAE,0BAA0B;AAC/C,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,gCAAgC;AACrD,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,mBAAmB;AACxC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,wBAAwB;AAC7C,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,aAAa;AAClC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,0BAA0B;AAC/C,cAAc,WAAW,EAAE;AAC3B;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE;AACzC,MAAM,KAAK,EAAE,kBAAkB;AAC/B,MAAM,WAAW,EAAE,kZAAkZ;AACra,MAAM,QAAQ,EAAE;AAChB,QAAQ,WAAW,EAAE;AACrB,UAAU,KAAK,EAAE,aAAa;AAC9B,UAAU,WAAW,EAAE,yKAAyK;AAChM,UAAU,MAAM,EAAE;AAClB,YAAY;AACZ,cAAc,KAAK,EAAE,4BAA4B;AACjD,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,uBAAuB;AAC5C,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,iBAAiB;AACtC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,aAAa;AAClC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,+BAA+B;AACpD,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,sBAAsB;AAC3C,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,WAAW;AAChC,cAAc,WAAW,EAAE;AAC3B;AACA;AACA,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,UAAU,KAAK,EAAE,iBAAiB;AAClC,UAAU,WAAW,EAAE,wPAAwP;AAC/Q,UAAU,MAAM,EAAE;AAClB,YAAY;AACZ,cAAc,KAAK,EAAE,mBAAmB;AACxC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,uBAAuB;AAC5C,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,kBAAkB;AACvC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,yBAAyB;AAC9C,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,qBAAqB;AAC1C,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,kBAAkB;AACvC,cAAc,WAAW,EAAE;AAC3B;AACA;AACA,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,UAAU,KAAK,EAAE,YAAY;AAC7B,UAAU,WAAW,EAAE,6QAA6Q;AACpS,UAAU,MAAM,EAAE;AAClB,YAAY;AACZ,cAAc,KAAK,EAAE,eAAe;AACpC,cAAc,WAAW,EAAE;AAC3B;AACA;AACA,SAAS;AACT,QAAQ,kBAAkB,EAAE;AAC5B,UAAU,KAAK,EAAE,qBAAqB;AACtC,UAAU,WAAW,EAAE,qPAAqP;AAC5Q,UAAU,MAAM,EAAE;AAClB,YAAY;AACZ,cAAc,KAAK,EAAE,kBAAkB;AACvC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,0BAA0B;AAC/C,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,4BAA4B;AACjD,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,oBAAoB;AACzC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,cAAc;AACnC,cAAc,WAAW,EAAE;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,4BAA4B;AACjD,cAAc,WAAW,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;AACzB,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACrE,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gDAAgD,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC,8LAA8L,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,iGAAiG,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,uBAAuB,CAAC,CAAC;AAC3e,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,SAAS,CAAC;AAC7K,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,mBAAmB,EAAE,8BAA8B,EAAE,SAAS,CAAC;AAC9L,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,QAAQ,CAAC;AACrK,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,MAAM,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,OAAO,CAAC;AACjM,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+BAA+B,CAAC,CAAC;AACvD;;;;"}