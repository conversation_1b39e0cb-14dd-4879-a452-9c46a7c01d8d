import { u as push, x as head, z as escape_html, y as attr, N as ensure_array_like, w as pop, Q as copy_payload, T as assign_payload } from './index-0Ke2LYl0.js';
import './current-user-BM0W6LNm.js';
import { g as getClient } from './acrpc-D2IaApDT.js';
import { g as goto } from './client-BUddp2Wf.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';
import { M as Modal } from './modal-BDhz9azZ.js';
import { L as Localized_input } from './localized-input-BFX4O5ct.js';
import { L as Localized_textarea } from './localized-textarea-SdDnJXwN.js';
import './schema-CmMg_B_X.js';
import './exports-DxMY0jlE.js';
import './index2-DkUtb91y.js';
import './state.svelte-BMxoNtw-.js';

/* empty css                                                                           */
function Create_commune_modal($$payload, $$props) {
  push();
  const i18n = {
    en: {
      createNewCommune: "Create New Commune",
      communeCreatedSuccess: "Commune created successfully!",
      name: "Name",
      enterCommuneName: "Enter commune name",
      description: "Description (optional)",
      enterCommuneDescription: "Enter commune description",
      cancel: "Cancel",
      create: "Create",
      creating: "Creating...",
      provideName: "Please provide a name for the commune.",
      failedToCreate: "Failed to create commune",
      unexpectedError: "An unexpected error occurred. Please try again."
    },
    ru: {
      createNewCommune: "Создать новую коммуну",
      communeCreatedSuccess: "Коммуна успешно создана!",
      name: "Название",
      enterCommuneName: "Введите название коммуны",
      description: "Описание (опционально)",
      enterCommuneDescription: "Введите описание коммуны",
      cancel: "Отмена",
      create: "Создать",
      creating: "Создание...",
      provideName: "Пожалуйста, укажите название коммуны.",
      failedToCreate: "Не удалось создать коммуну",
      unexpectedError: "Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова."
    }
  };
  const { fetcher: api } = getClient();
  const { show, locale, onHide, toLocaleHref } = $$props;
  const t = i18n[locale];
  let communeName = [];
  let communeDescription = [];
  let error = "";
  let isSubmitting = false;
  let submitSuccess = false;
  function handleClose() {
    onHide();
    communeName = [];
    communeDescription = [];
    error = "";
    submitSuccess = false;
  }
  async function handleSubmit() {
    error = "";
    if (!communeName.some((item) => item.value.trim().length)) {
      error = t.provideName;
      return;
    }
    isSubmitting = true;
    try {
      const { id } = await api.commune.post({ name: communeName, description: communeDescription });
      submitSuccess = true;
      setTimeout(
        () => {
          goto(toLocaleHref(`/communes/${id}`));
        },
        1500
      );
    } catch (err) {
      console.error("Error creating commune:", err);
      error = t.unexpectedError;
    } finally {
      isSubmitting = false;
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Modal($$payload2, {
      show,
      title: t.createNewCommune,
      onClose: handleClose,
      onSubmit: handleSubmit,
      submitText: isSubmitting ? t.creating : t.create,
      cancelText: t.cancel,
      submitDisabled: !communeName.some((item) => item.value.trim().length) || isSubmitting,
      cancelDisabled: isSubmitting,
      isSubmitting,
      children: ($$payload3) => {
        if (submitSuccess) {
          $$payload3.out.push("<!--[-->");
          $$payload3.out.push(`<div class="alert alert-success mb-3">${escape_html(t.communeCreatedSuccess)}</div>`);
        } else {
          $$payload3.out.push("<!--[!-->");
        }
        $$payload3.out.push(`<!--]--> `);
        if (error) {
          $$payload3.out.push("<!--[-->");
          $$payload3.out.push(`<div class="alert alert-danger mb-3">${escape_html(error)}</div>`);
        } else {
          $$payload3.out.push("<!--[!-->");
        }
        $$payload3.out.push(`<!--]--> <form>`);
        Localized_input($$payload3, {
          id: "communeName",
          label: t.name,
          placeholder: t.enterCommuneName,
          required: true,
          locale,
          get value() {
            return communeName;
          },
          set value($$value) {
            communeName = $$value;
            $$settled = false;
          }
        });
        $$payload3.out.push(`<!----> `);
        Localized_textarea($$payload3, {
          id: "communeDescription",
          label: t.description,
          placeholder: t.enterCommuneDescription,
          rows: 4,
          locale,
          get value() {
            return communeDescription;
          },
          set value($$value) {
            communeDescription = $$value;
            $$settled = false;
          }
        });
        $$payload3.out.push(`<!----></form>`);
      }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
function _page($$payload, $$props) {
  push();
  const i18n = {
    en: {
      _page: { title: "Communes — Commune" },
      communes: "Communes",
      create: "Create",
      invitations: "Invitations",
      joinRequests: "Join Requests",
      myInvitations: "My Invitations",
      myJoinRequests: "My Join Requests",
      loading: "Loading...",
      noCommunes: "No communes found",
      member: "member",
      members: "members",
      headMember: "Head",
      errorFetchingCommunes: "Failed to fetch communes",
      errorOccurred: "An error occurred while fetching communes",
      first: "First",
      previous: "Previous",
      next: "Next",
      last: "Last",
      page: "Page",
      loadingMore: "Loading more communes...",
      noImage: "No image",
      communeImageAlt: "Commune image"
    },
    ru: {
      _page: {
        title: "Коммуны — Коммуна"
      },
      communes: "Коммуны",
      create: "Создать",
      invitations: "Приглашения",
      joinRequests: "Заявки",
      myInvitations: "Мои приглашения",
      myJoinRequests: "Мои заявки",
      loading: "Загрузка...",
      noCommunes: "Коммуны не найдены",
      member: "участник",
      members: "участников",
      headMember: "Глава",
      errorFetchingCommunes: "Не удалось загрузить коммуны",
      errorOccurred: "Произошла ошибка при загрузке коммун",
      first: "Первая",
      previous: "Предыдущая",
      next: "Следующая",
      last: "Последняя",
      page: "Страница",
      loadingMore: "Загружаем больше коммун...",
      noImage: "Нет изображения",
      communeImageAlt: "Изображение коммуны"
    }
  };
  const { fetcher: api } = getClient();
  const { data } = $$props;
  const { locale, toLocaleHref, getAppropriateLocalization } = data;
  const t = i18n[locale];
  let communes = data.communes;
  let showCreateModal = false;
  let isHasMoreCommunes = data.isHasMoreCommunes;
  function handleCreateModalClose() {
    showCreateModal = false;
  }
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;
  });
  $$payload.out.push(`<div class="container my-4 mb-5"><div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center my-4 gap-3"><h1 class="mb-0">${escape_html(t.communes)}</h1> <div class="d-flex flex-column flex-sm-row align-items-center gap-2">`);
  if (data.isLoggedIn) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<a${attr("href", toLocaleHref("/communes/invitations"))} class="btn btn-outline-info position-relative"><span class="d-none d-sm-inline">${escape_html(t.myInvitations)}</span> <span class="d-sm-none">${escape_html(t.invitations)}</span> `);
    if (data.pendingInvitationsCount > 0) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">${escape_html(data.pendingInvitationsCount)} <span class="visually-hidden">pending invitations</span></span>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></a> <a${attr("href", toLocaleHref("/communes/join-requests"))} class="btn btn-outline-secondary ms-sm-4"><span class="d-none d-sm-inline">${escape_html(t.myJoinRequests)}</span> <span class="d-sm-none">${escape_html(t.joinRequests)}</span></a>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <button class="btn btn-primary ms-sm-4">${escape_html(t.create)}</button></div></div> `);
  if (communes.length === 0) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-5"><p class="text-muted">${escape_html(t.noCommunes)}</p></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    const each_array = ensure_array_like(communes);
    $$payload.out.push(`<div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-4"><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let commune = each_array[$$index];
      $$payload.out.push(`<div class="col"><div class="card h-100 shadow-sm hover-card svelte-wfgbe0"><a${attr("href", toLocaleHref(`/communes/${commune.id}`))} class="text-decoration-none text-black">`);
      if (commune.image) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<div class="image-container svelte-wfgbe0"><img${attr("src", `/images/${commune.image}`)}${attr("alt", `${t.communeImageAlt}`)} class="svelte-wfgbe0"/></div>`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`<div class="bg-light text-center d-flex align-items-center justify-content-center" style="height: 140px;"><span class="text-muted">${escape_html(t.noImage)}</span></div>`);
      }
      $$payload.out.push(`<!--]--> <div class="card-body d-flex flex-column"><h5 class="card-title fs-5 text-truncate">${escape_html(getAppropriateLocalization(commune.name))}</h5> <p class="card-text text-muted small" style="height: 3rem; overflow: hidden">${escape_html(getAppropriateLocalization(commune.description) || "")}</p> <div class="mt-auto"><span class="badge bg-primary mb-2">${escape_html(commune.memberCount)}
                    ${escape_html(commune.memberCount === 1 ? t.member : t.members)}</span> <div class="small text-muted"><div>${escape_html(t.headMember)}:</div> <div class="d-flex flex-column">${escape_html(getAppropriateLocalization(commune.headMember.name))}</div></div></div></div></a></div></div>`);
    }
    $$payload.out.push(`<!--]--></div>`);
  }
  $$payload.out.push(`<!--]--> `);
  if (isHasMoreCommunes) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-center py-3">`);
    {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  Create_commune_modal($$payload, {
    locale,
    show: showCreateModal,
    onHide: handleCreateModalClose,
    toLocaleHref
  });
  $$payload.out.push(`<!----></div>`);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-iZR-YDNM.js.map
