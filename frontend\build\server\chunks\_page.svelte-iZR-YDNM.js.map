{"version": 3, "file": "_page.svelte-iZR-YDNM.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/__locale__/(index)/communes/_page.svelte.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, w as pop, u as push, z as escape_html, x as head, y as attr, K as ensure_array_like } from \"../../../../../chunks/index.js\";\nimport \"../../../../../chunks/current-user.js\";\nimport { g as getClient } from \"../../../../../chunks/acrpc.js\";\nimport \"clsx\";\nimport { g as goto } from \"../../../../../chunks/client.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nimport { M as Modal } from \"../../../../../chunks/modal.js\";\nimport { L as Localized_input } from \"../../../../../chunks/localized-input.js\";\nimport { L as Localized_textarea } from \"../../../../../chunks/localized-textarea.js\";\n/* empty css                                                                           */\nfunction Create_commune_modal($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      createNewCommune: \"Create New Commune\",\n      communeCreatedSuccess: \"Commune created successfully!\",\n      name: \"Name\",\n      enterCommuneName: \"Enter commune name\",\n      description: \"Description (optional)\",\n      enterCommuneDescription: \"Enter commune description\",\n      cancel: \"Cancel\",\n      create: \"Create\",\n      creating: \"Creating...\",\n      provideName: \"Please provide a name for the commune.\",\n      failedToCreate: \"Failed to create commune\",\n      unexpectedError: \"An unexpected error occurred. Please try again.\"\n    },\n    ru: {\n      createNewCommune: \"Создать новую коммуну\",\n      communeCreatedSuccess: \"Коммуна успешно создана!\",\n      name: \"Название\",\n      enterCommuneName: \"Введите название коммуны\",\n      description: \"Описание (опционально)\",\n      enterCommuneDescription: \"Введите описание коммуны\",\n      cancel: \"Отмена\",\n      create: \"Создать\",\n      creating: \"Создание...\",\n      provideName: \"Пожалуйста, укажите название коммуны.\",\n      failedToCreate: \"Не удалось создать коммуну\",\n      unexpectedError: \"Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова.\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { show, locale, onHide, toLocaleHref } = $$props;\n  const t = i18n[locale];\n  let communeName = [];\n  let communeDescription = [];\n  let error = \"\";\n  let isSubmitting = false;\n  let submitSuccess = false;\n  function handleClose() {\n    onHide();\n    communeName = [];\n    communeDescription = [];\n    error = \"\";\n    submitSuccess = false;\n  }\n  async function handleSubmit() {\n    error = \"\";\n    if (!communeName.some((item) => item.value.trim().length)) {\n      error = t.provideName;\n      return;\n    }\n    isSubmitting = true;\n    try {\n      const { id } = await api.commune.post({ name: communeName, description: communeDescription });\n      submitSuccess = true;\n      setTimeout(\n        () => {\n          goto(toLocaleHref(`/communes/${id}`));\n        },\n        1500\n      );\n    } catch (err) {\n      console.error(\"Error creating commune:\", err);\n      error = t.unexpectedError;\n    } finally {\n      isSubmitting = false;\n    }\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    Modal($$payload2, {\n      show,\n      title: t.createNewCommune,\n      onClose: handleClose,\n      onSubmit: handleSubmit,\n      submitText: isSubmitting ? t.creating : t.create,\n      cancelText: t.cancel,\n      submitDisabled: !communeName.some((item) => item.value.trim().length) || isSubmitting,\n      cancelDisabled: isSubmitting,\n      isSubmitting,\n      children: ($$payload3) => {\n        if (submitSuccess) {\n          $$payload3.out.push(\"<!--[-->\");\n          $$payload3.out.push(`<div class=\"alert alert-success mb-3\">${escape_html(t.communeCreatedSuccess)}</div>`);\n        } else {\n          $$payload3.out.push(\"<!--[!-->\");\n        }\n        $$payload3.out.push(`<!--]--> `);\n        if (error) {\n          $$payload3.out.push(\"<!--[-->\");\n          $$payload3.out.push(`<div class=\"alert alert-danger mb-3\">${escape_html(error)}</div>`);\n        } else {\n          $$payload3.out.push(\"<!--[!-->\");\n        }\n        $$payload3.out.push(`<!--]--> <form>`);\n        Localized_input($$payload3, {\n          id: \"communeName\",\n          label: t.name,\n          placeholder: t.enterCommuneName,\n          required: true,\n          locale,\n          get value() {\n            return communeName;\n          },\n          set value($$value) {\n            communeName = $$value;\n            $$settled = false;\n          }\n        });\n        $$payload3.out.push(`<!----> `);\n        Localized_textarea($$payload3, {\n          id: \"communeDescription\",\n          label: t.description,\n          placeholder: t.enterCommuneDescription,\n          rows: 4,\n          locale,\n          get value() {\n            return communeDescription;\n          },\n          set value($$value) {\n            communeDescription = $$value;\n            $$settled = false;\n          }\n        });\n        $$payload3.out.push(`<!----></form>`);\n      }\n    });\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nfunction _page($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      _page: { title: \"Communes — Commune\" },\n      communes: \"Communes\",\n      create: \"Create\",\n      invitations: \"Invitations\",\n      joinRequests: \"Join Requests\",\n      myInvitations: \"My Invitations\",\n      myJoinRequests: \"My Join Requests\",\n      loading: \"Loading...\",\n      noCommunes: \"No communes found\",\n      member: \"member\",\n      members: \"members\",\n      headMember: \"Head\",\n      errorFetchingCommunes: \"Failed to fetch communes\",\n      errorOccurred: \"An error occurred while fetching communes\",\n      first: \"First\",\n      previous: \"Previous\",\n      next: \"Next\",\n      last: \"Last\",\n      page: \"Page\",\n      loadingMore: \"Loading more communes...\",\n      noImage: \"No image\",\n      communeImageAlt: \"Commune image\"\n    },\n    ru: {\n      _page: {\n        title: \"Коммуны — Коммуна\"\n      },\n      communes: \"Коммуны\",\n      create: \"Создать\",\n      invitations: \"Приглашения\",\n      joinRequests: \"Заявки\",\n      myInvitations: \"Мои приглашения\",\n      myJoinRequests: \"Мои заявки\",\n      loading: \"Загрузка...\",\n      noCommunes: \"Коммуны не найдены\",\n      member: \"участник\",\n      members: \"участников\",\n      headMember: \"Глава\",\n      errorFetchingCommunes: \"Не удалось загрузить коммуны\",\n      errorOccurred: \"Произошла ошибка при загрузке коммун\",\n      first: \"Первая\",\n      previous: \"Предыдущая\",\n      next: \"Следующая\",\n      last: \"Последняя\",\n      page: \"Страница\",\n      loadingMore: \"Загружаем больше коммун...\",\n      noImage: \"Нет изображения\",\n      communeImageAlt: \"Изображение коммуны\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  const { data } = $$props;\n  const { locale, toLocaleHref, getAppropriateLocalization } = data;\n  const t = i18n[locale];\n  let communes = data.communes;\n  let showCreateModal = false;\n  let isHasMoreCommunes = data.isHasMoreCommunes;\n  function handleCreateModalClose() {\n    showCreateModal = false;\n  }\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(t._page.title)}</title>`;\n  });\n  $$payload.out.push(`<div class=\"container my-4 mb-5\"><div class=\"d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center my-4 gap-3\"><h1 class=\"mb-0\">${escape_html(t.communes)}</h1> <div class=\"d-flex flex-column flex-sm-row align-items-center gap-2\">`);\n  if (data.isLoggedIn) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<a${attr(\"href\", toLocaleHref(\"/communes/invitations\"))} class=\"btn btn-outline-info position-relative\"><span class=\"d-none d-sm-inline\">${escape_html(t.myInvitations)}</span> <span class=\"d-sm-none\">${escape_html(t.invitations)}</span> `);\n    if (data.pendingInvitationsCount > 0) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<span class=\"position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger\">${escape_html(data.pendingInvitationsCount)} <span class=\"visually-hidden\">pending invitations</span></span>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></a> <a${attr(\"href\", toLocaleHref(\"/communes/join-requests\"))} class=\"btn btn-outline-secondary ms-sm-4\"><span class=\"d-none d-sm-inline\">${escape_html(t.myJoinRequests)}</span> <span class=\"d-sm-none\">${escape_html(t.joinRequests)}</span></a>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> <button class=\"btn btn-primary ms-sm-4\">${escape_html(t.create)}</button></div></div> `);\n  if (communes.length === 0) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-5\"><p class=\"text-muted\">${escape_html(t.noCommunes)}</p></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    const each_array = ensure_array_like(communes);\n    $$payload.out.push(`<div class=\"row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-4\"><!--[-->`);\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let commune = each_array[$$index];\n      $$payload.out.push(`<div class=\"col\"><div class=\"card h-100 shadow-sm hover-card svelte-wfgbe0\"><a${attr(\"href\", toLocaleHref(`/communes/${commune.id}`))} class=\"text-decoration-none text-black\">`);\n      if (commune.image) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`<div class=\"image-container svelte-wfgbe0\"><img${attr(\"src\", `/images/${commune.image}`)}${attr(\"alt\", `${t.communeImageAlt}`)} class=\"svelte-wfgbe0\"/></div>`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        $$payload.out.push(`<div class=\"bg-light text-center d-flex align-items-center justify-content-center\" style=\"height: 140px;\"><span class=\"text-muted\">${escape_html(t.noImage)}</span></div>`);\n      }\n      $$payload.out.push(`<!--]--> <div class=\"card-body d-flex flex-column\"><h5 class=\"card-title fs-5 text-truncate\">${escape_html(getAppropriateLocalization(commune.name))}</h5> <p class=\"card-text text-muted small\" style=\"height: 3rem; overflow: hidden\">${escape_html(getAppropriateLocalization(commune.description) || \"\")}</p> <div class=\"mt-auto\"><span class=\"badge bg-primary mb-2\">${escape_html(commune.memberCount)}\n                    ${escape_html(commune.memberCount === 1 ? t.member : t.members)}</span> <div class=\"small text-muted\"><div>${escape_html(t.headMember)}:</div> <div class=\"d-flex flex-column\">${escape_html(getAppropriateLocalization(commune.headMember.name))}</div></div></div></div></a></div></div>`);\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  }\n  $$payload.out.push(`<!--]--> `);\n  if (isHasMoreCommunes) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-center py-3\">`);\n    {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  Create_commune_modal($$payload, {\n    locale,\n    show: showCreateModal,\n    onHide: handleCreateModalClose,\n    toLocaleHref\n  });\n  $$payload.out.push(`<!----></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAUA;AACA,SAAS,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE;AAClD,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,gBAAgB,EAAE,oBAAoB;AAC5C,MAAM,qBAAqB,EAAE,+BAA+B;AAC5D,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,gBAAgB,EAAE,oBAAoB;AAC5C,MAAM,WAAW,EAAE,wBAAwB;AAC3C,MAAM,uBAAuB,EAAE,2BAA2B;AAC1D,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,WAAW,EAAE,wCAAwC;AAC3D,MAAM,cAAc,EAAE,0BAA0B;AAChD,MAAM,eAAe,EAAE;AACvB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,gBAAgB,EAAE,uBAAuB;AAC/C,MAAM,qBAAqB,EAAE,0BAA0B;AACvD,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,gBAAgB,EAAE,0BAA0B;AAClD,MAAM,WAAW,EAAE,wBAAwB;AAC3C,MAAM,uBAAuB,EAAE,0BAA0B;AACzD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,WAAW,EAAE,uCAAuC;AAC1D,MAAM,cAAc,EAAE,4BAA4B;AAClD,MAAM,eAAe,EAAE;AACvB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO;AACxD,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,WAAW,GAAG,EAAE;AACtB,EAAE,IAAI,kBAAkB,GAAG,EAAE;AAC7B,EAAE,IAAI,KAAK,GAAG,EAAE;AAChB,EAAE,IAAI,YAAY,GAAG,KAAK;AAC1B,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,SAAS,WAAW,GAAG;AACzB,IAAI,MAAM,EAAE;AACZ,IAAI,WAAW,GAAG,EAAE;AACpB,IAAI,kBAAkB,GAAG,EAAE;AAC3B,IAAI,KAAK,GAAG,EAAE;AACd,IAAI,aAAa,GAAG,KAAK;AACzB,EAAE;AACF,EAAE,eAAe,YAAY,GAAG;AAChC,IAAI,KAAK,GAAG,EAAE;AACd,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE;AAC/D,MAAM,KAAK,GAAG,CAAC,CAAC,WAAW;AAC3B,MAAM;AACN,IAAI;AACJ,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,IAAI;AACR,MAAM,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;AACnG,MAAM,aAAa,GAAG,IAAI;AAC1B,MAAM,UAAU;AAChB,QAAQ,MAAM;AACd,UAAU,IAAI,CAAC,YAAY,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/C,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO;AACP,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,GAAG,CAAC;AACnD,MAAM,KAAK,GAAG,CAAC,CAAC,eAAe;AAC/B,IAAI,CAAC,SAAS;AACd,MAAM,YAAY,GAAG,KAAK;AAC1B,IAAI;AACJ,EAAE;AACF,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,IAAI;AACV,MAAM,KAAK,EAAE,CAAC,CAAC,gBAAgB;AAC/B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,UAAU,EAAE,YAAY,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM;AACtD,MAAM,UAAU,EAAE,CAAC,CAAC,MAAM;AAC1B,MAAM,cAAc,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,IAAI,YAAY;AAC3F,MAAM,cAAc,EAAE,YAAY;AAClC,MAAM,YAAY;AAClB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,IAAI,aAAa,EAAE;AAC3B,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,CAAC;AACpH,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACxC,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;AACjG,QAAQ,CAAC,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ;AACR,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AAC9C,QAAQ,eAAe,CAAC,UAAU,EAAE;AACpC,UAAU,EAAE,EAAE,aAAa;AAC3B,UAAU,KAAK,EAAE,CAAC,CAAC,IAAI;AACvB,UAAU,WAAW,EAAE,CAAC,CAAC,gBAAgB;AACzC,UAAU,QAAQ,EAAE,IAAI;AACxB,UAAU,MAAM;AAChB,UAAU,IAAI,KAAK,GAAG;AACtB,YAAY,OAAO,WAAW;AAC9B,UAAU,CAAC;AACX,UAAU,IAAI,KAAK,CAAC,OAAO,EAAE;AAC7B,YAAY,WAAW,GAAG,OAAO;AACjC,YAAY,SAAS,GAAG,KAAK;AAC7B,UAAU;AACV,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvC,QAAQ,kBAAkB,CAAC,UAAU,EAAE;AACvC,UAAU,EAAE,EAAE,oBAAoB;AAClC,UAAU,KAAK,EAAE,CAAC,CAAC,WAAW;AAC9B,UAAU,WAAW,EAAE,CAAC,CAAC,uBAAuB;AAChD,UAAU,IAAI,EAAE,CAAC;AACjB,UAAU,MAAM;AAChB,UAAU,IAAI,KAAK,GAAG;AACtB,YAAY,OAAO,kBAAkB;AACrC,UAAU,CAAC;AACX,UAAU,IAAI,KAAK,CAAC,OAAO,EAAE;AAC7B,YAAY,kBAAkB,GAAG,OAAO;AACxC,YAAY,SAAS,GAAG,KAAK;AAC7B,UAAU;AACV,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AAC7C,MAAM;AACN,KAAK,CAAC;AACN,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;AAC5C,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,YAAY,EAAE,eAAe;AACnC,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,cAAc,EAAE,kBAAkB;AACxC,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,UAAU,EAAE,mBAAmB;AACrC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,UAAU,EAAE,MAAM;AACxB,MAAM,qBAAqB,EAAE,0BAA0B;AACvD,MAAM,aAAa,EAAE,2CAA2C;AAChE,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,WAAW,EAAE,0BAA0B;AAC7C,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,eAAe,EAAE;AACvB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,YAAY,EAAE,QAAQ;AAC5B,MAAM,aAAa,EAAE,iBAAiB;AACtC,MAAM,cAAc,EAAE,YAAY;AAClC,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,UAAU,EAAE,oBAAoB;AACtC,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,UAAU,EAAE,OAAO;AACzB,MAAM,qBAAqB,EAAE,8BAA8B;AAC3D,MAAM,aAAa,EAAE,sCAAsC;AAC3D,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,WAAW,EAAE,4BAA4B;AAC/C,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,eAAe,EAAE;AACvB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,0BAA0B,EAAE,GAAG,IAAI;AACnE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC9B,EAAE,IAAI,eAAe,GAAG,KAAK;AAC7B,EAAE,IAAI,iBAAiB,GAAG,IAAI,CAAC,iBAAiB;AAChD,EAAE,SAAS,sBAAsB,GAAG;AACpC,IAAI,eAAe,GAAG,KAAK;AAC3B,EAAE;AACF,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACrE,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yKAAyK,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,2EAA2E,CAAC,CAAC;AACtS,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;AACvB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,uBAAuB,CAAC,CAAC,CAAC,iFAAiF,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvQ,IAAI,IAAI,IAAI,CAAC,uBAAuB,GAAG,CAAC,EAAE;AAC1C,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8FAA8F,EAAE,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,gEAAgE,CAAC,CAAC;AACtO,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,yBAAyB,CAAC,CAAC,CAAC,4EAA4E,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AACtR,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iDAAiD,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,CAAC;AACvH,EAAE,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAC7B,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC;AACpH,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC;AAClD,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kFAAkF,CAAC,CAAC;AAC5G,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACvC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8EAA8E,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,yCAAyC,CAAC,CAAC;AAC3M,MAAM,IAAI,OAAO,CAAC,KAAK,EAAE;AACzB,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+CAA+C,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC;AAC3L,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mIAAmI,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AACvM,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6FAA6F,EAAE,WAAW,CAAC,0BAA0B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,mFAAmF,EAAE,WAAW,CAAC,0BAA0B,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,8DAA8D,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;AACva,oBAAoB,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,2CAA2C,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,wCAAwC,EAAE,WAAW,CAAC,0BAA0B,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,wCAAwC,CAAC,CAAC;AAChT,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE,IAAI,iBAAiB,EAAE;AACzB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AACxD,IAAI;AACJ,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE,oBAAoB,CAAC,SAAS,EAAE;AAClC,IAAI,MAAM;AACV,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,MAAM,EAAE,sBAAsB;AAClC,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AACrC,EAAE,GAAG,EAAE;AACP;;;;"}