{"version": 3, "file": "_server.ts-CqQwnGzg.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/robots.txt/_server.ts.js"], "sourcesContent": ["const content = `\nUser-agent: *\nAllow: /\nDisallow: /users\nDisallow: /reactor$\n\nSitemap: https://commune.my/sitemap.xml\n`.trim();\nasync function GET() {\n  return new Response(\n    content,\n    {\n      headers: {\n        \"Content-Type\": \"text/plain\"\n      }\n    }\n  );\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": "AAAA,MAAM,OAAO,GAAG;AAChB;AACA;AACA;AACA;;AAEA;AACA,CAAC,CAAC,IAAI,EAAE;AACR,eAAe,GAAG,GAAG;AACrB,EAAE,OAAO,IAAI,QAAQ;AACrB,IAAI,OAAO;AACX,IAAI;AACJ,MAAM,OAAO,EAAE;AACf,QAAQ,cAAc,EAAE;AACxB;AACA;AACA,GAAG;AACH;;;;"}