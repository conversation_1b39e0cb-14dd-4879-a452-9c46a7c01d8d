{"version": 3, "file": "acrpc-D2IaApDT.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/acrpc.js"], "sourcesContent": ["import { redirect } from \"@sveltejs/kit\";\nimport { d as dir, k as kebabTransformer, l as log, j as jsonTransformer, t as transformer, s as schema } from \"./schema.js\";\nimport { z } from \"zod\";\nimport \"./current-user.js\";\nfunction isEndpoint(schemaEntry) {\n  return schemaEntry != null && typeof schemaEntry === \"object\" && (\"input\" in schemaEntry && (schemaEntry[\"input\"] instanceof z.ZodType || schemaEntry[\"input\"] === null || schemaEntry[\"input\"] === void 0) && (\"output\" in schemaEntry && (schemaEntry[\"output\"] instanceof z.ZodType || schemaEntry[\"output\"] === null || schemaEntry[\"output\"] === void 0)));\n}\nvar HttpError = class extends Error {\n  method;\n  url;\n  status;\n  description;\n  constructor(method, url, status, description) {\n    super(`Fetch at ${method.toUpperCase()} ${url} failed, status ${status}, description: '${description}'`);\n    this.method = method;\n    this.url = url;\n    this.status = status;\n    this.description = description;\n  }\n};\nfunction getLocalStorage() {\n  if (typeof window !== \"undefined\") {\n    return window.localStorage ?? null;\n  }\n  if (typeof globalThis !== void 0) {\n    return globalThis.localStorage ?? null;\n  }\n  return null;\n}\nfunction parsePathToMasterPaths(path) {\n  if (!path.length) {\n    return [];\n  }\n  const parts = path.split(\"/\").slice(1);\n  const masterPaths = [];\n  for (let i = 0; i < parts.length; i++) {\n    masterPaths.push(\"/\" + parts.slice(0, i + 1).join(\"/\"));\n  }\n  return masterPaths;\n}\nfunction initMasterPathMapEntryFactory(map) {\n  return function initMasterPathMapEntry(path) {\n    const masterPaths = parsePathToMasterPaths(path);\n    for (const masterPath of masterPaths) {\n      if (!map.has(masterPath)) {\n        map.set(masterPath, []);\n      }\n      map.get(masterPath).push(path);\n    }\n  };\n}\nfunction normalizeMasterPathMap(masterPathMap) {\n  for (const [masterPath, paths] of masterPathMap) {\n    masterPathMap.set(masterPath, [...new Set(paths)]);\n  }\n}\nfunction fillReverseMasterPathMap(masterPathMap, reverseMasterPathMap) {\n  for (const [masterPath, paths] of masterPathMap) {\n    for (const path of paths) {\n      if (!reverseMasterPathMap.has(path)) {\n        reverseMasterPathMap.set(path, []);\n      }\n      reverseMasterPathMap.get(path).push(masterPath);\n    }\n  }\n}\nfunction hydrateInvalidPathCacheSet(invalidPathCacheSet) {\n  const invalidPaths = getLocalStorage()?.getItem(\"acrpc:invalid-paths\");\n  if (invalidPaths) {\n    try {\n      const parsedInvalidPaths = JSON.parse(invalidPaths);\n      for (const invalidPath of parsedInvalidPaths) {\n        invalidPathCacheSet.add(invalidPath);\n      }\n    } catch (error) {\n      console.error(\"Error parsing invalid paths\", error);\n      getLocalStorage()?.removeItem(\"acrpc:invalid-paths\");\n    }\n  }\n}\nfunction invalidatePathCache2Factory(masterPathMap, reverseMasterPathMap, invalidPathCacheSet) {\n  return function invalidatePathCache2(path, depth) {\n    const masterPaths = masterPathMap.get(path) ?? [];\n    dir(\n      \"invalidating path cache\",\n      {\n        path,\n        depth,\n        masterPaths\n      }\n    );\n    const masterPath = masterPaths[Math.max(0, masterPaths.length - depth)];\n    const paths = reverseMasterPathMap.get(masterPath) ?? [];\n    dir(\n      \"invalidating path cache 2\",\n      {\n        masterPath,\n        paths\n      }\n    );\n    for (const path2 of paths) {\n      invalidPathCacheSet.add(path2);\n    }\n    getLocalStorage()?.setItem(\n      \"acrpc:invalid-paths\",\n      JSON.stringify([...invalidPathCacheSet])\n    );\n  };\n}\nfunction createClient(schema2, options) {\n  const transformer2 = options.transformer ?? jsonTransformer;\n  const entrypointUrl = options.entrypointUrl.endsWith(\"/\") ? options.entrypointUrl.slice(0, -1) : options.entrypointUrl;\n  const masterPathMap = /* @__PURE__ */ new Map();\n  const reverseMasterPathMap = /* @__PURE__ */ new Map();\n  const invalidPathCacheSet = /* @__PURE__ */ new Set();\n  const initMasterPathMapEntry = initMasterPathMapEntryFactory(masterPathMap);\n  const invalidatePathCache2 = invalidatePathCache2Factory(\n    masterPathMap,\n    reverseMasterPathMap,\n    invalidPathCacheSet\n  );\n  dir({\n    invalidPathCacheSet\n  });\n  const baseFetch = options.fetch ?? fetch;\n  const baseInit = { ...options.init };\n  function fillClientFetcher(schema22, names, result) {\n    for (const [name, schemaEntry] of Object.entries(schema22)) {\n      const kebabName = kebabTransformer.transform(name);\n      if (isEndpoint(schemaEntry)) {\n        let parseArgs = function(args) {\n          if (schemaEntry.input === null) {\n            return [void 0, { ...args[0] }];\n          }\n          return [args[0], args[1]];\n        };\n        const path = [\"\", ...names].join(\"/\");\n        const method = name;\n        initMasterPathMapEntry(path);\n        const obj = {\n          [method]: async function(...args) {\n            const [input, init] = parseArgs(args);\n            if (schemaEntry.input != null && !input) {\n              throw new Error(\"Input data argument not provided.\");\n            }\n            log(`Performing ${method.toUpperCase()} ${path}...`);\n            dir({\n              entrypointUrl,\n              path\n            });\n            const isInvalidCache = invalidPathCacheSet.has(path);\n            const requestInit = {\n              ...baseInit,\n              ...init,\n              headers: {\n                ...baseInit.headers,\n                ...init?.headers,\n                ...isInvalidCache ? {\n                  \"Cache-Control\": \"reload\"\n                } : null\n              },\n              method: method.toUpperCase()\n            };\n            let searchQuery = \"\";\n            if (schemaEntry.input !== null && input !== void 0) {\n              const serializedInput = transformer2.serialize(input);\n              if (method === \"get\") {\n                searchQuery = `__body=${encodeURIComponent(serializedInput)}`;\n              } else {\n                requestInit.headers[\"Content-Type\"] = \"application/json\";\n                requestInit.body = serializedInput;\n              }\n            }\n            const fetch2 = init?.fetch ?? baseFetch;\n            delete init?.fetch;\n            const search = searchQuery ? `?${searchQuery}` : \"\";\n            dir({\n              fetchUrl: entrypointUrl + path + search\n            });\n            const fetchResult = await fetch2(\n              entrypointUrl + path + search,\n              requestInit\n            );\n            if (!init?.skipInterceptor) {\n              await options.interceptor?.({\n                method,\n                path,\n                response: fetchResult,\n                ctx: init?.ctx\n              });\n            }\n            if (fetchResult.ok) {\n              let output = null;\n              if (schemaEntry.output !== null) {\n                const rawOutput = await fetchResult.text();\n                output = transformer2.deserialize(rawOutput);\n              }\n              dir({\n                autoScopeInvalidationDepth: schemaEntry.autoScopeInvalidationDepth,\n                invalidate: schemaEntry.invalidate\n              });\n              dir(\n                \"before invalidations\",\n                {\n                  masterPathMap,\n                  // cacheVersionMap,\n                  invalidPathCacheSet\n                }\n              );\n              const autoScopeInvalidationDepth = schemaEntry.autoScopeInvalidationDepth ?? 0;\n              if (autoScopeInvalidationDepth) {\n                invalidatePathCache2(path, autoScopeInvalidationDepth);\n              }\n              if (schemaEntry.invalidate) {\n                for (const invalidate of schemaEntry.invalidate) {\n                  invalidatePathCache2(invalidate, 0);\n                }\n              }\n              dir(\n                \"after invalidations\",\n                {\n                  masterPathMap,\n                  // cacheVersionMap,\n                  invalidPathCacheSet\n                }\n              );\n              return output;\n            }\n            throw new HttpError(\n              method,\n              path,\n              fetchResult.status,\n              await fetchResult.text() || fetchResult.statusText\n            );\n          }\n        };\n        Object.assign(result, obj);\n      } else {\n        const nestedResult = result[name] = {};\n        fillClientFetcher(\n          schemaEntry,\n          [...names, kebabName],\n          nestedResult\n        );\n      }\n    }\n    return result;\n  }\n  const fetcher = fillClientFetcher(\n    schema2,\n    [],\n    {}\n  );\n  normalizeMasterPathMap(masterPathMap);\n  fillReverseMasterPathMap(masterPathMap, reverseMasterPathMap);\n  hydrateInvalidPathCacheSet(invalidPathCacheSet);\n  return {\n    fetcher\n  };\n}\nlet __client = void 0;\nfunction getClient() {\n  return __client ??= createClient(schema, {\n    entrypointUrl: \"/api\",\n    transformer,\n    init: {\n      credentials: \"include\"\n    },\n    interceptor: async ({ response, ctx }) => {\n      if (response.status === 401) {\n        const redirectFrom = ctx ? ctx.url.pathname + ctx.url.search : \"/\";\n        redirect(302, `/auth?redirectFrom=${encodeURIComponent(redirectFrom)}`);\n      }\n    }\n  });\n}\nexport {\n  getClient as g\n};\n"], "names": ["z.ZodType"], "mappings": ";;;;AAIA,SAAS,UAAU,CAAC,WAAW,EAAE;AACjC,EAAE,OAAO,WAAW,IAAI,IAAI,IAAI,OAAO,WAAW,KAAK,QAAQ,KAAK,OAAO,IAAI,WAAW,KAAK,WAAW,CAAC,OAAO,CAAC,YAAYA,OAAS,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,WAAW,KAAK,WAAW,CAAC,QAAQ,CAAC,YAAYA,OAAS,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,IAAI,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC;AACjW;AACA,IAAI,SAAS,GAAG,cAAc,KAAK,CAAC;AACpC,EAAE,MAAM;AACR,EAAE,GAAG;AACL,EAAE,MAAM;AACR,EAAE,WAAW;AACb,EAAE,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE;AAChD,IAAI,KAAK,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;AAC5G,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;AACxB,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG;AAClB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;AACxB,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW;AAClC,EAAE;AACF,CAAC;AACD,SAAS,eAAe,GAAG;AAC3B,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACrC,IAAI,OAAO,MAAM,CAAC,YAAY,IAAI,IAAI;AACtC,EAAE;AACF,EAAE,IAAI,OAAO,UAAU,KAAK,MAAM,EAAE;AACpC,IAAI,OAAO,UAAU,CAAC,YAAY,IAAI,IAAI;AAC1C,EAAE;AACF,EAAE,OAAO,IAAI;AACb;AACA,SAAS,sBAAsB,CAAC,IAAI,EAAE;AACtC,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACpB,IAAI,OAAO,EAAE;AACb,EAAE;AACF,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACxC,EAAE,MAAM,WAAW,GAAG,EAAE;AACxB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3D,EAAE;AACF,EAAE,OAAO,WAAW;AACpB;AACA,SAAS,6BAA6B,CAAC,GAAG,EAAE;AAC5C,EAAE,OAAO,SAAS,sBAAsB,CAAC,IAAI,EAAE;AAC/C,IAAI,MAAM,WAAW,GAAG,sBAAsB,CAAC,IAAI,CAAC;AACpD,IAAI,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;AAC1C,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AAChC,QAAQ,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC;AAC/B,MAAM;AACN,MAAM,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACpC,IAAI;AACJ,EAAE,CAAC;AACH;AACA,SAAS,sBAAsB,CAAC,aAAa,EAAE;AAC/C,EAAE,KAAK,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,aAAa,EAAE;AACnD,IAAI,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACtD,EAAE;AACF;AACA,SAAS,wBAAwB,CAAC,aAAa,EAAE,oBAAoB,EAAE;AACvE,EAAE,KAAK,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,aAAa,EAAE;AACnD,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAC9B,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC3C,QAAQ,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;AAC1C,MAAM;AACN,MAAM,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;AACrD,IAAI;AACJ,EAAE;AACF;AACA,SAAS,0BAA0B,CAAC,mBAAmB,EAAE;AACzD,EAAE,MAAM,YAAY,GAAG,eAAe,EAAE,EAAE,OAAO,CAAC,qBAAqB,CAAC;AACxE,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,IAAI;AACR,MAAM,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;AACzD,MAAM,KAAK,MAAM,WAAW,IAAI,kBAAkB,EAAE;AACpD,QAAQ,mBAAmB,CAAC,GAAG,CAAC,WAAW,CAAC;AAC5C,MAAM;AACN,IAAI,CAAC,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACzD,MAAM,eAAe,EAAE,EAAE,UAAU,CAAC,qBAAqB,CAAC;AAC1D,IAAI;AACJ,EAAE;AACF;AACA,SAAS,2BAA2B,CAAC,aAAa,EAAE,oBAAoB,EAAE,mBAAmB,EAAE;AAC/F,EAAE,OAAO,SAAS,oBAAoB,CAAC,IAAI,EAAE,KAAK,EAAE;AACpD,IAAI,MAAM,WAAW,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;AACrD,IAAI,GAAG;AACP,MAAM,yBAAyB;AAC/B,MAAM;AACN,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ;AACR;AACA,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;AAC3E,IAAI,MAAM,KAAK,GAAG,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;AAC5D,IAAI,GAAG;AACP,MAAM,2BAA2B;AACjC,MAAM;AACN,QAAQ,UAAU;AAClB,QAAQ;AACR;AACA,KAAK;AACL,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE;AAC/B,MAAM,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC;AACpC,IAAI;AACJ,IAAI,eAAe,EAAE,EAAE,OAAO;AAC9B,MAAM,qBAAqB;AAC3B,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAC7C,KAAK;AACL,EAAE,CAAC;AACH;AACA,SAAS,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE;AACxC,EAAE,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,IAAI,eAAe;AAC7D,EAAE,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,OAAO,CAAC,aAAa;AACxH,EAAE,MAAM,aAAa,mBAAmB,IAAI,GAAG,EAAE;AACjD,EAAE,MAAM,oBAAoB,mBAAmB,IAAI,GAAG,EAAE;AACxD,EAAE,MAAM,mBAAmB,mBAAmB,IAAI,GAAG,EAAE;AACvD,EAAE,MAAM,sBAAsB,GAAG,6BAA6B,CAAC,aAAa,CAAC;AAC7E,EAAE,MAAM,oBAAoB,GAAG,2BAA2B;AAC1D,IAAI,aAAa;AACjB,IAAI,oBAAoB;AACxB,IAAI;AACJ,GAAG;AACH,EAAE,GAAG,CAAC;AACN,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,IAAI,KAAK;AAC1C,EAAE,MAAM,QAAQ,GAAG,EAAE,GAAG,OAAO,CAAC,IAAI,EAAE;AACtC,EAAE,SAAS,iBAAiB,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;AACtD,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAChE,MAAM,MAAM,SAAS,GAAG,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC;AACxD,MAAM,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE;AACnC,QAAQ,IAAI,SAAS,GAAG,SAAS,IAAI,EAAE;AACvC,UAAU,IAAI,WAAW,CAAC,KAAK,KAAK,IAAI,EAAE;AAC1C,YAAY,OAAO,CAAC,MAAM,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;AAC3C,UAAU;AACV,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACnC,QAAQ,CAAC;AACT,QAAQ,MAAM,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AAC7C,QAAQ,MAAM,MAAM,GAAG,IAAI;AAC3B,QAAQ,sBAAsB,CAAC,IAAI,CAAC;AACpC,QAAQ,MAAM,GAAG,GAAG;AACpB,UAAU,CAAC,MAAM,GAAG,eAAe,GAAG,IAAI,EAAE;AAC5C,YAAY,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;AACjD,YAAY,IAAI,WAAW,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AACrD,cAAc,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC;AAClE,YAAY;AACZ,YAAY,GAAG,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAChE,YAAY,GAAG,CAAC;AAChB,cAAc,aAAa;AAC3B,cAAc;AACd,aAAa,CAAC;AACd,YAAY,MAAM,cAAc,GAAG,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC;AAChE,YAAY,MAAM,WAAW,GAAG;AAChC,cAAc,GAAG,QAAQ;AACzB,cAAc,GAAG,IAAI;AACrB,cAAc,OAAO,EAAE;AACvB,gBAAgB,GAAG,QAAQ,CAAC,OAAO;AACnC,gBAAgB,GAAG,IAAI,EAAE,OAAO;AAChC,gBAAgB,GAAG,cAAc,GAAG;AACpC,kBAAkB,eAAe,EAAE;AACnC,iBAAiB,GAAG;AACpB,eAAe;AACf,cAAc,MAAM,EAAE,MAAM,CAAC,WAAW;AACxC,aAAa;AACb,YAAY,IAAI,WAAW,GAAG,EAAE;AAChC,YAAY,IAAI,WAAW,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;AAChE,cAAc,MAAM,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC;AACnE,cAAc,IAAI,MAAM,KAAK,KAAK,EAAE;AACpC,gBAAgB,WAAW,GAAG,CAAC,OAAO,EAAE,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC;AAC7E,cAAc,CAAC,MAAM;AACrB,gBAAgB,WAAW,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;AACxE,gBAAgB,WAAW,CAAC,IAAI,GAAG,eAAe;AAClD,cAAc;AACd,YAAY;AACZ,YAAY,MAAM,MAAM,GAAG,IAAI,EAAE,KAAK,IAAI,SAAS;AACnD,YAAY,OAAO,IAAI,EAAE,KAAK;AAC9B,YAAY,MAAM,MAAM,GAAG,WAAW,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,EAAE;AAC/D,YAAY,GAAG,CAAC;AAChB,cAAc,QAAQ,EAAE,aAAa,GAAG,IAAI,GAAG;AAC/C,aAAa,CAAC;AACd,YAAY,MAAM,WAAW,GAAG,MAAM,MAAM;AAC5C,cAAc,aAAa,GAAG,IAAI,GAAG,MAAM;AAC3C,cAAc;AACd,aAAa;AACb,YAAY,IAAI,CAAC,IAAI,EAAE,eAAe,EAAE;AACxC,cAAc,MAAM,OAAO,CAAC,WAAW,GAAG;AAC1C,gBAAgB,MAAM;AACtB,gBAAgB,IAAI;AACpB,gBAAgB,QAAQ,EAAE,WAAW;AACrC,gBAAgB,GAAG,EAAE,IAAI,EAAE;AAC3B,eAAe,CAAC;AAChB,YAAY;AACZ,YAAY,IAAI,WAAW,CAAC,EAAE,EAAE;AAChC,cAAc,IAAI,MAAM,GAAG,IAAI;AAC/B,cAAc,IAAI,WAAW,CAAC,MAAM,KAAK,IAAI,EAAE;AAC/C,gBAAgB,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,IAAI,EAAE;AAC1D,gBAAgB,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC;AAC5D,cAAc;AACd,cAAc,GAAG,CAAC;AAClB,gBAAgB,0BAA0B,EAAE,WAAW,CAAC,0BAA0B;AAClF,gBAAgB,UAAU,EAAE,WAAW,CAAC;AACxC,eAAe,CAAC;AAChB,cAAc,GAAG;AACjB,gBAAgB,sBAAsB;AACtC,gBAAgB;AAChB,kBAAkB,aAAa;AAC/B;AACA,kBAAkB;AAClB;AACA,eAAe;AACf,cAAc,MAAM,0BAA0B,GAAG,WAAW,CAAC,0BAA0B,IAAI,CAAC;AAC5F,cAAc,IAAI,0BAA0B,EAAE;AAC9C,gBAAgB,oBAAoB,CAAC,IAAI,EAAE,0BAA0B,CAAC;AACtE,cAAc;AACd,cAAc,IAAI,WAAW,CAAC,UAAU,EAAE;AAC1C,gBAAgB,KAAK,MAAM,UAAU,IAAI,WAAW,CAAC,UAAU,EAAE;AACjE,kBAAkB,oBAAoB,CAAC,UAAU,EAAE,CAAC,CAAC;AACrD,gBAAgB;AAChB,cAAc;AACd,cAAc,GAAG;AACjB,gBAAgB,qBAAqB;AACrC,gBAAgB;AAChB,kBAAkB,aAAa;AAC/B;AACA,kBAAkB;AAClB;AACA,eAAe;AACf,cAAc,OAAO,MAAM;AAC3B,YAAY;AACZ,YAAY,MAAM,IAAI,SAAS;AAC/B,cAAc,MAAM;AACpB,cAAc,IAAI;AAClB,cAAc,WAAW,CAAC,MAAM;AAChC,cAAc,MAAM,WAAW,CAAC,IAAI,EAAE,IAAI,WAAW,CAAC;AACtD,aAAa;AACb,UAAU;AACV,SAAS;AACT,QAAQ,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;AAClC,MAAM,CAAC,MAAM;AACb,QAAQ,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE;AAC9C,QAAQ,iBAAiB;AACzB,UAAU,WAAW;AACrB,UAAU,CAAC,GAAG,KAAK,EAAE,SAAS,CAAC;AAC/B,UAAU;AACV,SAAS;AACT,MAAM;AACN,IAAI;AACJ,IAAI,OAAO,MAAM;AACjB,EAAE;AACF,EAAE,MAAM,OAAO,GAAG,iBAAiB;AACnC,IAAI,OAAO;AACX,IAAI,EAAE;AACN,IAAI;AACJ,GAAG;AACH,EAAE,sBAAsB,CAAC,aAAa,CAAC;AACvC,EAAE,wBAAwB,CAAC,aAAa,EAAE,oBAAoB,CAAC;AAC/D,EAAE,0BAA0B,CAAC,mBAAmB,CAAC;AACjD,EAAE,OAAO;AACT,IAAI;AACJ,GAAG;AACH;AACA,IAAI,QAAQ,GAAG,MAAM;AACrB,SAAS,SAAS,GAAG;AACrB,EAAE,OAAO,QAAQ,KAAK,YAAY,CAAC,MAAM,EAAE;AAC3C,IAAI,aAAa,EAAE,MAAM;AACzB,IAAI,WAAW;AACf,IAAI,IAAI,EAAE;AACV,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,KAAK;AAC9C,MAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;AACnC,QAAQ,MAAM,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG;AAC1E,QAAQ,QAAQ,CAAC,GAAG,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AAC/E,MAAM;AACN,IAAI;AACJ,GAAG,CAAC;AACJ;;;;"}