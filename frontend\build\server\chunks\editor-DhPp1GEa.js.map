{"version": 3, "file": "editor-DhPp1GEa.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/editor.js"], "sourcesContent": ["import { W as current_component, u as push, G as attr_class, T as clsx, y as attr, V as bind_props, w as pop, O as copy_payload, P as assign_payload } from \"./index.js\";\nimport { j as fallback } from \"./utils.js\";\nfunction onDestroy(fn) {\n  var context = (\n    /** @type {Component} */\n    current_component\n  );\n  (context.d ??= []).push(fn);\n}\nconst uuid = (prefix) => {\n  return prefix + \"_\" + Math.floor(Math.random() * 1e9) + String(Date.now());\n};\nfunction Editor$1($$payload, $$props) {\n  push();\n  let id = fallback($$props[\"id\"], () => uuid(\"tinymce-svelte\"), true);\n  let inline = fallback($$props[\"inline\"], void 0);\n  let disabled = fallback($$props[\"disabled\"], false);\n  let readonly = fallback($$props[\"readonly\"], false);\n  let apiKey = fallback($$props[\"apiKey\"], \"no-api-key\");\n  let licenseKey = fallback($$props[\"licenseKey\"], void 0);\n  let channel = fallback($$props[\"channel\"], \"7\");\n  let scriptSrc = fallback($$props[\"scriptSrc\"], void 0);\n  let conf = fallback($$props[\"conf\"], () => ({}), true);\n  let modelEvents = fallback($$props[\"modelEvents\"], \"change input undo redo\");\n  let value = fallback($$props[\"value\"], \"\");\n  let text = fallback($$props[\"text\"], \"\");\n  let cssClass = fallback($$props[\"cssClass\"], \"tinymce-wrapper\");\n  onDestroy(() => {\n  });\n  $$payload.out.push(`<div${attr_class(clsx(\n    //\n    // bind model events\n    cssClass\n  ))}>`);\n  if (inline) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div${attr(\"id\", id)}></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    $$payload.out.push(`<textarea${attr(\"id\", id)} style=\"visibility:hidden\"></textarea>`);\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  bind_props($$props, {\n    id,\n    inline,\n    disabled,\n    readonly,\n    apiKey,\n    licenseKey,\n    channel,\n    scriptSrc,\n    conf,\n    modelEvents,\n    value,\n    text,\n    cssClass\n  });\n  pop();\n}\nfunction Editor($$payload, $$props) {\n  push();\n  let { content = void 0, onEditorInit } = $$props;\n  const editorConfig = {\n    // plugins: [\"lists\", \"link\", \"image\", \"code\", \"table\"],\n    // toolbar:\n    //   \"undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist | link image | code\",\n    // menubar: false,\n    // height: 300,\n    toolbar: \"undo redo | bold italic strikethrough underline | alignleft aligncenter alignright\",\n    menubar: false,\n    height: 300,\n    promotion: false,\n    // branding: false,\n    // Preserve absolute URLs and prevent TinyMCE from modifying them\n    relative_urls: false,\n    init_instance_callback: (editor) => {\n      onEditorInit?.(editor);\n    }\n  };\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    Editor$1($$payload2, {\n      licenseKey: \"gpl\",\n      scriptSrc: \"/tinymce/tinymce.min.js\",\n      conf: editorConfig,\n      get value() {\n        return content;\n      },\n      set value($$value) {\n        content = $$value;\n        $$settled = false;\n      }\n    });\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { content });\n  pop();\n}\nexport {\n  Editor as E\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,SAAS,CAAC,EAAE,EAAE;AACvB,EAAE,IAAI,OAAO;AACb;AACA,IAAI;AACJ,GAAG;AACH,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC;AAC7B;AACA,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK;AACzB,EAAE,OAAO,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AAC5E,CAAC;AACD,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,IAAI,CAAC;AACtE,EAAE,IAAI,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;AAClD,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC;AACrD,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC;AACrD,EAAE,IAAI,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,YAAY,CAAC;AACxD,EAAE,IAAI,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC;AAC1D,EAAE,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;AACjD,EAAE,IAAI,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC;AACxD,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC;AACxD,EAAE,IAAI,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,wBAAwB,CAAC;AAC9E,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;AAC5C,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;AAC1C,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,iBAAiB,CAAC;AACjE,EAAE,SAAS,CAAC,MAAM;AAClB,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI;AAC3C;AACA;AACA,IAAI;AACJ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;AACtD,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,sCAAsC,CAAC,CAAC;AAC1F,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,UAAU,CAAC,OAAO,EAAE;AACtB,IAAI,EAAE;AACN,IAAI,MAAM;AACV,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,IAAI,UAAU;AACd,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,IAAI;AACR,IAAI,WAAW;AACf,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,GAAG,EAAE;AACP;AACA,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,GAAG,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO;AAClD,EAAE,MAAM,YAAY,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,EAAE,oFAAoF;AACjG,IAAI,OAAO,EAAE,KAAK;AAClB,IAAI,MAAM,EAAE,GAAG;AACf,IAAI,SAAS,EAAE,KAAK;AACpB;AACA;AACA,IAAI,aAAa,EAAE,KAAK;AACxB,IAAI,sBAAsB,EAAE,CAAC,MAAM,KAAK;AACxC,MAAM,YAAY,GAAG,MAAM,CAAC;AAC5B,IAAI;AACJ,GAAG;AACH,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,QAAQ,CAAC,UAAU,EAAE;AACzB,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,SAAS,EAAE,yBAAyB;AAC1C,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,IAAI,KAAK,GAAG;AAClB,QAAQ,OAAO,OAAO;AACtB,MAAM,CAAC;AACP,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,OAAO,GAAG,OAAO;AACzB,QAAQ,SAAS,GAAG,KAAK;AACzB,MAAM;AACN,KAAK,CAAC;AACN,EAAE;AACF,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,EAAE,CAAC,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC;AAClC,EAAE,GAAG,EAAE;AACP;;;;"}