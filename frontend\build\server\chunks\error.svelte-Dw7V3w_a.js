import { u as push, z as escape_html, w as pop } from './index-0Ke2LYl0.js';
import { p as page } from './index3-DDIiUUg1.js';
import './client2-BuT6F0Df.js';
import './state.svelte-BMxoNtw-.js';
import './exports-DxMY0jlE.js';
import './client-BUddp2Wf.js';
import './index2-DkUtb91y.js';

function Error($$payload, $$props) {
  push();
  $$payload.out.push(`<h1>${escape_html(page.status)}</h1> <p>${escape_html(page.error?.message)}</p>`);
  pop();
}

export { Error as default };
//# sourceMappingURL=error.svelte-Dw7V3w_a.js.map
