import { g as goto } from './client-BUddp2Wf.js';
import './current-user-BM0W6LNm.js';

async function fetchWithAuth(input, init) {
  const requestInit = {
    ...init,
    credentials: "include"
  };
  const response = await fetch(input, requestInit);
  if (response.status === 401) {
    return redirectToLoginPage();
  }
  return response;
}
function redirectToLoginPage() {
  goto();
  return new Promise(() => {
  });
}

export { fetchWithAuth as f };
//# sourceMappingURL=fetch-with-auth-DyBoKb7G.js.map
