{"version": 3, "file": "fetch-with-auth-DyBoKb7G.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/fetch-with-auth.js"], "sourcesContent": ["import { g as goto } from \"./client.js\";\nimport \"./current-user.js\";\nasync function fetchWithAuth(input, init) {\n  const requestInit = {\n    ...init,\n    credentials: \"include\"\n  };\n  const response = await fetch(input, requestInit);\n  if (response.status === 401) {\n    return redirectToLoginPage();\n  }\n  return response;\n}\nfunction redirectToLoginPage() {\n  goto();\n  return new Promise(() => {\n  });\n}\nexport {\n  fetchWithAuth as f\n};\n"], "names": [], "mappings": ";;;AAEA,eAAe,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE;AAC1C,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,GAAG,IAAI;AACX,IAAI,WAAW,EAAE;AACjB,GAAG;AACH,EAAE,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,KAAK,EAAE,WAAW,CAAC;AAClD,EAAE,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;AAC/B,IAAI,OAAO,mBAAmB,EAAE;AAChC,EAAE;AACF,EAAE,OAAO,QAAQ;AACjB;AACA,SAAS,mBAAmB,GAAG;AAC/B,EAAE,IAAI,EAAE;AACR,EAAE,OAAO,IAAI,OAAO,CAAC,MAAM;AAC3B,EAAE,CAAC,CAAC;AACJ;;;;"}