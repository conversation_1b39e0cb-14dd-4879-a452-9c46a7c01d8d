{"version": 3, "file": "format-date-DgRnEWcB.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/format-date.js"], "sourcesContent": ["function formatDatetime(date, locale = \"en-US\") {\n  return new Intl.DateTimeFormat(locale, {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\"\n  }).format(date);\n}\nfunction formatDate(date, locale = \"en-US\") {\n  return new Intl.DateTimeFormat(locale, {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\"\n  }).format(date);\n}\nexport {\n  formatDatetime as a,\n  formatDate as f\n};\n"], "names": [], "mappings": "AAAA,SAAS,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,EAAE;AAChD,EAAE,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;AACzC,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,KAAK,EAAE,MAAM;AACjB,IAAI,GAAG,EAAE,SAAS;AAClB,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,MAAM,EAAE;AACZ,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;AACjB;AACA,SAAS,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,EAAE;AAC5C,EAAE,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;AACzC,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,KAAK,EAAE,MAAM;AACjB,IAAI,GAAG,EAAE;AACT,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;AACjB;;;;"}