function getUserRateColor(rate) {
  let red, green, blue = 0;
  if (rate <= 5) {
    red = 255;
    green = Math.round(rate / 5 * 165);
  } else {
    const progress = (rate - 5) / 5;
    red = Math.round(255 * (1 - progress));
    green = Math.round(165 + 90 * progress);
  }
  return `rgb(${red}, ${green}, ${blue})`;
}

export { getUserRateColor as g };
//# sourceMappingURL=get-user-rate-color-CzjBgne7.js.map
