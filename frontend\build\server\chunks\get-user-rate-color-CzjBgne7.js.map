{"version": 3, "file": "get-user-rate-color-CzjBgne7.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/get-user-rate-color.js"], "sourcesContent": ["function getUserRateColor(rate) {\n  let red, green, blue = 0;\n  if (rate <= 5) {\n    red = 255;\n    green = Math.round(rate / 5 * 165);\n  } else {\n    const progress = (rate - 5) / 5;\n    red = Math.round(255 * (1 - progress));\n    green = Math.round(165 + 90 * progress);\n  }\n  return `rgb(${red}, ${green}, ${blue})`;\n}\nexport {\n  getUserRateColor as g\n};\n"], "names": [], "mappings": "AAAA,SAAS,gBAAgB,CAAC,IAAI,EAAE;AAChC,EAAE,IAAI,GAAG,EAAE,KAAK,EAAE,IAAI,GAAG,CAAC;AAC1B,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE;AACjB,IAAI,GAAG,GAAG,GAAG;AACb,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AACtC,EAAE,CAAC,MAAM;AACT,IAAI,MAAM,QAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC;AACnC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;AAC1C,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAG,QAAQ,CAAC;AAC3C,EAAE;AACF,EAAE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;AACzC;;;;"}