{"version": 3, "file": "index-CT944rr3.js", "sources": ["../../../../node_modules/@sveltejs/kit/src/exports/internal/index.js", "../../../../node_modules/@sveltejs/kit/src/exports/index.js"], "sourcesContent": ["export class HttpError {\n\t/**\n\t * @param {number} status\n\t * @param {{message: string} extends App.Error ? (App.Error | string | undefined) : App.Error} body\n\t */\n\tconstructor(status, body) {\n\t\tthis.status = status;\n\t\tif (typeof body === 'string') {\n\t\t\tthis.body = { message: body };\n\t\t} else if (body) {\n\t\t\tthis.body = body;\n\t\t} else {\n\t\t\tthis.body = { message: `Error: ${status}` };\n\t\t}\n\t}\n\n\ttoString() {\n\t\treturn JSON.stringify(this.body);\n\t}\n}\n\nexport class Redirect {\n\t/**\n\t * @param {300 | 301 | 302 | 303 | 304 | 305 | 306 | 307 | 308} status\n\t * @param {string} location\n\t */\n\tconstructor(status, location) {\n\t\tthis.status = status;\n\t\tthis.location = location;\n\t}\n}\n\n/**\n * An error that was thrown from within the SvelteKit runtime that is not fatal and doesn't result in a 500, such as a 404.\n * `SvelteKitError` goes through `handleError`.\n * @extends Error\n */\nexport class SvelteKitError extends Error {\n\t/**\n\t * @param {number} status\n\t * @param {string} text\n\t * @param {string} message\n\t */\n\tconstructor(status, text, message) {\n\t\tsuper(message);\n\t\tthis.status = status;\n\t\tthis.text = text;\n\t}\n}\n\n/**\n * @template {Record<string, unknown> | undefined} [T=undefined]\n */\nexport class ActionFailure {\n\t/**\n\t * @param {number} status\n\t * @param {T} data\n\t */\n\tconstructor(status, data) {\n\t\tthis.status = status;\n\t\tthis.data = data;\n\t}\n}\n", "import { HttpError, Redirect, ActionFailure } from './internal/index.js';\nimport { BROWSER, DEV } from 'esm-env';\nimport {\n\tadd_data_suffix,\n\tadd_resolution_suffix,\n\thas_data_suffix,\n\thas_resolution_suffix,\n\tstrip_data_suffix,\n\tstrip_resolution_suffix\n} from '../runtime/pathname.js';\n\nexport { VERSION } from '../version.js';\n\n// TODO 3.0: remove these types as they are not used anymore (we can't remove them yet because that would be a breaking change)\n/**\n * @template {number} TNumber\n * @template {any[]} [TArray=[]]\n * @typedef {TNumber extends TArray['length'] ? TArray[number] : LessThan<TNumber, [...TArray, TArray['length']]>} LessThan\n */\n\n/**\n * @template {number} TStart\n * @template {number} TEnd\n * @typedef {Exclude<TEnd | LessThan<TEnd>, LessThan<TStart>>} NumericRange\n */\n\n// Keep the status codes as `number` because restricting to certain numbers makes it unnecessarily hard to use compared to the benefits\n// (we have runtime errors already to check for invalid codes). Also see https://github.com/sveltejs/kit/issues/11780\n\n// we have to repeat the JSDoc because the display for function overloads is broken\n// see https://github.com/microsoft/TypeScript/issues/55056\n\n/**\n * Throws an error with a HTTP status code and an optional message.\n * When called during request handling, this will cause SvelteKit to\n * return an error response without invoking `handleError`.\n * Make sure you're not catching the thrown error, which would prevent SvelteKit from handling it.\n * @param {number} status The [HTTP status code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status#client_error_responses). Must be in the range 400-599.\n * @param {App.Error} body An object that conforms to the App.Error type. If a string is passed, it will be used as the message property.\n * @overload\n * @param {number} status\n * @param {App.Error} body\n * @return {never}\n * @throws {HttpError} This error instructs SvelteKit to initiate HTTP error handling.\n * @throws {Error} If the provided status is invalid (not between 400 and 599).\n */\n/**\n * Throws an error with a HTTP status code and an optional message.\n * When called during request handling, this will cause SvelteKit to\n * return an error response without invoking `handleError`.\n * Make sure you're not catching the thrown error, which would prevent SvelteKit from handling it.\n * @param {number} status The [HTTP status code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status#client_error_responses). Must be in the range 400-599.\n * @param {{ message: string } extends App.Error ? App.Error | string | undefined : never} [body] An object that conforms to the App.Error type. If a string is passed, it will be used as the message property.\n * @overload\n * @param {number} status\n * @param {{ message: string } extends App.Error ? App.Error | string | undefined : never} [body]\n * @return {never}\n * @throws {HttpError} This error instructs SvelteKit to initiate HTTP error handling.\n * @throws {Error} If the provided status is invalid (not between 400 and 599).\n */\n/**\n * Throws an error with a HTTP status code and an optional message.\n * When called during request handling, this will cause SvelteKit to\n * return an error response without invoking `handleError`.\n * Make sure you're not catching the thrown error, which would prevent SvelteKit from handling it.\n * @param {number} status The [HTTP status code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status#client_error_responses). Must be in the range 400-599.\n * @param {{ message: string } extends App.Error ? App.Error | string | undefined : never} body An object that conforms to the App.Error type. If a string is passed, it will be used as the message property.\n * @return {never}\n * @throws {HttpError} This error instructs SvelteKit to initiate HTTP error handling.\n * @throws {Error} If the provided status is invalid (not between 400 and 599).\n */\nexport function error(status, body) {\n\tif ((!BROWSER || DEV) && (isNaN(status) || status < 400 || status > 599)) {\n\t\tthrow new Error(`HTTP error status codes must be between 400 and 599 — ${status} is invalid`);\n\t}\n\n\tthrow new HttpError(status, body);\n}\n\n/**\n * Checks whether this is an error thrown by {@link error}.\n * @template {number} T\n * @param {unknown} e\n * @param {T} [status] The status to filter for.\n * @return {e is (HttpError & { status: T extends undefined ? never : T })}\n */\nexport function isHttpError(e, status) {\n\tif (!(e instanceof HttpError)) return false;\n\treturn !status || e.status === status;\n}\n\n/**\n * Redirect a request. When called during request handling, SvelteKit will return a redirect response.\n * Make sure you're not catching the thrown redirect, which would prevent SvelteKit from handling it.\n *\n * Most common status codes:\n *  * `303 See Other`: redirect as a GET request (often used after a form POST request)\n *  * `307 Temporary Redirect`: redirect will keep the request method\n *  * `308 Permanent Redirect`: redirect will keep the request method, SEO will be transferred to the new page\n *\n * [See all redirect status codes](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status#redirection_messages)\n *\n * @param {300 | 301 | 302 | 303 | 304 | 305 | 306 | 307 | 308 | ({} & number)} status The [HTTP status code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status#redirection_messages). Must be in the range 300-308.\n * @param {string | URL} location The location to redirect to.\n * @throws {Redirect} This error instructs SvelteKit to redirect to the specified location.\n * @throws {Error} If the provided status is invalid.\n * @return {never}\n */\nexport function redirect(status, location) {\n\tif ((!BROWSER || DEV) && (isNaN(status) || status < 300 || status > 308)) {\n\t\tthrow new Error('Invalid status code');\n\t}\n\n\tthrow new Redirect(\n\t\t// @ts-ignore\n\t\tstatus,\n\t\tlocation.toString()\n\t);\n}\n\n/**\n * Checks whether this is a redirect thrown by {@link redirect}.\n * @param {unknown} e The object to check.\n * @return {e is Redirect}\n */\nexport function isRedirect(e) {\n\treturn e instanceof Redirect;\n}\n\n/**\n * Create a JSON `Response` object from the supplied data.\n * @param {any} data The value that will be serialized as JSON.\n * @param {ResponseInit} [init] Options such as `status` and `headers` that will be added to the response. `Content-Type: application/json` and `Content-Length` headers will be added automatically.\n */\nexport function json(data, init) {\n\t// TODO deprecate this in favour of `Response.json` when it's\n\t// more widely supported\n\tconst body = JSON.stringify(data);\n\n\t// we can't just do `text(JSON.stringify(data), init)` because\n\t// it will set a default `content-type` header. duplicated code\n\t// means less duplicated work\n\tconst headers = new Headers(init?.headers);\n\tif (!headers.has('content-length')) {\n\t\theaders.set('content-length', encoder.encode(body).byteLength.toString());\n\t}\n\n\tif (!headers.has('content-type')) {\n\t\theaders.set('content-type', 'application/json');\n\t}\n\n\treturn new Response(body, {\n\t\t...init,\n\t\theaders\n\t});\n}\n\nconst encoder = new TextEncoder();\n\n/**\n * Create a `Response` object from the supplied body.\n * @param {string} body The value that will be used as-is.\n * @param {ResponseInit} [init] Options such as `status` and `headers` that will be added to the response. A `Content-Length` header will be added automatically.\n */\nexport function text(body, init) {\n\tconst headers = new Headers(init?.headers);\n\tif (!headers.has('content-length')) {\n\t\tconst encoded = encoder.encode(body);\n\t\theaders.set('content-length', encoded.byteLength.toString());\n\t\treturn new Response(encoded, {\n\t\t\t...init,\n\t\t\theaders\n\t\t});\n\t}\n\n\treturn new Response(body, {\n\t\t...init,\n\t\theaders\n\t});\n}\n\n/**\n * Create an `ActionFailure` object. Call when form submission fails.\n * @param {number} status The [HTTP status code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status#client_error_responses). Must be in the range 400-599.\n * @overload\n * @param {number} status\n * @returns {import('./public.js').ActionFailure<undefined>}\n */\n/**\n * Create an `ActionFailure` object. Call when form submission fails.\n * @template {Record<string, unknown> | undefined} [T=undefined]\n * @param {number} status The [HTTP status code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status#client_error_responses). Must be in the range 400-599.\n * @param {T} data Data associated with the failure (e.g. validation errors)\n * @overload\n * @param {number} status\n * @param {T} data\n * @returns {import('./public.js').ActionFailure<T>}\n */\n/**\n * Create an `ActionFailure` object. Call when form submission fails.\n * @param {number} status\n * @param {any} [data]\n * @returns {import('./public.js').ActionFailure<any>}\n */\nexport function fail(status, data) {\n\t// @ts-expect-error unique symbol missing\n\treturn new ActionFailure(status, data);\n}\n\n/**\n * Checks whether this is an action failure thrown by {@link fail}.\n * @param {unknown} e The object to check.\n * @return {e is import('./public.js').ActionFailure}\n */\nexport function isActionFailure(e) {\n\treturn e instanceof ActionFailure;\n}\n\n/**\n * Strips possible SvelteKit-internal suffixes and trailing slashes from the URL pathname.\n * Returns the normalized URL as well as a method for adding the potential suffix back\n * based on a new pathname (possibly including search) or URL.\n * ```js\n * import { normalizeUrl } from '@sveltejs/kit';\n *\n * const { url, denormalize } = normalizeUrl('/blog/post/__data.json');\n * console.log(url.pathname); // /blog/post\n * console.log(denormalize('/blog/post/a')); // /blog/post/a/__data.json\n * ```\n * @param {URL | string} url\n * @returns {{ url: URL, wasNormalized: boolean, denormalize: (url?: string | URL) => URL }}\n * @since 2.18.0\n */\nexport function normalizeUrl(url) {\n\turl = new URL(url, 'http://internal');\n\n\tconst is_route_resolution = has_resolution_suffix(url.pathname);\n\tconst is_data_request = has_data_suffix(url.pathname);\n\tconst has_trailing_slash = url.pathname !== '/' && url.pathname.endsWith('/');\n\n\tif (is_route_resolution) {\n\t\turl.pathname = strip_resolution_suffix(url.pathname);\n\t} else if (is_data_request) {\n\t\turl.pathname = strip_data_suffix(url.pathname);\n\t} else if (has_trailing_slash) {\n\t\turl.pathname = url.pathname.slice(0, -1);\n\t}\n\n\treturn {\n\t\turl,\n\t\twasNormalized: is_data_request || is_route_resolution || has_trailing_slash,\n\t\tdenormalize: (new_url = url) => {\n\t\t\tnew_url = new URL(new_url, url);\n\t\t\tif (is_route_resolution) {\n\t\t\t\tnew_url.pathname = add_resolution_suffix(new_url.pathname);\n\t\t\t} else if (is_data_request) {\n\t\t\t\tnew_url.pathname = add_data_suffix(new_url.pathname);\n\t\t\t} else if (has_trailing_slash && !new_url.pathname.endsWith('/')) {\n\t\t\t\tnew_url.pathname += '/';\n\t\t\t}\n\t\t\treturn new_url;\n\t\t}\n\t};\n}\n"], "names": [], "mappings": "AAAO,MAAM,SAAS,CAAC;AACvB;AACA;AACA;AACA;AACA,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE;AAC3B,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM;AACtB,EAAE,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAChC,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE;AAChC,EAAE,CAAC,MAAM,IAAI,IAAI,EAAE;AACnB,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI;AACnB,EAAE,CAAC,MAAM;AACT,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,EAAE;AAC9C,EAAE;AACF,CAAC;;AAED,CAAC,QAAQ,GAAG;AACZ,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAClC,CAAC;AACD;;AAEO,MAAM,QAAQ,CAAC;AACtB;AACA;AACA;AACA;AACA,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE;AAC/B,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM;AACtB,EAAE,IAAI,CAAC,QAAQ,GAAG,QAAQ;AAC1B,CAAC;AACD;;AAEA;AACA;AACA;AACA;AACA;AACO,MAAM,cAAc,SAAS,KAAK,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE;AACpC,EAAE,KAAK,CAAC,OAAO,CAAC;AAChB,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM;AACtB,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI;AAClB,CAAC;AACD;;AAEA;AACA;AACA;AACO,MAAM,aAAa,CAAC;AAC3B;AACA;AACA;AACA;AACA,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE;AAC3B,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM;AACtB,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI;AAClB,CAAC;AACD;;ACjDA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE;AACpC,CAAC,IAAyB,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,CAAC,EAAE;AAC3E,EAAE,MAAM,IAAI,KAAK,CAAC,CAAC,sDAAsD,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;AAC/F,CAAC;;AAED,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC;AAClC;;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE;AAC3C,CAAC,IAAyB,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,CAAC,EAAE;AAC3E,EAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC;AACxC,CAAC;;AAED,CAAC,MAAM,IAAI,QAAQ;AACnB;AACA,EAAE,MAAM;AACR,EAAE,QAAQ,CAAC,QAAQ;AACnB,EAAE;AACF;;AAWA;AACA;AACA;AACA;AACA;AACO,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AACjC;AACA;AACA,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;AAElC;AACA;AACA;AACA,CAAC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;AAC3C,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;AACrC,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC3E,CAAC;;AAED,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;AACnC,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;AACjD,CAAC;;AAED,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC3B,EAAE,GAAG,IAAI;AACT,EAAE;AACF,EAAE,CAAC;AACH;;AAEA,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE;;AAEjC;AACA;AACA;AACA;AACA;AACO,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AACjC,CAAC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;AAC3C,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;AACrC,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;AACtC,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC9D,EAAE,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC/B,GAAG,GAAG,IAAI;AACV,GAAG;AACH,GAAG,CAAC;AACJ,CAAC;;AAED,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC3B,EAAE,GAAG,IAAI;AACT,EAAE;AACF,EAAE,CAAC;AACH;;;;", "x_google_ignoreList": [0, 1]}