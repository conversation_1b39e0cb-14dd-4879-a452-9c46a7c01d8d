import './client2-BuT6F0Df.js';
import { F as getContext } from './index-0Ke2LYl0.js';

function context() {
  return getContext("__request__");
}
const page$1 = {
  get error() {
    return context().page.error;
  },
  get params() {
    return context().page.params;
  },
  get status() {
    return context().page.status;
  }
};
const page = page$1;

export { page as p };
//# sourceMappingURL=index3-DDIiUUg1.js.map
