{"version": 3, "file": "index3-DDIiUUg1.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/index3.js"], "sourcesContent": ["import \"./client2.js\";\nimport { Q as getContext } from \"./index.js\";\nfunction context() {\n  return getContext(\"__request__\");\n}\nconst page$1 = {\n  get error() {\n    return context().page.error;\n  },\n  get params() {\n    return context().page.params;\n  },\n  get status() {\n    return context().page.status;\n  }\n};\nconst page = page$1;\nexport {\n  page as p\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,OAAO,GAAG;AACnB,EAAE,OAAO,UAAU,CAAC,aAAa,CAAC;AAClC;AACA,MAAM,MAAM,GAAG;AACf,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK;AAC/B,EAAE,CAAC;AACH,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM;AAChC,EAAE,CAAC;AACH,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM;AAChC,EAAE;AACF,CAAC;AACI,MAAC,IAAI,GAAG;;;;"}