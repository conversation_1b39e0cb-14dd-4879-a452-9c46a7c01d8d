{"version": 3, "file": "layout.svelte-BSvrn-4a.js", "sources": ["../../../.svelte-kit/adapter-node/entries/fallbacks/layout.svelte.js"], "sourcesContent": ["import \"clsx\";\nfunction Layout($$payload, $$props) {\n  let { children } = $$props;\n  children($$payload);\n  $$payload.out.push(`<!---->`);\n}\nexport {\n  Layout as default\n};\n"], "names": [], "mappings": "AACA,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;AAC5B,EAAE,QAAQ,CAAC,SAAS,CAAC;AACrB,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;AAC/B;;;;"}