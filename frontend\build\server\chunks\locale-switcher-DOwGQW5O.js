import { u as push, y as attr, z as escape_html, J as attr_class, w as pop } from './index-0Ke2LYl0.js';
import './current-user-BM0W6LNm.js';
import './exports-DxMY0jlE.js';
import './state.svelte-BMxoNtw-.js';
import '@formatjs/intl-localematcher';
import './index-CT944rr3.js';

function Locale_switcher($$payload, $$props) {
  push();
  const { currentLocale } = $$props;
  let isOpen = false;
  function getLocaleDisplayText(locale) {
    switch (locale) {
      case "en":
        return "English";
      case "ru":
        return "Русский";
      case null:
        return "Auto";
      default:
        return "Auto";
    }
  }
  $$payload.out.push(`<div class="dropdown mx-2"><button class="btn btn-outline-secondary btn-sm dropdown-toggle d-flex align-items-center justify-content-center" style="width: 110px; min-width: 110px;" type="button" id="locale-dropdown"${attr("aria-expanded", isOpen)}><i class="bi bi-globe me-1"></i> ${escape_html(getLocaleDisplayText(currentLocale))}</button> <ul${attr_class(`dropdown-menu ${""}`)} aria-labelledby="locale-dropdown"><li><button${attr_class(`dropdown-item ${currentLocale === "en" ? "active" : ""}`)}><i class="bi bi-translate me-2"></i> English</button></li> <li><button${attr_class(`dropdown-item ${currentLocale === "ru" ? "active" : ""}`)}><i class="bi bi-translate me-2"></i> Русский</button></li> <li><button${attr_class(`dropdown-item ${currentLocale === null ? "active" : ""}`)}><i class="bi bi-globe me-2"></i> Auto</button></li></ul></div>`);
  pop();
}

export { Locale_switcher as L };
//# sourceMappingURL=locale-switcher-DOwGQW5O.js.map
