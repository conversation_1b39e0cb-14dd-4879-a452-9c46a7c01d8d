{"version": 3, "file": "locale-switcher-DOwGQW5O.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/locale-switcher.js"], "sourcesContent": ["import { y as attr, z as escape_html, G as attr_class, w as pop, u as push } from \"./index.js\";\nimport \"./current-user.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"./exports.js\";\nimport \"./state.svelte.js\";\nimport \"@formatjs/intl-localematcher\";\nimport \"@sveltejs/kit\";\nfunction Locale_switcher($$payload, $$props) {\n  push();\n  const { currentLocale } = $$props;\n  let isOpen = false;\n  function getLocaleDisplayText(locale) {\n    switch (locale) {\n      case \"en\":\n        return \"English\";\n      case \"ru\":\n        return \"Русский\";\n      case null:\n        return \"Auto\";\n      default:\n        return \"Auto\";\n    }\n  }\n  $$payload.out.push(`<div class=\"dropdown mx-2\"><button class=\"btn btn-outline-secondary btn-sm dropdown-toggle d-flex align-items-center justify-content-center\" style=\"width: 110px; min-width: 110px;\" type=\"button\" id=\"locale-dropdown\"${attr(\"aria-expanded\", isOpen)}><i class=\"bi bi-globe me-1\"></i> ${escape_html(getLocaleDisplayText(currentLocale))}</button> <ul${attr_class(`dropdown-menu ${\"\"}`)} aria-labelledby=\"locale-dropdown\"><li><button${attr_class(`dropdown-item ${currentLocale === \"en\" ? \"active\" : \"\"}`)}><i class=\"bi bi-translate me-2\"></i> English</button></li> <li><button${attr_class(`dropdown-item ${currentLocale === \"ru\" ? \"active\" : \"\"}`)}><i class=\"bi bi-translate me-2\"></i> Русский</button></li> <li><button${attr_class(`dropdown-item ${currentLocale === null ? \"active\" : \"\"}`)}><i class=\"bi bi-globe me-2\"></i> Auto</button></li></ul></div>`);\n  pop();\n}\nexport {\n  Locale_switcher as L\n};\n"], "names": [], "mappings": ";;;;;;;AAOA,SAAS,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE;AAC7C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,OAAO;AACnC,EAAE,IAAI,MAAM,GAAG,KAAK;AACpB,EAAE,SAAS,oBAAoB,CAAC,MAAM,EAAE;AACxC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,IAAI;AACf,QAAQ,OAAO,SAAS;AACxB,MAAM,KAAK,IAAI;AACf,QAAQ,OAAO,SAAS;AACxB,MAAM,KAAK,IAAI;AACf,QAAQ,OAAO,MAAM;AACrB,MAAM;AACN,QAAQ,OAAO,MAAM;AACrB;AACA,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uNAAuN,EAAE,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,kCAAkC,EAAE,WAAW,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,8CAA8C,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,aAAa,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,uEAAuE,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,aAAa,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,uEAAuE,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,aAAa,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,+DAA+D,CAAC,CAAC;AACx2B,EAAE,GAAG,EAAE;AACP;;;;"}