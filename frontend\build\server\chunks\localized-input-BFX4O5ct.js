import { u as push, y as attr, z as escape_html, J as attr_class, K as stringify, N as ensure_array_like, V as bind_props, w as pop } from './index-0Ke2LYl0.js';

function Localized_input($$payload, $$props) {
  push();
  const i18n = {
    en: {
      languages: { en: "English", ru: "Russian" },
      providedTranslations: "Provided translations:"
    },
    ru: {
      languages: {
        en: "Английский",
        ru: "Русский"
      },
      providedTranslations: "Указанные переводы:"
    }
  };
  let { value = void 0, $$slots, $$events, ...props } = $$props;
  const { id, label, placeholder, required = false, locale } = props;
  const t = i18n[locale];
  let selectedLanguage = locale;
  function getCurrentValue() {
    const localization = value.find((val) => val.locale === selectedLanguage);
    return localization?.value || "";
  }
  function getLanguageDisplay() {
    return selectedLanguage.toUpperCase();
  }
  $$payload.out.push(`<div class="mb-3"><label${attr("for", id)} class="form-label">${escape_html(label)} `);
  if (required) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<span class="text-danger">*</span>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></label> <div class="input-group"><input type="text" class="form-control"${attr("id", id)}${attr("placeholder", placeholder)}${attr("value", getCurrentValue())}${attr("required", required, true)}/> <div class="dropdown"><button class="btn btn-outline-secondary dropdown-toggle" type="button"${attr("id", `dropdown-${id}`)} data-bs-toggle="dropdown" aria-expanded="false" style="width: 60px; display: flex; justify-content: space-between; align-items: center;">${escape_html(getLanguageDisplay())}</button> <ul class="dropdown-menu"${attr("aria-labelledby", `dropdown-${id}`)}><li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === "en" ? "active" : "")}`)} type="button">${escape_html(t.languages.en)}</button></li> <li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === "ru" ? "active" : "")}`)} type="button">${escape_html(t.languages.ru)}</button></li></ul></div></div> `);
  if (value.length > 0) {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(value.filter(Boolean));
    $$payload.out.push(`<div class="mt-2 small text-muted"><div>${escape_html(t.providedTranslations)}</div> <ul class="list-unstyled mb-0 mt-1"><!--[-->`);
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let val = each_array[$$index];
      $$payload.out.push(`<li class="badge bg-light text-dark me-1">${escape_html(t.languages[val.locale])}: ${escape_html(val.value)}</li>`);
    }
    $$payload.out.push(`<!--]--></ul></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  bind_props($$props, { value });
  pop();
}

export { Localized_input as L };
//# sourceMappingURL=localized-input-BFX4O5ct.js.map
