{"version": 3, "file": "localized-input-BFX4O5ct.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/localized-input.js"], "sourcesContent": ["import { u as push, y as attr, z as escape_html, G as attr_class, K as ensure_array_like, V as bind_props, w as pop, J as stringify } from \"./index.js\";\nfunction Localized_input($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      languages: { en: \"English\", ru: \"Russian\" },\n      providedTranslations: \"Provided translations:\"\n    },\n    ru: {\n      languages: {\n        en: \"Английский\",\n        ru: \"Русский\"\n      },\n      providedTranslations: \"Указанные переводы:\"\n    }\n  };\n  let { value = void 0, $$slots, $$events, ...props } = $$props;\n  const { id, label, placeholder, required = false, locale } = props;\n  const t = i18n[locale];\n  let selectedLanguage = locale;\n  function getCurrentValue() {\n    const localization = value.find((val) => val.locale === selectedLanguage);\n    return localization?.value || \"\";\n  }\n  function getLanguageDisplay() {\n    return selectedLanguage.toUpperCase();\n  }\n  $$payload.out.push(`<div class=\"mb-3\"><label${attr(\"for\", id)} class=\"form-label\">${escape_html(label)} `);\n  if (required) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<span class=\"text-danger\">*</span>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></label> <div class=\"input-group\"><input type=\"text\" class=\"form-control\"${attr(\"id\", id)}${attr(\"placeholder\", placeholder)}${attr(\"value\", getCurrentValue())}${attr(\"required\", required, true)}/> <div class=\"dropdown\"><button class=\"btn btn-outline-secondary dropdown-toggle\" type=\"button\"${attr(\"id\", `dropdown-${id}`)} data-bs-toggle=\"dropdown\" aria-expanded=\"false\" style=\"width: 60px; display: flex; justify-content: space-between; align-items: center;\">${escape_html(getLanguageDisplay())}</button> <ul class=\"dropdown-menu\"${attr(\"aria-labelledby\", `dropdown-${id}`)}><li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === \"en\" ? \"active\" : \"\")}`)} type=\"button\">${escape_html(t.languages.en)}</button></li> <li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === \"ru\" ? \"active\" : \"\")}`)} type=\"button\">${escape_html(t.languages.ru)}</button></li></ul></div></div> `);\n  if (value.length > 0) {\n    $$payload.out.push(\"<!--[-->\");\n    const each_array = ensure_array_like(value.filter(Boolean));\n    $$payload.out.push(`<div class=\"mt-2 small text-muted\"><div>${escape_html(t.providedTranslations)}</div> <ul class=\"list-unstyled mb-0 mt-1\"><!--[-->`);\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let val = each_array[$$index];\n      $$payload.out.push(`<li class=\"badge bg-light text-dark me-1\">${escape_html(t.languages[val.locale])}: ${escape_html(val.value)}</li>`);\n    }\n    $$payload.out.push(`<!--]--></ul></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  bind_props($$props, { value });\n  pop();\n}\nexport {\n  Localized_input as L\n};\n"], "names": [], "mappings": ";;AACA,SAAS,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE;AAC7C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,SAAS,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;AACjD,MAAM,oBAAoB,EAAE;AAC5B,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,SAAS,EAAE;AACjB,QAAQ,EAAE,EAAE,YAAY;AACxB,QAAQ,EAAE,EAAE;AACZ,OAAO;AACP,MAAM,oBAAoB,EAAE;AAC5B;AACA,GAAG;AACH,EAAE,IAAI,EAAE,KAAK,GAAG,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO;AAC/D,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,GAAG,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK;AACpE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,gBAAgB,GAAG,MAAM;AAC/B,EAAE,SAAS,eAAe,GAAG;AAC7B,IAAI,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,gBAAgB,CAAC;AAC7E,IAAI,OAAO,YAAY,EAAE,KAAK,IAAI,EAAE;AACpC,EAAE;AACF,EAAE,SAAS,kBAAkB,GAAG;AAChC,IAAI,OAAO,gBAAgB,CAAC,WAAW,EAAE;AACzC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wBAAwB,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,oBAAoB,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5G,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kCAAkC,CAAC,CAAC;AAC5D,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iFAAiF,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,gGAAgG,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,0IAA0I,EAAE,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,mCAAmC,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,gBAAgB,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,0BAA0B,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,gBAAgB,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,gCAAgC,CAAC,CAAC;AAC36B,EAAE,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACxB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/D,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wCAAwC,EAAE,WAAW,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,mDAAmD,CAAC,CAAC;AAC3J,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC;AACnC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0CAA0C,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AAC7I,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mBAAmB,CAAC,CAAC;AAC7C,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC;AAChC,EAAE,GAAG,EAAE;AACP;;;;"}