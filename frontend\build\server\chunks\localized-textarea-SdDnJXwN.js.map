{"version": 3, "file": "localized-textarea-SdDnJXwN.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/localized-textarea.js"], "sourcesContent": ["import { u as push, y as attr, z as escape_html, G as attr_class, K as ensure_array_like, V as bind_props, w as pop, J as stringify } from \"./index.js\";\nfunction Localized_textarea($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      languages: { en: \"English\", ru: \"Russian\" },\n      providedTranslations: \"Provided translations:\"\n    },\n    ru: {\n      languages: {\n        en: \"Английский\",\n        ru: \"Русский\"\n      },\n      providedTranslations: \"Указанные переводы:\"\n    }\n  };\n  let { value = void 0, $$slots, $$events, ...props } = $$props;\n  const {\n    id,\n    label,\n    placeholder,\n    rows = 3,\n    required = false,\n    locale,\n    languageSelectPosition = \"top\",\n    children\n  } = props;\n  const t = i18n[locale];\n  let selectedLanguage = locale;\n  function getCurrentValue() {\n    const localization = value.find((val) => val.locale === selectedLanguage);\n    return localization?.value || \"\";\n  }\n  function getLanguageDisplay() {\n    return selectedLanguage.toUpperCase();\n  }\n  $$payload.out.push(`<div class=\"mb-3\">`);\n  if (languageSelectPosition === \"top\") {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"d-flex justify-content-between align-items-center mb-2\"><label${attr(\"for\", id)} class=\"form-label mb-0\">${escape_html(label)} `);\n    if (required) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<span class=\"text-danger\">*</span>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></label> <div class=\"dropdown\"><button class=\"btn btn-outline-secondary btn-sm dropdown-toggle\" type=\"button\"${attr(\"id\", `dropdown-${id}`)} data-bs-toggle=\"dropdown\" aria-expanded=\"false\" style=\"width: 60px; display: flex; justify-content: space-between; align-items: center;\">${escape_html(getLanguageDisplay())}</button> <ul class=\"dropdown-menu dropdown-menu-end\"${attr(\"aria-labelledby\", `dropdown-${id}`)}><li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === \"en\" ? \"active\" : \"\")}`)} type=\"button\">${escape_html(t.languages.en)}</button></li> <li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === \"ru\" ? \"active\" : \"\")}`)} type=\"button\">${escape_html(t.languages.ru)}</button></li></ul></div></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    $$payload.out.push(`<label${attr(\"for\", id)} class=\"form-label mb-2\">${escape_html(label)} `);\n    if (required) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<span class=\"text-danger\">*</span>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></label>`);\n  }\n  $$payload.out.push(`<!--]--> <textarea class=\"form-control\"${attr(\"id\", id)}${attr(\"rows\", rows)}${attr(\"placeholder\", placeholder)}${attr(\"required\", required, true)}>`);\n  const $$body = escape_html(getCurrentValue());\n  if ($$body) {\n    $$payload.out.push(`${$$body}`);\n  }\n  $$payload.out.push(`</textarea> `);\n  if (languageSelectPosition === \"bottom\") {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"d-flex justify-content-between align-items-center mt-2\"><div class=\"d-flex align-items-center\">`);\n    if (children) {\n      $$payload.out.push(\"<!--[-->\");\n      children($$payload);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div> <div class=\"dropdown\"><button class=\"btn btn-outline-secondary btn-sm dropdown-toggle\" type=\"button\"${attr(\"id\", `dropdown-${id}`)} data-bs-toggle=\"dropdown\" aria-expanded=\"false\" style=\"width: 60px; display: flex; justify-content: space-between; align-items: center;\">${escape_html(getLanguageDisplay())}</button> <ul class=\"dropdown-menu dropdown-menu-end\"${attr(\"aria-labelledby\", `dropdown-${id}`)}><li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === \"en\" ? \"active\" : \"\")}`)} type=\"button\">${escape_html(t.languages.en)}</button></li> <li><button${attr_class(`dropdown-item ${stringify(selectedLanguage === \"ru\" ? \"active\" : \"\")}`)} type=\"button\">${escape_html(t.languages.ru)}</button></li></ul></div></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  if (value.length > 0) {\n    $$payload.out.push(\"<!--[-->\");\n    const each_array = ensure_array_like(value.filter(Boolean));\n    $$payload.out.push(`<div class=\"mt-2 small text-muted\"><div>${escape_html(t.providedTranslations)}</div> <ul class=\"list-unstyled mb-0 mt-1\"><!--[-->`);\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let val = each_array[$$index];\n      $$payload.out.push(`<li class=\"badge bg-light text-dark me-1\">${escape_html(t.languages[val.locale])}: ${escape_html(val.value.length > 50 ? val.value.slice(0, 47) + \"...\" : val.value)}</li>`);\n    }\n    $$payload.out.push(`<!--]--></ul></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  bind_props($$props, { value });\n  pop();\n}\nexport {\n  Localized_textarea as L\n};\n"], "names": [], "mappings": ";;AACA,SAAS,kBAAkB,CAAC,SAAS,EAAE,OAAO,EAAE;AAChD,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,SAAS,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;AACjD,MAAM,oBAAoB,EAAE;AAC5B,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,SAAS,EAAE;AACjB,QAAQ,EAAE,EAAE,YAAY;AACxB,QAAQ,EAAE,EAAE;AACZ,OAAO;AACP,MAAM,oBAAoB,EAAE;AAC5B;AACA,GAAG;AACH,EAAE,IAAI,EAAE,KAAK,GAAG,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO;AAC/D,EAAE,MAAM;AACR,IAAI,EAAE;AACN,IAAI,KAAK;AACT,IAAI,WAAW;AACf,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,MAAM;AACV,IAAI,sBAAsB,GAAG,KAAK;AAClC,IAAI;AACJ,GAAG,GAAG,KAAK;AACX,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,gBAAgB,GAAG,MAAM;AAC/B,EAAE,SAAS,eAAe,GAAG;AAC7B,IAAI,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,gBAAgB,CAAC;AAC7E,IAAI,OAAO,YAAY,EAAE,KAAK,IAAI,EAAE;AACpC,EAAE;AACF,EAAE,SAAS,kBAAkB,GAAG;AAChC,IAAI,OAAO,gBAAgB,CAAC,WAAW,EAAE;AACzC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC1C,EAAE,IAAI,sBAAsB,KAAK,KAAK,EAAE;AACxC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,yBAAyB,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACrK,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kCAAkC,CAAC,CAAC;AAC9D,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qHAAqH,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,0IAA0I,EAAE,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,qDAAqD,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,gBAAgB,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,0BAA0B,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,gBAAgB,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,+BAA+B,CAAC,CAAC;AACxwB,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,yBAAyB,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACjG,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kCAAkC,CAAC,CAAC;AAC9D,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,CAAC;AAC1C,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uCAAuC,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5K,EAAE,MAAM,MAAM,GAAG,WAAW,CAAC,eAAe,EAAE,CAAC;AAC/C,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC;AACpC,EAAE,IAAI,sBAAsB,KAAK,QAAQ,EAAE;AAC3C,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2GAA2G,CAAC,CAAC;AACrI,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,QAAQ,CAAC,SAAS,CAAC;AACzB,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mHAAmH,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,0IAA0I,EAAE,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,qDAAqD,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,gBAAgB,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,0BAA0B,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,gBAAgB,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,+BAA+B,CAAC,CAAC;AACtwB,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACxB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/D,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wCAAwC,EAAE,WAAW,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,mDAAmD,CAAC,CAAC;AAC3J,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC;AACnC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0CAA0C,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AACtM,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mBAAmB,CAAC,CAAC;AAC7C,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC;AAChC,EAAE,GAAG,EAAE;AACP;;;;"}