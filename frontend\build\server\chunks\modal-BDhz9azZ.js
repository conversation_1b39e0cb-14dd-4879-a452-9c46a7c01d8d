import { u as push, J as attr_class, z as escape_html, P as clsx, y as attr, w as pop } from './index-0Ke2LYl0.js';

/* empty css                                                       */
function Modal($$payload, $$props) {
  push();
  const {
    children,
    show = false,
    title = "",
    onClose,
    onSubmit,
    submitText = "Submit",
    cancelText = "Cancel",
    submitDisabled = false,
    cancelDisabled = false,
    isSubmitting = false,
    size = "md",
    centered = true,
    showFooter = true,
    showCloseButton = true,
    showSubmitButton = true
  } = $$props;
  const modalDialogClass = `modal-dialog modal-${size} ${centered ? "modal-dialog-centered" : ""}`;
  if (show) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="modal fade show" style="display: block;" tabindex="-1" aria-modal="true" role="dialog"><div class="modal-backdrop fade show svelte-1swe5em"></div> <div${attr_class(clsx(modalDialogClass), "svelte-1swe5em")}><div class="modal-content svelte-1swe5em"><div class="modal-header svelte-1swe5em"><h5 class="modal-title svelte-1swe5em">${escape_html(title)}</h5> `);
    if (showCloseButton) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<button type="button" class="btn-close" aria-label="Close"></button>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> <div class="modal-body svelte-1swe5em">`);
    children($$payload);
    $$payload.out.push(`<!----></div> `);
    if (showFooter) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="modal-footer svelte-1swe5em"><button type="button" class="btn btn-secondary"${attr("disabled", cancelDisabled, true)}>${escape_html(cancelText)}</button> `);
      if (showSubmitButton) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<button type="button" class="btn btn-primary"${attr("disabled", submitDisabled || isSubmitting, true)}>${escape_html(isSubmitting ? `${submitText}...` : submitText)}</button>`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}

export { Modal as M };
//# sourceMappingURL=modal-BDhz9azZ.js.map
