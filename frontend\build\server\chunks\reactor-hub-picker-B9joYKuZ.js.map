{"version": 3, "file": "reactor-hub-picker-B9joYKuZ.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/reactor-hub-picker.js"], "sourcesContent": ["import { u as push, z as escape_html, V as bind_props, w as pop } from \"./index.js\";\nimport { g as getClient } from \"./acrpc.js\";\n/* empty css                                                       */\nfunction Reactor_hub_picker($$payload, $$props) {\n  push();\n  const i18n = {\n    en: {\n      hub: \"Hub\",\n      selectHub: \"Select hub\",\n      searchHubs: \"Search hubs...\",\n      noHubsFound: \"No hubs found\",\n      loading: \"Loading...\",\n      clearSelection: \"Clear selection\"\n    },\n    ru: {\n      hub: \"Хаб\",\n      selectHub: \"Выбрать хаб\",\n      searchHubs: \"Поиск хабов...\",\n      noHubsFound: \"Хабы не найдены\",\n      loading: \"Загрузка...\",\n      clearSelection: \"Очистить выбор\"\n    }\n  };\n  const { fetcher: api } = getClient();\n  let { selectedHubId = void 0, locale, label, placeholder } = $$props;\n  const t = i18n[locale];\n  let showHubInput = false;\n  $$payload.out.push(`<div class=\"mb-3\">`);\n  if (label && showHubInput) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<label for=\"hub-search-input\" class=\"form-label\">${escape_html(label)}</label>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> <div class=\"hub-picker svelte-11tmxf3\"><div class=\"selected-hub d-flex align-items-center gap-2 mb-2 svelte-11tmxf3\">`);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n    {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<button type=\"button\" class=\"btn btn-outline-secondary\"><i class=\"bi bi-collection-fill\"></i> ${escape_html(t.selectHub)}</button>`);\n    }\n    $$payload.out.push(`<!--]-->`);\n  }\n  $$payload.out.push(`<!--]--></div> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div></div>`);\n  bind_props($$props, { selectedHubId });\n  pop();\n}\nexport {\n  Reactor_hub_picker as R\n};\n"], "names": [], "mappings": ";;;AAEA;AACA,SAAS,kBAAkB,CAAC,SAAS,EAAE,OAAO,EAAE;AAChD,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,EAAE,EAAE;AACR,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,UAAU,EAAE,gBAAgB;AAClC,MAAM,WAAW,EAAE,eAAe;AAClC,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,cAAc,EAAE;AACtB,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,UAAU,EAAE,gBAAgB;AAClC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,cAAc,EAAE;AACtB;AACA,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;AACtC,EAAE,IAAI,EAAE,aAAa,GAAG,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,OAAO;AACtE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,EAAE,IAAI,YAAY,GAAG,KAAK;AAC1B,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAC1C,EAAE,IAAI,KAAK,IAAI,YAAY,EAAE;AAC7B,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iDAAiD,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC;AACxG,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8HAA8H,CAAC,CAAC;AACtJ,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI;AACJ,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,8FAA8F,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC;AAC9J,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AACvC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,CAAC;AACxC,EAAE,GAAG,EAAE;AACP;;;;"}