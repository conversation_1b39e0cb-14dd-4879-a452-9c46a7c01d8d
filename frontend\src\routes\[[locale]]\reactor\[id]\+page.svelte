<script lang="ts">
  import type { Common } from "@commune/api";
  import type { CommentEntity } from "./comment-tree";

  import { getClient } from "$lib/acrpc";
  import PostCard from "../post-card.svelte";
  import RightMenu from "../right-menu.svelte";
  import Comment from "./comment.svelte";
  import { CommentTree } from "./comment-tree";
  import { LocalizedTextarea } from "$lib/components";
  import CreatePostModal from "$lib/components/create-post-modal.svelte";

  const i18n = {
    en: {
      reactor: "Reactor",
      comments: "Comments",
      commentPlaceholder: "Write your comment...",
      submit: "Submit",
    },
    ru: {
      reactor: "Реактор",
      comments: "Комментарии",
      commentPlaceholder: "Напишите ваш комментарий...",
      submit: "Отправить",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, routeLocale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  let post = $state(data.post);

  const title = $derived(getAppropriateLocalization(post.title));

  let comments = $state<CommentEntity[]>(data.comments);
  const commentTree = $derived(new CommentTree(comments));

  let commentText = $state<Common.Localizations>([]);

  // Edit modal state
  let showEditModal = $state(false);
  let postToEdit = $state<typeof post | undefined>(undefined);

  function handleEditPost(postData: typeof post) {
    postToEdit = postData;
    showEditModal = true;
  }

  function handleCloseEditModal() {
    showEditModal = false;
    postToEdit = undefined;
  }

  function handlePostUpdated() {
    // Refresh the page to show updated content
    window.location.reload();
  }

  async function addComment(id: string) {
    const [comment] = await api.reactor.comment.list.get({ id });

    if (!comment) {
      throw new Error("Comment not found");
    }

    comments = [
      ...comments,
      {
        ...comment,
        isMustBeTop: true,
      },
    ];

    const parentPath = commentTree.getParentPath(comment.path);

    if (parentPath) {
      commentTree.incrementChildrenCount(parentPath);
    }
  }

  async function submitComment() {
    const { id } = await api.reactor.comment.post({
      entityType: "post",
      entityId: post.id,
      body: commentText,
    });

    commentText = [];

    addComment(id);
  }
</script>

<svelte:head>
  <title>{title} — {t.reactor}</title>
</svelte:head>

<div class="row g-4 mt-3">
  <!-- Left spacer - only on desktop -->
  <div class="col-1 d-none d-xl-block"></div>

  <!-- Main Content - responsive columns -->
  <div class="col-12 col-lg-8 col-xl-8">
    <div class="post-detail">
      <PostCard
        {locale}
        {post}
        {toLocaleHref}
        {getAppropriateLocalization}
        currentUser={data.user}
        onEditPost={handleEditPost}
      />

      <!-- Comments Section -->
      <div class="comments-section mt-4">
        <h4 class="mb-3">{t.comments} ({comments.length})</h4>

        <div class="comments-list">
          {#if commentTree}
            {#each commentTree.getRootComments() as comment (comment.id)}
              <Comment
                {comment}
                {locale}
                {routeLocale}
                expanded={false}
                {commentTree}
                {addComment}
                {toLocaleHref}
                {getAppropriateLocalization}
              />
            {/each}
          {/if}

          <!-- {#each comments as comment (comment.id)}
            <Comment {comment} {locale} expanded={false} />
          {/each} -->
        </div>
      </div>

      <!-- Post Comment Form -->
      <div class="post-comment-form mt-4">
        <LocalizedTextarea
          {locale}
          id="post-comment"
          label=""
          placeholder={t.commentPlaceholder}
          rows={3}
          bind:value={commentText}
          languageSelectPosition="bottom"
        >
          <button class="btn btn-success btn-sm" onclick={submitComment}>
            <i class="bi bi-send me-1"></i>
            {t.submit}
          </button>
        </LocalizedTextarea>
      </div>
    </div>
  </div>

  <!-- Right Menu - only visible on large screens -->
  <div class="col-2 d-none d-lg-block">
    <RightMenu {locale} {toLocaleHref} />
  </div>

  <!-- Right spacer - only on desktop -->
  <div class="col-1 d-none d-xl-block"></div>
</div>

<!-- Edit Post Modal -->
<CreatePostModal
  show={showEditModal}
  {locale}
  {toLocaleHref}
  onClose={handleCloseEditModal}
  onPostCreated={handlePostUpdated}
  post={postToEdit}
/>

<style>
  .comments-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
  }

  .post-detail {
    max-width: 100%;
  }

  /* Mobile optimizations */
  @media (max-width: 767.98px) {
    .post-detail {
      padding: 0 0.5rem;
    }

    .comments-section {
      padding: 1rem;
      margin: 1rem 0;
      border-radius: 0.375rem;
    }

    .post-comment-form {
      padding: 0 0.5rem;
    }
  }

  /* Tablet optimizations */
  @media (min-width: 768px) and (max-width: 991.98px) {
    .post-detail {
      padding: 0 1rem;
    }

    .comments-section {
      padding: 1.25rem;
    }
  }

  /* Desktop optimizations */
  @media (min-width: 992px) {
    .post-detail {
      padding: 0;
    }
  }
</style>
