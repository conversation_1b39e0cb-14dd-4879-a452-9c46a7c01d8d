import {
  dir,
  jsonTransformer,
  kebabTransformer,
  log
} from "../chunk-P75QZ3CQ.mjs";
import "../chunk-J5LGTIGS.mjs";

// src/acrpc/client.ts
import { z } from "zod";
function isEndpoint(schemaEntry) {
  return schemaEntry != null && typeof schemaEntry === "object" && ("input" in schemaEntry && (schemaEntry["input"] instanceof z.ZodType || schemaEntry["input"] === null || schemaEntry["input"] === void 0) && ("output" in schemaEntry && (schemaEntry["output"] instanceof z.ZodType || schemaEntry["output"] === null || schemaEntry["output"] === void 0)));
}
var HttpError = class extends Error {
  method;
  url;
  status;
  description;
  constructor(method, url, status, description) {
    super(`Fetch at ${method.toUpperCase()} ${url} failed, status ${status}, description: '${description}'`);
    this.method = method;
    this.url = url;
    this.status = status;
    this.description = description;
  }
};
function getLocalStorage() {
  if (typeof window !== "undefined") {
    return window.localStorage ?? null;
  }
  if (typeof globalThis !== void 0) {
    return globalThis.localStorage ?? null;
  }
  return null;
}
function parsePathToMasterPaths(path) {
  if (!path.length) {
    return [];
  }
  const parts = path.split("/").slice(1);
  const masterPaths = [];
  for (let i = 0; i < parts.length; i++) {
    masterPaths.push("/" + parts.slice(0, i + 1).join("/"));
  }
  return masterPaths;
}
function initMasterPathMapEntryFactory(map) {
  return function initMasterPathMapEntry(path) {
    const masterPaths = parsePathToMasterPaths(path);
    for (const masterPath of masterPaths) {
      if (!map.has(masterPath)) {
        map.set(masterPath, []);
      }
      map.get(masterPath).push(path);
    }
  };
}
function normalizeMasterPathMap(masterPathMap) {
  for (const [masterPath, paths] of masterPathMap) {
    masterPathMap.set(masterPath, [...new Set(paths)]);
  }
}
function fillReverseMasterPathMap(masterPathMap, reverseMasterPathMap) {
  for (const [masterPath, paths] of masterPathMap) {
    for (const path of paths) {
      if (!reverseMasterPathMap.has(path)) {
        reverseMasterPathMap.set(path, []);
      }
      reverseMasterPathMap.get(path).push(masterPath);
    }
  }
}
function hydrateInvalidPathCacheSet(invalidPathCacheSet) {
  const invalidPaths = getLocalStorage()?.getItem("acrpc:invalid-paths");
  if (invalidPaths) {
    try {
      const parsedInvalidPaths = JSON.parse(invalidPaths);
      for (const invalidPath of parsedInvalidPaths) {
        invalidPathCacheSet.add(invalidPath);
      }
    } catch (error) {
      console.error("Error parsing invalid paths", error);
      getLocalStorage()?.removeItem("acrpc:invalid-paths");
    }
  }
}
function invalidatePathCache2Factory(masterPathMap, reverseMasterPathMap, invalidPathCacheSet) {
  return function invalidatePathCache2(path, depth) {
    const masterPaths = masterPathMap.get(path) ?? [];
    dir(
      "invalidating path cache",
      {
        path,
        depth,
        masterPaths
      }
    );
    const masterPath = masterPaths[Math.max(0, masterPaths.length - depth)];
    const paths = reverseMasterPathMap.get(masterPath) ?? [];
    dir(
      "invalidating path cache 2",
      {
        masterPath,
        paths
      }
    );
    for (const path2 of paths) {
      invalidPathCacheSet.add(path2);
    }
    getLocalStorage()?.setItem(
      "acrpc:invalid-paths",
      JSON.stringify([...invalidPathCacheSet])
    );
  };
}
function createClient(schema, options) {
  const transformer = options.transformer ?? jsonTransformer;
  const entrypointUrl = options.entrypointUrl.endsWith("/") ? options.entrypointUrl.slice(0, -1) : options.entrypointUrl;
  const masterPathMap = /* @__PURE__ */ new Map();
  const reverseMasterPathMap = /* @__PURE__ */ new Map();
  const invalidPathCacheSet = /* @__PURE__ */ new Set();
  const initMasterPathMapEntry = initMasterPathMapEntryFactory(masterPathMap);
  const invalidatePathCache2 = invalidatePathCache2Factory(
    masterPathMap,
    reverseMasterPathMap,
    invalidPathCacheSet
  );
  dir({
    invalidPathCacheSet
  });
  const baseFetch = options.fetch ?? fetch;
  const baseInit = { ...options.init };
  function fillClientFetcher(schema2, names, result) {
    for (const [name, schemaEntry] of Object.entries(schema2)) {
      const kebabName = kebabTransformer.transform(name);
      if (isEndpoint(schemaEntry)) {
        let parseArgs = function(args) {
          if (schemaEntry.input === null) {
            return [void 0, { ...args[0] }];
          }
          return [args[0], args[1]];
        };
        const path = ["", ...names].join("/");
        const method = name;
        initMasterPathMapEntry(path);
        const obj = {
          [method]: async function(...args) {
            const [input, init] = parseArgs(args);
            if (schemaEntry.input != null && !input) {
              throw new Error("Input data argument not provided.");
            }
            log(`Performing ${method.toUpperCase()} ${path}...`);
            dir({
              entrypointUrl,
              path
            });
            const isInvalidCache = invalidPathCacheSet.has(path);
            const requestInit = {
              ...baseInit,
              ...init,
              headers: {
                ...baseInit.headers,
                ...init?.headers,
                ...isInvalidCache ? {
                  "Cache-Control": "reload"
                } : null
              },
              method: method.toUpperCase()
            };
            let searchQuery = "";
            if (schemaEntry.input !== null && input !== void 0) {
              const serializedInput = transformer.serialize(input);
              if (method === "get") {
                searchQuery = `__body=${encodeURIComponent(serializedInput)}`;
              } else {
                requestInit.headers["Content-Type"] = "application/json";
                requestInit.body = serializedInput;
              }
            }
            const fetch2 = init?.fetch ?? baseFetch;
            delete init?.fetch;
            const search = searchQuery ? `?${searchQuery}` : "";
            dir({
              fetchUrl: entrypointUrl + path + search
            });
            const fetchResult = await fetch2(
              entrypointUrl + path + search,
              requestInit
            );
            if (!init?.skipInterceptor) {
              await options.interceptor?.({
                method,
                path,
                response: fetchResult,
                ctx: init?.ctx
              });
            }
            if (fetchResult.ok) {
              let output = null;
              if (schemaEntry.output !== null) {
                const rawOutput = await fetchResult.text();
                output = transformer.deserialize(rawOutput);
              }
              dir({
                autoScopeInvalidationDepth: schemaEntry.autoScopeInvalidationDepth,
                invalidate: schemaEntry.invalidate
              });
              dir(
                "before invalidations",
                {
                  masterPathMap,
                  // cacheVersionMap,
                  invalidPathCacheSet
                }
              );
              const autoScopeInvalidationDepth = schemaEntry.autoScopeInvalidationDepth ?? 0;
              if (autoScopeInvalidationDepth) {
                invalidatePathCache2(path, autoScopeInvalidationDepth);
              }
              if (schemaEntry.invalidate) {
                for (const invalidate of schemaEntry.invalidate) {
                  invalidatePathCache2(invalidate, 0);
                }
              }
              dir(
                "after invalidations",
                {
                  masterPathMap,
                  // cacheVersionMap,
                  invalidPathCacheSet
                }
              );
              return output;
            }
            throw new HttpError(
              method,
              path,
              fetchResult.status,
              await fetchResult.text() || fetchResult.statusText
            );
          }
        };
        Object.assign(result, obj);
      } else {
        const nestedResult = result[name] = {};
        fillClientFetcher(
          schemaEntry,
          [...names, kebabName],
          nestedResult
        );
      }
    }
    return result;
  }
  const fetcher = fillClientFetcher(
    schema,
    [],
    {}
  );
  normalizeMasterPathMap(masterPathMap);
  fillReverseMasterPathMap(masterPathMap, reverseMasterPathMap);
  hydrateInvalidPathCacheSet(invalidPathCacheSet);
  return {
    fetcher
  };
}
export {
  HttpError,
  createClient
};
//# sourceMappingURL=client.mjs.map